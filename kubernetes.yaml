---
apiVersion: "v1"
kind: "List"
items:
  # this backend config is used in multiple deployments - dont touch it without knowing what you do :D - and it will affect all of the apps in allo-frontend
  - apiVersion: cloud.google.com/v1
    kind: BackendConfig
    metadata:
      labels:
        app: "gluttony-client"
        app.kubernetes.io/name: "gluttony-client"
        app.kubernetes.io/instance: "gluttony-client"
        app.kubernetes.io/component: "gluttony-client"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
      name: "gluttony-client"
    spec:
      connectionDraining:
        drainingTimeoutSec: 60
      healthCheck:
        checkIntervalSec: 15
        port: 4160
        requestPath: /health-check
        type: HTTP
      securityPolicy:
        name: global-sp-default
      timeoutSec: 80
      customResponseHeaders:
        headers:
        - 'Strict-Transport-Security: max-age=86400; includeSubDomains'
        - 'X-Permitted-Cross-Domain-Policies: none'
        - 'X-Frame-Options: SAMEORIGIN'
        - 'Cross-Origin-Resource-Policy: same-origin'
        - 'Cross-Origin-Embedder-Policy: same-origin'
        - 'Cross-Origin-Opener-Policy: same-origin'
        - 'X-Content-Type-Options: nosniff'
        - 'Referrer-Policy: strict-origin-when-cross-origin'
  - apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      labels:
        app: "gluttony-client"
        app.kubernetes.io/name: "gluttony-client"
        app.kubernetes.io/instance: "gluttony-client"
        app.kubernetes.io/component: "gluttony-client"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
      name: "gluttony-client"
    spec:
      minAvailable: 1
      selector:
        matchLabels:
          app: "gluttony-client"
          #app.kubernetes.io/name: "gluttony-client"
          #app.kubernetes.io/instance: "gluttony-client"
  - apiVersion: "v1"
    kind: "Service"
    metadata:
      annotations:
        cloud.google.com/backend-config: '{"default":"gluttony-client"}' # backend configuration to use (eg for cloud armor)
      labels:
        app: "gluttony-client"
        app.kubernetes.io/name: "gluttony-client"
        app.kubernetes.io/instance: "gluttony-client"
        app.kubernetes.io/component: "gluttony-client"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
      name: "gluttony-client"
    spec:
      ports:
        - name: "http"
          port: 80
          targetPort: 4160
      selector:
        app: "gluttony-client"
        #app.kubernetes.io/name: "gluttony-client"
        #app.kubernetes.io/instance: "gluttony-client"
  - apiVersion: "apps/v1"
    kind: "Deployment"
    metadata:
      labels:
        app: "gluttony-client"
        app.kubernetes.io/name: "gluttony-client"
        app.kubernetes.io/instance: "gluttony-client"
        app.kubernetes.io/component: "gluttony-client"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
        kube-monkey/enabled: enabled
        kube-monkey/identifier: "gluttony-client"
        kube-monkey/kill-mode: fixed
        kube-monkey/kill-value: "1"
        kube-monkey/mtbf: "2"
      name: "gluttony-client"
    spec:
      replicas: 2
      selector:
        matchLabels:
          app: "gluttony-client"
      template:
        metadata:
          labels:
            app: "gluttony-client"
            app.kubernetes.io/name: "gluttony-client"
            app.kubernetes.io/instance: "gluttony-client"
            app.kubernetes.io/component: "gluttony-client"
            app.kubernetes.io/component-type: "nodejs"
            app.kubernetes.io/part-of: "allO"
            kube-monkey/enabled: enabled
            kube-monkey/identifier: "gluttony-client"
            kube-monkey/kill-mode: fixed
            kube-monkey/kill-value: "1"
            kube-monkey/mtbf: "2"
          annotations:
            # important for emptyDir volume mounts            
            cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
        spec:
          topologySpreadConstraints:
            - labelSelector:
                matchLabels:
                  app.kubernetes.io/name: "gluttony-client"
                  app.kubernetes.io/instance: "gluttony-client"
              matchLabelKeys:
                - pod-template-hash
              maxSkew: 1
              whenUnsatisfiable: DoNotSchedule
              topologyKey: "kubernetes.io/hostname"
        spec:
          imagePullSecrets:
            - name: artifact-registry-publisher
          containers:
          - env:
            - name: "KUBERNETES_NAMESPACE"
              valueFrom:
                fieldRef:
                  fieldPath: "metadata.namespace"
            - name: FRONT_CHAT_ID
              value: 599434b39c0a372153b94f237d8b5cac
            - name: FRONT_CHAT_IDENTITY
              value: 81aaaf2368e8e2f9dd12750cb3a317b1
            image: "SERVICE-VERSION"
            imagePullPolicy: "IfNotPresent"
            name: "gluttony-client"
            resources:
              requests:
                cpu: 25m
                memory: 128Mi
              limits:
                memory: 512Mi
            lifecycle:
              preStop:
                exec:
                  command: 
                  - "/bin/sh"
                  - "-ce" 
                  - |
                    sleep 10 # give k8s svc balancer the chance to unregister routing before shutting down
            ports:
              - containerPort: 4160
                name: "http"
                protocol: "TCP"
            startupProbe:
              httpGet:
                scheme: HTTP
                path: /health-check
                port: 4160
              periodSeconds: 5
              timeoutSeconds: 10
              successThreshold: 1
              failureThreshold: 40 # number of tries every periodSeconds - total time would be failureThreshold * periodSeconds
            readinessProbe:
              httpGet:
                scheme: HTTP
                path: /health-check
                port: 4160
              initialDelaySeconds: 5
              periodSeconds: 10
              timeoutSeconds: 3
              successThreshold: 2
              failureThreshold: 10
            livenessProbe:
              httpGet:
                #Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.
                scheme: HTTP
                #Path to access on the HTTP server.
                path: /health-check
                #Name or number of the port to access on the container. Number must be in the range 1 to 65535.
                port: 4160
              #Number of seconds after the container has started before liveness or readiness probes are initiated. Defaults to 0 seconds. Minimum value is 0.
              initialDelaySeconds: 5
              #How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.
              periodSeconds: 10
              #Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1.
              timeoutSeconds: 3
              #Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness. Minimum value is 1.
              successThreshold: 1
              #When a Pod starts and the probe fails, Kubernetes will try failureThreshold times before giving up. Giving up in case of liveness probe means restarting the container. In case of readiness probe the Pod will be marked Unready. Defaults to 3. Minimum value is 1
              failureThreshold: 10
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              seccompProfile:
                type: "RuntimeDefault"
              capabilities:
                drop:
                  - "ALL"
            volumeMounts:
              - mountPath: /tmp
                name: tmp-volume
              - mountPath: /var/run
                name: tmp-run-volume
              - mountPath: /var/log
                name: tmp-log-volume
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          terminationGracePeriodSeconds: 20
          volumes:
          - emptyDir: {}
            name: tmp-volume
          - emptyDir: {}
            name: tmp-run-volume
          - emptyDir: {}
            name: tmp-log-volume
