module.exports = () => {

  const publicRuntimeConfig = {
    basePath: '',
    localeSubpaths: 'none',
    API_URL: (() => {
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'staging') return '';
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'production') return '';
      if (!process.env.NEXT_PUBLIC_LEVIEE_ENV) return 'https://app-dev.allo.restaurant'
    })(),
    APPLICATION_ENV: (() => {
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'staging') return 'development';
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'production') return 'production';
      if (!process.env.NEXT_PUBLIC_LEVIEE_ENV) return 'local';
    })(),
    CLIENT_SECRET: (() => {
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'staging') return 'ab967a01-ee4b-4a0c-ba83-00d2596d7244';
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'production') return 'c6050ec7-7c97-4b41-95ab-dc92c22058f9';
      if (!process.env.NEXT_PUBLIC_LEVIEE_ENV) return 'ab967a01-ee4b-4a0c-ba83-00d2596d7244';
    })(),
    RESTAURANT_APP_CLIENT_SECRET: (() => {
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'staging') return "d7993ccc-a4e3-4c63-a9e7-132deafaf686";
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'production') return '4c82438b-7f13-48d9-b8a9-83d9143a649c';
      if (!process.env.NEXT_PUBLIC_LEVIEE_ENV) return "67e098b2-9248-4be2-a6c2-ee2ad1ea7bef";
    })(),
    ADMINISTRATION_URI: (() => {
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'staging') return 'https://restaurant-preview.leviee.de';
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'production') return 'https://restaurant.leviee.de';
      if (!process.env.NEXT_PUBLIC_LEVIEE_ENV) return 'http://localhost:4160/administration';
    })(),
    BETA_DINE_IN_V2_URL: (() => {
      if (!process.env.NEXT_PUBLIC_LEVIEE_ENV) {
        return 'http://localhost:3002/';
      }

      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'production') {
        return 'https://operations.stage.allo.restaurant';
      } 

      return 'https://operations.dev.allo.restaurant';
    })(),
    COOKIE_PWD: '9FTC5tCeNsa9GnBJJyjw',
    CRISP_ID: 'f8161227-6f85-406b-8291-832c319f4766',
    FRONT_CHAT_ID: process.env.FRONT_CHAT_ID, //'599434b39c0a372153b94f237d8b5cac',
    FRONT_CHAT_IDENTITY: process.env.FRONT_CHAT_IDENTITY, //'81aaaf2368e8e2f9dd12750cb3a317b1'
    POSTHOG_KEY: process.env.POSTHOG_KEY,
  };

  const webpack = (config, { isServer }) => {
    // Fixes npm packages that depend on `fs` module
    // if (!isServer) {
    //   config.resolve.fallback = {
    //     fs: false,
    //     path: require.resolve("path-browserify")
    //   };
    //
    // }
    //
    // config.output.hashFunction = 'sha256'
    // console.log(config.output.hashFunction)
    // return config


    if (!isServer) {
      config.node = {
        fs: 'empty'
      }
    }

    return config

  };

  return {
    publicRuntimeConfig,
    webpack,
    future: {
      webpack5: false,
    },
    eslint: {
      enable: false
    }
  }

  // const withBundleAnalyzer = require('@next/bundle-analyzer')({
  //   enabled: process.env.ANALYZE === 'true',
  // })

  // return withBundleAnalyzer({
  //   publicRuntimeConfig,
  //   webpack,
  //   eslint: {
  //     enable: false
  //   }
  // })
};
