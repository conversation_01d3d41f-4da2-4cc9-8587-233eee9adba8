{"login-by-username-password-failed": "ユーザー名またはパスワードが間違っています。", "login-failed": "アカウントを確認できませんでした。もう一度お試しください。", "verify-one-time-password-failed": "コードが無効です。もう一度お試しください。", "complete-account-failed": "アカウントを完了できませんでした。", "complete-account-conflict-with-mobile-secondary": "携帯電話番号は既に登録されています。", "complete-account-conflict-with-email-secondary": "メールアドレスは既に登録されています。", "create-restaurant-conflict-restaurant-exists": "同様のレストランが既にallOに存在します！", "create-restaurant-failed": "申し訳ありませんが、レストランを追加できませんでした。もう一度お試しください。", "network-status-offline": "再接続を試みています...", "exploration-status": "探索モード", "pushing-print-request": "印刷リクエストをプッシュしています", "sold-gift-card-created": "{{amount}}€のギフトカードが作成されました。", "stock-gift-card-created": "在庫のギフトカードが作成されました。", "gift-card-topped-up": "{{code}}のギフトカードに{{amount}}€が追加されました。", "gift-card-updated": "{{code}}のギフトカードが更新されました。", "gift-card-payment-failed": "支払いを処理できませんでした。", "account-updated": "アカウントが更新されました。", "account-update-error": "エラーが発生しました。後でもう一度お試しください。", "file-upload-started": "ファイルをアップロードしています...", "file-uploaded": "ファイルが正常にアップロードされました。", "file-upload-error": "ファイルをアップロードできませんでした。{{description}}", "shareholder-created": "株主が作成されました。", "shareholder-creation-error": "株主を追加できませんでした。", "shareholder-deleted": "株主が削除されました。", "shareholder-deletion-error": "株主を削除できませんでした。", "sending-selected-payment-receipt-to-email": "{{email}}に支払いのデジタルレシートを送信しています", "sending-pdf-receipt-to-email": "{{email}}にデジタルレシートを送信しています", "receipt-generated-for-email": "{{email}}用のレシートが生成されました。", "receipt-not-generated-for-email": "{{email}}用のレシートを生成できませんでした。", "tables-could-not-be-created-notification": "最初にテーブルを作成するフロアを選択してください。", "tables-created-notification": "テーブルが正常に作成されました。", "dynamic-qr-code-linked": "動的QRコードがリンクされました。", "dynamic-qr-code-not-linked": "動的QRコードのリンクに失敗しました。{{ title }}", "dynamic-qr-code-unlinked": "動的QRコードが切断されました。", "preferences-updated": "設定が更新されました。", "preferences-update-error": "エラーが発生しました。後でもう一度お試しください。", "customer-will-be-notified": "予約がキャンセルされました。顧客にはメールで通知されます。", "account-pin-updated": "アカウントPINが更新されました。", "account-pin-update-error": "アカウントPINを更新できませんでした。", "item-added-notification": "アイテムが追加されました。", "snapshot-created-notification": "メニューのスナップショットが正常に撮影されました。", "menu-could-not-be-published-to-wolt": "メニューを公開できませんでした。", "integration-setup-failed": "統合の設定に失敗しました。サポートに連絡してください。", "integration-setup-success": "統合設定が完了しました。", "integration-removed": "統合が削除されました。", "removing-integration-failed": "統合を削除できませんでした。サポートに連絡してください。", "menu-successfully-published-to-wolt": "メニューが正常に公開されました。", "floor-plan-updated": "レイアウトが正常に更新されました。", "floor-plan-update-error": "レイアウトを更新できませんでした。", "printer-updated": "プリンターが正常に更新されました。", "printer-created": "プリンターが正常に作成されました。", "printer-update-error": "プリンターを更新できませんでした。", "printer-creation-error": "プリンターを作成できませんでした。", "payment-terminal-updated": "ターミナルが正常に更新されました。", "payment-terminal-created": "ターミナルが正常に作成されました。", "payment-terminal-update-error": "ターミナルを更新できませんでした。", "payment-terminal-creation-error": "ターミナルを作成できませんでした。", "guest-monitor-updated": "サイドスクリーンが正常に更新されました。", "guest-monitor-created": "サイドスクリーンが正常に作成されました。", "guest-monitor-update-error": "サイドスクリーンを更新できませんでした。", "guest-monitor-creation-error": "サイドスクリーンを作成できませんでした。", "user-invited": "ユーザーが正常に招待されました。", "user-invite-error": "ユーザーを招待できませんでした。メールアドレス/電話番号を確認してください。", "user-updated": "ユーザーが正常に更新されました。", "user-update-error": "ユーザーを更新できませんでした。", "user-removed": "ユーザーが正常に削除されました。", "user-removal-error": "ユーザーを削除できませんでした。", "supplier-updated": "サプライヤーが正常に更新されました。", "supplier-update-error": "サプライヤーを更新できませんでした。", "supplier-created": "サプライヤーが正常に作成されました。", "supplier-creation-error": "サプライヤーを作成できませんでした。", "SKU-item-updated": "SKUアイテムが正常に更新されました。", "SKU-item-update-error": "SKUアイテムを更新できませんでした。", "SKU-item-created": "SKUアイテムが正常に作成されました。", "SKU-item-creation-error": "SKUアイテムを作成できませんでした。", "ingredient-updated": "成分が正常に更新されました。", "ingredient-update-error": "成分を更新できませんでした。", "ingredient-created": "成分が正常に作成されました。", "ingredient-creation-error": "成分を作成できませんでした。", "supplier-invoice-creation-error": "請求書を作成できませんでした。", "supplier-invoice-created": "請求書が正常に作成されました。", "promotion-updated": "プロモーションが正常に更新されました。", "promotion-update-error": "プロモーションを更新できませんでした。", "promotion-created": "プロモーションが正常に作成されました。", "promotion-creation-error": "プロモーションを作成できませんでした。", "card-updated": "カードが正常に更新されました。", "card-update-error": "カードを更新できませんでした。", "card-created": "カードが正常に作成されました。", "card-creation-error": "カードを作成できませんでした。", "receipt-refunded": "注文が正常に払い戻されました。", "receipt-refund-error": "注文を払い戻すことができませんでした。", "new-order-when-sound-notification": "新しい注文が追加されました。", "amount-payout-created": "{{amount}}€の支払いが作成されました。", "amount-payout-not-created": "{{amount}}€の支払いを作成できませんでした。", "test-email-successfully-sent": "テストメールが正常に送信されました。", "test-email-error": "テストメールを送信できませんでした。", "settings-updated": "設定が正常に更新されました。", "settings-update-failed": "設定を保存できませんでした！", "items-updated": "アイテムが正常に更新されました。", "items-update-failed": "アイテムを更新できませんでした。", "email-not-valid": "このメールアドレスは無効です。", "phone-number-not-valid": "この電話番号は無効です。", "course-added": "コースが追加されました。", "could-not-add-course": "コースを追加できませんでした。", "select-limit-has-been-reached": "選択制限の6に達しました。", "reservation-successfully-recovered": "予約が正常に回復されました。", "reservation-could-not-be-recovered": "予約を回復できませんでした。", "ordering-device-updated": "注文デバイスが更新されました。", "ordering-device-created": "注文デバイスが作成されました。", "ordering-device-update-error": "注文デバイスを更新できませんでした。", "ordering-device-creation-error": "注文デバイスを作成できませんでした。", "creating-open-invoice": "請求書を作成しています。", "open-invoice-created": "請求書が作成されました。", "open-invoice-creation-error": "請求書を作成できませんでした。", "link-copied-success": "リンクが正常にコピーされました。", "link-copied-error": "リンクをコピーできませんでした。", "gift-card-successfully-deleted": "ギフトカードが削除されました。", "gift-card-could-not-be-deleted": "ギフトカードを削除できませんでした。", "item-duplication-success": "アイテムが正常に複製されました。", "item-duplication-error": "アイテムを複製できませんでした。", "item-delete-success": "アイテムが正常に削除されました。", "item-delete-error": "アイテムを削除できませんでした。", "copy-success": "正常にコピーされました。", "copy-error": "コピーできませんでした。", "invoice-deleted-success": "請求書が削除されました。", "invoice-deleted-error": "請求書を削除できませんでした。", "ordering-device-delete-success": "注文デバイスが正常に削除されました。", "ordering-device-delete-error": "注文デバイスを削除できませんでした。", "terminal-delete-success": "ターミナルが正常に削除されました。", "terminal-delete-error": "ターミナルを削除できませんでした。", "dynamic-code-delete-success": "QRコードが正常に削除されました。", "dynamic-code-delete-error": "QRコードを削除できませんでした。", "floor-delete-success": "フロアが正常に削除されました。", "floor-delete-error": "フロアを削除できませんでした。", "processing-inventory-invoice": "在庫請求書を処理しています。", "common-updated-success": "正常に更新されました。", "common-updated-error": "更新できませんでした: {{ error }}", "wrong-pin-when-adding-discount": "割引を追加できませんでした。確認コードが間違っています。", "delete-payment-method-success": "支払い方法が正常に削除されました。", "delete-payment-method-error": "支払い方法を削除できませんでした。", "set-payment-method-as-default-success": "支払い方法がデフォルトに設定されました。", "set-payment-method-as-default-error": "支払い方法をデフォルトに設定できませんでした。", "TSE-set-up-success": "TSEが正常に設定されました。", "TSE-set-up-error": "TSEを設定できませんでした。", "order-removal-success": "注文が削除されました", "order-removal-error": "注文を削除できませんでした", "gift-card-receipt-success": "領収書を送信しました", "gift-card-receipt-error": "領収書を送信できませんでした", "open-invoice-updated": "請求書が正常に更新されました", "open-invoice-updated-error": "請求書を更新できませんでした", "export-processing-start-success": "エクスポート処理が正常に開始されました", "export-processing-start-error": "エクスポート処理を開始できませんでした", "invoice-could-not-be-closed-please-check-data": "請求書を生成できませんでした。データを確認してください。", "kitchen-monitor-bulk-status-could-not-be-updated": "一括更新を使用するには、すべてのアイテムが同じステータスである必要があります", "kitchen-monitor-manual-printing-success": "アイテムが正常に印刷されました", "kitchen-monitor-manual-printing-error": "アイテムを印刷できませんでした", "email-sent-successfully": "メールが正常に送信されました", "email-could-not be-sent": "メールを送信できませんでした"}