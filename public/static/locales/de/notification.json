{"login-by-username-password-failed": "Benutzername oder Passwort war falsch.", "login-failed": "Wir konnten Dein Konto nicht validieren. Bitte versuche es erneut.", "verify-one-time-password-failed": "Dein Code ist ungültig, bitte versuche es erneut.", "complete-account-failed": "Wir konnten Dein Konto nicht vervollständigen.", "complete-account-conflict-with-mobile-secondary": "<PERSON><PERSON> ist bereits registriert.", "complete-account-conflict-with-email-secondary": "Deine E-mail-Adresse ist bereits registriert.", "create-restaurant-conflict-restaurant-exists": "Es gibt bereits ein ähnliches Restaurant auf allO!", "create-restaurant-failed": "<PERSON><PERSON> konnten wir das Restaurant nicht hinzufügen, bitte versuche es erneut.", "network-status-offline": "Verbindung wird hergestellt...", "exploration-status": "EXPLORATION MODUS", "pushing-print-request": "Druckauftrag erteilen", "sold-gift-card-created": "{{amount}}€ Gutschein erstellt.", "stock-gift-card-created": "<PERSON><PERSON> Lager Gutschein erstellt.", "gift-card-topped-up": "{{amount}}€ wurde auf den Gutschein {{code}} aufgeladen", "gift-card-updated": "Gutschein {{code}} aktualisiert", "gift-card-payment-failed": "Wir konnten die Zahlung nicht bearbeiten.", "account-updated": "Konto aktualisiert.", "account-update-error": "Etwas ist schief gelaufen, bitte versuche es später noch einmal.", "file-upload-started": "Datei wird hochgeladen ...", "file-uploaded": "<PERSON>i wurde erfolgreich hoch<PERSON>aden", "file-upload-error": "<PERSON>i konnte nicht hoch<PERSON>aden werden. {{description}}", "shareholder-created": "Anteils<PERSON><PERSON><PERSON> er<PERSON>", "shareholder-creation-error": "Anteilseigner konnte nicht erstellt werden.", "shareholder-deleted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shareholder-deletion-error": "Anteilseigner konnte nicht gelöscht werden.", "sending-selected-payment-receipt-to-email": "Digitale Quittung für Zahlung an {{email}} senden}", "sending-pdf-receipt-to-email": "<PERSON><PERSON>g an {{email}} senden", "receipt-generated-for-email": "Quittung für {{email}} generiert", "receipt-not-generated-for-email": "Quittung für {{email}} konnte nicht generiert werden", "tables-could-not-be-created-notification": "<PERSON>te wähle zu<PERSON>t den Raum aus auf dem Du deine Tische erstellen möchtest.", "tables-created-notification": "Tisch(e) erfolgreich erstellt.", "dynamic-qr-code-linked": "Dynamischer QR-Code verlinkt", "dynamic-qr-code-not-linked": "Verknüpfung des dynamischen QR-Codes fehlgeschlagen. {{ title }}", "dynamic-qr-code-unlinked": "Dynamic QR disconnected", "preferences-updated": "Präferenzen aktualisiert.", "preferences-update-error": "Etwas ist schief gelaufen, bitte versuche es später noch einmal.", "customer-will-be-notified": "Reservierung wurde storniert. Der Kunde wird per E-Mail informiert.", "account-pin-updated": "Account PIN aktualisiert", "account-pin-update-error": "Account PIN konnte nicht aktualisiert werden.", "item-added-notification": "<PERSON><PERSON><PERSON>", "snapshot-created-notification": "Menü Snapshot erfolgreich erstellt", "menu-could-not-be-published-to-wolt": "Menü konnte nicht aktualisiert werden", "integration-setup-failed": "Die Einrichtung der Integration ist fehlgeschlagen. Bitte kontaktiere den Support.", "integration-setup-success": "Die Einrichtung der Integration ist abgeschlossen.", "integration-removed": "Integration entfernt", "removing-integration-failed": "Die Entfernung der Integration ist fehlgeschlagen. Bitte kontaktiere den Support.", "menu-successfully-published-to-wolt": "<PERSON>ü erfolgreich veröffentlicht", "floor-plan-updated": "Layout erfolgreich aktualisiert", "floor-plan-update-error": "Layout konnte nicht aktualisiert werden", "printer-updated": "<PERSON><PERSON><PERSON> erfolgreich aktualisiert", "printer-created": "<PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON><PERSON> erste<PERSON>t", "printer-update-error": "<PERSON>ucker konnte nicht aktualisiert werden", "printer-creation-error": "<PERSON>ucker konnte nicht erstellt werden", "payment-terminal-updated": "Terminal erfolgreich aktualisiert", "payment-terminal-created": "Terminal erfolgreich erstellt", "payment-terminal-update-error": "Terminal konnte nicht aktualisiert werden", "payment-terminal-creation-error": "Terminal konnte nicht aktualisiert werden", "guest-monitor-updated": "Nebenbildschirm erfolgreich aktualisiert", "guest-monitor-created": "Nebenbildschirm erfolgreich erstellt", "guest-monitor-update-error": "Nebenbildschirm konnte nicht aktualisiert werden", "guest-monitor-creation-error": "Nebenbildschirm konnte nicht erstellt werden", "user-invited": "<PERSON>utzer erfolgreich e<PERSON>laden", "user-invite-error": "<PERSON>utzer konnte nicht eingeladen werden, überprüfe E-Mail/Telefonnummer", "user-updated": "Nutzer erfolgreich aktualisiert", "user-update-error": "<PERSON>utzer konnte nicht aktualisiert werden", "user-removed": "<PERSON>utzer erfolgreich entfernt", "user-removal-error": "<PERSON>utzer konnte nicht entfernt werden", "supplier-updated": "Lieferant erfolgreich aktualisiert", "supplier-update-error": "Lieferant konnte nicht aktualisiert werden", "supplier-created": "Lieferant erfolgreich erstellt", "supplier-creation-error": "Lieferant konnte nicht erstellt werden", "SKU-item-updated": "SKU erfolgreich aktualisiert", "SKU-item-update-error": "SKU  konnte nicht aktualisiert werden", "SKU-item-created": "SKU erfolgreich erstellt", "SKU-item-creation-error": "SKU  konnte nicht erstellt werden", "ingredient-updated": "Zutat erfolgreich aktualisiert", "ingredient-update-error": "Zutat konnte nicht aktualisiert werden", "ingredient-created": "Zutat erfolgreich erstellt", "ingredient-creation-error": "<PERSON>utat konnte nicht erstellt werden", "supplier-invoice-creation-error": "Rechnung konnte nicht erstellt werden", "supplier-invoice-created": "Rechnung erfolgreich erstellt", "promotion-updated": "Promotion erfolgreich aktualisiert", "promotion-update-error": "Promotion konnte nicht aktualisiert werden", "promotion-created": "Promotion erfolgreich erstellt", "promotion-creation-error": "Promotion konnte nicht erstellt werden", "card-updated": "Kundenkarte erfolgreich aktualisiert", "card-update-error": "Kundenkarte konnte nicht aktualisiert werden", "card-created": "Kundenkarte erfolgreich erstellt", "card-creation-error": "Kundenkarte konnte nicht erstellt werden", "receipt-refunded": "Bestellung erfolgreich erstattet", "receipt-refund-error": "Bestellung konnte nicht erstattet werden", "new-order-when-sound-notification": "Eine neue Bestellung ist eingegangen", "amount-payout-created": "Auszahlung von {{amount}}€ wurde erstellt", "amount-payout-not-created": "Auszahlung von {{amount}}€ konnte nicht erstellt werden", "test-email-successfully-sent": "Test E-mail erfolgreich gesendet", "test-email-error": "Test E-mail konnte nicht gesendet werden", "settings-updated": "Einstellungen erfolgreich aktualisiert", "settings-update-failed": "Einstellungen konnten nicht gesichert werden!", "items-updated": "Artikel erfolgreich aktualisiert", "items-update-failed": "Artikel konnten nicht aktualisiert werden", "email-not-valid": "Ungültige E-Mail Adresse", "phone-number-not-valid": "Ungültige Telefonnummer", "course-added": "Gang hinzugefügt", "could-not-add-course": "Gang konnte nicht hinzugefügt werden", "select-limit-has-been-reached": "<PERSON><PERSON><PERSON> diese Auswahl sind nur 6 Optionen wählbar", "reservation-successfully-recovered": "Reservierung wurde erfolgreich wiederhergestellt", "reservation-could-not-be-recovered": "Reservierung konnte nicht wiederhergestellt werden", "ordering-device-updated": "Bestellgerät erfolgreich aktualisiert", "ordering-device-created": "Bestellgerät erfolgreich erstellt", "ordering-device-update-error": "Bestellgerät konnte nicht aktualisiert werden", "ordering-device-creation-error": "Bestellgerät konnte nicht erstellt werden", "creating-open-invoice": "Rechnung wird erstellt", "open-invoice-created": "Rechnung erstellt", "open-invoice-creation-error": "Rechnung konnte nicht erstellt werden", "link-copied-success": "Link erfolg<PERSON><PERSON> k<PERSON>t", "link-copied-error": "Link konnte nicht kopiert werden", "gift-card-successfully-deleted": "Kundenkar<PERSON>", "gift-card-could-not-be-deleted": "Kundenkarte konnte nicht gelöscht werden", "item-duplication-success": "Artikel er<PERSON><PERSON><PERSON><PERSON><PERSON>", "item-duplication-error": "Artikel konnte nicht dupliziert werden", "item-delete-success": "Artikel er<PERSON><PERSON>g<PERSON><PERSON>", "item-delete-error": "Artikel konnte nicht gelöscht werden", "copy-success": "Erfolgreich k<PERSON>", "copy-error": "Konnte nicht kopiert werden", "invoice-deleted-success": "<PERSON><PERSON><PERSON>ng gelö<PERSON>", "invoice-deleted-error": "Rechnung konnte nicht gelöscht werden", "ordering-device-delete-success": "Bestellgerät erfolgreich gelöscht", "ordering-device-delete-error": "Bestellgerät konnte nicht gelöscht werden", "terminal-delete-success": "Terminal erfolgreich gelöscht", "terminal-delete-error": "Terminal konnte nicht gelöscht werden", "dynamic-code-delete-success": "QR Code erfolgreich gelöscht", "dynamic-code-delete-error": "QR Code konnte nicht gelöscht werden", "floor-delete-success": "<PERSON><PERSON> erfolg<PERSON>ich <PERSON>", "floor-delete-error": "Raum konnte nicht gelöscht werden", "processing-inventory-invoice": "Bearbeitung der Bestandsrechnung", "common-updated-success": "Erfolgreich aktualisiert", "common-updated-error": "Konnte nicht aktualisiert werden: {{ error }}", "wrong-pin-when-adding-discount": "<PERSON><PERSON><PERSON> konnte nicht hinzugef<PERSON>gt werden, falscher Verifizierungscode", "delete-payment-method-success": "Zahlungsmethode erfolgreich gelöscht", "delete-payment-method-error": "Zahlungsmethode konnte nicht gelöscht werden", "set-payment-method-as-default-success": "Standard-Zahlungsmethode erfolgreich geändert", "set-payment-method-as-default-error": "Standard-Zahlungsmethode konnte nicht geändert werden", "TSE-set-up-success": "TSE erfolgreich eingerichtet", "TSE-set-up-error": "TSE Einrichtung fehlgeschlagen", "order-removal-success": "Bestellung wurde entfernt", "order-removal-error": "Bestellung konnte nicht entfernt werden", "gift-card-receipt-success": "Beleg gesendet", "gift-card-receipt-error": "Beleg konnte nicht gesendet werden", "open-invoice-updated": "Rechnung erfolgreich aktualisiert", "open-invoice-updated-error": "Rechnung konnte nicht aktualisiert werden", "export-processing-start-success": "Exportbearbeitung erfolgreich gestartet", "export-processing-start-error": "Exportbearbeitung konnte nicht gestartet werden", "invoice-could-not-be-closed-please-check-data": "Die Rechnung konnte nicht erstellt werden. Bitte überprüfe die Daten.", "kitchen-monitor-bulk-status-could-not-be-updated": "Alle Artikel müssen im gleichen Status sein, um das Massen-Update zu nutzen", "kitchen-monitor-manual-printing-success": "Artikel erfolg<PERSON><PERSON> gedru<PERSON>t", "kitchen-monitor-manual-printing-error": "Artikel konnten nicht gedruckt werden", "email-sent-successfully": "E-Mail wurde erfolgreich verschickt", "email-could-not be-sent": "E-Mail konnte nicht verschickt werden"}