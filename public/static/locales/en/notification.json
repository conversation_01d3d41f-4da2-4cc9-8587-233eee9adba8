{"login-by-username-password-failed": "Username or password was incorrect.", "login-failed": "We could not validate your account. Please try again.", "verify-one-time-password-failed": "Your code is invalid, please try again.", "complete-account-failed": "We could not complete your account", "complete-account-conflict-with-mobile-secondary": "Your mobile is already registered", "complete-account-conflict-with-email-secondary": "Your email is already registered", "create-restaurant-conflict-restaurant-exists": "A similar restaurant is already on allO!", "create-restaurant-failed": "Sorry, we could not add the restaurant, please try again.", "network-status-offline": "Trying to reconnect...", "exploration-status": "EXPLORATION MODE", "pushing-print-request": "Pushing print request", "sold-gift-card-created": "{{amount}}€ gift card created.", "stock-gift-card-created": "Stock gift card created.", "gift-card-topped-up": "{{amount}}€ was added to gift card {{code}}", "gift-card-updated": "Updated gift card {{code}}", "gift-card-payment-failed": "We could not process the payment.", "account-updated": "Account updated.", "account-update-error": "Something went wrong, please try again later.", "file-upload-started": "Uploading file...", "file-uploaded": "File was uploaded successfully", "file-upload-error": "File could not be uploaded. {{description}}", "shareholder-created": "Shareholder created", "shareholder-creation-error": "Could not add shareholder.", "shareholder-deleted": "Shareholder deleted", "shareholder-deletion-error": "Could not delete shareholder.", "sending-selected-payment-receipt-to-email": "Sending digital receipt for payment to {{email}}", "sending-pdf-receipt-to-email": "Sending digital receipt to {{email}}", "receipt-generated-for-email": "Receipt generated for {{email}}", "receipt-not-generated-for-email": "Failed to generate receipt for {{email}}", "tables-could-not-be-created-notification": "Please select the floor on which you want to create your tables first.", "tables-created-notification": "Tables successfully created.", "dynamic-qr-code-linked": "Dynamic QR code linked", "dynamic-qr-code-not-linked": "Linking dynamic QR code failed. {{ title }}", "dynamic-qr-code-unlinked": "Dynamic QR disconnected", "preferences-updated": "Preferences updated.", "preferences-update-error": "Something went wrong, please try again later.", "customer-will-be-notified": "Reservation has been cancelled. The customer will be notified via Email.", "account-pin-updated": "Account PIN updated", "account-pin-update-error": "Account PIN could not be updated", "item-added-notification": "<PERSON><PERSON> added", "snapshot-created-notification": "Menu snapshot successfully taken", "menu-could-not-be-published-to-wolt": "<PERSON><PERSON> could not be published", "integration-setup-failed": "Failed to setup integration. Please contact support.", "integration-setup-success": "Integration setup completed.", "integration-removed": "Integration was removed", "removing-integration-failed": "Failed to remove integration. Please contact support.", "menu-successfully-published-to-wolt": "<PERSON><PERSON> has been successfully published", "floor-plan-updated": "Layout successfully updated", "floor-plan-update-error": "Layout could not be updated", "printer-updated": "Printer successfully updated", "printer-created": "Printer successfully created", "printer-update-error": "Printer could not be updated", "printer-creation-error": "Printer could not be created", "payment-terminal-updated": "Terminal successfully updated", "payment-terminal-created": "Terminal successfully created", "payment-terminal-update-error": "Terminal could not be updated", "payment-terminal-creation-error": "Terminal' could not be created", "guest-monitor-updated": "Side screen successfully updated", "guest-monitor-created": "Side screen successfully created", "guest-monitor-update-error": "Side screen could not be updated", "guest-monitor-creation-error": "Side screen' could not be created", "user-invited": "User successfully invited", "user-invite-error": "User could not be invited, please check email/phone number", "user-updated": "User successfully updated", "user-update-error": "User could not be updated", "user-removed": "User successfully removed", "user-removal-error": "User could not be removed", "supplier-updated": "Supplier successfully updated", "supplier-update-error": "Supplier could not be updated", "supplier-created": "Supplier successfully created", "supplier-creation-error": "Supplier could not be created", "SKU-item-updated": "SKU-item successfully updated", "SKU-item-update-error": "SKU-item could not be updated", "SKU-item-created": "SKU-item successfully created", "SKU-item-creation-error": "SKU-item could not be created", "ingredient-updated": "Ingredient successfully updated", "ingredient-update-error": "Ingredient could not be updated", "ingredient-created": "Ingredient successfully created", "ingredient-creation-error": "Ingredient could not be created", "supplier-invoice-creation-error": "Invoice could not be created", "supplier-invoice-created": "Invoice successfully created", "promotion-updated": "Promotion successfully updated", "promotion-update-error": "Promotion could not be updated", "promotion-created": "Promotion successfully created", "promotion-creation-error": "Promotion could not be created", "card-updated": "Card successfully updated", "card-update-error": "Card could not be updated", "card-created": "Card successfully created", "card-creation-error": "Card could not be created", "receipt-refunded": "Order successfully refunded", "receipt-refund-error": "Order could not be refunded", "new-order-when-sound-notification": "A new order has been added", "amount-payout-created": "Payout of {{amount}}€ was created", "amount-payout-not-created": "Payout of {{amount}}€ could not be created", "test-email-successfully-sent": "Test Email successfully sent", "test-email-error": "Test email could not be sent", "settings-updated": "Settings updated successfully", "settings-update-failed": "Your settings could not be saved!", "items-updated": "Items updated successfully", "items-update-failed": "Items could not be updated", "email-not-valid": "This email address is invalid", "phone-number-not-valid": "This phone number is invalid", "course-added": "Course added", "could-not-add-course": "Could not add course", "select-limit-has-been-reached": "You have reached the select limit of 6", "reservation-successfully-recovered": "Reservation has been successfully recovered", "reservation-could-not-be-recovered": "Reservation could not be recovered", "ordering-device-updated": "Ordering device updated", "ordering-device-created": "Ordering device created", "ordering-device-update-error": "Could not update ordering device", "ordering-device-creation-error": "Could not create ordering device", "creating-open-invoice": "Creating invoice", "open-invoice-created": "Invoice created", "open-invoice-creation-error": "Failed to create invoice", "link-copied-success": "Link successfully copied", "link-copied-error": "Link could not be copied", "gift-card-successfully-deleted": "Gift card deleted", "gift-card-could-not-be-deleted": "Gift card could not be deleted", "item-duplication-success": "<PERSON><PERSON> successfully duplicated", "item-duplication-error": "Item could not be duplicated", "item-delete-success": "Item successfully deleted", "item-delete-error": "Item could not be deleted", "copy-success": "<PERSON><PERSON>d successfully", "copy-error": "Could not copy", "invoice-deleted-success": "Invoice deleted", "invoice-deleted-error": "Invoice could not be deleted", "ordering-device-delete-success": "Ordering device successfully deleted", "ordering-device-delete-error": "Ordering device could not be deleted", "terminal-delete-success": "Terminal successfully deleted", "terminal-delete-error": "Terminal could not be deleted", "dynamic-code-delete-success": "QR Code successfully deleted", "dynamic-code-delete-error": "QR Code could not be deleted", "floor-delete-success": "Floor successfully deleted", "floor-delete-error": "Floor could not be deleted", "processing-inventory-invoice": "Processing inventory invoice", "common-updated-success": "Successfully updated", "common-updated-error": "Could not be updated: {{ error }}", "wrong-pin-when-adding-discount": "Discount could not be added, wrong verification code", "delete-payment-method-success": "Payment method successfully deleted", "delete-payment-method-error": "Payment method could not be deleted", "set-payment-method-as-default-success": "Payment method successfully set as default", "set-payment-method-as-default-error": "Payment method could not be set as default", "TSE-set-up-success": "TSE successfully configured", "TSE-set-up-error": "TSE could not be configured", "order-removal-success": "Order has been removed", "order-removal-error": "Order could not been removed", "gift-card-receipt-success": "Receipt send", "gift-card-receipt-error": "Receipt could not be send", "open-invoice-updated": "Invoice updated successfully", "open-invoice-updated-error": "Invoice could not be updated", "export-processing-start-success": "Export processing successfully started", "export-processing-start-error": "Export processing could not be started ", "invoice-could-not-be-closed-please-check-data": "Invoice could not be generated. Please check data.", "kitchen-monitor-bulk-status-could-not-be-updated": "All items need to be in the same status to use bulk update", "kitchen-monitor-manual-printing-success": "Item(s) successfully printed", "kitchen-monitor-manual-printing-error": "Item(s) could not be printed", "email-sent-successfully": "Email successfully sent", "email-could-not be-sent": "Email could not be sent"}