{"english": "English", "german": "De<PERSON>ch", "chinese": "中文", "traditional-chinese": "繁體中文", "albanian": "Shqip", "italian": "Italiano", "turkish": "Türkçe", "french": "Français", "spanish": "Español", "czech": "Čeština", "polish": "<PERSON><PERSON>", "serbian": "<PERSON><PERSON><PERSON>", "vietnamese": "Tiếng <PERSON>", "portuguese": "Português", "romanian": "Română", "bulgarian": "Български", "russian": "Pусский", "dutch": "Dutch", "germany": "Germany", "austria": "Austria", "italy": "Italy", "china": "China", "denmark": "Denmark", "netherlands": "Netherlands", "belgium": "Belgium", "france": "France", "switzerland": "Switzerland", "poland": "Poland", "contact-support": "Contact support", "get-started-with-allo": "Get started with allO", "continue-to-allo": "Continue to your allO account", "enter-your-email-address": "Enter your email address.", "enter-your-phone-number": "Enter your phone number.", "enter-your-username-and-password": "Enter your username and password", "to-proceed": "to proceed.", "you-will-receive-a-6-digit-code-for-verification": "You’ll receive a 6-digit code for verification.", "by-proceeding-you-agree": "By proceeding you agree to our", "and": "and", "privacy-policy": "Privacy Policy", "terms-and-conditions-to-agree-to": "Terms and Conditions", "terms-and-conditions": "Terms and Conditions", "or-continue-with-phone-number": "Or continue with mobile number", "or-continue-with-email-address": "Or continue with email address", "i-have-a-password": "I have a password", "select-country": "Select country", "search-your-country": "Search your country", "enter-the-6-digit-code-you-received-at": "Enter the 6-digit code sent to you at", "your-6-digit-code": "Your 6 digit code", "resend-code": "Resend code", "stop-verification": "Stop verification?", "verification-process-is-in-progress": "Your verification process is still in progress. Are you sure you want to stop and go back?", "cancel-verification": "Cancel verification", "dont-leave": "Don't leave", "we-could-not-reach-your-email": "We could not reach your email", "we-could-not-reach-your-phone-number": "We could not reach your phone number", "your-verification-code-is-incorrect": "Your verification code is incorrect. Please try again.", "back": "Back", "how-should-we-call-you": "How should we call you?", "you-can-also-add-your-email-address": "You can also add your email address.", "you-can-also-add-your-phone-number": "You can also add your phone phone number.", "your-email": "Your Email", "your-email-optional": "Your Email (Optional)", "your-phone": "Your Phone", "your-phone-optional": "Your Phone (Optional)", "enter-your-email-here": "Enter your email here", "hi-there": "Hi there 👋", "your-name": "Your name", "enter-your-name-here": "Enter your name here", "welcome-to-allo": "Welcome to allO", "welcome-on-board-name": "Welcome on board, {{name}}", "name-excited-to-have-you-on-board-create-or-join-a-restaurant": "Hi {{name}}, we are exited to have you onboard! Add your restaurant in just a few minutes or join your restaurant with the help of your manager. See you on the other side!", "create-your-restaurant": "Create your restaurant", "join-a-restaurant": "Join a restaurant", "lets-create-your-restaurant": "Let's create your restaurant", "start-typing-your-restaurant-to-search-on-google": "Start typing your restaurant's name, and we will try to find it on Google.", "if-not-on-google-enter-your-restaurant-information": "If we can't find it, you can always fill the data manually.", "confirm": "Confirm", "type-to-search-your-restaurant": "Type to search your restaurant", "fetching-google-maps-data": "Fetching google maps data...", "select-to-fill-in-your-restaurant-address": "Select to fill in your restaurant address.", "ask-your-manager-to-scan-qr-code-to-join": "Ask your restaurant manager to scan this QR code to validate your account.", "restaurant-on-allo": "Restaurant already on allO", "similar-restaurant-already-on-allo": "Your restaurant might already be on allO. Talk to a manager or reach out for support. We are happy to help.", "lets-go": "Let's go", "congratulations": "Congratulations", "you-have-successfully-created-your-restaurant-lets-customize-name": "You’ve successfully created your restaurant. Let’s customize it to best fit your business.", "almost-there": "Almost there", "you-have-successfully-created-your-restaurant-but-we-need-you-to-answer-a-few-questions": "You have successfully created your restaurant. Before you start using the incredible features of allO, we still need you to answer a few questions.", "something-went-wrong": "Oh no, something is missing", "we-could-not-process-your-payment-method": "We could not process your payment method. No worries, we have stored most of the data for you, just try again!", "try-again": "Try again", "what-kind-of-restaurant-do-you-own": "What kind of restaurant do you own?", "select-one-of-the-options-below": "Select one of the options below.", "bubble-tea": "Bubble Tea", "hotpot": "Hotpot", "burger": "Burger", "pizza": "Pizza", "sushi": "<PERSON><PERSON>", "dumpling": "Dumpling", "dark-kitchen": "Dark Kitchen", "fine-dinning": "Fine Dinning", "cafe": "Cafe", "fast-food": "Fast Food", "dinner": "Dinner", "pasta": "Pasta", "other": "Other", "what-services-would-you-like-to-integrate": "What services would like to integrate?", "you-can-always-change-this-in-the-settings": "You can always edit this later on the settings, you wont be charged without permission.", "reach-customers-in-their-own-language": "Reach customers in their own language", "add-languages-to-your-system": "Add more than one language to your system to allow for content translation. If there’s no match, your default language will be shown.", "dine-in-setting": "<PERSON>e In", "dine-in-description": "In restaurant ordering, including Scan to Order and Pay.", "reservation-setting": "Reservation", "reservation-description": "Channel all reservations and connect them to your tables.", "takeaway-setting": "Takeaway", "takeaway-description": "All takeaway orders in one place, including UberEats, Wolt and Lieferando.", "express-setting": "Express", "express-description": "Enable fast checkout experience.", "select-default-language": "Select default language", "select-language": "Select language", "other-language": "Other language", "add-another-language": "Add another language", "select-your-plan": "Select your plan", "you-will-not-be-charged-until-you-go-live": "Don't worry, you will not be charged until you go live on allO. You can update your plan at any time, free of charge.", "save-50-percent-with-allo-pay": "50% off with allO Pay", "standard-price-allo-pay": "Standard price: 1.4%+0.09€ per transaction for all debit (incl. EC) and credit cards", "volume-pricing-allo-pay": "Volume pricing with lower rate from €25k payment volume per month", "select": "Select", "enter-billing-info": "Enter your billing information", "your-data-is-safe-with-us": "Your data is safe with us.", "enter-legal-info": "Enter your legal information", "let-us-know-a-bit-more-about-your-business": "In order to fulfil relevant legal requirements, we are required to verify that our software is being used by actual restaurant owners. But don’t worry; we promise to only collect the minimum amount of necessary information from you.", "we-will-only-use-this-data-to-create-the-right-invoice-for-you": "We are strictly compliant to the GDPR data protection regulations of the EU. We will only use your billing information to send you the right invoice if you decide to use allO in the end.", "legal-name": "Name", "legal-name-placeholder": "Enter name", "company-name": "Company Name", "company-name-placeholder": "My Company", "company-legal-form": "Legal Form", "steuer-nummer": "Tax Number", "steuer-nummer-placeholder": "eg. 123/456/7890", "umsatz-steuer-id": "VAT ID", "umsatz-steuer-id-placeholder": "eg. DE123456789", "address-street": "Street", "address-street-placeholder": "Street", "address-number": "No.", "address-number-placeholder": "No.", "address-zip-code": "Zip code", "address-zip-code-placeholder": "Zip code", "address-city": "City", "address-city-placeholder": "City", "billing-address": "Billing address", "my-billing-address-different-from-restaurant-address": "My billing address is different from my legal address", "legal-address": "Legal address", "my-legal-address-different-from-restaurant-address": "My legal address is different from my restaurant address", "email": "Email", "email-placeholder": "Email here", "to-proceed-please-agree-to-our-terms-and-conditions": "To proceed, please agree to our terms and conditions.", "switch-account": "Switch account", "add-account": "Add account", "enter-your-account": "Enter your account", "the-following-accounts-are-logged-in-switch-or-add": "The following accounts are logged into this device. Select your account or add another one", "get-ready-to-go-live": "Get ready to go live", "a-few-steps-to-complete-before-going-live": "Here are a few steps to complete before going live:", "start": "Start", "index-explore-scrollable-title": "Explore", "scrollable-recommend-body-line1": "Do not find your", "scrollable-recommend-body-line2": "favorite restaurant", "scrollable-recommend-body-line3": "on <PERSON><PERSON>?", "scrollable-recommend-action-btn": "Recommend Now", "welcome-header-line1": "All-in-one", "welcome-header-line2": "dinning experience", "welcome-body": "<PERSON><PERSON> brings fantastic dining experience to your favourite restaurants. Scan your table now and invite your friends to start ordering. Split the bill in seconds and enjoy your walk-out dining experience", "error-with-status": "An error ({{statusCode}}) occurred on the server", "error-without-status": "An error has occurred on the server", "footer-motto": "Reinventing the dinning experience.", "footer-impressum-link": "Imprint", "footer-datenschutzerklaerung-link": "Data Privacy", "footer-agb-link": "Terms of Service", "footer-agb-restaurant-link": "Terms of Service for Restaurants", "bottom-nav-explore": "Explore", "bottom-nav-community": "Community", "bottom-nav-inbox": "Inbox", "bottom-nav-account": "Account", "bottom-nav-scan": "Order", "bottom-nav-return-to-order": "Dining", "account-general-subheader": "General", "account-support-subheader": "Support", "account-my-orders-title": "My Orders", "account-my-orders-description": "View your orders history", "account-payment-methods-title": "Payment Methods", "account-payment-methods-description": "Store payment method", "account-help-center-title": "Help Center", "account-help-center-description": "Contact our support", "account-logout": "Logout", "payment-methods-title": "Payment Methods", "empty-payment-methods-title": "Add Payment Method", "empty-payment-methods-body": "Tell us how you would like to pay later and you will be ready to order.", "empty-payment-methods-feature1": "You won’t be charged yet.", "empty-payment-methods-feature2": "Payments will always be approved by you before they are made.", "empty-payment-methods-feature3": "Secure payment.", "common-download": "Download", "common-continue": "Continue", "common-next": "Next", "common-back": "Back", "common-confirm": "Confirm", "common-close": "Close", "common-delete": "Delete", "common-create": "Create", "common-coming-soon": "Coming Soon", "common-reject": "Kick-Out", "common-admit": "Admit", "common-cancel": "Cancel", "common-save": "Save", "common-saving": "Saving...", "common-reset": "Reset", "common-remove": "Remove", "common-finish": "Finish", "common-add": "Add", "common-enum-1": "1.", "common-enum-2": "2.", "common-enum-3": "3.", "common-enum-4": "4.", "common-update": "Update", "common-beverage": "Drink", "common-dish": "Dish", "common-receipt": "Receipt", "common-duplicate": "Duplicate", "common-search": "Search", "common-reprint": "Reprint", "common-waiter": "Waiter", "common-disabled": "Disabled", "common-monday": "Monday", "common-tuesday": "Tuesday", "common-wednesday": "Wednesday", "common-thursday": "Thursday", "common-friday": "Friday", "common-saturday": "Saturday", "common-sunday": "Sunday", "common-guest": "Guest", "common-guests": "Guests", "common-january": "January", "common-february": "February", "common-march": "March", "common-april": "April", "common-may": "May", "common-june": "June", "common-july": "July", "common-august": "August", "common-september": "September", "common-october": "October", "common-november": "November", "common-december": "December", "language-field-option-de": "De<PERSON>ch", "language-field-option-en": "English", "language-field-option-zh": "中文", "scroll-to-top-label": "To Top", "add-payment-method-title": "Add Payment Method", "add-payment-method-checkout-methods-disclaimer": "Following payment methods are available at checkout", "add-payment-method-digital-wallets-disclaimer": "You can also pay with your digital wallet", "add-payment-method-leviee-disclaimer": "<PERSON><PERSON> UG (haftungsbeschränkt) will never send any payment instructions to the financial institution that issued your card or payment instruments to charge you without notifying you or having your consent.", "add-payment-method-validity-disclaimer": "Please make sure you have sufficient funds or credits in your account to avoid any fees.", "add-card-title": "Add Credit Card", "add-card-encryption-disclaimer": "Your payment info is securely encrypted", "add-card-agree-disclaimer": "By clicking the “Confirm” button below, I authorise Leviee UG (haftungsbeschränkt) to send instructions to the financial institution that issued my card to take payments from my card account in accordance with the terms of my agreement with you.", "orders-title": "My Orders", "orders-empty": "You don't have any orders yet.", "inbox-notifications": "Notifications", "inbox-friends": "Friends", "notifications-empty-title": "No notifications yet!", "notifications-empty-body": "Here you can read all your notifications from orders and restaurants.", "friends-title": "Friends", "friends-empty-title": "Your contacts.", "friends-empty-body": "Keep your friends close and quickly add them to tables.", "friends-scan-user": "<PERSON>an <PERSON>r", "friends-create-group": "New Group", "user-scan-title": "Add Friend", "user-scan-error-title": "Not a valid Friend profile.", "user-scan-error-body": "We could not find your friend, please scan again.", "user-scan-error-btn": " <PERSON>an <PERSON>", "user-scan-success-title": "Friend added.", "user-scan-success-body": "You can go back to your Friends list or scan again.", "user-scan-success-btn": "Add Another Friend", "user-scan-info-title": "Scan a Profile Code to add them to your Friends list.", "user-scan-info-body": "You can easily invite Friends to join in your table, share order and split the bill.", "create-group-title": "New Group", "create-group-description": "Give your group a name and select your friends.", "community-coming-soon-body": "We are working hard to bring a great foodie community together. Stay tuned.", "order-activity-tab-table": "Table", "order-activity-tab-menu": "<PERSON><PERSON>", "order-activity-tab-order": "Order", "order-activity-tab-payment": "Payment", "table-title": "Table", "table-select-friends-title": "Invite Friends", "table-pending-invites-subheader": "Pending Invites", "error-title": "Error", "error-body": "Something went wrong. Please refresh.", "forbidden-title": "No Access", "forbidden-body": "You will be redirect back in {{counter}} ...", "checkout-title": "Payment", "checkout-stored-payment-method-label": "Stored Payment Method", "checkout-use-stored-payment-method-checkbox-label": "Use stored payment method", "checkout-select-payment-method-label": "Select Payment Method", "checkout-select-payment-option-label": "Select Payment Option", "checkout-select-tip-option-label": "Select Tip (Optional)", "checkout-form-full-name-label": "Full name", "checkout-form-email-label": "Email", "payment-channels-other": "Other", "payment-channels-app": "Online", "payment-channels-cash": "Cash", "payment-channels-card": "Card", "payment-channels-lieferando": "<PERSON><PERSON><PERSON><PERSON>", "payment-channels-wolt": "<PERSON><PERSON>", "payment-channels-foodpanda": "FoodPanda", "payment-channels-ubereats": "Uber Eats", "payment-channels-food2go": "Food2go", "payment-channels-food2u": "Food2u", "payment-channels-mchoice": "MChoice", "payment-channels-leben": "<PERSON><PERSON>", "payment-channels-paypal": "PayPal", "payment-channels-ueberweisung": "Online Transfer", "payment-channels-ec": "EC", "payment-channels-visa": "Visa", "payment-channels-mastercard": "Mastercard", "payment-channels-american_express": "American Express", "payment-channels-jcb": "JCB", "payment-channels-stripe": "Stripe", "payment-channels-gutschein": "Gutschein", "payment-channels-kaiyuan": "Kaiyuan", "payment-channels-order_smart": "OrderSmart", "payment-channels-allo_pay": "allO Pay", "payment-channels-offene_rechnung": "Outstanding bill", "payment-channels-anzahlung": "Prepayment", "payment-channels-sodexo": "Sodexo", "payment-channels-bayernets": "Bayernets", "payment-channels-sonstige": "Other", "payment-options-all": "All", "payment-options-split": "Split", "payment-options-own": "Yours", "complete-checkout-in-app-title": "Complete Checkout", "complete-checkout-cash-card-title": "Your payment will be processed by the waiter.", "complete-checkout-price-breakdown": "({{amount}} € your amount + {{tipAmount}} € tip)", "complete-checkout-encryption-disclaimer": "Your payment info is securely encrypted", "complete-checkout-disclaimer": "By clicking the “Confirm” button below, I authorise Leviee UG (haftungsbeschränkt) to send instructions to the financial institution that issued my card to take payments from my card account in accordance with the terms of my agreement with you.", "complete-checkout-pay-btn": "Pay Now", "complete-checkout-cancel-disclaimer": "You can cancel this checkout if you want to select another payment method.", "complete-checkout-cancel-btn": "Back", "checkout-choose-payment-option-disclaimer": "Choose your Payment option", "checkout-choose-payment-option-host-with-participants-disclaimer": "As Host, you can select how to split the bill or pay for the entire table.", "checkout-no-payment-option-disclaimer": "Host has not initiated payment yet.", "checkout-no-payment-option-guest-disclaimer": "As a Guest, you can pay for everything or wait for <PERSON> to initiate the payment.", "checkout-selected-payment-option": "Host has chosen {{paymentOption}}.", "checkout-no-payment-option-guest-detailed-disclaimer": "As a Guest in this table, you can pay for everything or wait for the Host to choose a payment option for the table. This view will update to the latest payment option in real time.", "payment-information-title": "How payment works.", "payment-information-explanation-line1": "1. All:  you will pay for the whole table", "payment-information-explanation-line2": "2. Split: your payment amount will be the whole table amount divided by the total number of diners at the table plus tips", "payment-information-explanation-line3": "3. Yours: your payment amount will be the amount for what you have ordered plus tips", "payment-information-disclaimer": "Everyone can pay for the whole table and only table host can initiate Split & Yours", "delete-payment-method-dialog-title": "Delete Payment Method", "delete-payment-method-dialog-description": "Having a payment method in store allows to easily order and pay on all of our restaurants.", "common-credit-card": "Credit Card", "common-sepa": "SEPA Direct Debit (Coming Soon)", "receipt-overview": "Overview", "receipt-dining-started-label": "Dining Started", "receipt-dining-ended-label": "Dining Ended", "receipt-participants-label": "Participants", "receipt-table-bill-label": "Table Bill", "receipt-your-bill-label": "Your Payment", "receipt-your-tip-label": "Your Tip", "receipt-payment-method-label": "Payment Method", "receipt-payment-receipt-label": "Payment Receipt", "receipt-payment-channel-label": "Paid by", "receipt-details": "Details", "receipt-details-subheader": "Receipt details", "receipt-sub-total-label": "Subtotal", "receipt-tip-label": "Tip", "receipt-total-label": "Total", "receipt-review": "Review", "receipt-rate": "Rate your experience", "receipt-your-rating-label": "Your rating", "receipt-your-comment-label": "Your comment", "receipt-review-btn": "Review", "receipt-cancellation-reason": "Cancellation Reason", "order-item-estimated-preparation-time-label": "Estimated Preparation Time: {{preparationTime}} min", "order-item-notes-label": "Notes: {{notes}}", "order-item-remove-btn": "Remove", "order-item-edit-btn": "Edit", "order-item-edit-note-btn": "Edit Note", "order-item-add-note-btn": "Add Note", "order-item-note-input-label": "Order Note", "order-item-note-input-placeholder": "Add Note", "menu-title": "<PERSON><PERSON>", "restaurant-header-description-show-more": "Show more.", "restaurant-header-description-show-less": "Show less.", "restaurant-header-address-label": "Address", "restaurant-header-opening-times-label": "Opening Times", "restaurant-header-reserve-action": "Reserve", "restaurant-header-pick-up-action": "Pick Up", "restaurant-header-pre-order-action": "Pre Order", "restaurant-header-order-action": "Order", "restaurant-header-directions-action": "Directions", "restaurant-header-call-action": "Call", "restaurant-header-share-action": "Share", "scan-title": "Book your table", "scan-description": "Please scan the QR code to book the table. You will get a confirmation of the booking shortly after. We will also let your waiter know you have checked in.", "scan-error-title": "We could not book your table.", "scan-error-description": "There was a problem with the QR code, please try again or enter the table code manually.", "scan-scan-again-btn": "<PERSON><PERSON>", "scan-input-manually-description": "Input your table code here manually or scan again.", "scan-input-manually-btn": "Write Table Code", "scan-input-placeholder": "Table Code", "scan-enter-table-btn": "Enter table", "order-title": "Orders", "order-details-title": "Current Order", "order-unconfirmed-subheader": "Edit or Place these orders", "order-confirmed-subheader": "Placed Orders", "order-offline-orders-subheader": "Other", "order-other-orders-in-table-subtitle": "From guests in your table", "order-other-orders-in-table-avatar-char": "G", "order-place-action-confirm-dialog-title-with-approval": "This is a chargeable action", "order-place-action-confirm-dialog-title-with-approval-description-line1": "Please click \"Continue\" to proceed and send your orders directly to the kitchen.", "order-place-action-confirm-dialog-title-with-approval-description-line2": "If you wish to cancel already placed orders, please call the waiter.", "order-place-action-confirm-dialog-title-with-approval-description-line3": "By continuing, you agree to our policy and terms of service.", "order-place-action-confirm-dialog-title-without-approval": "Your table needs verification", "order-place-action-confirm-dialog-title-without-approval-description-for-host-line1": "You can get your table verified in one of the following ways:", "order-place-action-confirm-dialog-title-without-approval-description-for-host-line2-main": "Add a payment method", "order-place-action-confirm-dialog-title-without-approval-description-for-host-line2-secondary": "(You will not be charged before you finish your meal)", "order-place-action-confirm-dialog-title-without-approval-description-for-host-line3-main": "Call waiter", "order-place-action-confirm-dialog-title-without-approval-description-for-host-line3-secondary": "to verify your table.", "order-place-action-confirm-dialog-title-without-approval-action-btn": "Add Payment Method", "order-place-action-confirm-dialog-title-without-approval-description-for-guest": "The host of the table has not stored a payment method yet. Please wait for the waiter to verify the booking.", "order-place-action-confirm-dialog-title-non-authenticated-guest": "Proceed to Ordering", "order-place-action-confirm-dialog-title-non-authenticated-guest-description-line1": "Create an account or Continue as Guest to complete your order. By registering an account, you can:", "order-place-action-confirm-dialog-title-non-authenticated-guest-description-line2": "Order together with your friends", "order-place-action-confirm-dialog-title-non-authenticated-guest-description-line3": "Keep sending your orders to the kitchen and pay when you are about to leave", "order-place-action-confirm-dialog-title-non-authenticated-guest-description-line4": "Split the bill with your friends in a matter of seconds", "order-place-action-confirm-dialog-title-non-authenticated-guest-description-line5": "Get a digital receipt and keep track of your previous orders", "order-place-action-confirm-dialog-title-non-authenticated-guest-login-action-btn": "<PERSON><PERSON>", "order-place-action-confirm-dialog-title-non-authenticated-guest-continue-as-guest-action-btn": "Continue as Guest", "order-place-action-btn": "Order ({{amount}} €)", "order-removed-item-notification": "Item removed from your order.", "order-user-guide-bar-place-order": "Place Order to the Kitchen", "order-user-guide-bar-place-order-description": "You can still review and make changes on your above order now. After this step, cancellation on your order is not guaranteed.", "order-user-guide-bar-place-order-description-non-authenticated-guest": "Once you finish reviewing the selected items, you can place your order to the kitchen", "order-user-guide-bar-proceed-to-payment": "Order has been successfully placed to the kitchen", "order-user-guide-bar-proceed-to-payment-description": "Your order will be served by the waiter soon", "order-user-guide-no-orders": "You have not selected any item.", "order-user-guide-no-orders-description": "Please go back to the menu page to select the items which you would like to order", "order-user-guidance-payment": "Go to Payment", "table-user-guide-go-to-menu": "You are ready to Order", "table-user-guide-go-to-menu-description": "You can quickly add your friend to the table to order together by clicking the add button (“+”) above and sharing the link", "menu-user-guide-no-items-selected": "Please Add Items", "menu-user-guide-no-items-selected-description": "You can place the dishes and drinks you would like to order in this page. You can still review and make changes in the next step.", "menu-user-guide-go-to-order-items": "({{count}} items)", "menu-user-guide-go-to-order-total": "Ready to order ({{total}} €)", "menu-user-guide-review-selected-items": "Review in Next Step", "menu-user-guide-review-selected-items-description": "Your friends can also join the table to order together with you in few seconds by scanning the QR code on the table.", "menu-user-guide-review-selected-items-description-non-authenticated-guest": "You can review the items you have selected in the next step.", "menu-user-guide-review-selected-items-action-btn": "Review {{count}} items ({{total}} €)", "payment-title": "Payment", "payment-receipt-details-subheader": "Receipt details", "payment-offline-orders-subheader": "Other", "payment-total-header": "Total", "payment-checkout-btn": "Start Payment", "payment-checkout-in-progress-btn": "Creating secure session...", "payment-checkout-confirm-dialog-title": "Continue to Checkout", "payment-checkout-confirm-dialog-description": "Please confirm all items have been served to your table before proceeding to checkout.", "payment-checkout-unconfirmed-items-dialog-title": "Pending Order Items", "payment-checkout-unconfirmed-items-dialog-description": "There are pending items in your order. By clicking \"Confirm\", your pending/unplaced orders will be removed and you will be directed to payment", "payment-user-guide-no-items-selected": "You have nothing to pay", "payment-user-guide-no-items-selected-description": "You have not placed any order to the kitchen yet. You can start payment once you have placed order to the kitchen.", "payment-user-guide-no-items-selected-action-btn": "Back to Order", "payment-user-guide-proceed-to-checkout": "You do not have to start the payment before you finish your meal!", "payment-user-guide-proceed-to-checkout-description": "If you are done with your meal, you can proceed to payment.", "payment-user-guide-proceed-to-checkout-action-btn": "Start Payment", "payment-user-guide-proceed-to-checkout-non-authenticated-guest": "Pay without Waiting", "payment-user-guide-proceed-to-checkout-description-non-authenticated-guest": "Your food is on the way! You do not have to pay before you finish dining! To skip waiting for waiter, you can pay directly here and leave.", "checkout-finished-title": "Order completed.", "checkout-finished-description": "Thank you very much for your visit.", "checkout-finished-disclaimer": "Thank you for using <PERSON><PERSON>. You can find the details of your order history and receipts under ”Account”", "checkout-finished-disclaimer-non-authenticated-guest": "Thank you for using <PERSON><PERSON><PERSON> Login or create an Account if you wish to order in groups, split the bill and have an overview of past orders.", "checkout-create-account-non-authenticated-guest-action-btn": "Register Account", "checkout-finish-non-authenticated-guest-action-btn": "No thanks.", "booking-external-login-btn": "Login To Proceed", "booking-external-continue-as-guest-btn": "Continue as Guest", "booking-external-booking-error": "We could not book the table. Please verify your QR code is valid or the table is not reserved already.", "booking-external-title": "Booking Code", "table-invite-copy-link": "Copy Link", "table-invite-link-copied": "<PERSON>d", "table-invite-add-to-table-btn": "Invite to table", "friend-group-information-subheader": "People in this group:", "order-approval-status-label": "Status", "order-approval-header": "Waiting for {{name}} (Host).", "order-approval-description": "Please wait. {{name}}, as table host, will approve you shortly.", "error-bar-message": "There was a problem.", "profile-title": "Profile Information", "profile-description": "Your QR-Code profile.", "profile-full-name-label": "Legal name", "profile-email-label": "Email", "profile-form-first-name-label": "First name", "profile-form-last-name-label": "Last name", "profile-form-email-label": "Email", "menu-item-remarks-footnote-disclaimer": "(See footnote below.)", "admin-stripe-confirmation-title": "Setting up your Stripe account.", "admin-stripe-confirmation-connected-description": "Stripe account connected. You are now ready to accept payments.", "admin-stripe-confirmation-not-connected-description": "Could not connect account. Please try again or contact our support team.", "admin-stripe-confirmation-back-button": "Back to Payments", "waiter-bottom-nav-menu": "<PERSON><PERSON>", "waiter-bottom-nav-tables": "Tables", "waiter-bottom-nav-scan": "<PERSON><PERSON>", "waiter-bottom-nav-receipts": "Receipts", "waiter-bottom-nav-payments": "Payments", "waiter-table-list-title": "Manage Tables", "waiter-table-list-table-status-label": "Table Status", "waiter-table-list-table-status-free-label": "Free", "waiter-table-list-order-status-in-progress-label": "In Progress", "waiter-table-list-order-status-prepaid-label": "Guest", "waiter-table-list-order-status-closed-label": "Closed", "waiter-table-list-order-approval-label": "This table needs approval to order.", "waiter-cart-overview-order-served-action-btn": "Order Served.", "waiter-table-overview-title": "Table {{table}}", "waiter-table-overview-order-action-btn": "Order", "waiter-table-overview-payment-action-btn": "Payment", "waiter-table-overview-clear-action-btn": "Clear", "waiter-table-overview-clear-action-confirmation-dialog-title": "Clear order", "waiter-table-overview-clear-action-confirmation-dialog-description-line1": "This action will remove all user selected items which are not placed yet.", "waiter-table-overview-clear-action-confirmation-dialog-description-line2": "Are you sure you wish to proceed?", "waiter-table-overview-cancel-action-btn": "Cancel", "waiter-table-overview-summary-tab": "Summary", "waiter-table-overview-order-tab": "Order", "waiter-table-overview-checkout-tab": "Checkout", "waiter-table-overview-summary-status-label": "Status", "waiter-table-overview-summary-status-table-booked": "Table is booked.", "waiter-table-overview-summary-status-payment-requested": "Payment Requested", "waiter-table-overview-summary-status-table-free": "Table is free.", "waiter-table-overview-summary-code-label": "Code", "waiter-table-overview-summary-approved-label": "Approved", "waiter-table-overview-approve-table-action-btn": "Approve", "waiter-table-overview-approved-table-label": "Table is ready to order.", "waiter-table-overview-approve-table-action-confirmation-dialog-title": "Approve Table", "waiter-table-overview-approve-table-action-confirmation-dialog-description-line1": "This action will allow users to place their orders directly to the kitchen", "waiter-table-overview-approve-table-action-confirmation-dialog-description-line2": "Are you sure you wish to proceed?", "waiter-table-overview-promote-customer-action-btn": "Promote", "waiter-table-overview-cancel-item-action-btn": "Cancel", "waiter-table-overview-cancel-item-action-confirmation-dialog-title": "Cancel Item", "waiter-table-overview-cancel-item-action-confirmation-dialog-description-line1": "This action will cancel an already placed order.", "waiter-table-overview-cancel-item-action-confirmation-dialog-description-line2": "Are you sure you wish to proceed?", "waiter-table-overview-clear-item-action-btn": "Clear", "waiter-table-overview-clear-item-action-confirmation-dialog-title": "Clear Item", "waiter-table-overview-clear-item-action-confirmation-dialog-description-line1": "This action will remove an item selected by the user which is not placed yet.", "waiter-table-overview-clear-item-action-confirmation-dialog-description-line2": "Are you sure you wish to proceed?", "waiter-payment-order-total": "{{amount}} €", "waiter-payment-order-total-disclaimer": "({{amount}} € does not include individual tips.)", "waiter-payment-details-title": "Payment Details", "waiter-payment-payment-not-started-title": "Payment was not started for this order.", "waiter-payment-payment-not-started-finish-action-btn": "Finish Payment ({{amount}} €)", "waiter-add-payment-action-btn": "Add Payment ({{amount}} €)", "waiter-payment-payment-not-started-finish-confirmation-dialog-title": "Finish Payment", "waiter-payment-payment-not-started-finish-confirmation-dialog-description": "This is not a reversible action. Would you like to proceed ?", "waiter-payment-user-details-payment-not-started": "Not set.", "waiter-payment-user-details-paid-label": "Paid", "waiter-payment-user-details-pending-in-app-payment-label": "Pending in app payment.", "waiter-payment-user-details-confirm-user-payment-action-btn": "Confirm ({{total}} €)", "waiter-payment-user-details-confirm-user-payment-confirmation-dialog-title": "Confirm Payment", "waiter-payment-user-details-confirm-user-payment-confirmation-dialog-description": "This is not a reversible action. Would you like to proceed ?", "waiter-payment-user-details-user-total": "({{amount}} € + {{tipAmount}} € tip)", "waiter-receipts-title": "All Receipts", "waiter-payments-title": "All Transactions", "waiter-payments-total-label": "Total", "waiter-payments-stripe-payment-paid-label": "Authorized", "activity-duration-hours": "hours", "activity-duration-min": "min", "activity-restaurant-label": "Restaurant", "activity-total-revenue-label": "Total Revenue", "activity-number-of-orders-label": "# of Orders", "activity-revenue-order-label": "Revenue/Order", "activity-table-turnover-label": "Table Turnover", "activity-dining-time-label": "Dining Time", "activity-table-utilization-label": "Table Utilization", "activity-guest-label": "Guest", "activity-total-guests-label": "Total Guests", "activity-new-guests-label": "New Guests", "activity-recurring-guests-label": "Recurring Guests", "activity-spending-per-guest-label": "Spending/Guest", "activity-fav-payment-method-label": "Favourite Payment Method", "activity-food-and-drinks-label": "Food & Drinks", "activity-top-5-dishes-label": "Top 5 Dishes", "activity-top-5-drinks-label": "Top 5 Drinks", "terminal-table-total": "Total outstanding amount:", "terminal-orders-remove-unconfirmed-item-action-btn": "Remove", "terminal-orders-confirm-order-items-dialog-title": "Send items to the kitchen", "terminal-orders-confirm-order-items-dialog-description-line1": "This action will send the selected items to the kitchen or bar for preparation", "terminal-orders-confirm-order-items-dialog-description-line2": "Are you sure you wish to proceed ?", "terminal-orders-start-payment-action-btn": "Process Payment ({{total}} €)", "terminal-orders-unconfirmed-order-items-title": "Pending Items", "terminal-orders-confirmed-order-items-title": "Confirmed", "terminal-orders-confirmed-order-items-description": "Items Placed to Kitchen", "terminal-orders-confirm-pending-items-action-btn": "Order ({{amount }} €)", "terminal-cart-confirm-serving-action-btn": "Confirm served", "terminal-cart-confirm-serving-and-payment-action-btn": "Order Completed ({{amount }} €)", "terminal-cart-confirm-serving-dialog-title": "Confirm items served", "terminal-cart-confirm-serving-dialog-description-line1": "This action will mark all items as served.", "terminal-cart-confirm-serving-dialog-description-line2": "Are you sure you wish to proceed ?", "terminal-cart-approve-cart-action-btn": "Approve Order for {{name}}", "terminal-cart-approve-cart-dialog-title": "Approve Guests", "terminal-cart-approve-cart-dialog-description-line1": "This action will approve the table and allow Guests to order directly to the kitchen.", "terminal-cart-approve-cart-dialog-description-line2": "Are you sure you wish to proceed ?", "terminal-no-orders-table-name-label": "Table number: {{table}}", "terminal-no-orders-label": "No Orders", "terminal-no-orders-description": "This table does not have any orders yet.", "terminal-closed-order-time-range": "Today from {{from}} to {{to}}", "terminal-closed-order-time-range-older": "{{date}}, from {{from}} to {{to}}", "terminal-active-order-order-time-start": "Today from {{from}}", "terminal-order-header-label": "Order-Nr.：******{{last4}}", "terminal-orders-clear-order-label": "There are no orders in this table", "terminal-orders-clear-order-action-btn": "Clear Table", "terminal-orders-order-approval-label": "This table needs approval to order. You can approve or clear the table.", "terminal-orders-clear-order-action-btn-action-confirmation-dialog-title": "Clear Table", "terminal-orders-clear-order-action-btn-action-confirmation-dialog-description-line1": "This action will free the table and close the order for anyone sitting in the table.", "terminal-orders-clear-order-action-btn-action-confirmation-dialog-description-line2": "Are you sure you wish to proceed ?", "terminal-cart-approved-cart-guest-user-label": "Customer: {{name}}", "terminal-menu-item-add-action-btn-label": "Add", "terminal-menu-open-menu-btn-label": "Open", "terminal-orders-cancel-confirmed-item-confirmation-dialog-title": "Cancel Item", "terminal-orders-cancel-confirmed-item-confirmation-dialog-description-line1": "This action will cancel an item already sent to the kitchen.", "terminal-orders-cancel-confirmed-item-confirmation-dialog-description-line2": "Are you sure you wish to proceed ?", "terminal-orders-cancel-confirmed-item-action-btn": "Cancel", "terminal-orders-reorder-ongoing-confirmed-item-action-btn": "Order", "terminal-orders-reorder-ongoing-confirmed-item-round-label": "Round {{count}}", "terminal-table-list-label": "Label", "terminal-table-list-code": "Code", "terminal-table-list-status": "Status", "order-history-table-header-order-id": "Id", "order-history-table-header-order-number": "Order nº", "order-history-table-header-order-date": "Date", "order-history-table-header-table-number": "Table Number", "order-history-table-header-number-of-participants": "# of Participants", "order-history-table-header-number-of-dishes": "# of Dishes", "order-history-table-header-dishes-tax": "VAT (Dishes €)", "order-history-table-header-dishes-total": "Total (Dishes €)", "order-history-table-header-number-of-drinks": "# of Drinks", "order-history-table-header-drinks-tax": "VAT (Drinks €)", "order-history-table-header-drinks-total": "Total (Drinks €)", "order-history-table-header-order-started": "Started", "order-history-table-header-order-ended": "Ended", "order-history-table-header-order-duration": "Duration", "order-history-table-header-order-category": "Order Category", "order-history-table-header-order-type": "Order Type", "order-history-table-header-order-customer-name": "Customer", "order-history-table-header-order-customer-phone": "Phone", "order-history-table-header-order-pickupTime": "Scheduled Time", "order-history-table-header-order-takeawayDate": "Scheduled Date", "order-history-table-header-order-notes": "Notes", "order-history-table-header-payment-type": "Payment Type", "order-history-table-header-payment-option": "Payment Option", "order-history-table-header-in-app-amount": "Online Amount", "order-history-table-header-cash-amount": "Cash Amount", "order-history-table-header-card-amount": "Card Amount", "order-history-table-header-cash-and-card-amount": "Cash & Card Amount", "order-history-table-header-tip": "Tip", "order-history-table-header-table-total": "Total (Table €)", "order-history-table-header-table-tax": "Total VAT", "order-history-table-header-implied-tax-rate": "Implied VAT Rate", "order-history-table-header-order-total": "Order Total", "order-history-table-header-total": "Total (€)", "order-history-table-header-receipt-action-label": "Receipt", "order-history-table-header-current-status-action-label": "Current Status", "waiter-daily-report": "Current Report", "waiter-daily-report-tip-category": "Tip", "waiter-daily-report-total-and-tip-category": "Total + Tip", "request-approval-dialog-title": "Table Approval", "request-approval-dialog-description": "To Continue as Guest, please add your last name and email. Your receipt will be emailed to you after payment.", "request-approval-form-last-name-field-label": "Your last name", "request-approval-form-last-name-field-placeholder": "<PERSON>", "request-approval-form-email-field-label": "Email", "request-approval-form-email-field-placeholder": "<EMAIL>", "request-approval-confirm-action-btn-label": "Confirm", "request-approval-qr-code-dialog-title": "Table Approval", "request-approval-qr-code-dialog-description": "Please show this QR code to the waiter.", "request-approval-qr-code-dialog-success-title": "Verification Completed", "request-approval-qr-code-dialog-success-description": "You have been verified by the Waiter. You can continue to place your order.", "waiter-qr-code-scanner-dialog-verification-success-title": "Verification Completed", "waiter-qr-code-scanner-dialog-verification-success-description": "Table has been successfully verified. Continue to view the table.", "report-history-report-id": "Daily Report ID", "report-history-report-start-date": "Date", "report-history-report-start-time": "Starting Time", "report-history-report-end-date": "Closing Date", "report-history-report-end-time": "Closing Time", "report-history-report-user": "Closed by", "report-history-no-reports": "No ongoing reports found. Please create a new daily report.", "report-history-create-report-btn": "Create Report", "report-history-close-report-btn": "Close report", "report-history-print-report-btn": "Print", "report-history-close-report-dialog-title": "Close report", "report-history-close-report-dialog-description-line1": "This will close the reporting session for the day.", "report-history-close-report-dialog-description-line2": "Are you sure you wish to proceed ?", "waiter-tax-report": "Tax Report", "waiter-tax-report-active-report-headline": "Daily Report", "waiter-tax-report-active-report-title": "Print daily report", "waiter-my-report": "My Report", "waiter-my-report-active-report-headline": "Daily Report Waiter receipt", "waiter-my-report-active-report-title": "Print your report", "waiter-log-out": "Log out", "waiter-common-print": "Print", "waiter-common-print-running-receipt": "Print Zwischenbon", "waiter-common-print-business-receipt": "Print Business Receipt", "waiter-common-close": "Close", "administration-layout-terminal-nav-label": "<PERSON>e In", "administration-layout-receipts-nav-label": "Receipts", "administration-layout-pickup-nav-label": "Takeaway", "administration-layout-customers-nav-label": "Customers", "administration-layout-reporting-nav-label": "Reporting", "administration-layout-management-nav-label": "Management", "administration-layout-current-report-nav-label": "Current Report", "administration-layout-my-report-nav-label": "My Report", "administration-layout-tax-report-nav-label": "Tax Report", "administration-layout-payments-nav-label": "Payments", "administration-layout-floor-management-nav-label": "Floor Management", "administration-layout-payment-settings-nav-label": "Payment Settings", "administration-layout-menu-editor-nav-label": "Menu Editor", "administration-layout-monthly-report-nav-label": "Monthly Report", "administration-layout-cash-register-nav-label": "Cash Journal", "administration-layout-settings-nav-label": "Settings", "administration-layout-feedback-nav-label": "<PERSON><PERSON><PERSON>", "administration-layout-marketing-nav-label": "Marketing", "administration-layout-reservation-nav-label": "Reservation", "administration-layout-marketplace-nav-label": "App Store", "administration-layout-allo-shop-nav-label": "allO Shop", "administration-layout-allo-shop-order-history-nav-label": "Order History", "setup-payments-connect-stripe-btn": "Connect with Stripe", "setup-payments-connect-stripe-btn-action-loading": "Checking your Stripe configuration...", "setup-payments-connect-stripe-btn-action-connected": "Account connected. Please login to your Stripe account for more information.", "pickup-order-creation-form-dialog-title": "New Order", "pickup-order-creation-form-create-btn-label": "Create", "pickup-order-creation-form-type-field-label": "Type", "pickup-order-creation-form-type-field-pickup-option-label": "Pickup", "pickup-order-creation-form-type-field-delivery-option-label": "Delivery", "pickup-order-creation-form-title-field-label": "Title", "pickup-order-creation-form-title-field-mr-option-label": "Mr.", "pickup-order-creation-form-title-field-ms-option-label": "Ms.", "pickup-order-creation-form-firstName-field-label": "Firstname", "pickup-order-creation-form-lastName-field-label": "Lastname", "pickup-order-creation-form-phone-field-label": "Phone", "pickup-order-creation-form-pickupTime-field-label": "Pickup Time", "pickup-order-creation-form-notes-field-label": "Notes", "pickup-order-creation-add-btn-label": "Takeaway", "pickup-order-creation-form-address-street-field-label": "Street", "pickup-order-creation-form-address-number-field-label": "House Number", "pickup-order-creation-form-address-zipCode-field-label": "Zip Code", "common-pickupTime": "Pickup Time", "common-deliveryTime": "Delivery Time", "pickup-order-ongoing-orders-title": "Ongoing", "pickup-order-open-orders-title": "Pending", "delete-pickup-order-dialog-title": "Delete pickup order", "delete-pickup-order-dialog-description": "Are you sure you wish to delete this order ?", "customers-table-header-creation-time": "Created At", "customers-table-header-full-name": "Full name", "customers-table-header-phone": "Phone", "customers-table-header-email": "Email", "customers-table-header-street": "Address", "customers-table-header-zipCode": "Zip Code", "customers-table-header-city": "City", "customers-table-header-new-order": "New order", "order-item-pickupTime-asap-label": "As soon as possible", "common-address": "Address", "common-phone-code": "Phone code", "common-pager": "Pager", "menu-editor-tabs-menus": "Menus", "menu-editor-tabs-remarks": "Remarks", "menu-editor-tabs-notes": "Notes", "menu-editor-tabs-options": "Options", "menu-editor-tabs-extras": "Extras", "menu-editor-actions-create-menu": "Add Category", "menu-editor-actions-create-menu-item": "Add Item", "menu-editor-actions-export-menu-items": "Export", "menu-editor-actions-create-remark": "Add Remark", "menu-editor-actions-create-note": "Add Note", "menu-editor-actions-update-note": "Update Note", "menu-editor-actions-create-option": "Add Option", "menu-editor-actions-create-option-item": "Add Item", "menu-editor-actions-create-extra": "Add Extra", "menu-editor-actions-create-extra-item": "Add Item", "menu-editor-table-header-numeration": "Numeration", "menu-editor-table-header-annotation": "Annotation", "menu-editor-table-header-title": "Title", "menu-editor-table-header-internal-title": "Internal Title", "menu-editor-table-header-description": "Description", "menu-editor-table-header-price": "Price", "menu-editor-table-header-restricted": "Not visible for guests", "menu-editor-table-header-disabled": "Sold Out", "menu-editor-table-header-hidden": "Hidden", "menu-editor-table-header-type": "Type", "menu-editor-form-thumbnail-field-label": "Photo", "menu-editor-form-thumbnail-field-placeholder": "Drag and drop or click to select file (max. 5MB)", "menu-editor-form-thumbnail-field-clear-image-label": "Remove image", "menu-editor-form-menu-field-label": "<PERSON><PERSON>", "menu-editor-form-shared-menu-field-label": "Display also in", "menu-editor-form-shared-menu-field-option-none": "None", "menu-editor-form-option-field-label": "Option", "menu-editor-form-extra-field-label": "Extra", "menu-editor-form-numeration-field-label": "Numeration", "menu-editor-form-volume-field-label": "Volume", "menu-editor-form-category-field-label": "Category", "menu-editor-form-category-field-option-dish": "Dish", "menu-editor-form-category-field-option-beverage": "Beverage", "menu-editor-form-printer-category-field-label": "Printer", "menu-editor-form-printer-category-field-option-bar": "Bar", "menu-editor-form-printer-category-field-option-kitchen": "Kitchen", "menu-editor-form-display-type-field-label": "Display", "menu-editor-form-display-type-field-option-item": "<PERSON><PERSON>", "menu-editor-form-display-type-field-option-card": "Card", "menu-editor-form-printer-format-field-option-paper": "Paper", "menu-editor-form-printer-format-field-option-label": "Label", "menu-editor-form-annotation-field-label": "Annotation", "menu-editor-form-title-field-label": "Title", "menu-editor-form-name-field-label": "Title", "menu-editor-form-remarkAnnotations-field-label": "Remark Annotations (comma separated)", "menu-editor-form-remarkAnnotations-field-placeholder": "A,<PERSON>,1", "menu-editor-form-title-i18n-field-label": "Title {{language}}", "menu-editor-form-name-i18n-field-label": "Title {{language}}", "menu-editor-form-customize-internal-field-label": "Customize Internal Name", "menu-editor-form-internal-title-field-label": "Internal title", "menu-editor-form-internal-name-field-label": "Internal title", "menu-editor-form-internal-title-i18n-field-label": "Internal title {{language}}", "menu-editor-form-internal-name-i18n-field-label": "Internal title {{language}}", "menu-editor-form-description-field-label": "Description", "menu-editor-form-description-i18n-field-label": "Description {{language}}", "menu-editor-form-ongoing-field-label": "Ongoing", "menu-editor-form-favourite-field-label": "Favorite", "menu-editor-form-restricted-field-label": "Not visible for guests", "menu-editor-form-hidden-field-label": "Hidden", "menu-editor-form-disabled-field-label": "Sold Out", "menu-editor-form-recommended-field-label": "Recommended", "menu-editor-form-scheduled-field-label": "Scheduled", "menu-editor-form-schedule-from-field-label": "From", "menu-editor-form-schedule-to-field-label": "Until", "menu-editor-form-price-field-label": "Price", "menu-editor-form-quantity-field-label": "Quantity", "menu-editor-form-min-field-label": "Min. Quantity", "menu-editor-form-max-field-label": "Max. Quantity", "menu-editor-form-rounds-field-label": "Rounds", "menu-editor-options-section": "Options", "menu-editor-extras-section": "Extras", "menu-editor-form-dine-tax-category-field-label": "Dine-In Tax", "menu-editor-form-takeaway-tax-category-field-label": "Takeaway Tax", "menu-editor-form-tax-category-field-option-normal": "Normal (19%)", "menu-editor-form-tax-category-field-option-reduced": "Reduced (7%)", "menu-editor-form-tax-category-field-option-zero": "0%", "menu-editor-form-options-field-label": "Options", "menu-editor-form-extras-field-label": "Extras", "report-history-report-month": "Month", "report-history-dishes-total": "7%", "report-history-beverages-total": "19%", "report-history-report-total": "Total", "settings-tabs-general": "General", "settings-tabs-printers": "Printers", "settings-tabs-configuration": "Configuration", "marketing-tabs-discounts": "Discounts", "marketing-tabs-promotions": "Promotions", "marketing-tabs-cards": "Cards", "marketing-tabs-cards-view-selector-title": "View By", "marketing-tabs-cards-view-selector-cards": "Cards", "marketing-tabs-cards-view-selector-transactions": "Card Transactions", "printer-editor-table-header-category": "Category", "printer-editor-table-header-format": "Format", "printer-editor-table-header-label": "Label", "printer-editor-table-header-key": "Key", "printer-editor-table-header-sn": "Serial No.", "printer-editor-actions-create-printer": "Add Printer", "printer-editor-form-category-field-label": "Category", "printer-editor-form-printer-format-field-label": "Format", "printer-editor-form-label-field-label": "Label", "printer-editor-form-key-field-label": "Key", "printer-editor-form-serial-number-field-label": "Serial Number", "printer-editor-form-printer-category-field-option-main": "Main", "printer-editor-form-has-cutter-field-label": "Split orders (CUT)", "printer-editor-form-disabled-field-label": "Disabled", "printer-editor-form-multiply-additions-field-label": "Multiply Additions", "printer-editor-form-print-account-field-label": "Include waiter account", "language-field-label": "Language", "show-participants-manager-btn-label": "Show Table", "participants-manager-label": "Manage Table", "order-item-details-remarks-section-title": "Remarks", "order-item-details-remarks-section-descriptions": "Allergens & Additives", "order-item-details-quantity-label": "Quantity", "order-item-details-add-to-order-btn-label": "Add to Order", "menu-item-price-from": "From", "menu-item-price-included": "Included", "order-item-base-price-label": "Base Price", "restaurant-editor-name-field-label": "Name", "restaurant-editor-name-i18n-field-label": "Name {{language}}", "restaurant-editor-slug-field-label": "Slug", "restaurant-editor-description-field-label": "Description", "restaurant-editor-description-i18n-field-label": "Description {{language}}", "restaurant-editor-code-field-label": "Code", "restaurant-editor-timezone-field-label": "Timezone", "restaurant-editor-thumbnailUrl-field-label": "<PERSON><PERSON><PERSON><PERSON>", "restaurant-editor-taxId-field-label": "Tax ID", "restaurant-editor-vatNumber-field-label": "VAT Number", "restaurant-editor-phone-field-label": "Phone", "restaurant-editor-website-field-label": "Website", "restaurant-editor-has-pickup-field-label": "Pickup enabled", "restaurant-editor-has-scan-field-label": "Scan to Order enabled", "restaurant-editor-has-delivery-field-label": "Delivery enabled", "restaurant-editor-delivery-fee-field-label": "Delivery Fee", "restaurant-editor-delivery-threshold-field-label": "Delivery Min Total", "restaurant-editor-has-express-checkout-field-label": "Express Checkout", "restaurant-editor-has-one-click-checkout-field-label": "One Click Checkout", "terminal-orders-add-discount-btn": "Add Discount", "terminal-orders-add-promotion-btn": "Add Promotion", "terminal-orders-discount-label": "Discount", "discount-editor-amount-field-label": "Amount", "discount-editor-promo-code-field-label": "Promo Code", "promotion-type-flatoff": "Fixed", "promotion-type-percentoff": "% Off", "promotion-type-freeitem_minbasket": "Free Item", "promotion-type-category_discount": "Category", "promotion-type-bogo": "2x1", "promotion-type-freedelivery": "Free Delivery", "promotion-type-menu_item_discount": "<PERSON><PERSON>", "terminal-orders-fast-checkout-action-btn": "Fast Checkout", "change-calculator-change-label": "Change", "change-calculator-tip-label": "Tip", "change-calculator-received-label": "Received", "terminal-orders-delivery-fee-label": "Delivery Fee", "receipt-identifier": "Receipt Number", "order-item-details-notes-section-title": "Notes to the kitchen", "order-item-details-notes-section-descriptions": "Allergic restrictions or any requests", "order-item-details-notes-section-notes-filed-placeholder": "Add your note", "order-update-dialog-title": "Send order update to your customer", "send-update-btn-label": "Send Update", "update-sent-btn-label": "Update Sen<PERSON>", "order-update-current-status-label": "Order Status", "order-update-eta-label": "Estimated Delivery Time", "order-update-eta-form-field-label": "Select Time", "order-update-message-label": "Delay Notification", "order-update-preparation-option-label": "In Preparation", "order-update-delivery-option-label": "In Delivery", "order-update-ready-to-pickup-label": "The order is ready to be picked up.", "report-revenue": "Revenue", "report-order-count": "Orders", "report-revenue-per-order": "Revenue per Order", "report-customer-revenue": "Customer Volume", "report-customer-count": "Customers", "report-revenue-per-customer": "Revenue per Customer", "floor-management-actions-create-floor": "Add Floor", "floor-management-actions-create-table": "Add Table", "floor-management-table-header-label": "Label", "floor-management-form-menu-field-label": "Floor", "floor-management-form-category-field-label": "Category", "floor-management-form-category-field-option-inside": "Inside", "floor-management-form-category-field-option-outside": "Outside", "floor-management-form-shape-field-label": "<PERSON><PERSON><PERSON>", "floor-management-form-shape-field-option-square": "Square", "floor-management-form-shape-field-option-circle": "Circle", "floor-management-form-minimum-capacity-field-label": "Minimum Capacity", "floor-management-form-capacity-field-label": "Maximum Capacity", "floor-management-actions-layout-editor": "Layout View", "floor-management-actions-table-editor": "Table View", "floor-management-actions-update-layout": "Update Layout", "floor-management-printers-section": "Printer <PERSON>s", "floor-management-select-main-printer-label": "Cashier Printers", "floor-management-select-bar-printer-label": "Bar Printers", "floor-management-select-kitchen-printer-label": "Kitchen Printers", "printer-editor-table-header-size": "Paper Size", "printer-editor-printer-paper-size-field-option-label-small": "40x30", "printer-editor-printer-paper-size-field-option-label-50-30": "50x30", "printer-editor-printer-paper-size-field-option-label-big": "50x40", "printer-editor-table-header-disabled": "Disabled", "menu-editor-form-tags-field-label": "Tags", "menu-editor-form-order-field-label": "Order", "receipt-refund-order-action-confirmation-dialog-title": "Refund order", "receipt-refund-order-action-confirmation-dialog-description-line1": "This action will refund all the items in this order.", "receipt-refund-order-action-confirmation-dialog-description-line2": "Are you sure you wish to proceed?", "refund-order-btn-label": "Refund Order", "menu-editor-form-collapsed-field-label": "Collapsed", "menu-editor-form-printer-field-label": "Printer", "tip-amount-title": "Enter tip", "tip-amount-field-placeholder": "2.00", "cash-register-actions-create-record": "Create Record", "cash-register-identifier": "Identifier", "cash-register-id": "ID", "cash-register-date": "Doc-Date", "cash-register-creation-date": "Entry Date", "cash-register-name": "Subject", "cash-register-type": "Type", "cash-register-amount": "Amount", "cash-register-category": "Category", "cash-register-tax-rate": "Tax Rate", "cash-register-tax-rate-none": "0%", "cash-register-tax-rate-seven": "7%", "cash-register-tax-rate-nineteen": "19%", "cash-register-tax-rate-mixed": "Mix", "cash-register-supplier": "Supplier", "cash-register-description": "Description", "cash-register-type-cash_out": "Cash Out", "cash-register-type-cash_in": "Cash In", "cash-register-category-normal": "General", "cash-register-category-tip": "Tip", "cash-register-category-daily_revenue": "Daily Revenue", "cash-register-category-cash_deposit": "Cash Deposit", "cash-register-category-bank_deposit": "Bank Deposit", "cash-register-category-salary_payment": "Salary Payment", "cash-register-category-private": "Private", "cash-register-category-adjustment": "Adjustment", "cash-register-category-gutschein": "Gift Card", "cash-register-category-money_transit": "Money Transit", "cash-register-category-money_transit_lieferando": "Money Transit (Lieferando)", "cash-register-category-money_transit_wolt": "Money Transit (Wolt)", "cash-register-category-money_transit_uber_eats": "Money Transit (UberEats)", "cash-register-category-money_transit_stripe": "Money Transit (Stripe)", "cash-register-category-money_transit_online": "Money Transit (Online)", "cash-registry-not-setup": "Cash Journal is not started.", "cash-register-form-start-balance-field-label": "Starting Balance", "cash-register-actions-create-register": "New Cash Journal", "cash-register-actions-create-register-btn": "Create", "cash-register-total": "Current Balance", "cash-register-start-balance": "Start Balance", "cash-register-end-balance": "End Balance", "report-history-total": "Total", "cash-register-document": "Document", "cash-register-konto": "Ko<PERSON>", "cash-register-gegen-konto": "GegenKonto", "cash-register-standard": "DATEV Standard", "receipt-payment-receipt-open-btn": "Open Receipt", "report-history-first-ordering": "First Ordering", "report-history-last-ordering": "Last Ordering", "report-history-cash": "Cash", "report-history-non-cash": "Non Cash", "payment-by-cash-breakdown": "Cash Flow", "payment-by-channel-breakdown": "Payment Method", "payment-by-tax-breakdown": "Tax", "payment-by-tax-breakdown-normal-label": "Normal (19%)", "payment-by-tax-breakdown-reduced-label": "Reduced (7%)", "order-item-cancellation-notes-section-title": "Notes", "order-item-cancellation-notes-section-descriptions": "Please write a note or select reasons for the cancellation", "order-item-cancellation-quantity-label": "Items to Cancel", "note-type-order": "Order", "note-type-cancellation": "Cancellation", "menu-editor-form-order-types-field-label": "Display for", "menu-editor-form-order-type-field-option-dine-in": "<PERSON>e In", "menu-editor-form-order-type-field-option-pickup": "Pickup", "menu-editor-form-order-type-field-option-delivery": "Delivery", "menu-editor-form-order-type-field-option-express": "Express", "menu-editor-form-order-type-field-option-express-togo": "Express ToGo", "checkout-select-participant-title": "Customer", "checkout-select-participant-description": "You can select a customer for this payment", "checkout-summary-title": "Summary", "checkout-summary-description": "Select items to be paid", "checkout-payment-channel-title": "Payment Method", "checkout-payment-channel-description": "Select the payment method", "receipt-payment-label": "Payment", "add-tip-btn": "Add Tip", "add-payment-btn": "Add Payment", "partial-checkout-pay-by-items-label": "Select Items", "partial-checkout-pay-by-amount-label": "Select Amount", "view-order-drawer-btn": "View Order", "view-order-drawer-items-quantity-btn-label": "{{count}} items", "activity-title-order-details": "Order Details", "search-table-btn": "Find table", "search-menu-btn": "Search item", "configuration-editor-has-auto-closure-field-label": "Scheduled Closing Tagesbericht", "configuration-editor-block-print-on-daily-closing-field-label": "Block Tagesbericht Autoprint", "configuration-editor-block-print-on-customer-receipt-field-label": "Block Digital Receipt Autoprint", "configuration-editor-block-item-print-on-cancellation-field-label": "Disable cancelled item printout", "configuration-editor-auto-closure-time-field-label": "Scheduled Time", "configuration-editor-has-ongoing-items-field-label": "Ongoing Orders", "configuration-editor-print-by-menu-field-label": "Menu Printers", "configuration-editor-print-by-addition-field-label": "Extras&Options Printers", "configuration-editor-has-cash-journal-field-label": "Cash Journal", "configuration-editor-has-partial-checkout-field-label": "<PERSON>", "configuration-editor-has-express-field-label": "Express ordering", "create-customer-title": "Customer", "create-customer-description": "Add information", "create-customer-btn": "Add Customer", "takeaway-order-information-title": "Order", "takeaway-order-information-description": "Update details", "takeaway-order-time-field-label": "Scheduled Time", "terminal-table-rotation-label": "Move table", "table-rotation-confirm-description-line1": "This action will move the order from table {{table}} to the selected table.", "table-rotation-confirm-description-line2": "Are you sure you wish to proceed ?", "promotion-editor-actions-create-promotion": "Create Promotion", "promotion-code-label": "Code", "cancel-order": "Cancel Order", "cancel-this-order": "Cancel this order?", "cancel-order-confirmation-message": "This will cancel the order and remove it from the list. Are you sure?", "cancel-order-dine-in-confirmation-message": "This will remove unconfirmed items, cancel confirmed items, and close the order.", "cancel-order-dine-in-confirmation-message-line-2": "Are you sure you want to proceed?", "go-back": "Go Back", "use-first-ordering-for-monthly-reports": "Use First Ordering for Monthly Reports", "when-enabled-the-first-ordering-decides-monthly-report-assignment": "When enabled, the first ordering is what decides on which month a particular report belongs to in the monthly report.", "promotion-percentage-label": "Percentage (%)", "promotion-amount-label": "Amount (€)", "discount-amount-label": "Amount (€)", "discount-percentage-label": "Percentage (%)", "discount-code-label": "Code", "card-editor-actions-create-card": "Create Card", "card-code-label": "Code", "card-external-code-label": "External Code", "card-disabled-label": "Card is disabled", "card-disabled-description": "Card can not be used until it is enabled.", "card-initial-cash-amount-label": "Starting Balance (€)", "card-cash-amount-label": "Balance (€)", "card-firstName-label": "First Name", "card-lastName-label": "Last Name", "card-email-label": "Email", "card-external-code-information-label": "Connect to existing loyalty card (Optional)", "create-pricing-schema": "Create P<PERSON><PERSON> Schem<PERSON>", "update-pricing-schema": "Update Pricing Schema", "fee-type-blended": "BLENDED", "fee-type-fixed": "FIXED", "fee-type-variable": "VARIABLE", "new-pricing-schema": "New Pricing Schema", "fees": "Fees", "fee-type": "Fee type:", "card-customer-information-label": "Customer Information (Optional)", "receipt-card-label": "Card", "card-amount-title": "Add customer card", "add-card-btn": "Add Card", "card-status-label": "Status", "card-status-stock": "Stock", "card-status-stock-description": "This card is in stock and can not be used until it is sold.", "card-status-set-to-stock-description": "Set the card in stock and sell it later.", "card-status-sold": "Sold", "card-sell-label": "Activate Card", "settings-business-hours": "Business Hours", "cards-total-count-label": "Count", "cards-balance-label": "Balance", "order-history-closed-by": "Closed By", "payments-report-committed-label": "Incomplete Revenue", "payments-report-projected-total-label": "Projected Total", "cards-label-card": "Card", "cards-label-history": "History", "card-load-money-label": "Load Money", "recover-order-label": "Update receipt", "recover-order-btn-label": "Recover", "terminal-order-recovery-label": "Recover Order", "order-recovery-confirm-description-line1": "Please select a table where to recover this order to.", "order-recovery-confirm-takeaway-description-line1": "You are recovering a takeaway order. Please confirm to proceed.", "order-recovery-confirm-express-description-line1": "You are recovering an express order. Please confirm to proceed.", "reservation-add-btn-label": "Add Reservation", "reservation-editor-actions-create-reservation": "Add Reservation", "reservation-customer-information-label": "Customer", "reservation-firstName-label": "Firstname", "reservation-lastName-label": "Lastname", "reservation-email-label": "Email", "reservation-phone-label": "Phone", "reservation-date-label": "Date", "common-minutes-annotation": "min", "common-available": "Available", "reservation-people-label": "People", "reservation-manage-btn": "Manage", "reservation-confirm-btn": "Confirm", "month-selector-placeholder": "Select month", "reservation-reference-label": "Reference", "reservation-note-label": "Notes", "reservation-status-label": "Status", "reservation-status-cancelled": "Cancelled", "reservation-status-no-show": "Customer did not show up", "common-install": "Add", "common-uninstall": "Remove", "app-store-category-payment": "Payment", "app-store-category-delivery": "Delivery", "app-store-category-reservation": "Reservation", "app-store-category-export": "Export", "app-store-category-cash-management": "Cash Management", "restaurant-editor-actions-create-restaurant": "New Restaurant", "restaurant-description": "Description", "restaurant-code": "Code", "restaurant-slug": "Public link", "restaurant-address-street": "Street", "restaurant-address-number": "Number", "restaurant-address-zipcode": "Postal Code", "restaurant-address-city": "City", "restaurant-address-country": "Country", "restaurant-website": "Website", "restaurant-phone": "Phone", "restaurant-vat-number": "VAT Number", "restaurant-tax-id": "Tax ID", "receipts-actions-filter": "Filter", "mobile": "Phone Number", "username": "Username", "password": "Password", "by-mobile": "By Mobile", "by-email": "By Email", "by-username-password": "By Password", "delete-table-title": "Delete Table", "delete-table-message": "You are about to permanently delete a table.", "are-you-sure-message": "Are you sure you wish to proceed?", "switch-to-live-mode": "Switch to Live Mode", "switch-to-exploration-mode": "Switch to Training Mode", "euro": "Euro", "dollar": "Dollar", "datev-format-category-accounts-receivable-payable": "debtors/creditors", "datev-format-category-general-ledger-account-labels": "general/ledger account labels", "datev-format-category-posting-batch": "Account-stack", "datev-format-category-terms-of-payment": "Payment Terms", "datev-format-category-various-addresses": "Various Addresses", "datev-format-category-recurring-postings": "Recurring Postings", "datev-booking-type-financial-account": "Financial Accounting", "datev-booking-type-annual-report": "Annual Report", "datev-accounting-purpose-independent": "Independent", "datev-accounting-purpose-taxation": "Taxation", "datev-accounting-purpose-calculation": "Calculation", "datev-accounting-purpose-commercial": "Commercial", "datev-accounting-purpose-ifrs": "IFRS", "datev-no-codification": "No Codification", "datev-codification": "Codification", "datev-export-marker": "<PERSON><PERSON>", "datev-version-number": "Version Number", "datev-format-category": "Format Category", "datev-format-version": "Format Version", "datev-advisor-number": "Consultant Number", "datev-client-number": "Client Number", "datev-financial-year-start-date": "WY-Start", "datev-general-ledger-account-length": "Account Length", "datev-booking-type": "Booking Type", "datev-accounting-purpose": "Accounting Purpose", "currency": "<PERSON><PERSON><PERSON><PERSON>", "add-restaurant": "Add Restaurant", "team-management": "Team Management", "billing": "Billing", "purchasing": "Purchasing", "firstname": "Firstname", "lastname": "Lastname", "invite": "Invite", "can-cancel-order-permission": "Allow to cancel an Order", "can-update-cash-register-permission": "Allow to edit Cash Journal", "can-update-general-settings-permission": "Allow to open Settings", "can-view-customer-editor-permission": "Allow to open Customer Manager", "can-view-floor-plan-permission": "Allow to open Floor Plan", "can-view-historical-orders-permission": "Allow to view Receipts", "can-view-historical-orders-analytics-permission": "Allow to view Receipts Analytics", "can-view-marketing-permission": "Allow to open Marketing", "can-view-menu-editor-permission": "Allow to open Menu Editor", "can-view-own-historical-orders-permission": "Allow to view only own Receipts", "can-view-own-reports-permission": "Allow to view only own Reports", "can-view-reports-permission": "Allow to view all Reports", "can-recover-order-permission": "Allow to recover an Order", "can-refund-order-permission": "Allow to refund an Order", "can-collaborate-on-order-permission": "Allow staff to collaborate in orders", "can-rotate-table-permission": "Allow to move Order between tables", "can-discount-order-permission": "Allow to add Order Discount", "can-manage-apps-permission": "Allow to manage Apps", "can-view-cash-register-permission": "Allow to open Cash Journal", "can-order-permission": "Allow to Order", "can-manage-takeaways-permission": "Allow to manage Takeaways", "can-manage-reservations-permission": "Allow to manage Reservations", "can-move-order-item-permission": "Allow to move order item", "can-delete-gift-card-permission": "Allow to delete gift cards", "color": "Color", "salmon-500-color": "Medium Salmon (allO)", "grey-600-color": "Medium Grey", "grey-800-color": "Dark Grey", "purple-700-color": "Dark Purple", "yellow-700-color": "Dark Yellow", "blue-700-color": "Dark Blue", "green-600-color": "Medium Green", "brown-800-color": "<PERSON>", "green-400-color": "Light Green", "yellow-500-color": "Light Yellow", "red-800-color": "Red", "dine-in": "<PERSON>e In", "takeaway": "Takeaway", "reservations": "Reservations", "express": "Express", "account": "Account", "select-icon": "Select Icon", "remove-icon": "Remove Icon", "popular-items": "Popular items", "menu": "<PERSON><PERSON>", "status-open": "Open", "status-closed": "Closed", "status-preparing": "Preparing", "status-ready": "Ready", "status-delivering": "Delivering", "status-cancelled": "Cancelled", "assign-customer": "Assign Customer", "pending": "Pending", "preparing": "Preparing", "ready": "Ready", "model": "Model", "new": "New", "no-items-title": "No pending items", "no-items-description": "Click the items on the left to add", "overview": "Overview", "daily-report": "Daily Report", "my-report": "My Report", "revenue": "Revenue", "products": "Products", "download-center": "Download Center", "team": "Team", "inventory": "Inventory", "payroll": "Payroll", "category": "Category", "amount": "Amount", "payment-method": "Payment Method", "tax": "Tax", "item": "<PERSON><PERSON>", "item-number": "Item Nº", "quantity-sold": "Quantity Sold", "table-count": "Count {{count}}", "table-total": "Total {{total}}€", "group-by": "Group By", "see-details": "See details", "top-products": "Top Products", "original-display": "Original Display", "count-sold": "{{count}} sold", "start-time": "Start time", "end-time": "End time", "opened-by": "Opened By", "closed-by": "Closed By", "reduced-tax": "tax 7%", "normal-tax": "tax 19%", "tips": "tips", "total-revenue": "Total Revenue", "improvement-area": "Improvement Area", "active": "active", "shifts-planned": "shifts planned", "shift-hours": "shift hours", "reservations-booked": "reservations booked", "wait-list": "waitlist", "expected-duration": "expected duration", "items-expiring-soon": "items expiring soon", "items-low-on-stock": "items low on stock", "most-ordered": "most ordered", "total-loss": "Total Mistakes", "new-customers": "new customers", "all-customers": "total customers", "amount-orders-cancelled": "orders cancelled", "amount-orders-refunded": "orders refunded", "amount-items-cancelled": "items cancelled", "discounted-amount": "discounted amount", "amount-gift-cards-applied": "gift cards applied", "amount-discounts-applied": "discounts applied", "host": "Host", "more-options": "More options", "filter": "Filter", "sort-by": "Sort by", "properties": "Properties", "clear": "Clear", "all": "All", "table-details": "Order details", "removed-orders": "Removed Orders", "refunded-orders": "Refunded Orders", "removed-items": "Removed Items", "monthly-report": "Monthly Report", "order-type": "Order Type", "cancelled-items": "Cancelled Items", "online-payment": "Online payment", "table-ordering": "Table ordering", "recommended-for-your-store": "Recommended for your store", "reporting": "Reporting", "export": "Export", "app-store": "App store", "my-apps": "My apps", "added-apps": "Added apps", "reservation": "Reservation", "support": "Support", "board": "Board", "list": "List", "plan": "Floorplan", "tile": "Tile", "timeline": "Timeline", "incoming": "Incoming", "scheduled": "Scheduled", "delivering": "Delivering", "watch-tutorial": "Watch tutorial", "no-orders": "No orders yet", "no-tables": "No tables yet", "click-the-button-below-to-create-an-order": "Click the button below to create an order.", "click-the-button-below-to-open-floor-plan-editor": "Click the button below to open floor editor", "start-creating-a-table-on-top-bar-above": "Start by creating a table on the top bar above", "create-table": "Create table", "create-express": "Create express", "create-takeaway": "Create takeaway", "add-items": "Add items", "done": "Done", "step-count-of-total": "Step {{count}} of {{total}}", "pay-and-finish": "Pay & finish", "checkout": "Checkout", "payment-status": "Payment status", "paid": "Paid", "not-paid": "Not paid", "mark-as-ready": "Mark as ready", "create-space": "Create space", "select-up-to-count": "Select up to {{count}}", "dinning-time": "Dinning time", "waiter": "Waiter", "customers": "Customers", "items": "Items", "remaining-time": "Progress", "select-payment-method": "Select payment method", "scan-gift-card": "Scan gift card", "add-tip": "Add tip", "add-promotion-or-discount": "Add promotion or discount", "payment": "Payment", "amount-due": "Amount due", "process-payment": "Process payment", "define-amount-to-pay": "Define amount to pay", "define-percentage-to-pay": "Define % to pay", "amount-to-pay": "Amount to pay", "of-amount": "of {{amount}}", "as-tip": "Set as tip", "change": "Change", "tip": "Tip", "scan": "<PERSON><PERSON>", "enter-code": "Enter code", "camera": "Camera", "input-manually": "Input manually", "card": "Card", "order-total": "Order total", "deducted": "Deducted", "custom": "Custom", "percentage": "Percentage", "exploration-mode": "Exploration Mode", "training-mode": "Training Mode", "service-mode": "Service mode", "table-service-mode": "Table", "express-service-mode": "Express", "hybrid-service-mode": "Hybrid", "promotion": "Promotion", "discount": "Discount", "add-promotion-code": "Add promotion code", "allo": "allO", "uber-eats": "Uber Eats", "wolt": "<PERSON><PERSON>", "lieferando": "<PERSON><PERSON><PERSON><PERSON>", "quandoo": "Quandoo", "google": "Google", "open-table": "OpenTable", "takeaway-partner": "Takeaway partner", "select-date": "Select date", "select-time": "Select time", "order-notes": "Order notes", "add-note": "Add note", "customer": "Customer", "search-or-create-customer": "Type to search or create", "see-more": "See more", "see-less": "See less", "create-order": "Create order", "minutes": "Minutes", "preparation-time": "Preparation time", "remaining-preparation-time": "Remaining preparation time", "preparation-time-picker-description": "Estimated time to prepare this order", "remaining-preparation-time-picker-description": "Remaining time to prepare this order", "discard-changes": "Discard changes", "pay-order": "Pay", "accept-order": "Accept order", "reject-order": "Reject order", "start-cooking": "Start cooking", "update-order": "Update order", "asap": "ASAP", "mark-as-delivering": "<PERSON> as delivering", "restaurant": "Restaurant", "select-takeaway-time": "You must select time", "edit-items": "Update order", "customer-name-and-address-required": "Customer information missing", "updated-later-notice": "(This can be also update later)", "reject-order-title": "Reject order", "reject-order-description": "Are you sure you want to reject this order?", "today": "Today", "at": "at", "newly-added": "New", "close-order-immediately": "Close the order directly", "no-reservations": "No reservations", "click-the-button-below-to-create-a-reservation": "Click the button below to create a reservation", "create-reservation": "Create reservation", "additive": "Additive", "allergenic": "Allergenic", "type": "Type", "waitlist": "Waitlist", "confirmed": "Confirmed", "ongoing": "Ongoing", "inactive": "Cancelled", "table": "Table", "guests": "Guests", "status": "Status", "number-of-guests": "Number of guests", "reservation-partner": "Reservation partner", "select-tables": "Select tables", "add-participant": "Add participant", "no-time-slots-available": "No time slots available", "suggestions": "Suggestions", "available": "Available", "minutes-overlap": "{{minutes}} min overlap", "select-table": "Select table", "seats": "seats", "this-reservation-has-no-orders-yet": "This reservation has no orders yet", "notes": "Notes", "accept": "Accept", "reject": "Reject", "mark-as-no-show": "<PERSON> as no show", "prepare-table-and-get-started": "Prepare the table and get started", "reservation-coming-action-message": "Upcoming reservation in {{minutes}} minutes.", "reservation-passed-action-message": "Reservation to be started since {{minutes}} minutes.", "reservation-in-progress": "{{name}} reservation in progress.", "pricing": "Pricing", "this-integration-is-free": "This integration is free.", "contact-allo-team": "Contact allO team for questions & technical support.", "start-reservation": "Customer showed up", "hand-over-to-partner-for-delivery": "Hand over to {{partner}} and finish", "app-configuration": "Configure {{appName}}", "operations": "Operations", "update-your-restaurant-operating-settings": "Update your restaurants operating settings", "operating": "Operating", "accepting-orders-from-partner": "Accepting orders from partner", "loading-time-slots": "Searching for time slots", "loading-tables-available": "Searching for tables", "no-time-slots": "No time slots", "no-tables-available": "No tables", "apps": "Apps", "app-settings": "App settings", "open": "Open", "about": "About", "remove-app": "Remove app", "guest-reservations": "Guest reservations", "control-settings-for-reservations-by-customers": "Control settings for reservations done by customers", "allow-guest-reservation": "Allow reservations by guests", "this-will-allow-guests-to-create-online-reservations": "This will allow guests to create online reservations", "restaurant-opening-hours": "Restaurant opening hours", "max-reservation-overlap": "Maximum reservation minutes overlap", "set-the-maximum-allowed-overlap-between-reservations": "Set the maximum allowed overlap between reservations", "day": "Day", "time-period": "Time period", "reservation-rules": "Reservation rules", "update-settings-for-all-reservations": "Update settings for all reservations", "max-number-of-guests-allowed": "Maximum number of guests allowed", "set-the-max-amount-of-guests-a-reservation-can-have": "Set the highest number of people a reservation can have", "estimated-dinning-times": "Estimated dinning times", "configure-estimations-for-dinning-duration-by-guests": "Configure estimations for dinning duration by guests during a reservation", "estimated-dinning-minutes": "Estimated dinning minutes", "limit-guest-reservations": "Limit guest reservations", "configure-max-number-of-guests-during-specific-times": "Configure maximum number of guests allowed to reserve a table during specific time periods", "number-of-guests-at": "Number of guests at time interval", "require-number-of-guests-in-reservation": "Require number of guests in reservation", "require-waiters-to-input-number-of-guests-in-table": "Require waiters to input number of guests sitting at the table during a reservation", "no-time-periods-added": "No time periods added", "add-a-period-to-configure-start-and-end-times": "Add a time period to configure start and end times", "update-time-slots": "Update time slots", "update-estimated-dinning-time": "Update estimated dinning time", "table-is-reservable": "Table is reservable by guest online", "allow-table-to-be-reserved-during-a-reservation": "Allow the table to be reserved by guest directly online", "reservation-pricing": "Reservation management is free. Online reservation by guest is 99€/Month or 0.5€/reservation", "send-back-to-confirmed": "Send back to confirmed", "go-to-table": "Go to table", "show-reservation-duration-in-email": "Show reservation duration in emails", "duration-of-the-reservation-will-be-included-in-the-customer-emails": "Duration of the reservation will be included in the customer emails", "add-message-in-customer-email": "Add message in customer emails", "add-allo-widget-to-your-restaurant-website-and-the-allo-reservation-link-to-your-google-business": "Add allO Widget to your restaurant website and the allO Reservation link in your Google business account to allow users to reserve directly.", "copy": "Copy", "allo-widget": "allO Widget", "allo-reservation-link": "allO Reservation link", "generate-fiskal-closing": "Generate Fiskal closing", "ip": "IP Address", "brand": "Brand", "no-printers": "No printers", "click-the-button-below-to-add-a-printer": "Click the button below to add a printer", "create-timesheet": "Create timesheet", "update-timesheet": "Update timesheet", "date": "Date", "select-account": "Select account", "select-type": "Select type", "no-timesheet-records": "No timesheet records", "click-the-button-below-to-create-a-timesheet-record": "Click the button below to create a timesheet record", "break": "Break", "working": "Working", "company": "Company", "my-work": "My work", "hourly-rate": "Hourly rate", "working-time": "Working time", "break-time": "Break time", "payable": "Payable", "no-mobile": "No phone number", "no-email": "No email", "update-hourly-rate": "Update hourly rate", "add-one-time-item": "Custom item", "quantity": "Quantity", "joined-since": "Joined since", "manage-your-day": "Manage your day", "quickly-log-your-working-time": "Quickly log your working time", "start-working": "Start working", "stop-working": "Stop working", "start-break": "Start break", "worked-time": "Worked time", "report-can-not-be-closed": "Daily report can not be closed", "this-daily-report-can-only-be-closed-after-datetime": "Report can only be closed after {{datetime}}", "close-report": "Close report", "daily-report-closing": "Daily report closing", "you-can-select-a-custom-date-to-close-this-report": "You can select a custom date to close the report.", "time": "Time", "local-printing-accounts": "Local printing accounts", "test": "Test", "test-long-content": "Test long content", "test-cash-drawer": "Test cash drawer", "assigned-pager": "Assigned pager", "order-number": "Order {{number}}", "keep-search-keyboard-visible": "Keep search keyboard visible", "allow-express-terminal-reorder": "Allow express terminal re-order", "order-to-go": "Order ToGo", "dine-preference": "Dine preference", "auto-close-on-checkout": "Auto close on checkout", "set-api-key": "Set API Key", "allo-pay-settings": "allO Pay settings", "control-settings-for-payment-terminals": "Control settings for your terminal", "allo-pay-terminal": "allO Pay terminal", "device-manager": "Device manager", "payment-terminals": "Terminals", "select-payment-terminal": "Select terminal", "terminal-editor-actions-create-terminal": "Add terminal", "processing-terminal-payment": "Processing payment. Please check your terminal...", "no-payment-terminals": "No payment terminals", "click-the-button-below-to-add-a-payment-terminal": "Click the button below to add a terminal", "payment-in-progress": "Payment in progress...", "order": "Order", "refunded": "Refunded", "recovered": "Recovered", "completed": "Completed", "ordered-by": "Ordered by", "moved-from-table": "Moved from table", "moved-to-table": "Moved to table", "moved": "Moved", "profile": "Profile", "preferences": "Preferences", "payroll-information": "Payroll Information", "language": "Language", "delete-fiskal-closing": "Delete Fiskal Closing", "basic-info": "Basic Information", "basic-info-description": "Enter your profile information", "customize-your-terminal": "Customize your terminal", "fine-tune-the-terminal-to-work-efficiently": "Fine tune the allO Terminal to best fit your work", "select-menu-layout": "Select menu layout", "cards-layout": "Cards view (Standard)", "tabs-layout": "Tabs view", "birthday": "Birthday", "gender": "Gender", "male": "Male", "female": "Female", "marital-status": "Marital status", "single": "Single", "married": "Married", "personal-info": "Personal Information", "update-your-personal-legal-information": "Update your personal legal information.", "address": "Address", "update-your-official-address": "Update your official address information.", "tax-information": "Tax information", "enter-your-tax-details": "Enter your tax details", "tax-class": "Tax class", "tax-number": "Tax number", "health-insurance": "Health insurance", "update-your-health-insurance-data": "Update your health insurance data", "social-security": "Social security", "update-your-social-security-information": "Enter your social security details", "social-security-id": "Social security id", "health-insurance-type": "Health insurance type", "health-insurance-provider": "Health insurance provider", "public": "Public", "private": "Private", "select-content-language": "Select content language", "set-the-language-for-the-terminal-and-content": "Set the preferred language for ordering and items translation", "see-payroll-details": "See payroll details", "role": "Role", "shifts": "Shifts", "payroll-history": "Payroll history", "no-payrolls": "No payrolls found", "identifier": "Identifier", "issued-date": "Issued date", "id": "ID", "provider": "Provider", "send-profile-to-tax-advisor": "Send profile to TAX advisor", "none": "None", "brazil": "Brazil", "greece": "Greece", "country": "Country", "ie": "IE", "crt-code": "CRT Code", "im": "IM", "cnae": "CNAE", "set-reply-to-email": "Set reply to email", "ongoing-item-base": "Ongoing item base", "billing-information": "Billing information", "no-payment-method": "No payment method", "add-payment-method-description": "Create a payment method to activate your allO system.", "name": "Name", "billing-summary": "Billing Summary", "billing-summary-line": "You are paying {{period}} {{amount}}€ under the {{package}} pricing package.", "card-ending-in": "Card ending in {{number}}", "general": "General", "legal-information": "Legal Information", "configuration": "Configuration", "restaurant-name": "Restaurant name", "set-restaurant-name": "Set restaurant name", "restaurant-settings": "Restaurant settings", "payment-plans": "Plans", "monthly": "Monthly", "per-month": "/month", "yearly": "Yearly", "per-year": "/year", "compare-pricing-plans": "Compare pricing plans", "on-our-website": "on our website", "no-hidden-costs": "No hidden costs", "no-minimum-contract-period": "No minimum contract period", "leasing-for-card-terminal": "Leasing for card terminal", "help-and-feedback": "Help & feedback", "print-logo": "Print logo", "alcohol-percentage": "Alcohol percentage", "delivery-partners": "Delivery partners", "menu-editor": "Menu editor", "menu-categories": "Categories", "no-menu-categories": "No categories", "menu-items": "Items", "no-menu-items": "No items", "option-categories": "Options", "extra-categories": "Extras", "allergens-and-additives": "Allergens & Additives", "no-allergens-and-additives": "No allergens & additives", "menu-item-notes": "Ordering notes", "no-menu-item-notes": "No ordering notes", "click-the-button-below-to-create": "Click the button below to create", "please-add-payment-operation-interruption": "Please complete your billing information as soon as possible.", "no-charge-until-you-go-live": "Nothing will be charged until you go live.", "business-verification": "Verifying your business", "to-validate-this-payment-method-we-may-need-to-charge-and-refund": "To be compliant with relevant regulations in Germany, please click “Confirm” below ONLY if you are a restaurant owner in Germany.", "the-hold-will-be-charged-if-not-legit": "⚠️ If you are NOT a restaurant owner in Germany and still continue to the next step, we will charge 100€ verification fee according to our terms & conditions.", "agree-and-continue": "Agree and continue", "all-done": "All done", "click-below-to-start-exploring-managing-restaurant-never-easier": "Click the button bellow to start exploring allO. Embrace the end of the software chaos in your restaurant.", "start-exploring": "Start exploring", "managing-director": "Owner/Managing director", "restaurant-information": "Restaurant Information", "update-your-configurations": "Update your configurations.", "update-your-documents": "Update your documents.", "update-your-legal-information": "Update your legal information", "update-your-contact-information": "Update your contact information", "contact-information": "Contact Information", "update-opening-hours": "Update your restaurant's opening hours. Note: you can set separat opening hours for your webshop in the webshop app's settings.", "update-restaurant-information": "Update your restaurant information.", "documents": "Documents", "common-scan-to-order": "<PERSON>an to Order", "allow-scan-to-order-toggle-description": "This will enable guests to use the scan to order feature, where they can order through the scanning of qr codes.", "set-the-delivery-fee": "Set the fee for deliveries.", "set-the-delivery-min-total": "Set the minimum order value for deliveries.", "settings-for-guests": "Guest settings", "control-guest-settings": "Control settings regarding features for you guests.", "reporting-settings": "Reporting Settings", "control-reporting-settings": "Control settings regarding your reporting.", "checkout-settings": "Checkout Settings", "control-checkout-settings": "Control settings regarding your checkout.", "printer-settings": "Printer <PERSON>s", "control-printer-settings": "Control settings regarding your printers.", "pick-up-toggle-header": "Pick Up", "enable-pickup-toggle-description": "This will enable pickup orders.", "enable-delivery-toggle-description": "This will enable delivery orders.", "enable-express-checkout-toggle-description": "This will enable express checkouts.", "enable-one-click-checkout-toggle-description": "This will enable one click checkouts.", "enable-partial-checkout-toggle-description": "This will enable bill splitting during checkouts.", "enable-scheduled-auto-closure-toggle-description": "This will enable the automated and scheduled closure of your daily report.", "set-scheduled-closure-time-daily-report": "Set the automated closure time for your daily report.", "disable-daily-report-autoprint": "This will disable the automated print of your daily report.", "disable-digital-receipt-autoprint": "This will disable the automated print of digital receipts.", "disable-cancelled-item-printout": "This will disable the print out of a cancelled items receipt. The receipt for cancelled items is usually send to the kitchen if an item (that already had been send to the kitchen) gets cancelled. In restaurants where register and kitchen are connected or close by this receipt might not be needed.", "enable-express-ordering": "This will enable express ordering.", "enable-cash-register": "This will enable the cash register.", "enable-ongoing-ordering": "This will enable ongoing orders. Ongoing orders are especially useful for restaurants with buffet menus. You can set whether or not you want to offer an item in the buffet in the menu editor.", "enable-menu-printers": "This will enable menu printing.", "enable-menu-extras-and-options-printers": "This will enable the printing of extras and options.", "daily-report-autoprint-lable": "Daily Report Autoprint", "digital-receipt-autoprint-lable": "Digital Receipt Autoprint", "driver-arrives": "Driver arrives", "you-are-getting-50-percent-off": "You are getting 50% off!", "billed-annually": "billed annually", "finish-setup": "Finish setup", "keep-exploring": "Keep exploring", "you-still-have-a-few-steps-to-complete": "Complete all the steps above and get to know allO 📚", "everything-is-ready": "Everything is ready", "you-have-completed-all-the-onboarding-steps": "You have completed all the onboarding steps 🙌", "go-live": "Go live!", "our-humans-would-like-to-say-hello-watch-video": "Our humans would like to say helloooo. Watch the video 📢", "welcome-to-your-restaurant": "Welcome to your restaurant", "lets-start-exploring": "Lets start exploring!", "floor-plan-editor": "Floorplan editor", "migrate-changes-from-test-mode": "Do you want to migrate changes from exploration when going live?", "migrate-menu-changes": "Migrate all menu changes", "migrate-floors-and-tables": "Migrate floors and tables", "migrate-installed-apps": "Migrate installed apps", "yes-keep-selected-changes": "Yes, keep selected changes", "no-start-fresh": "No, start fresh", "switch-restaurant": "Change restaurant", "business-registration": "Business registration", "shareholder-id": "Shareholder passport or ID", "shareholder-residence-permit": "Shareholder residence permit", "account-statement": "Account statement", "supplier-invoice": "Supplier invoice", "drag-a-file-or-click-to-upload": "Drop a file or click to upload", "you-can-upload-documents-of-max-10-mb": "You can upload new or replace documents of up to max. 10MB", "payment-info": "Payment Information", "checkout-auto-close": "Terminal checkout auto-close", "directly-close-express-orders-on-checkout": "Enable automatically closing express orders on checkout", "reorder-express-terminal": "Terminal Re-order", "enable-reordering-flow-for-express-terminal-after-closing-order": "Enable re-ordering flow for Express Terminal after closing order", "shareholders": "Company shareholders", "add-all-shareholders-with-more-than-25-including-you": "Please add all shareholders that hold more than 25%, including you.", "bank-documents": "Bank information and documents", "fill-in-your-bank-details-and-upload-official-documents": "Please fill in your bank payout details and verifying documents", "add-shareholder": "Add shareholder", "delete-shareholder": "Delete shareholder", "shareholder-identification-document": "Shareholder passport or ID", "shareholder-name": "Shareholder legal name", "shareholder-email": "Shareholder email", "shareholder-phone": "Shareholder phone", "pdf-receipt": "PDF receipt", "send-by-email": "Send via email", "show-qr-code": "Show QR code", "send-receipt": "Send receipt", "operational-modes": "Operational Modes", "how-many-tables-do-you-want-to-add": "How many tables do you want to add ?", "select-element-in-table-panel": "Select elements", "common-format": "Format", "common-space": "Space", "common-table-QR-code": "Table QR Code", "common-angle": "<PERSON><PERSON>", "floor-management-min-capacity-field-label": "Min Capacity", "floor-management-max-capacity-field-label": "Max Capacity", "done-editing-layout": "Done editing layout?", "your-operations": "Your operations", "select-the-capabilities-you-want-to-use": "Select the capabilities you want to use", "select-sale-discount": "Select promotional added amount", "qr-code": "QR Code", "ongoing-item-extras-limit": "Extras limit (Ongoing item)", "payment-settings": "Payment settings", "setup-payment-settings": "Setup the payment options for your terminal", "card-enabled-label": "Card is active", "card-enabled-description": "This card can only be used when this is on.", "more-btn-label": "More", "allo-webshop-link": "allO Webshop link", "webshop": "Webshop", "control-settings-for-webshop": "Control your webshop settings.", "webshop-settings": "Webshop Settings", "use-allo-webshop-for-takeaway-express-scan-to-order": "Use allO Webshop link for Takeaway. You can add it to your website and your restaurant Google account", "scan-to-order-settings": "<PERSON>an to <PERSON> Settings", "control-settings-for-guests-scanning-to-order": "Control settings for guests who scan to order", "use-allo-scan-to-order-for-digital-ordering": "Use allO ScanToOrder for digital ordering on the go or sitting in the restaurant.", "link-dynamic-qr-code": "Link dynamic QR code", "menu-editor-form-thumbnail-field": "Add a photo", "menu-editor-items-sell-better-with-photo": "Items sell better when they have a well-taken photo!", "menu-editor-items-basic-description": "Enter basic information about the item.", "menu-editor-category-basic-description": "Enter basic information about the category.", "menu-editor-items-thumbnail-field-description": "Drop here or click to upload", "menu-editor-item-name-field": "Item name", "menu-editor-name-field-placeholder": "Type name here", "menu-editor-name-translation-field-placeholder": "Type item name translation here", "menu-editor-description-field-placeholder": "Type description here", "menu-editor-prep-time-description": "How long you need to prepare this item? You’ll be notified accordingly.", "menu-editor-category-description": "Keep your menu organized by organizing items into categories.", "menu-editor-options-description": "Increase customization by adding options to this item.", "menu-editor-remarks-description": "Ensure your customer's safety by providing allergen and additive remarks for this item.", "chose-receipt-option-payment-process": "Choose receipt option", "business-receipt": "Business receipt", "no-receipt-option": "None", "provide-email-for-pdf-receipt-checkout": "Send pdf receipt to this email address:", "chose-default-receipt-option": "Choose your default receipt type", "default-receipt-option-configuration-description": "Your default receipt type will automatically be preselected during the payment process. You can still choose a different receipt type during the payment process despite the default setting.", "send-reports-to-email": "Report email", "by-default-reports-will-be-sent-to-these-emails": "By default, reports will be sent to these emails", "jump-to-table-btn": "Jump to", "dont-miss-out-on-all-our-features": "Don't miss out on all our features and tutorials", "tutorials": "Tutorials", "can-use-timesheet-permission": "Allow to use the allO Timesheet", "item-has-additions-limit": "Item has a limit of {{limit}} options", "additions-max": "Maximum additions", "finished": "finished", "running": "running", "expected": "expected", "delete-table-description": "This action can not be reversed.", "are-you-sure-description": "Are you sure you want to delete this table ?", "employees": "employees", "menu-editor-item-typ-field": "Item Type", "menu-editor-category-field-placeholder": "Select a category here", "menu-editor-option-field-placeholder": "Select a option here", "menu-editor-remark-field-placeholder": "Select and add remarks here", "menu-editor-show-advanced-options": "Show advanced options", "menu-editor-hide-advanced-options": "Hide advanced options", "menu-editor-additional-information-section": "Additional Information", "menu-editor-additional-information-section-description": "Add extra details about this items", "menu-editor-service-section": "Services", "menu-editor-service-section-description": "Set the VAT for this item depending on where it's consumed", "menu-editor-extras-description": "Increase customization by adding extras for this item.", "menu-editor-checkbox-section": "Availability", "menu-editor-checkbox-description": "Control the availability of this item by selecting the corresponding checkboxes.", "daily-report-auto-closing-toggle": "Daily Report Auto-Closing", "menu-editor-delete-item": "Delete item", "menu-editor-hide-item-from-customer": "Hide from customer", "menu-editor-category-name": "Category Name", "menu-editor-category-add-a-photo": "Add a photo to this category", "menu-editor-categories-better-with-photo": "Improve your menu by adding photos.", "menu-editor-translation-checkbox": "Translate", "menu-editor-additional-information-category-section": "Add additional details about this category", "menu-editor-delivery-partner-section": "Delivery Partners", "menu-editor-scheduled-category-section": "Scheduling", "menu-editor-actions-add-option-item": "Add option item", "menu-editor-i18n-field-label": "{{language}}", "menu-editor-price-description": "Set different prices for this item.", "processing-payment": "Processing payment", "cancelling-payment": "Cancelling payment", "allo-is-cancelling-terminal-payment": "allO is trying to cancel the payment, this might take a few seconds.", "check-your-allo-terminal-for-payment": "Please process the payment in allO Terminal by phone or card", "could-not-cancel-payment-in-progress-or-issue-with-terminal": "allO could not cancel operation due to payment in progress or connectivity issue.", "more-actions": "More actions", "hide-actions": "Hide actions", "i-have-received-the-payment": "I have received payment", "allo-could-not-confirm-payment-are-you-sure-you-wish-to-proceed": "allO could not confirm the payment. Are you sure you wish to proceed?", "force-abort": "Force abort", "allo-could-not-confirm-cancellation-are-you-sure-you-wish-to-proceed": "allO could not confirm payment cancellation. Are you sure you wish to abort payment?", "range-too-big": "Selected range is too big", "the-selected-range-is-too-big-please-select-up-to-days": "Please select a smaller date range on the date selector of up to 7 days.", "lan-printer-polling-accounts": "LAN printer accounts", "are-you-sure-description-to-delete-item": "Are you sure you want to delete this item?", "delete-item-title": "Delete item", "delete-category-title": "Delete category", "delete-option-title": "Delete option", "delete-extra-title": "Delete extra", "delete-note-title": "Delete note", "delete-remark-title": "Delete remark", "are-you-sure-description-to-delete-category": "Are you sure you want to delete this category?", "are-you-sure-description-to-delete-option": "Are you sure you want to delete this option?", "are-you-sure-description-to-delete-extra": "Are you sure you want to delete this extra?", "are-you-sure-description-to-delete-note": "Are you sure you want to delete this note?", "are-you-sure-description-to-delete-remark": "Are you sure you want to delete this remark?", "promotion-card-header-explore-all-functionalities": "Explore all functionalities.", "promotion-card-text-quick-and-easy": "It's quick and easy!", "promotion-card-text-new-menu-editor": "Checkout our new menu editor!", "open-menu-editor": "Open editor", "common-dismiss": "<PERSON><PERSON><PERSON>", "back-to-old-menu-editor": "Back to old menu editor", "more": "More", "select-discount": "Select discount (%)", "custom-discount": "Add custom discount", "remove": "Remove", "remove-payment-of-amount": "Remove {{amount}} payment", "are-you-sure-you-want-to-remove-payment": "Are you sure you want to remove this payment? This action is final.", "payment-order-value": "Order value", "not-editable": "Not editable", "translate": "Translate", "secure-your-account": "Secure your account", "manage-how-you-switch-and-protect-your-account": "Manage how you switch and protect your account", "pin-code": "PIN code", "update-pin": "Update pin", "setup-pin": "Setup pin", "remove-current-pin": "Remove current pin", "beta-features": "Beta features", "enable-features-that-are-still-in-beta": "Enable latest features that available, in Beta stage", "upgraded-menu-editor": "Upgraded menu editor", "use-the-latest-menu-editor-by-default": "Use the latest menu editor by default", "improved-order-flow": "Improved ordering flow", "enable-the-latest-version-of-the-ordering-flow-in-your-terminal": "Enable the latest version of the ordering flow in your Terminal", "improved-mobile-ordering-flow": "Improved mobile ordering flow", "enabled-new-ordering-flow-for-mobile-users": "Enable new ordering flow for mobile users", "improved-checkout": "Improved checkout", "enable-new-and-improved-checkout-flow": "Enable new and improved checkout flow in your system", "improved-mobile-checkout": "Improved checkout", "enable-new-checkout-process-for-mobile-users": "Enable new checkout process for mobile users", "table-lable-placeholder": "Table label", "item-placeholder": "Item name/numeration", "item-numeration-placeholder": "Item numeration", "update-wolt-menu": "Update Menu", "no-wolt-menu-uploaded-yet": "No menu uploads", "take-menu-snapshot-for-wolt-menu-now-empty-screen": "Take a snapshot of your menu now, to publish it to <PERSON><PERSON>.", "take-menu-snapshot-button-label": "Take Snapshot", "get-started-with-wolt-menu": "Get Started", "publish-menu-wolt-btn-label": "Publish on Wolt", "compare-button-label": "Compare", "integrated-payments-with-allo-pay": "Integrated payments with allO Pay", "i-have-a-voucher": "I have a voucher", "voucher": "Voucher", "wolt-menu-tool-explanation-line-1": "1. press \"Take Snapshot\" to take new snapshot of menu to get most current menu version. This snapshot will be shown under DRAFT", "wolt-menu-tool-explanation-line-2": "2. check if all changes are correct. If the changes are not correct simply change them in your menu Editor and follow these step again", "wolt-menu-tool-explanation-line-3": "3. press \"Publish to Wolt\". This will update the latest changes to your Wolt menu.", "menu-updating-tool": "Wolt Menu Updating Tool", "delete-user-team-management-dialog-title": "Delete Team member", "delete-user-team-management-dialog-description": "Are you sure you want to delete this team member ?", "purchases": "Purchases", "suppliers": "Suppliers", "skus": "SKUs", "setup-supplier-address": "Setup supplier address", "delete-supplier": "Delete supplier", "this-actions-is-not-reversible": "This action is not reversible.", "delete-sku-item": "Delete SKU item", "no-sku-items": "No SKU items", "click-the-button-below-to-create-an-sku": "Click the button below to start creating SKUs", "create-sku": "Create SKU", "no-suppliers-found": "No suppliers found", "to-create-sku-items-you-will-need-to-setup-suppliers": "To creat SKUs, you will need to setup suppliers", "setup-suppliers": "Setup suppliers", "no-inventory-categories-found": "No product categories found", "to-create-sku-items-you-will-need-to-setup-inventory-categories": "To creat SKUs, you will need to setup product categories", "product-categories": "Product categories", "product-category": "Product category", "unit-price": "Unit price", "delete-product-category": "Delete product category", "no-purchases": "No purchases", "click-the-button-below-to-create-a-purchase": "Click the button below to create a purchase", "create-purchase": "Create purchase", "no-product-categories": "No product categories", "click-the-button-below-to-create-a-product-category": "Click the button below to create a product category", "create-product-category": "Create product category", "setup-product-categories": "Setup product categories", "gram": "gram (g)", "kilogram": "kilogram (kg)", "liters": "liters (l)", "milliliters": "milliliters (ml)", "pieces": "pieces (pcs)", "measurement-unit": "Measurement unit", "no-suppliers": "No suppliers", "click-the-button-below-to-create-a-supplier": "Click the button below to create a supplier", "ingredient-quantity": "{{ingredient}} quantity", "unit-price-per-sku": "Unit-price/SKU", "ingredient": "Ingredient", "ingredients": "Ingredients", "no-ingredients": "No ingredients", "click-the-button-below-to-create-an-ingredient": "Click the button below to create an ingredient", "create-ingredient": "Create ingredient", "no-ingredients-found": "No ingredients found", "to-create-sku-items-you-will-need-to-setup-ingredients": "To create SKUs, you will need to setup ingredients", "setup-ingredients": "Setup ingredients", "sku-category": "SKU category", "vat-id": "VAT id", "create-invoice": "Create invoice", "purchase-cart": "<PERSON><PERSON>", "purchase-requested": "Requested", "purchase-confirmed": "Confirmed", "purchase-order": "Purchase order", "customize-btn-size": "Customize your button sizes", "manage-btn-size-in-top-navigation": "Manage the size of your main buttons in the navigation bar at the top.", "normal-btn-size": "Normal", "large-btn-size": "Large", "button-size-label": "<PERSON><PERSON>", "configuration-editor-has-checkout-default-toGo-label": "To-go Checkout by <PERSON><PERSON><PERSON>", "enable-checkout-default-toGo-toggle-description": "Is this toggle enabled, all express orders will by default be labeled as \"to go\". This label will also appear on the kitchen receipt to signal your kitchen, that the order is a to-go order.", "pre-approve": "Guest", "reserve-with-google-settings": "Reserve with Google settings", "control-settings-for-reserve-with-google-integration": "Control settings for the integration with Google reservations", "google-place-id": "Google Place id", "set-google-place-id-here": "Set your unique id found in Google Maps", "enable-reservations": "Enable reservations", "allow-google-to-send-reservations-to-your-allo-app": "Activate this option if you want to allow Google to send reservations to your allO app. Please be aware that it may take up to 24 hours for Google to process the change if you decide to disable this feature.", "uber-eats-key": "UberEats key", "setup-uber-eats-integration-by-using-store-id": "Setup UberEats integration by using the store id", "delete-integration": "Delete integration", "manage-category-per-row": "Manage the number of categories manually.", "category-number-label": "Number of Categories", "preferences-keep-categories-responsive-toggle": "Manage the menu categories display in your terminal according to your screen size.", "preferences-responsive-toggle-label": "Responsive Number of Menu Categories", "enable-responsive-toggle-description": "This will automatically calculate number of categories based on screen size.", "preferences-hint-number-of-categories-mobile": "The maximum of categories on very small screens is limited to 4.", "enable-webshop-qr-in-receipt": "Webshop QR in receipt", "print-qr-of-webshop-in-final-receipt": "Enable printing of a QR code in the final receipt, that sends users to your webshop", "payment-channels-lieferando-bar": "Lieferando Bar", "setup": "Setup", "venue-id": "Venue id", "api-key": "API Key", "wolt-setup": "Wolt setup", "setup-wolt-integration-using-venue-id-and-api-key": "Setup Wolt integration with venue id, api key and authentication for menu sync.", "integration-info": "Integration info", "payment-channels-lieferando_cash": "Lieferando Bar", "report-by": "Report", "quantity-by-hour": "Quantity by hour", "revenue-by-hour": "Revenue by hour", "net-amount": "Net amount", "gross-amount": "Gross amount", "filter-for": "Filter for", "all-items": "All items", "publish-menu-confirm-title": "Publish Menu to Wolt", "publish-menu-to-wolt-confirm-description": "You are pushing a menu update to <PERSON><PERSON>. The content that will be synchronized to <PERSON><PERSON> is the menu snapshot you have created at the left. Please ensure that all the menu information is properly set, particularly the allergies and additives. Please be aware: if you push a menu without proper allergies and additive settings, you might be banned by <PERSON><PERSON> from using the integration.", "are-you-sure-description-to-publish-menu-to-wolt": "Are you sure you want to proceed ?", "product-update": "Product Update", "product-update-release-date": "Release Date:", "product-update-changelog": "Changelog", "product-update-go-to-main-page-btn-label": "For more updates go to product update", "product-update-entry": "Entry", "product-update-delete-entry": "Delete Entry", "product-update-are-you-sure-confirm": "Are you sure you want to delete this entry ?", "product-update-create-new-entry": "Create New Entry", "product-update-new-entry-btn-label": "New Entry", "product-update-delete-entry-btn-label": "Delete Entry", "product-update-publish-entry-btn-label": "Publish Entry", "menu-settings": "<PERSON><PERSON>", "control-menu-settings": "Control settings regarding your menu.", "go-to-ordering-to-add-items": "Start ordering to add items", "note": "Note", "po-number": "PO number", "continue-ordering": "Continue ordering", "update-info": "Update info", "no-items": "No items", "this-category-has-no-supplier-items": "This category has no supplier SKUs", "request-purchase": "Request purchase", "confirm-purchase": "Confirm purchase", "complete-purchase": "Complete purchase", "back-to-cart": "Back to cart", "purchase-completed": "Completed", "payment-channels-allo_pay_link": "allO Pay Link", "payment-channels-allo_pay_online": "allO Pay Online", "stripe": "Stripe", "emerchant-pay": "eMerchant Pay", "stripe-not-setup": "Stripe not setup", "click-the-button-below-to-setup-stripe-as-your-payment-gateway": "Click below to setup Stripe as your payment provider", "setup-stripe-account": "Setup my Stripe account", "stripe-setup-not-complete": "Stripe setup not complete", "some-details-are-still-missing-from-your-stripe-account": "Some details are still missing from your Stripe account setup", "update-account-setup": "Update account setup", "accepting-payments-with-stripe": "Accepting payments with Stripe", "your-stripe-setup-is-completed-and-active": "Your Stripe setup is complete and active", "view-dashboard": "View dashboard", "quick-checkout": "Quick checkout", "allo-is-cancelling-your-payment-link": "allO is cancelling payment link", "scan-allo-qr-code-for-payment": "Scan the QR code to use our online payment", "cancel-session": "Cancel session", "please-only-cancel-session-if-no-customer-is-processing-payment": "Please do not cancel session if customer is processing payment", "device": "<PERSON><PERSON>", "add-filter": "Add filter", "see-all": "See all", "pairing-code": "Pairing code", "side-screens": "Side screens", "no-side-screens": "No side screens", "click-the-button-below-to-add-a-side-screen": "Click the button below to add a side screen", "create-side-screen": "Create side screen", "register-a-new-side-screen-for-your-terminal": "Register a new side screen for your terminal", "register-a-new-payment-terminal": "Register a new payment terminal", "sort-by-order-number-smallest-first": "Smallest order number", "sort-by-order-number-biggest-first": "Biggest order number", "sort-by-earliest-creation-time": "Earliest created", "sort-by-latest-creation-time": "Latest created", "sort-by-earliest-finishing-time": "Earliest finishing", "sort-by-latest-finishing-time": "Latest finishing", "sorted-by": "Sorted by", "webshop-settings-set-the-delivery-fee": "Delivery fee", "webshop-settings-set-the-minimum-delivery-fee": "Order value", "webshop-settings-select-delivery-area-label": "Delivery Area", "zip-code-update-modal-title": "Add a zip code", "webshop-settings-min-order-value-label": "Min Order Value", "zipcode-fields-update-modal-title": "Customize delivery fee and min order value", "short-title-zipcode-fields-update-modal-title": "delivery fee/ min order value", "menu-editor-warning-message-wolt": "You are integrated with Wolt. Your menu change will NOT be updated to Wolt automatically. Please contact support for more details.", "menu-editor-warning-message-lieferando": "You are integrated with Lieferando. Your menu change will NOT be updated to Lieferando automatically. Please contact support for more details.", "menu-editor-warning-message-uberEats": "You are integrated with UberEats. Once you confirm, your menu change will be pushed to UberEats in real time.", "menu-editor-warning": "Attention", "default-prep-time-header": "Default Preparation Time (minutes)", "set-default-prep-time-description": "Set the default preparation time for your orders. You can always customize the preparation time on order creation. This is merely the default value displayed.", "email-or-mobile": "Email or Mobile", "accountant-role-name": "Accountant", "waiter-role-name": "Waiter", "manager-role-name": "Manager", "to-pay": "To pay", "filter-tables": "Filter tables", "filter-accounts": "Filter accounts", "filter-order-types": "Filter order types", "tables-filtered": "Tables filtered", "accounts-filtered": "Accounts filtered", "order-types-filtered": "Order types filtered", "sort-by-sold-at-smallest-first": "Earliest selling", "sort-by-sold-at-biggest-first": "Oldest selling", "latest-cash-in": "Latest cash in", "ali-pay": "AliPay", "enable-payments-through-alipay": "Enable payments through AliPay", "wechat-pay": "WeChat Pay", "enable-payments-through-wechat": "Enable payments through WeChat", "account-preferences-customize-general-appearance-title": "Customize general appearance", "account-preferences-manage-font-size": "Manage the font size of written text within your allO Account", "font-size-label": "Font Size", "new-order-sound": "New order sound", "enable-sound-notification-for-any-incoming-takeaway-order": "Enable sound notifications for any takeaway order", "unconfirmed": "Pending", "removed": "Removed", "cooking": "Cooking", "serving": "Serving", "cancelled": "Cancelled", "see-latest-table-receipts": "See latest table receipts", "lieferando-key": "Lieferando <PERSON>", "setup-lieferando-integration-by-using-id": "Setup Lieferando integration by using the id", "set-lieferando-id": "<PERSON> id", "printer-toggle-cash-drawer": "Cash drawer", "printer-toggle-description-cash-drawer": "Connect this printer with a cash drawer", "sound-notification-polling-accounts": "Sound Notification by Accounts", "set-sound-notification-accounts-description": "Leave this field empty, if you want every account connected to your restaurant to get a sound notification for incoming takeaway orders. Alternatively choose the accounts, that you want to receive the sound notification.", "allo-card": "allO Card", "add-the-code-manually": "Add the code manually", "open-camera-to-scan-code": "Open camera to scan code", "generate-allo-card": "Generate allO card", "allo-guest-checkout": "allO Guest Checkout", "stripe-tip-toggle": "Tip", "stripe-tip-toggle-description": "Enable the ability to receive tips through Stripe", "stripe-settings": "<PERSON>e Settings", "stripe-pricing-schema": "Stripe Pricing Schema", "stripe-settings-not-available-till-stripe-is-setup": "No settings available till Stripe set up is completed", "control-stripe-settings": "Control settings for Stripe", "stripe-capability-section-header": "Capabilities", "stripe-capability-section": "Control your Stripe capabilities", "gift-card-custom": "Custom", "allo-shop-product-add-to-cart": "Add to cart", "allo-shop-products-view-title": "Web Store", "allo-shop-products-view-table-column-payment-status-NOT_STARTED": "Not Started", "allo-shop-products-view-table-column-payment-status-REQUESTED": "Requested", "allo-shop-products-view-table-column-payment-status-FAILED": "Failed", "allo-shop-products-view-table-column-payment-status-CANCELLED": "Cancelled", "allo-shop-products-view-table-column-payment-status-SUCCEEDED": "Succeeded", "allo-shop-products-view-table-column-payment-status-COMPLETED": "Succeeded", "allo-shop-products-view-table-column-payment-status-ON_HOLD": "Processing", "allo-shop-products-view-table-column-shipping-status-IN_PREPARATION": "In Preparation", "allo-shop-products-view-table-column-shipping-status-IN_TRANSIT": "In Transit", "allo-shop-products-view-table-column-shipping-status-OUT_FOR_DELIVERY": "Out for Delivery", "allo-shop-products-view-table-column-shipping-status-DELIVERED": "Delivered", "allo-shop-products-view-table-column-shipping-status-undefined": "", "allo-shop-shopping-cart-title": "Orders Detail", "allo-shop-shopping-cart-total-label": "Total", "allo-shop-shopping-cart-payment-button-label": "Purchase", "allo-shop-order-history-title": "Order History", "allo-shop-order-history-table-name": "Ordered By", "allo-shop-order-history-table-payment-status": "Payment Status", "allo-shop-order-history-table-shipping-status": "Shipping Status", "allo-shop-order-history-table-total": "Total", "allo-shop-proceed-to-checkout": "Proceed to Checkout", "allo-shop-confirm-title": "Accept Order", "allo-shop-confirm-description": "You will be charged automatically {{amount}}€ plus VAT upon confirming.", "allo-shop-confirm-order": "Pay {{amount}}€ plus VAT", "use-allo-gift-card-to-offer-gift-cards-through-your-webshop": "Use allO Gift card to offer gift cards through your webshop", "allo-gift-card-webshop-link": "allO Gift Card webshop link", "gift-cards-webshop": "Gift card webshop", "enable-selling-gift-cars-through-online-webshop": "Enable selling gift cards through your webshop", "gift-cards-webshop-settings": "Gift card webshop settings", "control-settings-for-your-gift-card-webshop": "Control settings for your gift card webshop", "experiencing-network-issues": "Network unstable", "network-notifications": "Network issues notifications", "enable-notifications-on-network-issues": "Enabled notifications when network is unstable", "empty-app-screen-header": "No App Preview", "click-the-button-below-to-view-app-settings": "Click the button below to view the settings of this app", "payment-receipt": "Payment receipt", "print-receipt-for-business-purposes": "Print receipt including fields for business purposes", "print": "Print", "cancel": "Cancel", "pickup-discount-field-label": "Pickup Discount", "promotion-text-field-label": "Promotion Text", "set-promotion-text": "Set a promotion text, that will be displayed with your pickup discount on your webshop.", "pickup-discount-toggle": "Discount", "enable-discount-on-pickup-orders": "Enable discount on webshop pickup orders", "payouts": "Payouts", "no-payouts": "No payouts", "we-could-not-find-any-payouts": "We could not find any past payouts yet", "create-payout": "Create payout", "enter-amount": "Enter amount", "payout-amount": "Payout amount", "instant-payments-charge-percentage": "⚠️ Instant payouts are not reversible and are charged a 3% processing fee plus VAT", "can-manually-process-allo-pay-checkout-permission": "Can manually process allO Pay checkout", "pay": "Pay", "send": "Send", "order-btn": "Order", "takeaway-settings": "Takeaway Settings", "control-takeaway-settings": "Customize the order handling of unseen orders.", "configuration-editor-auto-reject-accept-time-field-label": "Auto reject or accept time", "set-time-for-auto-accept-or-reject-for-takeaway-order": "Please set the time in seconds that you want us to wait before your action takes effect. Please note: This field needs to be filled in for this feature to work.", "unseen-webshop-order-handling-label": "Auto-accept or Auto-reject for unseen Orders", "time-in-s-till-action": "Time (s) till action", "customize-your-dine-in-view": "Customize your Dine In View", "fine-tune-the-dine-in-view-to-work-efficiently": "Fine tune the appearance of the Dine In View to best fit your work", "select-dine-in-layout": "Select Default Dine In View", "dine-in-view-preference-hint": "The Dine In Layout can still be changed directly under Dine In. This preference only affects the default settings for this view.", "print-terminal-payment-receipt": "Print payment receipt", "confirmed-by-name": "Confirmed by {{name}}", "common-instant-available": "Instantly Available", "payouts-table-column-triggered-by": "Triggered By", "payouts-table-column-triggered-at": "Triggered At", "payouts-table-column-method": "Method", "payouts-table-status-PENDING": "Pending", "payouts-table-status-COMPLETED": "Completed", "payouts-table-status-FAILED": "Failed", "payouts-table-method-INSTANT": "Instant", "payouts-table-method-STANDARD": "Standard", "assigned-table": "Assigned table", "no-table": "No table", "pager": "Pager", "enable-pager": "Enable pager", "assign-pager-during-checkout": "Assign pager during checkout", "allow-guests-to-assign-pager-to-order": "Allow guests to assign pager to their order", "table-selection": "Table selection", "select-table-during-checkout": "Select table during checkout", "can-use-allo-shop-permission": "Can use allO Shop", "total": "Total", "shipping-status": "Shipping status", "emails-settings": "<PERSON><PERSON>s", "takeaway-email-settings-header": "Takeaway Emails", "update-takeaway-email-settings": "allO takeaway emails cannot currently be deactivated. This section only provides an overview of all takeaway related emails, allO sends on your restaurant's behalf.", "reservation-email-settings-header": "Reservation Emails", "update-reservation-email-settings": "allO reservation emails cannot currently be deactivated. This section only provides an overview of all reservation related emails, allO sends on your restaurant's behalf.", "send-preview-email": "Send Test Email", "send-preview-email-hint": "Fill in email address to which test email should be send and press send.", "common-send": "Send", "toggle-header-email-takeaway-order-notification": "Takeaway Order Update", "toggle-email-takeaway-order-notification": "Customers will receive this email in case of an update on their takeaway order such as a delay. This email also contains an estimated pickup or delivery time as well as your restaurants contact information.", "toggle-header-email-takeaway-order-summary": "Takeaway Order Summary", "toggle-email-takeaway-order-summary": "Customers will receive this email after making a takeaway order at your restaurant. This email contains an order summary, their order number and an order tracking link.", "toggle-header-email-takeaway-order-receipt": "Takeaway Order Receipt ", "toggle-email-takeaway-order-receipt": "Customers will receive this email after their takeaway order at your restaurant has been completed. This email contains their order receipt.", "toggle-header-email-reservation-creation": "Reservation Creation", "toggle-email-reservation-creation": "Customers will receive this email after booking a reservation at your restaurant. This email contains your restaurant contact information, the reservation's details as well as a link which lets the customer cancel the reservation if desired.", "toggle-header-email-reservation-cancellation": "Reservation Cancellation", "toggle-email-reservation-cancellation": "Customers will receive this email in case of a reservation cancellation through your restaurant. This email contains details of the cancelled reservation as well as a link which lets the customer book another reservation if desired.", "gift-card-email-settings-header": "allO Gift Card Emails", "update-gift-card-email-settings": "allO gift card emails cannot currently be deactivated. This section only provides an overview of all allO gift cards related emails, allO sends on your restaurant's behalf.", "reporting-email-settings-header": "Reporting Emails", "update-reporting-email-settings": "Reporting emails cannot currently be deactivated. This section only provides an overview of all reporting related emails, allO sends on your restaurant's behalf.", "purchase-email-settings-header": "Purchase Emails", "update-purchase-email-settings": "Purchase emails cannot currently be deactivated. This section only provides an overview of all purchase related emails, allO sends on your restaurant's behalf.", "digital-receipt-email-settings-header": "Digital Receipt Emails", "update-digital-receipt-email-settings": "Digital Receipt emails cannot currently be deactivated. This section only provides an overview of all digital receipt related emails, allO sends on your restaurant's behalf.", "registration-email-settings-header": "Registration Emails", "update-registration-email-settings": "Registration emails cannot currently be deactivated. This section only provides an overview of all registration related emails, allO sends on your restaurant's behalf.", "allo-account-creation": "allO Account Creation", "allo-account-creation-email-description": "Confirmation email after successful allO account creation.", "allo-restaurant-creation": "allO Restaurant Creation", "allo-restaurant-creation-email-description": "Confirmation email after successful allO restaurant account creation. This email also includes a link to the created restaurant account.", "user-invitation-email": "User Invitation", "user-invitation-email-description": "Invitation email, that users receive when you invite them to your restaurant. (This can be done under Team Management.) This email also includes a link to join your restaurant.", "account-verification": "Account Verification", "account-verification-email-description": "Verification email with code to complete allO account registration.", "billing-and-terms-completion-email": "Billing and Terms Completion", "billing-and-terms-completion-email-description": "Confirmation email after completing allO restaurant set up. This email also includes your subscription details as well as allO's terms and conditions.", "monthly-report-email-description": "This email contains your monthly report as a PDF attachment and can be triggered in the reporting section under download center.", "daily-report-email-description": "This email contains your daily report as a PDF attachment and can be triggered in the reporting section under download center.", "digital-receipt-generation": "Digital Receipt Generation", "digital-receipt-generation-email-description": " This email will be send to the provided email address, when you choose the receipt option PDF Receipt on checkout. It contains a link that leads the customer to a page where he needs to provide personal information for us to generate the receipt that will be send in a additional email. Please note, that the provided link is only valid for 24h.", "digital-receipt-email": "Digital Receipt", "digital-receipt-email-description": "This email contains the digital receipt as a PDF attachment and is triggered as soon as the customer has filled in all required personal information from the previous email: Digital Receipt Generation.", "allO-gift-card": "allO Gift Card", "allO-gift-card-email-description": "Customers receive this email after buying an allO gift card. This email contains the current credit of the gift card, a QR-Code, the gift card code and your restaurant information.", "purchase-request": "Purchase Request", "purchase-request-email-description": "Suppliers receive this email, when you send a purchase request. This email contains the purchase order summary as well as it's order number. Should you need to update this purchase, an update email under the same order number will be send.", "payment-channels-edenred": "<PERSON><PERSON>", "delivery-information": "Delivery Information", "takeaway-email-copy-toggle": "No Copies of Takeaway Emails to Restaurant", "takeaway-email-copy-toggle-description": "Enable this toggle if you do NOT want us to send a copy of every email that is send to a customer in regards to takeaway orders to your restaurant's email address as well.", "stripe-payout-section-header": "Payout Settings", "stripe-payout-section": "Control your payouts", "payouts-on-daily-closing-toggle": "Payout on Daily Report Closing", "payouts-on-daily-closing-toggle-description": "Enable Payouts on Daily Report Closing.", "datev-export-app-settings": "DATEV Exporter Settings", "control-your-datev-export-app-settings": "Control your DATEV Settings", "floor-management-rotate-table-btn-label": "Rotate", "floor-management-table-orientation": "Orientation", "control-takeaway-settings-configuration": "Control settings for Takeaway orders.", "configuration-editor-flash-takeaway-toggle": "Quick service takeaway", "configuration-editor-flash-takeaway-toggle-description": "Enable this option, if you would like the takeaway order flow to be quicker. The terminal will start with the second step of the general takeaway order flow instead of the first.", "express-terminal": "Express terminal", "update-express-ordering-and-checkout": "Update the way your Express terminal operates", "create-quick-takeaway": "Create quick takeaway", "fiskalization-app-active": "Your fiskalization is active.", "fiskalization-app-not-active": "There is no active fiskalization for this restaurant.", "fiskalization-creation-date": "Creation Date: ", "common-inactive": "inactive", "groups": "Groups", "value-cant-be-zero-auto-accept-reject": "Please enter a value that's greater than 0.", "action-unseen-order": "Action", "reorder": "Reorder", "menu-editor-items-view-items": "Items", "menu-editor-items-view-bulk-edit": "Bulk Editor", "edit-number-items": "Edit {{number}} items", "menu-group": "Menu Group", "course": "Course", "unassigned": "Unassigned", "table-rotation-entire-order-confirm-description-line1": "This action will move the entire order from table {{table}} to the selected table.", "table-rotation-partial-order-confirm-description-line1": "This action will move only the selected items from this order from table {{table}} to the selected table.", "table-rotation-single-item-confirm-description-line1": "This action will move only the selected item from this order from table {{table}} to the selected table.", "move-entire-order": "Entire order", "move-partial-order": "Partial order", "please-select-table-description": "Please select the table you want to move your order or selected items to.", "number-of-items-to-move": "Number of {{name}} to move:", "split-items": "Split items", "split-equally": "Equally", "split-amounts": "Amount", "paying": "Paying", "menu-editor-options-bulk-option-name": "Option Name", "menu-editor-options-bulk-item-name": "Item Name", "menu-editor-extras-bulk-extra-name": "Extra Name", "menu-editor-extras-bulk-item-name": "Item Name", "express-mode-modal": "Express Mode", "please-select-express-mode-to-proceed": "Select the mode for this express order.", "in-house-express-mode": "In House", "in-house-express-mode-description": "In house taxes will be applied and the kitchen will not be instructed to package the food.", "to-go-express-mode": "To Go", "to-go-express-mode-description": "To Go taxes will be applied and the kitchen will be instructed to package the food.", "to-go": "To Go", "app-side-bar-my-preferences": "My Preferences", "report-history-report-start-date-and-start-time": "Starting Date/Time", "report-history-report-end-date-and-end-time": "Closing Date/Time", "common-at": "at", "pay-by-items": "Pay items", "select-all": "Select all", "selected": "Selected", "there-are-no-terminals-available": "There are no terminals available", "add-amount": "Add amount", "show-group-by-in-terminal": "Show Group By operation in terminal", "chose-default-group-by-option": "Select default Group By option", "dynamic-codes": "Dynamic Codes", "no-qr-codes": "No Qr-Codes connected", "go-to-floor-management-to-connect-qr-codes": "Click the button below to go to Floor Management and connect Qr-Codes to your tables.", "qr-code-connection": "Connection", "qr-code-url": "Url", "connected-qr-code": "Connected Qr-Code", "common-table": "Table", "no-connection": "No Connection", "left": "Left", "right": "Right", "menu-orientation": "Menu orientation", "customer-availability": "Customer availability", "unavailable": "Unavailable", "fpx-settings": "FPX settings", "setup-fpx-authentication-to-send-orders": "Setup FPX authentication to send data for reporting", "set-bearer-id": "Set Bearer ID", "explain-slots-for-guests": "Explain customer availability", "allow-to-view-explanation-for-guest-reservation-slots": "Show explanation for guest reservation slots", "how-many-parts": "How many parts", "select-parts-to-pay": "Select parts to pay", "part": "Part", "delete-section-header-category": "Delete this Category", "delete-section-header-item": "Delete this Item", "delete-section-header-option": "Delete this Option", "delete-section-header-extra": "Delete this Extra", "delete-section-header-group": "Delete this Group", "menu-editor-this-action-deletes-category": "⚠️This action deletes the category {{ title }} and all the items that are included in it. This action can not be undone.", "menu-editor-this-action-deletes-item": "⚠️This action deletes the item {{ title }}. This action can not be undone.", "menu-editor-this-action-deletes-option": "⚠️This action deletes the option {{ title }} and all the items that are included in it. This action can not be undone.", "menu-editor-this-action-deletes-extra": "⚠️This action deletes the extra {{ title }} and all the items that are included in it. This action can not be undone.", "menu-editor-this-action-deletes-group": "⚠️This action deletes this group. This action can not be undone.", "restricted-field-menu-editor-explanation": "Restricted items are are not shown to your customers.", "disabled-field-menu-editor-explanation": "Sold out items and categories will be marked as such.", "hidden-field-menu-editor-explanation": "Hidden items or categories will not show in your menu and ordering flows.", "ongoing-field-menu-editor-explanation": "Ongoing items can be ordered as part of a buffet. Customers can reorder this item several times free of charge.", "favorite-field-menu-editor-explanation": "Favourite items are shown in the popular items section in your ordering flow.", "recommended-field-menu-editor-explanation": "Recommended items will be shown to your customers as such.", "collapsed-field-menu-editor-explanation": "Additions of this menu item will be collapsed during ordering.", "scheduled-field-menu-editor-explanation": "Scheduled categories are only displayed to the customer or in the menu and order process during the specified time window.", "courses": "Courses", "enable-course-ordering": "Enable courses in ordering and order group by", "add-course": "Add course", "setup-courses": "Setup courses", "no-courses": "No courses", "create-your-default-courses": "Create your default courses", "add-more-default-courses": "Add more default courses", "course-name": "Course name", "set-a-name": "Set a name", "order-course-name": "Order {{courseName}}", "choose-default-receipt-columns-orders": "Standard table columns for receipts grouped by orders", "choose-default-receipt-columns-payments": "Standard table columns for receipts grouped by payments", "default-receipt-columns-description": "Choose up to 6 Columns, which will be shown in your Receipt table. These are only default values and can be adjusted at any time.", "receipt-settings": "Receipt Settings", "control-receipt-settings": "Control what columns you would like to see in your receipt tables.", "select-receipt-columns": "Select Columns", "recover-this-reservation": "Recover this reservation", "cancelled-by-customer": "Cancelled by Customer", "cancelled-by-account": "Cancelled by {{ name }}", "see-inactive-reservation": "Show inactive reservation", "guest-approval-via-pin-toggle-header": "Guest Approval via PIN", "allow-guest-approval-via-pin-toggle-description": "This allows your customers to be automatically approved through entering a PIN. Instead of your waiters having to manually approve customers that use the scan to order feature.", "update-your-billing-information": "Update your billing information", "close-details": "Close details", "edit-details": "Edit details", "can-view-reservations-permission": "Can view reservations", "payment-option-pickup": "Payment Options Pickup", "payment-option-delivery": "Payment Options Delivery", "payments": "Payments", "reader-settings": "Reader settings", "with-payment-method": "with {{method}}", "pay-with-payment-method": "Pay with {{method}}", "with-tip": "With tip", "organization-switcher-showing-number-of": "{{ number }}/{{ total }}", "load-more": "Load more", "no-match-found": "No match found", "accounts-loaded-label": "{{number}} accounts", "side-screens-loaded-label": "{{number}} side screens", "showing-order": "Showing order", "default-monitor": "Default side screen", "enable-reminder-bon-printing": "Disable new order notification receipt", "print-reminder-bon": "Disable the printing of a new order receipts. New order receipts are printed when you get a new takeaway order. This setting will effect all webshop orders, including orders from delivery partners you're integrated with.", "cancellation-receipt-toggle-header": "Disable Cancellation Receipt", "enable-cancellation-receipt-toggle": "This will disable the printing of a receipt for a cancelled order. This receipt is usually printed when all items of an order get deleted before they were send to the kitchen.", "also-ordered-as": "Also ordered as", "select-dine-in-tile-size": "Select tile size for Dine in", "compact": "Compact", "with-properties": "With properties", "dine-in-view-mobile-tile-size-hint": "Select different sizes for the tile view in mobile mode, compact or able to show properties", "customers-loaded-label": "{{number}} customers", "top-spacing": "Top Spacing (Receipt Bar)", "hint-top-spacing": "Here you can add some empty spacing on top of receipts, if needed.", "no-lines": "no space", "one-line": "1 line", "two-lines": "2 lines", "three-lines": "3 lines", "ordering-devices": "Ordering Devices", "create-ordering-device": "Create ordering device", "register-a-new-ordering-device-for-table-ordering": "Register a new ordering device for table ordering", "ordering-devices-loaded-label": "{{number}} devices", "no-devices": "No devices", "click-the-button-below-to-add-a-device": "Click the button below to add a device", "device-settings": "Device settings", "charges": "Standalone charges", "reader-header": "Reader Id", "no-charges": "No charges", "select-reader": "Select Reader", "enable-terminal-payment-receipt": "Enable allO Pay Terminal receipt", "print-payment-receipt-for-every-transaction-with-allo-pay": "Print payment receipt for every transaction done via the readers with allO Pay", "in-this-table": "In this table", "invalid-email": "invalid email address", "item-ordered-in-high-quantity": "Mostly ordered in high quantity", "item-ordered-in-high-quantity-explanation": "This item is most commonly ordered in high quantities, usually sold by measurement unit", "order-quantity": "Enter quantity", "order-item": "Order item", "add-item": "Add item", "update-item": "Update item", "default-party-size": "Default party size", "max-quantity-per-person": "Limit of items per guest", "max-quantity-per-person-per-ordering": "Limit of items per guest per ordering", "min-quantity-per-person-that-triggers-blocking": "Limit to block guests ordering", "blocking-time-when-blocking-triggers": "Minutes to block guests ordering", "people-dinning-at-this-table": "People dinning at this table", "set-party": "Set party", "people": "People", "no-course": "No course", "tablet-ordering-modes": "Ordering Modes", "no-tablet-order-modes": "No Ordering Modes", "add-order-modus": "Add Ordering Mode", "edit-order-modus": "Edit Ordering Mode", "are-you-sure-description-to-delete-order-mode": "Are you sure you want to delete this ordering mode?", "delete-order-mode-title": "Delete Ordering Mode", "ordering-mode-position": "Position", "ordering-mode-mandatory-switch": "Mandatory Step", "mandatory-switch-description": "By switching on this toggle you make this step of the ordering flow mandatory. The user can only continue when this step is completed.", "add-order-modus-step": "Add Step", "edit-order-modus-step": "Edit Step", "number-of-participants": "Request Number of Participants", "menu-item-selection": "<PERSON><PERSON>", "min-qtd-items-ordering-mode": "<PERSON>. Item Quantity", "max-qtd-items-ordering-mode": "<PERSON><PERSON>em Quantity", "item-quantity-hint-ordering-modes": "Set a minimum and maximum limit for items ordered by customers. We will not show the maximum limit to your customer but will block a new order once the limit has been reached.", "ordering-steps-header": "Ordering steps", "ordering-steps-description": "Simply add the steps you would like us to include in this order mode. To save your changes, make sure to close this window by clicking 'Done'.", "add-new-tag": "Create Tag", "common-tags-label": "Tag Label", "blocking-time-when-blocking-triggers-explanation": "Set the minutes, that Guest should be blocked for from ordering, when the above set limit was reached.", "menu-editor-ordering-modes-tags": "Ordering Modes", "menu-editor-ordering-modes-tags-description": "Link specific items or categories to tags from the ordering mode you want this item or category to appear in. You can create these tags in the Menu Editor under 'Ordering Modes'.", "this-step-has-no-tags": "This step has no tags", "show-allergens": "Show allergens", "default-terminal-setting": "Set Terminal to Default Choice", "default-field-terminal-explanation": "The default terminal choice will be auto selected on payments.", "open-invoices": "Open Invoices", "pay-by-invoice": "Pay by Invoice", "no-invoices": "No Invoices", "click-the-button-below-to-add-an-invoice": "Click below to add an Invoice", "issued-on": "Issued on", "paid-on": "Paid on", "download-in-progress": "Download in progress", "your-download-will-start-shortly-please-do-not-close-window": "Your download will start shortly, please do not close this window until download is completed.", "download-pdf": "Download PDF", "open-invoices-loaded-label": "{{number}} Invoices", "invoice-amount": "Invoice amount", "payment-channels-kauf_auf_rechnung": "Pay by Invoice", "fill-invoice-data": "Fill invoice data", "edit-invoice-data": "Update invoice data", "invoice-status-open": "Open (not paid)", "common-failed": "Failed", "status-filtered": "Status filtered", "filter-status": "Filter status", "filter-by-status": "Filter by status", "copy-payment-link-open-invoice": "Copy payment link", "you-can-create-invoice-payment-from-terminal": "You can create a payment by invoice from the terminal", "can-not-split-payment-when-paying-by-receipt": "Receipt can not be split when paying by invoice", "data-missing-for-paying-by-receipt": "Invoice data is missing for payment by invoice", "this-popup-will-close-once-payment-is-completed": "This popup will close once payment is completed.", "mark-as-paid": "Mark as paid", "mark-invoice-as-paid-dialog-title": "Mark invoice as paid?", "this-can-not-be-undone-to-revert-you-will-need-to-create-a-new-invoice": "This action cannot be undone. To revert, you will need to create a new invoice.", "service-fee": "Service fee", "hide-zero-price-items": "Hide items with price 0€", "do-not-show-items-with-zero-price-in-checkout-and-receipts": "Do not show items with price 0€ in checkout and receipt.", "hint-scheduled-menu-category": "Please select the days and time you would like this category to appear on your menus.", "disable-notes-for-customers": "Disable notes for customers", "do-not-allow-customers-to-add-notes-for-the-kitchen": "Do not allow customers to add notes for the kitchen", "tablet-ordering-modes-tags": "Tags", "tablet-ordering-modes-actions": "Actions", "are-you-sure-description-to-delete-order-mode-tag": "Are you sure you want to delete this tag?", "no-tablet-order-mode-tags": "No Ordering Mode Tags", "no-tablet-actions": "No Actions", "menu-editor-this-action-deletes-step": "⚠️This action deletes the step '{{ title }}'. This action can not be undone.", "delete-section-header-step": "Delete this Step", "are-you-sure-description-to-delete-order-mode-step": "Are you sure you want to delete this step?", "delete-section-header-gift-card": "Delete this Gift Card", "this-action-deletes-gift-card": "⚠️This action deletes the gift card {{number}}. This action can not be undone.", "new-customer": "New Customer", "customers-added": "Customers added", "common-verified": "Verified", "need-help-invite-support-agent": "Need help? Quickly invite our support agents.", "paypal-settings": "PayPal Settings", "control-paypal-settings": "Control settings for PayPal", "setup-paypal-account": "Set up my PayPal account", "your-paypal-setup-is-completed-and-active": "Your PayPal setup is complete and active", "some-details-are-still-missing-from-your-paypal-account": "Some details are still missing from your PayPal account setup", "paypal-setup-not-complete": "PayPal setup not completed", "click-the-button-below-to-setup-paypal": "Click below to setup PayPal", "paypal-not-setup": "PayPal not setup", "accepting-payments-with-paypal": "Accepting payments with PayPal", "paypal-settings-not-available-till-paypal-is-setup": "No further settings available for PayPal at the moment.", "log-in-for-paypal": "Go to PayPal login", "common-permissions": "permissions", "full-permissions": "Full permissions", "delete-open-invoice-dialog-title": "Delete this invoice", "delete-open-invoice-dialog-description": "Are you sure you want to delete this invoice ?", "item-included-in-price": "Included in price", "item-included-in-price-explanation": "This item can only have price 0€ and will not appear during checkout", "setup-terms": "Setup terms and conditions", "no-terms": "No terms setup", "create-your-reservation-terms": "Create your reservation terms", "add-term": "Add term", "delete-cash-journal-setting": "Delete Cash Journal Setting", "this-action-deletes-cash-journal-setting": "⚠️This action deletes this setting. This action cannot be undone.", "value-can-not-be-0": "Value has to be between 1 and 9999999", "updating-failed": "Updating failed!", "delete-dynamic-code-title": "Delete code", "are-you-sure-description-to-delete-dynamic-code": "Are you sure you want to delete this code?", "delete-terminal-title": "Delete terminal", "are-you-sure-description-to-delete-terminal": "Are you sure you want to delete this terminal?", "delete-floor-title": "Delete Floor", "are-you-sure-description-to-delete-floor": "Are you sure you want to delete this floor?", "delete-ordering-device-title": "Delete ordering device", "are-you-sure-description-to-delete-ordering-device": "Are you sure you want to delete this ordering device?", "select-avatar": "Select avatar", "no-menu-items-found": "Sorry we couldn't find any items matching your search!", "printer-editor-actions-edit-printer": "Edit Printer", "update-inventory-settings": "Update inventory settings", "automatic-consumption": "Automatic consumption", "activate-inventory-to-get-automatic-menu-updates": "Activate inventory consumption to get menu updates during operation", "amount-unit-available": "{{amount}} {{unit}} available", "finalize-your-changes": "Finalize your changes", "update-inventory": "Update inventory", "review-changes": "Review changes", "no-inventory-changes": "No inventory changes", "go-back-to-dashboard-and-update-items": "Go back to dashboard and update items", "back-to-overview": "Back to overview", "consumption": "Consumption", "setup-the-ingredients-and-amounts-this-item-consumes": "Setup the ingredients and amounts this item consumes from inventory", "records": "Records", "expiration": "Expiration", "inventory-empty": "Inventory empty", "your-inventory-is-not-setup": "Your inventory is not setup", "added-amount-unit": "Added {{amount}} {{unit}}", "removed-amount-unit": "Removed {{amount}} {{unit}}", "update-ingredients": "Update ingredients", "create-ingredients-for-this-category-item": "Create ingredients for this category item", "add-ingredient": "Add ingredient", "menu-editor-actions-modal-content": "Content", "this-view-will-show-active-dine-in-orders": "This view will show active dine in orders", "no-records": "No records", "there-have-been-no-updates-to-your-inventory": "There have been no updates to your inventory", "out-of-stock": "Out of stock", "in-stock": "In stock", "low-stock": "Low stock", "no-show": "No show", "no-identifier": "No identifier", "low-and-out-of-stock": "Low and out of stock", "try-removing-filters-to-see-all-inventory-items": "Try removing filters to see all inventory items", "low-stock-threshold": "Low stock threshold", "no-items-ordered": "No items ordered", "no-order-number": "No order number", "generate": "Generate", "create": "Create", "let-allo-do-the-work-upload-pdf-to-fill-inventory-with-ai": "Let allO work for you. Upload an invoice and allO AI will generate your digital purchase and update your inventory.", "process-invoice": "Process invoice", "file-upload-error": "File could not be uploaded. {{description}}", "upload-code-file-too-large": "File is too large", "processing-invoice-failed": "Processing invoice failed", "processing-invoice": "Processing invoice", "invoice-processed": "Invoice processed", "draft-purchase": "Draft purchase", "processing-status": "Processing status", "added-manually": "Added manually", "purchase-number-from-supplier": "Purchase {{number}} from {{supplier}}", "no-article-number": "No article number", "no-unit": "No unit", "bottles": "bottles (bt)", "reservation-regular": "Regular", "reservation-no-show": "No show risk", "regenerate-report": "Regenerate report", "reset-kontos": "<PERSON><PERSON>", "finalize-purchase-item": "Finalize item", "you-are-about-to-finalize-purchase-item": "You are about to complete this item.", "action-move-to-complete": "Complete", "purchase-modal-invoice-quantity": "Quantity", "purchase-modal-invoice-sku": "SKU", "remove-purchase-item": "Remove item", "you-are-about-to-permanently-delete-a-purchase-item": "You are about to permanently delete this item.", "are-you-sure-you-want-to-proceed": "Are you sure you want to proceed?", "remove-filters": "Remove filters", "ingredient-amount": "Quantity", "article-number": "Article number", "upload-delivery-receipt-to-complete-inventory-purchase": "Upload a delivery receipt for the record to complete this inventory purchase.", "setup-inventory": "Setup inventory", "enable-if-you-are-uploading-historical-invoice-to-setup-your-inventory": "Use this historical purchase invoice to setup your inventory", "purchase-for-setup": "Purchase for setup", "purchase-email": "Purchase email", "create-purchases-from-attachments-to-your-inventory-email": "Create purchases from attachments to your purchase email", "synchronize-with-menu": "Synchronize with menu", "one-to-one-inventory-with-your-menu-items": "Have inventory setup synchronized one to one with your menu items", "menu-synchronization-creates-ingredients-and-consumptions-automatically-to-setup-inventory": "The menu synchronization creates ingredients and consumptions to setup your inventory", "global-configurations": "Global Configuration", "advanced-restaurant-configurations": "Advanced Restaurant Configurations", "advanced-configurations": "Advanced Configurations", "product-beta": "Product Beta", "beta-dine-in-v2": "Dine In V2", "discount-verification": "Discount verification", "set-a-verification-code-during-the-discount-process": "Set a verification code during discount process", "option-item-has-number-of-connected-items": "This option item is used in {{ number }} items of your menu.", "extra-item-has-number-of-connected-items": "This extra item is used in {{ number }} items of your menu.", "all-connected-items-will-be-affected-by-this-change-option-items": "This option item is used in {{ number }} items of your menu. Your changes will therefore affect all of these items as well.", "all-connected-items-will-be-affected-by-this-change-extra-items": "This extra item is used in {{ number }} items of your menu. Your changes will therefore affect all of these items as well.", "affected-items": "Affected Items", "disconnect-paypal-confirm-dialog-title": "Disconnect PayPal", "disconnect-paypal-confirm-dialog-description-line1": "Disconnecting your PayPal account will prevent you from offering PayPal as one of your paymentMethods on your checkout.", "disconnect-paypal-confirm-dialog-description-line2": "Are you sure you want to proceed?", "control-your-paypal-settings": "Control you PayPal settings", "paypal-unconfirmed-email-message": "Attention: Please confirm your email address on https://www.paypal.com/businessprofile/settings in order to receive payments! You currently cannot receive payments.", "paypal-payments-cant-be-received-message": "Attention: You currently cannot receive payments due to restriction on your PayPal account. Please reach out to PayPal Customer Support or connect to https://www.paypal.com for more information.", "paypal-copy-link-btn-label": "Open link", "paypal-unconfirmed-email": "Unconfirmed PayPal email", "paypal-can-not-receive-payments": "You can't receive payments", "common-article": "Article", "your-payment-methods": "Your Payment Methods", "default-payment-method": "Default Payment Method", "default-payment-method-switch-description": "Your default payment method will be used for your subscription.", "edit-payment-method": "Edit Payment Method", "add-payment-method": "Add Payment Method", "subscription-id": "Subscription Id", "no-subscription-id": "No Subscription Id", "common-subscription": "Subscription", "delete-section-header-payment-method": "Delete Payment Method", "this-is-your-default-payment-method-confirm-description": "We will select one of your remaining payment methods as default if you delete this payment method, which is your current default payment method.", "are-you-sure-description-to-delete-payment-method": "Are you sure you want to delete this payment method?", "this-payment-method-is-not-connected-to-any-subscription": "This payment method is not connected to any subscription.", "set-as-default": "Set as default", "common-default": "<PERSON><PERSON><PERSON>", "to-delete-this-payment-method-select-different-default-first": "To delete this payment method please select a new default first.", "kitchen-monitor-card-takeaway-title": "Takeaway", "kitchen-monitor-card-express-title": "Express", "kitchen-monitor-top-bar-expeditier-btn": "Expeditier", "kitchen-monitor-top-bar-open-tickets-btn": "Ongoing", "kitchen-monitor-top-bar-completed-btn": "Completed", "kitchen-monitor-top-bar-inventory-btn": "Inventory", "kitchen-monitor-top-bar-summary-btn": "Summary", "kitchen-monitor-empty-screen-description": "There are no tickets to be displayed", "kitchen-monitors": "Kitchen Monitors", "no-monitors": "No monitors", "click-the-button-below-to-add-a-monitor": "Click below to add a monitor", "bulk-delete-menu-items": "Delete multiple items", "are-you-sure-description-to-delete-multiple-menu-items": "You have selected {{numberOfItems}} items. This action will delete all of them and can not be undone.", "start-end-time": "Start - End", "order-history-receipts-loaded-label": "Receipts loaded", "kitchen-monitor": "Kitchen Monitor", "create-a-new-kitchen-monitor": "Create a new kitchen monitor", "payment-total": "Payment total", "no-printers-selected": "No printers selected", "number-printers-selected": "{{number}} printers selected", "no-codes-connected": "No codes connected", "fiskaly-app-settings": "<PERSON><PERSON><PERSON>", "control-settings-regarding-your-fiskaly": "Manage your settings regarding Fiskaly", "create-tse-config-btn-label": "Create TSE Configuration", "set-your-tse-config": "Set your TSE Configuration", "api-secret": "Api Secret", "tse-id": "TSE-Id", "set-menus": "<PERSON> Menus", "edit-set-menu-item": "Edit Set Menu Item", "add-set-menu-item": "Add Set Menu Item", "select-specific-printers": "Select specific printers", "select-printers": "Select printers", "send-this-menu-item-to-specific-selected-printers": "Send this item to specific printers", "print-by-menu-item": "Menu items printing", "enable-menu-item-printers": "Enable selecting printers for menu items", "menu-editor-set-menu-price-field-description": "Add a price to the whole set menu.", "printer-status-offline": "This printer is offline.", "printer-status-online": "This printer is online", "delete-section-header-set-menu": "Delete this Set Menu", "menu-editor-this-action-deletes-set-menu": "⚠️This action deletes this set menu. This action can not be undone.", "delete-section-header-set-menu-item": "Delete this Set Menu Item", "menu-editor-this-action-deletes-set-menu-item": "⚠️This action deletes this set menu item. This action can not be undone.", "are-you-sure-description-to-delete-set-menu-item": "Are you sure you want to delete this set menu item?", "no-status": "No status", "common-offline": "Offline", "common-online": "Online", "default-content-languages-title": "Default Language", "content-languages-title": "Content Languages", "content-language-hint": "Select the languages in which you want to translate your content.", "common-free": "Free", "common-taken": "Taken", "set-menu-item-courses-empty-item": "None", "no-set-menus": "No set menus", "takeaway-search-modal-modal-title": "Search takeaway orders", "takeaway-search-modal-no-search-title": "Search for takeaway orders", "takeaway-search-modal-no-search-description": "Search with name or order number with a given date range", "takeaway-search-modal-empty-title": "No orders", "takeaway-search-modal-empty-description": "Please check with your partner, no orders found with this search parameters.", "remove-order": "Remove order", "kitchen-monitor-card-continue-top-text": "Continued ↴", "kitchen-monitor-card-continue-bottom-text": "CONTINUED NEXT COLUMN ⇥", "payment-method-sepa-debit": "Sepa Debit", "payment-method-sofort": "Sofort", "iban-ending-in": "IBAN ends in {{ number}}", "enable-address-qr-in-receipt": "Address QR in receipt", "print-qr-of-google-maps-directions-in-receipt": "Print QR code in the receipt, that sends drivers to Google Maps for directions.", "paypal-redirect-to-link-btn-label": "Open account", "number-of-items-breakpoint-for-kitchen-monitor-field-lable": "Ticket Breakpoint", "set-number-of-items-breakpoint-for-kitchen-monitor": "Set the number of items after which you would like a table ticket to break into two adjacent columns.", "select-categories-shown-on-kitchen-monitor": "Choose the categories you want to view items from: beverages, dishes, or both. Your selection will determine which categories' items are displayed on the kitchen monitor.", "menu-language": "Menu Language", "select-which-language-to-use-for-menu-item-names": "Select which language to use for menu item names.", "kitchen-monitor-settings-summary": "Settings regarding Kitchen Monitor Summary", "kitchen-monitor-settings": "Settings regarding Kitchen Monitor", "kitchen-monitor-settings-summary-description": "Decide how you want to name the groups shown in the summary tab and choose which items with specific statuses should be displayed in each group.", "kitchen-monitor-summary-group-title": "Groupname {{ number }}", "select-status-shown-in-this-group-kitchen-monitor": "Select the statuses of the item you would like to be shown in this group.", "out": "Out", "ordering-flow": "Ordering flow", "takeaway-in-dine-in": "Takeaway in Dine in", "enable-takeaway-order-flow-during-dine-in": "Enable ordering takeaway items during dine order", "show-summary-toggle": "Auto-open Summary-tab", "show-summary-toggle-description": "Enable this setting if you want the Summary tab to open automatically when you open your kitchen monitor.", "add-group": "Add Group", "delete-groups-btn-label": "Delete group", "duplicate-section-header-item": "Duplicate this item", "delete-printer-header": "Delete this Printer", "printer-editor-this-action-deletes-printer": "⚠️This action deletes the printer {{ title }}. This action can not be undone.", "selected-tables-badge-for-reservation": "{{ number }} selected", "allow-table-merging-reservation": "Table merging for reservation", "this-will-allow-table-merging-for-reservations": "This setting will allow you to reserve multiple tables for one reservation.", "order-with-google-settings": "Order with Google Settings", "control-settings-for-order-with-google-integration": "Control your settings regarding the Order with Google app", "enable-order-with-google": "Order with Google", "allow-google-to-send-orders-to-your-allo-webshop": "Allow Google to send orders to your webshop. Customers will then be able to order from your webshop via Google.", "delete-inventory-description": "This action deletes your inventory. This action can not be undone.", "delete-inventory": "Delete inventory", "please-contact-support-you-are-not-authorized": "You are not authorized to perform this action. Please contact our support team.", "km-radius": "Radius (km)", "delivery-fee-mode": "Delivery Fee Mode", "delivery-fee-mode-hint": "Choose whether you would like to set your delivery fees based on different zip codes or by distance in kilometers (radius).", "add-more": "Add more", "floorplan-list-header-reservable": "Reservable", "generic-error-page-title": "Oh no, something went wrong", "generic-error-page-description": "Our team is on it", "generic-error-page-button-text": "Go back", "menu-editor-form-default-course-field-label": "Selected default course", "course-setup": "Course setup", "set-default-course-during-ordering": "Set default course during ordering", "special-days-for-blocking-reservations-header": "Reservation Restrictions", "set-special-days-for-blocking-reservations": "You can set specific days when customers won't be able to make reservations. Additionally, you can choose to prevent your waitstaff from entering any new reservations on these days as well.", "add-dates": "Add dates", "customer-and-staff-blocked": "Customer and Staff blocked", "only-customer-blocked": "Only Customers blocked", "also-blocked-staff-checkbox": "Also block staff", "please-remove-duplicate-date-to-proceed": "A date has been added multiple times. Please ensure each date is added only once.", "this-date-is-blocked-check-settings-for-list-of-blocked-days": "This day has been blocked for reservations. Please check your reservation settings for more info.", "no-dates-set-up-yet": "No dates set up yet", "beer-tap-app-settings": "DIRMEIER smartschank Settings", "control-settings-regarding-your-beer-tap": "You have full control over your DIRMEIER smartschank integration, allowing you to enable or disable it at any time. Please note that the integration requires a valid IP address to function correctly. We recommend using the test button to verify the provided IP address for optimal performance.", "beer-tap-items-header": "Items Selection", "set-up-items-for-beer-tap": "Set up which drinks form your menu you would like to sell using the DIRMEIER smartschank.", "beer-tap-id": "Smartschank id", "item-set-up-in-smart-schank": "set up for tap", "item-not-set-up-in-smart-schank": "Not set up", "smart-schank": "DIRMEIER smartschank", "item-selection-beer-tap": "Item Selection", "account-selection-beer-tap": "Account Selection", "set-up-accounts-for-beer-tap": "Set up which accounts you would like to be able to order drinks using the DIRMEIER smartschank.", "no-smart-schank-id": "No Smartschank id", "smart-schank-toggle-header": "DIRMEIER smartschank", "enable-smart-schank-description": "Enable this setting to activate your DIRMEIER smartschank integration.", "common-test": "test", "test-your-integration-to-ensure-optimal-performance": "Test your integration to ensure optimal performance", "beer-tap-integration-test-successful": "Test was successful. You can now use your DIRMEIER smartschank integration.", "beer-tap-integration-test-unsuccessful-please-check-ip-again": "Test was not successful. Please check your ip address.", "remove-smart-schank-integration-for-this-item-or-account": "Remove Smartschank Id", "no-matching-table-for-this-search": "No tables found that match your search query", "overview-table-month": "Month", "overview-table-volume": "Volume", "overview-table-transactions": "Transactions", "overview-table-fees": "Fees", "no-monthly-overview": "No monthly overview", "we-could-not-find-any-monthly-overview": "We couldn't find any monthly overview", "stripe-overview-title": "Monthly overview", "stripe-overview": "Monthly Overview", "webshop-opening-hours": "Webshop Opening Hours", "enable-to-set-different-opening-hours-for-webshop": "Enable this option to set different opening hours that only apply to your webshop.", "redirect-to-webshop-settings": "redirect to webshop settings", "disable-order-confirmation": "Disable order confirmation", "send-to-kitchen-directly-without-confirmation": "Send items to kitchen without confirmation step", "billing-history": "Billing History", "configuration-editor-hide-orders-without-payment-in-receipts": "Enable this option if you would like to hide receipts of cancelled orders without payment from your receipt table.", "hiding-cancelled-orders-without-payment-in-receipts": "Hiding Cancelled Orders without Payment", "hide-zero-payments-of-cancelled-orders": "Hide Cancelled Orders without Payments", "hiding-zero-payments-of-cancelled-orders": "Hiding Cancelled Orders without Payments", "cancelled-orders": "Cancelled Orders", "cancelled-orders-description": "This section shows all cancelled or rejected orders for visibility. <br/> These orders do not require any action and do not affect your daily closing.", "common-created-by": "Created by", "marketing-tabs-cards-transactions": "Transactions", "total-cash-in-gift-cards": "Total Cash In", "total-cash-out-gift-cards": "Total Cash Out", "total-adjustments-gift-cards": "Adjustments", "gift-card-selling": "<PERSON>", "transaction-type": "Transaction Type", "adjust-gift-card-total": "Adjust balance", "define-amount-to-reduce": "Define amount to reduce", "reduce-balance-by": "Reduce balance by", "adjusted-balance": "Adjusted balance", "common-type": "Type", "scan-this-qr-code-to-pay-or-download-invoice": "Scan this code to pay or download the open invoice.", "new-invoice-with-updated-information-will-be-generated": "A new invoice with the updated information will be generated.", "edit-open-invoice-dialog-title": "Editing Open Invoice", "fiskaly-reports": "Tax Reports", "download-fiskaly-reports": "You can download your TSE and DsFin-vK exports in this section. These are essential for your German tax audit.", "dsfin-vk-reports": "DSKinVk Exports", "download-will-start-automatically-hint": "This download may take a moment to complete, but it will start automatically. We appreciate your patience.", "cash-point-closings-header": "Cashpoint Closings", "cash-point-closings-description": "A summary of all your cashpoint closings.", "report-number": "report number", "cash-point-closings": "Cashpoint Closings", "tse-settings": "TSE Settings", "cashpoint-closing-auto-close": "Automated Cashpoint Closing ", "directly-generate-cash-point-closing-on-report-closing": "Enable this settings for the automated generation of cashpoint closings on daily report closing.", "tse-reports": "TSE Reports", "you-can-download-full-report-or-select-date-range": "You can either create the full report starting with the first recorded transaction or select a date range for your report. Your reports will be created automatically once you exist this popup by pressing 'Done'.", "request-full-report": "Request full report", "common-or": "or", "select-date-range": "Select date range", "processing-export-failed": "Processing export failed", "processing-export": "Processing export", "export-processed": "Export completed", "export-number": "Export number", "not-exported": "Not exported", "full-report": "Full Report", "common-exports": "Exports", "remove-current-customer": "Remove Customer", "number-of-gift-cards-sold": "Gift cards sold", "number-of-gift-cards-in-stock": "Gift cards in stock", "number-of-gift-cards-deleted": "Gift cards deleted", "this-action-will-log-you-out-of-your-account": "This action will log you out of your account.", "menu-editor-form-price-per-gramm": "Sold by weight", "price-per-gramm-field-menu-editor-explanation": "This item is priced based on it's weight and sold in grams.", "euro-per-gram": "€/g", "common-unit": "Unit", "weight": "Weight", "close-keyboard": "Close Keyboard", "report-availability-notice-heading": "Report Availability Notice", "reports-of-this-length-are-blocked-during-these-periods-of-time": "Reports for Past 6 Months and Past 1 Year are only accessible during the following time windows", "accessing-reports-outside-of-these-times-not-available": "If you attempt to access these reports outside of these times, they will not be available. For smaller reports (up to 3 months or custom reports up to 60 days), no time restrictions apply.", "scale-status": "Scale status", "enter-manually": "Enter manually", "update-connected-devices": "Update connected devices", "selected-peripheral": "Selected peripheral", "select-peripheral": "Select peripheral", "choose-external-device": "Choose external device", "delete-ordering-monitor-title": "Delete Monitor", "are-you-sure-description-to-delete-ordering-monitor": "Are you sure you would like to delete this monitor?", "price-by-weight": "Price by Weight", "change-weight": "Change Weight", "cash-journal-konto-migration-overwriting-explanation": "Do you want to overwrite any already set kontos?", "yes-please-overwrite-option": "Yes, overwrite", "no-dont-overwrite-option": "No, don't overwrite", "number-of-cards": "Number of Cards", "multiple-card-creation": "Creation of multiple Gift Cards", "multiple-card-creation-description": "Activate this option to be able to create multiple gift cards at once.", "reporting-section-promotions-and-discounts": "Promotions & Discounts", "filter-by-types": "Filter by type", "types-filtered": "Filtered by type", "filter-by-promotion-code": "Filter by promotion code", "promotion-code-filtered": "Filtered by promotion code", "discounted-item-name": "Discounted Item", "external-voucher": "Voucher", "payment-channels-externalvouchers": "Vouchers", "filter-by-menu-items": "Filter by menu items", "menu-items-filtered": "Filtered by menu items ", "gift-cards-in-webshop": "Gift Cards in Webshop", "enable-the-use-of-gift-cards-in-webshop": "This option will allow customers to use their gift cards when ordering through your webshop.", "tablet-ordering-settings": "Tablet Ordering Settings", "control-settings-for-tablet-ordering": "Control your settings regarding tablet ordering.", "color-theme": "Color Theme", "tablet-ordering-color-theme-hint": "Personalize your tablet ordering with a color that suits your restaurant’s style. A subtle splash of color will enhance the look while keeping the design clean and elegant.", "tablet-ordering-orange": "allO Orange", "tablet-ordering-green": "Forest Green", "tablet-ordering-blue": "Ozean Blue", "tablet-ordering-purple": "<PERSON>", "tablet-ordering-black": "Midnight Black", "tablet-ordering-yellow": "Sunshine Yellow", "tablet-ordering-red": "Crimson Red", "dark-mode": "Dark Mode", "light-mode": "Light Mode", "tablet-ordering-mode-hint": "Switch between light and dark modes to match your restaurant’s vibe.", "select-default-table": "Select Default Table", "no-default-table": "No Default Table", "restaurant-logo-field": "Restaurant Logo", "tablet-ordering-related-settings": "Tablet Ordering related Settings", "add-an-image-to-your-menu-group": "You can add an image that will be displayed in your tablet ordering flow.", "common-image": "Image", "show-image-tablet-ordering": "Showing Image", "enable-this-option-to-display-this-image-in-tablet-ordering": "Enable this option if you want the uploaded image above to be shown in you tablet ordering flow.", "image-width-size": "Image Width", "image-width-size-menu-group-hint": "Customize your tablet ordering menu by using images of different widths. Option Large equals to a full width with only 1 menu group per row, medium with 2 per row and small with 3 images per row.", "common-small": "Small", "common-medium": "Medium", "common-large": "Large", "gluten": "Gluten", "crustaceans": "Crustaceans", "eggs": "Eggs", "fish": "Fish", "peanuts": "Peanuts", "soybeans": "Soybeans", "milk": "Milk", "nuts": "Nuts", "celery": "Celery", "mustard": "Mustard", "sesame": "Sesame", "sulphur": "Sulphur", "lupins": "<PERSON><PERSON><PERSON>", "mollusks": "Mollusks", "promo-codes-in-webshop": "Promo Codes in Webshop", "enable-the-use-of-promo-codes-in-webshop": "This option will allow customers to use promo codes when ordering through your webshop.", "summary-groups": "Summary Groups", "common-legend": "Legend", "served": "Served", "late": "Late", "show-waiter-name-toggle": "Display Waiter Name on Item Level", "show-waiter-name-toggle-description": "Enable this setting to display the name of the waiter who sent the item.", "print-bon-for-completed-item-toggle": "Print Completed Items Bon", "print-bon-for-completed-item-toggle-description": "Enable this setting to automatically print a bon for completed items. The bon includes details like the item name and table number ensuring waiters are promptly informed.", "kitchen-monitor-printing-hint": "To achieve the desired printing behaviour please ensure that you have enabled the printing option as well as selected the correct printer.", "stripe-platform-pricing": "Platform pricing", "setup-the-group-id-for-platform-fee-calculation": "Setup the group id for platform pricing fee calculation", "group-id": "Group id", "connect-sunmi-secondary-display": "Connect SUNMI secondary display", "steps-for-order-status-progression": "Order Status Steps", "step-skipping": "Step Customization", "decide-which-step-you-would-like-to-skip": "To streamline your workflow, you can skip certain steps to better align with your specific needs and ensure efficiency.", "skipp-this-step": "Skip Step", "skipp-this-step-description": "Enable this option if you would like to hide this step from your workflow.", "bon-printing": "Bon Printing Customization", "bon-printing-description": "Determine the workflow steps at which you want to generate a receipt, including details such as the item name, order number, or table number. (This receipt will be printed from the printer selected above.)", "enabled-printing-for-this-step": "Enable this option if you would like to print a receipt once an item is pushed to this step.", "common-step": "Step", "common-tablet": "Tablet", "common-kiosk": "Kiosk", "manual-printing-kitchen-monitor-modal-header": "Manual Printing", "manual-printing-kitchen-monitor-modal-header-description": "Simply click on the desired item to print. You can choose between printing an entire order, a course or a single item.", "print-entire-order": "Print entire order", "print-course": "Print items of: {{ course<PERSON>abel }}", "print-no-course": "Print items of: No Course", "no-transactions": "No Transactions", "we-could-not-find-any-transaction": "We could not find any transactions.", "stripe-connect-transactions": "Stripe Connect Transactions", "common-previous": "Previous", "ending-in": "ending in {{ number }}", "paired": "Paired", "unpaired": "Unpaired", "kiosk-settings": "Kiosk Settings", "kiosk-settings-description": "Configure the settings for this kiosk.", "heroImage": "Hero Image", "connect-to-crm": "Connect to CRM", "fetching-data": "Fetching data", "type-to-search-on-hubspot": "<PERSON><PERSON> to search on hubspot", "find-restaurant": "Find restaurant", "no-matches-found": "No matches found", "try-typing-address-hubspot-id-or-tax-number": "Try typing address, Hubspot id or tax number", "cant-find-what-you-are-looking-for": "Can't find what you are looking for?", "create-company-on-hubspot": "Create company on Hubspot", "search-hubspot": "Search Hubspot", "invite-manager": "Invite manager", "invite-by-email": "Enter valid email", "enter-firstname": "Enter firstname", "enter-lastname": "Enter lastname", "onboarding-has-an-issue-please-contact-allO-support": "Onboarding has an issue, please contact allO support!", "invitation-sent": "Invitation sent", "name-will-receive-an-invite-shortly-at-email": "{{ name }} will receive an email shortly with the invite at {{ email }}.", "resend-invitation": "Resend invitation", "select-trial-base-package": "Select trial based package", "explore-trial-for-months-for-free-only-pay-when-you-start-using": "Explore allO for free for 3 months. Pay only when you go live.", "total-after-trial": "Total due after trial", "total-due-today": "Total due today", "topImage": "Top Image", "select-additional-languages": "Select additional languages", "your-pos-data": "Your POS Data", "pos-data-merchant-field": "Merchant", "pos-data-merchant-id-field": "Merchant ID", "pos-data-type-of-eas": "Electronic recording system type (eAs)", "pos-data-software-of-eas": "Software of eAs", "pos-data-software-version": "Software Version", "pos-data-serial-number": "Serial Number", "pos-data-manufacturer-of-eas": "Manufacturer (eAs)", "pos-data-model-of-eas": "Model (eAs)", "pos-data-date-of-acquisition": "Acquisition (eAs)", "pos-data-date-of-activation": "Date of Activation (eAs)", "pos-data-date-of-disabling": "Date of Disabling (eAs)", "pos-data-date-of-disabling-reason": "Reason of Disabling (eAs)", "pos-data-remarks-about-eas": "Remarks on eAs", "pos-data-tse-serial-number": "TSE Serial Number", "pos-data-tse-activation": "TSE Activation", "pos-data-tse-type": "TSE Type", "pos-data-bsi-certification-id": "BSI Certification ID", "recorded-value-pos-data": "Recorded Value", "displaying-ordered-items-quantities-toggle-header": "Displaying Ordered Items Quantities", "displaying-ordered-items-quantities-toggle-description": "Enable this option if you would like to also see the quantities of already ordered items to be displayed in the menu along side the quantities of pending items.", "disable-final-receipt-for-split-payments-toggle-header": "Disable Final Receipt Printing for Split Payments", "disable-final-receipt-for-split-payments-toggle-description": "Enable this option to suppress the automatic printing of the final full-order receipt in split payments, ensuring only individual payment receipts are generated.", "reservations-settings-v2-modal-schedule-title": "Schedule", "reservations-settings-v2-modal-schedule-description": "Define the days and time this shift should apply for", "reservations-settings-v2-modal-capacity-title": "Reservation Capacity", "reservations-settings-v2-modal-capacity-description": "How many people can be seated at the same time with a reservation", "reservations-settings-v2-modal-start-limit-title": "Start Limit", "reservations-settings-v2-modal-start-limit-description": "How many people can start a reservation in each 15 minute interval", "reservations-settings-v2-modal-specific-time-slots-title": "Manage Specific time slots", "reservations-settings-v2-modal-specific-time-slots-description": "Adapt the seating capacity & start limits for each 15 minute time slot individually.", "reservations-settings-v2-modal-shift-table-column-slot": "Slot", "reservations-settings-v2-modal-shift-table-column-start": "Start", "reservations-settings-v2-modal-shift-table-column-seating": "Seating", "reservations-window-title": "Reservation Windows", "reservations-window-v2-modal-allow-limits-title": "Apply reservation capacity and start limits", "reservations-window-v2-modal-allow-limits-description": "Control capacity and flow for this reservation slot", "reservations-sidebar-title": "allO Reservations", "settings-general-settings": "General Settings", "settings-guest-communication": "Guest Communication", "return-cancelled-items": "Return Cancelled Items", "toggle-to-return-cancelled-items-to-inventory": "Enable this option to restock cancelled items in your inventory.", "settings-table-groups": "Table Groups", "reservation-settings-table-groups-list-title": "Table Capacity & Availability", "reservation-settings-go-to-floorplan-button-text": "Go To Floor Management", "total-with-tips": "Total with tips", "no-table-groups": "No table groups", "click-the-button-below-to-create-a-table-group": "Click the button below to create", "table-groups-tables-header": "Tables", "table-groups-minimum-capacity-header": "Minimum Capacity", "table-groups-maximum-capacity-header": "Maximum Capacity", "table-groups-modal-title": "Create Table Group", "table-groups-modal-title-update": "Update Table Group", "table-groups-delete-btn-text": "Delete", "table-groups-create-btn-text": "Create", "table-groups-update-btn-text": "Update", "table-modal-minimum-capacity-label": "Minimum Capacity", "table-modal-maximum-capacity-label": "Maximum Capacity", "turn-this-device-into-customer-monitor": "Turn this device into a customer monitor", "open-monitor": "Open monitor", "payouts-report": "Payouts report", "no-payouts-yet": "No payouts yet", "we-could-not-find-any-monthly-payouts": "We could not find any monthly payouts", "report": "Report", "buffet-time-limit-per-order-round": "Time limit per ordering round", "menu-editor-form-limited-tags-field-label": "Limited Tags", "start-time-limit-after-first-ordering": "Start time limit after first ordering", "limited-tags-limits-based-on-items-below": "Limited tags will be limited based on items below", "limited-tags-limits-based-on-items-below-explanation": "Choose the menu items and the items per ordering round limit that will affect the limiting tags. To save your changes, make sure to close this window by clicking 'Done'.", "menu-item-code": "Menu item code", "menu-item-limit-per-order-round": "limit per round", "printing": "Printing", "allo-pay-report": "allO Pay Report", "select-table-group": "Select Table Group", "add-item-and-limit": "Add menu item and limit", "add-menu-item-and-limit": "Add another item with limit", "menu-item": "<PERSON><PERSON>", "quantity-limit": "Quantity Limit", "serial-number": "Serial Number", "registration-code": "Registration Code", "reader-id": "Reader Id", "allo-webshop": "allO Webshop", "uber-eats-app-not-active": "Uber Eats integration not active.", "uber-eats-app-active": "Uber Eats integration is active.", "add-allo-reservation-link-to-your-google-business": "Add allO Reservation link to your website to allow users to reserve directly.", "phone-call": "Phone call", "ongoing-call": "Ongoing call", "no-active-call": "No active call", "during-a-call-quick-options-will-become-available": "During a call, quick options will become available", "call-finished": "Call finished", "landline": "Landline", "new-guest": "New guest", "enable-call-monitor": "Enable call monitor", "using-allo-local-you-can-integrate-your-landline-with-allo": "Using allO Local, you can integrate your landline with your POS", "datev-exports": "DATEV Exports", "setup-tse-automatically": "Setup TSE automatically", "price-management": "Price Management", "buffer-time": "Buffer Time", "set-buffer-time-description": "Extra time allocated before the scheduled pickup/delivery to ensure the order is ready on time.", "wolt-store": "Wolt Store", "wolt-menu-updating": "<PERSON><PERSON> Update", "store-information": "Store Information", "store-information-explanation": "In this section you can view store information provided by the delivery partner.", "venue-details": "Venue Details", "venue-is-open": "Venue is currently open.", "venue-is-closed": "Venue is currently closed.", "venue-is-offline": "This venue is currently offline", "venue-is-online": "This venue is currently online", "venue-is-ipad-free": "This venue can operate without the Wolt Merchant app.", "venue-is-not-ipad-free": "This venue can not operate without the Wolt Merchant app.", "opening-days-from-to": "{{ startDate }} at {{ startTime }} - {{ endDate }} at {{ endTime }}", "special-opening-hours": "Special Opening Times", "last-three-orders": "Status of the last three orders", "menu-api-enabled-toggle": "<PERSON><PERSON>", "menu-api-enabled-toggle-desccription": "The Menu API allows integrated partners to push their menus to Wolt and then pull it to their POS.", "order-api-enabled-toggle": "Order Api", "order-api-enabled-toggle-desccription": "The Order Api allows integrated partners to retrieve order details from Wolt and direct them to their POS.", "venue-configurations": "Venue Configurations", "no-store-details": "No Store Details", "no-store-details-available": "No store details available right now. Please check your store setup.", "uber-eats-store": "Uber Eats Store", "uber-eats-settings": "Uber Eats Settings", "onboarding-is-active": "Onboarding is currently active.", "onboarding-is-not-active": "Onboarding is currently inactive.", "order-status-is-online": "Order status is currently online.", "order-status-is-not-offline": "Order status is currently offline.", "uber-eats-support-number": "For questions regarding your Uber Eats store you can reach Uber Eats Support under {{ number }}.", "uber-eats-offline-reason": "Offline Reason: {{ reason }}", "uber-eats-delivery-enabled-toggle": "Uber Eats Delivery", "uber-eats-delivery-enabled-toggle-description": "delivery via Uber", "uber-eats-delivery-extended-enabled-toggle": "Uber Eats Extended Delivery", "uber-eats-delivery-extended-enabled-toggle-description": "Extended delivery via Uber", "delivery-over-the-top": "Delivery Over the Top", "uber-eats-delivery-over-the-top": "Uber only handles delivery, not the customer ordering flow", "delivery-over-the-top-order-ahead": "Deliver Over the Top Order Ahead", "uber-eats-delivery-over-the-top-order-ahead": "Customers can pre-schedule deliveries using restaurant's system", "delivery-third-party": "Delivery Third Party", "uber-eats-delivery-third-party": "Delivery by non-Uber third-party services", "uber-eats-dine-in": "Dine In via Uber Eats", "uber-eats-pick-up": "Customers can order online via Uber Eats and pick up at the restaurant themselves", "auto-accept": "Automatic Order Acceptance", "uber-eats-auto-accept-toggle-description": "Automatic acceptance of incoming orders by the system.", "delivery-api": "Delivery Api", "delivery-api-description": "Delivery can be triggered via API, meaning orders can be placed programmatically (e.g., from a POS system or an integration partner).", "delivery-enabled": "Delivery enabled for this restaurant.", "maximum-delivery-partners-uber-eats": "Maximum Delivery Partners", "maximum-delivery-partners-uber-eats-hint": "This controls how many delivery partners (couriers) can be actively assigned or on their way to this restaurant at the same time.", "prep-time-header-scheduled-order-wolt": "Preparation Time for Scheduled Orders (minutes)", "max-prep-time-wolt": "Maximum Prepartation Time (minutes)", "max-prep-time-wolt-hint": "Maximum preparation time a venue can accept before the system considers the order invalid or delayed.", "allo-webshop-price": "Webshop Price", "allo-webshop-price-hint": "This price is only used for your allO Webshop. Should this field not be set, the webshop will use the primary price set for this item.", "expand-all": "Expand all", "collapse-all": "Collapse all", "store-status-toggle": "Store Status", "store-status-description": "You can set whether your store is online and accepting orders or offline and unavailable to customers. This setting overrides your configured working hours and takes immediate effect.", "can-only-be-set-offline-when-date-is-set": "Store status updates are only valid with a offline until field. Please make sure to always fill this field if you want to set the store offline.", "offline-until": "Offline until", "payout-settings": "Payout Settings", "debit-negative-balance": "Negative Balance", "debit-negative-balance-toggle-description": "If set to true, <PERSON><PERSON> will attempt to withdraw funds from your linked bank account to cover any negative balance. If set to false, <PERSON><PERSON> will not auto-debit your bank, meaning you'd have to manually top up your balance if it goes negative.", "delay-days-payout-settings": "Delay Days", "weekly-anchor-payout-settings": "Weekly Anchor", "monthly-anchor-payout-settings": "Monthly Anchor", "common-daily": "Daily", "common-weekly": "Weekly", "delivery-settings": "Delivery Settings", "allow-sms-notification-reservation": "SMS Notification for Reservation Creation/Update", "this-will-allow-sms-notification-for-reservations-update-and-creation": "This will allow us to send SMS notifications to you customers on reservation creations or updates.", "live-snapshot": "Live Snapshot", "last-published-at": "Last published at", "last-published-from": "Last published from", "last-updated-at": "Last updated at", "last-updated-from": "Last updated from", "last-menu-update-after-last-publish-warning-header": "There are unpublished changes to the menu.", "last-menu-update-after-last-publish-warning": "The menu was modified after the last version was published to Wolt. Please review and publish to ensure <PERSON><PERSON> has the latest updates.", "email-with-attachments": "DATEV export including receipt images from the cash journal", "enable-email-with-attachments": "Enable this option if you want to include receipt images from the cash journal in the export download.", "organization-settings": "Organization Settings", "setup-organization-settings": "Control your settings regarding your organisational structure", "webshop-status-toggle": "Webshop Status", "webshop-status-description": "You can set whether your webshop is online and accepting orders or offline and unavailable to customers. This setting overrides your configured working hours and takes immediate effect.", "can-access-current-daily-report-permission": "Allow to view and close current daily report", "cash-journal-with-attachment-hint": "Download format without receipt images is .csv, where as download format with receipt images is .zip.", "no-wolt-setup": "Wolt is not setup", "make-your-wolt-is-set-up-to-use-this-tool": "To use this tool you need to set up your wolt integration first.", "print-settings": "Print Settings", "control-your-settings-regarding-printing-of-takeaway-orders": "Control your settings regarding the printing of takeaway orders.", "always-print-new-order-notification-toggle": "Always Print New Order Notification", "new-order-notification-takeaway-toggle-description": "If enabled, you will always get a new order notification printout, regardless of any auto-acceptance / rejection behaviour that you might have set. This settings only affects orders from your webshop or that were made through delivery partners.", "can-work-as-driver-permission": "Can work as driver", "driver-role-name": "Driver", "no-wolt-set-up": "Wolt not set up", "cancel-reservation-title": "Cancel Reservation", "cancel-reservation-description": "This action is permanent", "are-you-sure-cancel-reservation": "Are you sure you want to cancel the reservation", "cancel-reservation": "Cancel Reservation", "upload-custom-avatar": "Upload custom avatar", "first-published-at": "First published at", "enable-driver-assignment-toggle-header": "Enable Driver Assignment", "enable-driver-assignment-toggle-description": "Allow staff to assign a driver from the team to a delivery order.", "assign-driver": "Assign Driver", "assign-drivers": "Assign Drivers", "select-driver": "Select Driver", "select-items-to-connect": "Select items to connect", "no-driver-assigned": "No driver assigned yet.", "uber-eats-webshop-link": "Uber Eats Webshop Link", "drivers": "Drivers", "print-cancelled-items-kitchen-monitor-toggle": "Print Cancelled Items", "print-cancelled-items-kitchen-monitor-toggle-description": "Enable this to print cancelled items on the assigned Kitchen Monitor printer.", "display-custom-item-price": "Display custom item price", "display-custom-item-price-description": "Enable this to show the total price for custom items.", "display-options-extras-multiplier": "Display options/extras multiplier", "display-options-extras-multiplier-description": "Show total quantity multiplier like 2x in front of each option and extra.", "print-numeration": "Print numeration", "print-price": "Print price", "control-your-settings-regarding-printing-kitchen-monitor": "Control your settings regarding the printing of your kitchen monitor receipts.", "enable-single-click-bulk-actions-toggle": "Enable Single-Click for Bulk Updates", "enable-single-click-bulk-actions-toggle-description": "Enable Single Click Bulk Updates if you would like to send bulk updates at course and order level with just a single click instead of the default behaviour with double click.", "eat-here": "Eat here", "gift-card-price-suggestions": "Gift Card Price Suggestions", "gift-card-price-suggestion-hint": "These three options will be shown as suggestions when customers buy digital gift cards online. (there will always also be a custom input option.)", "default-printer-setting": "Set Printer to Default Choice", "default-field-printer-explanation": "Select this checkbox to make this printer the default MAIN printer for your device. Only one printer can be set as default at a time. This ensures that any printing to the MAIN printer from this device will use the selected default automatically.", "minimum-time-advance-reservations": "Minimum time in advance", "set-the-minimum-time-advance-for-reservations": "Set how many minutes in advance a reservation can be made", "allow-promotions": "Allow Promotions", "enable-promotions-kiosk": "Enable this option to allow customers to use promotion codes during the kiosk checkout process.", "allow-gift-cards": "Allow Gift Cards", "enable-gift-cards-kiosk": "Enable this option to allow customers to use gift cards during the kiosk checkout process.", "print-compact-layout": "Print compact layout", "allow-bypassing-single-payment-method-toggle-kiosk": "Skip Payment Method Selection", "allow-bypassing-single-payment-method-toggle-description-kiosk": "Enable this option if you would like us to skip the payment method selection during kiosk checkout if only one payment method is configured (payment method selector above).", "allow-tips-toggle-kiosk": "Allow Tips", "allow-tips-toggle-description-kiosk": "Allow customers to add a tip to their order via the kiosk.", "dining-options": "Dining Options", "drag-to-navigate": "Drag to navigate", "enable-drag-to-navigate-between-menu-category-tabs": "Enable drag to navigate between menu category tabs in terminal", "print-driver-on-receipt-toggle": "Print Driver on Receipt", "print-driver-on-receipt-toggle-description": "Include the name of the assigned driver on the final receipt (only applies to internal drivers, not delivery partners).", "settings-special-days": "Special Days", "reservation-settings-special-days-header": "Special Days", "reservation-settings-special-days-modal-basic-information-title": "Basic Information", "reservation-settings-special-days-modal-basic-information-description": "Select date(s), name your special day, and enable/disable reservations", "reservation-settings-special-days-modal-basic-information-date-selection": "Date Selection", "reservation-settings-special-days-modal-basic-information-date-selection-information": "Select a single date or drag to select a range", "reservation-settings-special-days-modal-basic-information-name-title": "Special Day Name", "reservation-settings-special-days-modal-basic-information-enable-reservations": "Enable Reservations", "reservation-settings-special-days-modal-reservation-settings-title": "Reservation Settings", "reservation-settings-special-days-modal-reservation-settings-description": "Configure how reservations work for this special day", "reservation-settings-special-days-modal-reservation-settings-online-reservations": "Online Reservations", "reservation-settings-special-days-modal-reservation-windows-title": "Reservation Windows", "reservation-settings-special-days-modal-message-stuff-title": "Message for staff", "reservation-settings-special-days-modal-message-stuff-description": "This message will be displayed to staff explaining why reservations are disabled", "reservation-settings-special-days-modal-message-stuff-placeholder": "e.g. Reservations are disabled due to special day", "reservation-settings-special-days-modal-message-waiter-title": "Message for waiters", "reservation-settings-special-days-modal-message-waiter-description": "This message will be displayed to waiters explaining how to handle customers", "reservation-settings-special-days-modal-message-waiter-placeholder": "e.g. Please inform customers that we are not taking reservations today.", "reservation-settings-special-days-modal-message-customer-title": "Message for customers", "reservation-settings-special-days-modal-message-customer-description": "This message will be displayed to guests when they attempt to make an online reservation", "reservation-settings-special-days-modal-message-customer-placeholder": "e.g. Please call us directly for reservations on this day.", "no-reservation-windows": "No reservation window", "click-the-button-below-to-create-a-reservation-window": "No reservation windows added yet", "no-special-days": "No special days", "click-the-button-below-to-create-a-special-day": "Click the button below to create", "display-number-selector-toggle": "Show Number Selector", "display-number-selector-toggle-description": "Enable a quantity-selector bar to split grouped items.", "number-selector-header": "Split Items", "advance-one-item-per-click-toggle": "Advance One Item per Click", "advance-one-item-per-click-toggle-description": "When enabled, clicking a grouped item advances a single unit each time.", "show-small-menu-items": "Show small menu items", "enable-to-show-more-items-per-row-in-menu": "Enable to show more items in menu in mobile", "show-invoice-preview-in-checkout": "Show invoice preview", "enable-invoice-preview-for-selected-payment-items-in-checkout": "Enable invoice preview for selected payment items in checkout", "preview-invoice": "Preview invoice", "invoice": "Invoice", "reorder-item-with-all-data": "Reorder items in POS with notes and additions", "enable-cloning-item-during-reorder-instead-of-simple-copy": "Enable to close the item during reordering instead of simple item copy in Terminal", "show-inventory": "Show Inventory", "show-inventory-toggle-description": "Enable this to show inventory for this restaurant.", "show-payment-items-in-receipts": "Show paid items in receipt", "enable-receipts-to-show-paid-items-under-each-payment": "Enable receipts to have paid items per payment.", "open-invoice-settings": "Open Invoice Settings", "open-invoice-settings-description": "Control the settings regarding open invoices.", "open-invoice-due-date-field-label": "Days Until Due", "days-until-due-open-invoice-hint": "Define the number of days after which an open invoice should be due to be paid.", "open-invoice-additional-memo-field-label": "Additional Memo", "map": "Map", "show-takeaway-map": "Show takeaway map", "enable-map-in-takeaway-view-to-manage-orders": "Enable map in takeaway to manager orders", "enable-course-cutter": "Split Courses (CUT)", "enable-quick-actions-menu": "Menu quick actions", "on-long-press-show-menu-with-actions-for-menu-items": "On long press show menu with quick actions for menu items", "add-with-extras": "Add with extras", "order-item-details-custom-addition-section-title": "Custom Extra", "order-item-details-custom-addition-field-placeholder": "Shot of vodka", "create-custom-addition": "Create new custom extra", "enable-custom-extras-for-item-details": "Section for custom extras will be enabled on item details", "enable-custom-extras": "Enable custom extras", "bank-details-open-invoice": "Bank Information", "common-beneficiary": "Beneficiary", "common-bank": "Bank", "common-bic": "BIC", "common-iban": "IBAN", "add-bank-info": "Add bank information", "open-invoice-enable-these-bank-details-toggle": "Enable this bank account", "open-invoice-enable-these-bank-details-toggle-description": "Enable the use of this bank account for the payment of your open invoices.", "allow-to-select-last-payment-request": "Allow to select last payment request items", "enable-selecting-items-from-last-payment-request-in-checkout-to-redo-payment": "Enable selecting items from last payment request in checkout to redo failed or cancelled payments", "select-last-transaction": "Select last transaction", "receipt-print-stages": "Receipt Print Stages", "receipt-print-status-hint": "Select the order status(es) at which receipts should be automatically printed. Leave empty to disable automatic printing.", "enable-mandatory-cancellation-reason-toggle": "Require Cancellation Reason", "enable-mandatory-cancellation-reason-toggle-description": "When enabled, users must enter a custom cancellation reason or select a predefined cancellation reason before confirming a cancellation.", "include-reconciliation-report-toggle": "Including Reconciliation Report", "include-reconciliation-report-toggle-description": "When enabled, a cash reconciliation report will be added to your daily report.", "daily-report-for-certain-account-id-hint": "If you want to print or generate the pdf of a daily report from a certain account please select it here otherwise leave empty and generate the full report."}