import { test, expect} from "@playwright/test";

test.describe('Express order testing', () => {
  test('express-order', async ({ page}) => {
    await page.goto('/');

    await page.waitForTimeout(5000);

    await page.getByText('Express').click();
    await page.waitForTimeout(5000);

    await expect(page).toHaveURL(/.*v=express/);

    await page.getByRole('button', { name: 'New' }).last().click();
    await page.waitForTimeout(3000);

    await expect(page.getByText('Select the mode for this express order.')).toBeVisible();

    await page.getByRole('button', { name: 'In House' }).last().click();

    await expect(page.getByText('Express - Add items')).toBeVisible();

    await page.getByText('Beef').click();
    await page.waitForTimeout(2000);

    const responsePromise = page.waitForResponse('**/gluttony-api/restaurants/**/orders');
    // https://app-dev.allo.restaurant/gluttony-api/restaurants/62b1b639230d5d186d059699/orders
    await page.getByText('Big Burger').click(); // triggers request
    await page.waitForTimeout(2000);

    const response = await responsePromise;
    expect(response.ok()).toBeTruthy();

    const orderData = await response.json();
    const orderNumber = `#${orderData.number}`;

    await page.getByRole('button', { name: 'Checkout' }).last().click();
    await page.waitForTimeout(3000);

    await expect(page.getByText('Step 2 of 2')).toBeVisible(); // step 2 = Checkout page

    await page.getByTestId('suggestion-0').click();
    await page.waitForTimeout(2000);

    await page.getByText('None').click();
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Cash' }).first().click();
    await page.waitForTimeout(2000);

    await page.getByText('Process payment (Cash)').click();
    await page.getByText('Processing payment').isVisible();
    await page.waitForTimeout(2000);

    await expect(page).toHaveURL(/.*v=express/);
    await page.getByText(orderNumber).click();

    await expect(page.getByText('Big Burger')).toBeVisible();

    await page.getByText('Mark as ready').click();
    await page.waitForTimeout(2000);

    await page.getByText(orderNumber).click();
    await page.getByText('Finish').click();
    await page.waitForTimeout(2000);

    await expect(page.getByText(orderNumber)).not.toBeVisible();
  })
});
