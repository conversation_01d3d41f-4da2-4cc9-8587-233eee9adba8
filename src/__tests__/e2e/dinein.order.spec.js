import { test, expect } from '@playwright/test';

test.describe('Dine-in order testing', () => {
  test('dinein-order', async ({ page }) => {
    await page.goto('/');

    await page.waitForTimeout(2000);

    await page.locator('.dine-in-tile-view-item-FREE').first().click();
    await page.waitForTimeout(2000);

    await page.getByText('Beef').click();
    await page.waitForTimeout(2000);

    await page.getByText('Big Burger').click();
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Order' }).last().click();
    await page.waitForTimeout(3000);

    await expect(page.getByText('Send items to the kitchen')).toBeVisible();

    await page.getByText('Continue').click();
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Pay' }).click();
    await page.waitForTimeout(5000);

    const isPaymentButtonDisabled = await page.getByRole('button', { name: 'Process payment (Cash)' }).isDisabled();
    if (isPaymentButtonDisabled) {
      await page.getByText('Select all').click();
      await page.waitForTimeout(2000);
    }
   
    await page.getByTestId('receipt-option-NONE').click();
    await page.waitForTimeout(2000);

    await page.getByText('Process payment (Cash)').click();
    await expect(page.getByText('Processing payment')).toBeVisible();
    await page.waitForTimeout(2000);

    await expect(page).toHaveURL(/.*v=terminal/);

  });
});