import { test, expect } from '@playwright/test';

test.describe('Takeaway order testing', () => {
  test('takeaway-order', async ({ page }) => {
    await page.goto('/');
    
    await page.waitForTimeout(5000);
    await page.getByText('Takeaway').click();
    await page.waitForTimeout(5000);
    await page.getByRole('button', { name: 'New' }).click();

    await page.waitForTimeout(2000);
    await page.getByLabel('Customer').fill('E2E Customer');

    await page.getByText('Continue').click();
    await page.getByText('Beef').click();
    await page.waitForTimeout(2000);

    await page.getByText('Big Burger').click();
    await page.waitForTimeout(2000);

    await page.getByText('Start cooking').click();
    await page.waitForTimeout(2000);

    await page.getByText('Checkout').click();

    await page.getByTestId('suggestion-0').click();
    await page.waitForTimeout(2000);
    
    await page.getByTestId('receipt-option-0').click();
    await page.waitForTimeout(2000);

    await page.getByText('Process payment (Cash)').click();
    await page.getByText('Processing payment').isVisible();
    await page.waitForTimeout(2000);

    await expect(page).toHaveURL(/.*v=pickup/);

    await page.getByText('E2E Customer').last().click();
    await expect(page.getByText('Big Burger')).toBeVisible();

    await page.getByText('Mark as ready').click();
    await page.waitForTimeout(2000);

    await page.getByText('E2E Customer').last().click();
    await page.waitForTimeout(2000);

    await page.getByText('Finish').click();
    
  });
});