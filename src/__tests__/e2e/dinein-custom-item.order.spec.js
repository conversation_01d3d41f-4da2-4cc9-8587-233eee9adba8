import { test, expect } from '@playwright/test';

test.describe('Dine-in order testing', () => {
  test('dinein-order', async ({ page }) => {
    await page.goto('/');

    await page.waitForTimeout(2000);

    await page.locator('.dine-in-tile-view-item-FREE').first().click();
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Custom item' }).first().click();
    await page.waitForTimeout(2000);

    await page.getByRole('textbox', { name: 'Name*' }).fill('Custom item e2e');
    await page.waitForTimeout(2000);

    // fill price
    await page.getByRole('button', { name: '1', exact: true }).click();
    await page.waitForTimeout(500);

    await page.getByRole('button', { name: '0', exact: true }).click();
    await page.waitForTimeout(500);

    await page.getByRole('button', { name: '00', exact: true }).click();
    await page.waitForTimeout(500);

    await page.getByRole('dialog').getByTestId('numerical-input-plus').click();
    await page.waitForTimeout(500);

    await page.getByRole('textbox', { name: 'Notes' }).fill('Notes custom item e2e');
    await page.waitForTimeout(500);

    await page.getByText('Done').click();
    await page.waitForTimeout(2000);

    await expect(page.getByText('Custom item e2e', { exact: true })).toBeVisible();
    await expect(page.getByText('Notes custom item e2e', { exact: true })).toBeVisible();

    await page.getByRole('button', { name: 'Order' }).last().click();
    await page.waitForTimeout(3000);

    await expect(page.getByText('Send items to the kitchen')).toBeVisible();

    await page.getByText('Continue').click();
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Pay' }).click();
    await page.waitForTimeout(5000);

    await page.getByTestId('receipt-option-NONE').click();
    await page.waitForTimeout(2000);

    await page.getByText('Process payment (Cash)').click();
    await expect(page.getByText('Processing payment')).toBeVisible();
    await page.waitForTimeout(2000);

    await expect(page).toHaveURL(/.*v=terminal/);
  });
});