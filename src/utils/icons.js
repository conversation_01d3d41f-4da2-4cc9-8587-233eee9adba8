import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';

export const InfoIcon = ({ color = '#BAB9B8', size = 16 }) => (
  <svg fill={color} width={size} height={size} version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 245.334 245.334" enableBackground="new 0 0 245.334 245.334">
    <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
    <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" stroke={color} strokeWidth="0.9813360000000001"></g>
    <g id="SVGRepo_iconCarrier"> <g> 
      <path d="M122.667,0C55.028,0,0,55.028,0,122.667s55.027,122.667,122.666,122.667s122.667-55.028,122.667-122.667 S190.305,0,122.667,0z M122.667,215.334C71.57,215.334,30,173.764,30,122.667S71.57,30,122.667,30s92.667,41.57,92.667,92.667 S173.763,215.334,122.667,215.334z"></path> 
        <rect width="30" x="107.667" y="109.167" height="79"></rect> 
        <rect width="30" x="107.667" y="57.167" height="29"></rect> 
      </g> 
    </g>
  </svg>
)

export const SlidersHorizontal = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.5625 13.4365L3.125 13.4365" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M16.875 13.4365L14.6875 13.4365" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path
      d="M13.125 14.999C13.9879 14.999 14.6875 14.2995 14.6875 13.4365C14.6875 12.5736 13.9879 11.874 13.125 11.874C12.2621 11.874 11.5625 12.5736 11.5625 13.4365C11.5625 14.2995 12.2621 14.999 13.125 14.999Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.5625 6.5616L3.125 6.56152" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M16.875 6.56152L9.6875 6.5616" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path
      d="M8.125 8.12402C8.98794 8.12402 9.6875 7.42447 9.6875 6.56152C9.6875 5.69858 8.98794 4.99902 8.125 4.99902C7.26206 4.99902 6.5625 5.69858 6.5625 6.56152C6.5625 7.42447 7.26206 8.12402 8.125 8.12402Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const ChevronUpDown = ({ color = '#BAB9B8' }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M15.1755 10.6243C15.4099 10.8586 15.7898 10.8586 16.0241 10.6243C16.2584 10.39 16.2584 10.0101 16.0241 9.77574L12.4241 6.17574C12.3115 6.06321 12.1589 6 11.9998 6C11.8407 6 11.6881 6.06321 11.5755 6.17574L7.97554 9.77574C7.74123 10.0101 7.74123 10.39 7.97554 10.6243C8.20986 10.8586 8.58975 10.8586 8.82407 10.6243L11.9998 7.44853L15.1755 10.6243ZM8.82407 13.3757C8.58975 13.1414 8.20986 13.1414 7.97554 13.3757C7.74123 13.61 7.74123 13.9899 7.97554 14.2243L11.5755 17.8243C11.6881 17.9368 11.8407 18 11.9998 18C12.1589 18 12.3115 17.9368 12.4241 17.8243L16.0241 14.2243C16.2584 13.9899 16.2584 13.61 16.0241 13.3757C15.7898 13.1414 15.4099 13.1414 15.1755 13.3757L11.9998 16.5515L8.82407 13.3757Z"
          fill={color} />
  </svg>
)

export const ChevronUpDown20 = ({ color = '#BAB9B8' }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M13.7415 7.92384C13.9758 8.15816 14.3557 8.15816 14.59 7.92384C14.8243 7.68953 14.8243 7.30963 14.59 7.07531L10.4234 2.90865C10.189 2.67433 9.80914 2.67433 9.57483 2.90865L5.40816 7.07531C5.17384 7.30963 5.17384 7.68953 5.40816 7.92384C5.64247 8.15815 6.02237 8.15815 6.25669 7.92384L9.99909 4.18144L13.7415 7.92384ZM6.25701 12.0753C6.0227 11.841 5.6428 11.841 5.40849 12.0753C5.17417 12.3096 5.17417 12.6895 5.40849 12.9238L9.57515 17.0905C9.80947 17.3248 10.1894 17.3248 10.4237 17.0905L14.5904 12.9238C14.8247 12.6895 14.8247 12.3096 14.5904 12.0753C14.356 11.841 13.9761 11.841 13.7418 12.0753L9.99942 15.8177L6.25701 12.0753Z"
          fill={color} />
  </svg>
)

export const ChevronUpDown16 = ({ color = '#BAB9B8' }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10.9091 6.42449C11.1434 6.65881 11.5233 6.65881 11.7577 6.42449C11.992 6.19018 11.992 5.81028 11.7577 5.57596L8.42433 2.24263C8.19001 2.00832 7.81012 2.00832 7.5758 2.24263L4.24247 5.57596C4.00815 5.81028 4.00815 6.19018 4.24247 6.42449C4.47678 6.65881 4.85668 6.65881 5.09099 6.42449L8.00007 3.51542L10.9091 6.42449ZM5.09067 9.57596C4.85636 9.34165 4.47646 9.34165 4.24214 9.57596C4.00783 9.81028 4.00783 10.1902 4.24214 10.4245L7.57548 13.7578C7.80979 13.9921 8.18969 13.9921 8.424 13.7578L11.7573 10.4245C11.9917 10.1902 11.9917 9.81028 11.7573 9.57596C11.523 9.34165 11.1431 9.34165 10.9088 9.57596L7.99974 12.485L5.09067 9.57596Z"
          fill={color} />
  </svg>
)

export const CloseIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-x">
      <line x1="18" y1="6" x2="6" y2="18" />
      <line x1="6" y1="6" x2="18" y2="18" />
    </svg>
  </SvgIcon>
);

export const MoreIcon16 = () => (
  <svg width="2" height="12" viewBox="0 0 2 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M1 2C1.55228 2 2 1.55228 2 1C2 0.447715 1.55228 0 1 0C0.447715 0 0 0.447715 0 1C0 1.55228 0.447715 2 1 2ZM1 7C1.55228 7 2 6.55228 2 6C2 5.44772 1.55228 5 1 5C0.447715 5 0 5.44772 0 6C0 6.55228 0.447715 7 1 7ZM2 11C2 11.5523 1.55228 12 1 12C0.447715 12 0 11.5523 0 11C0 10.4477 0.447715 10 1 10C1.55228 10 2 10.4477 2 11Z"
          fill="#333332" />
  </svg>
)

export const CloseIcon16 = () => (
  <svg width="20" height="20" viewBox="1 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.5355 4.46447L4.46445 11.5355" stroke="#333332" strokeWidth="1.2"
          strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.5355 11.5355L4.46446 4.46447" stroke="#333332" strokeWidth="1.2"
          strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const HeartIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13 4.00001C12.4179 3.99901 11.8436 4.13385 11.3228 4.39381C10.802 4.65377 10.3491 5.03168 10 5.49747C9.52723 4.86844 8.86861 4.404 8.11736 4.16988C7.3661 3.93576 6.56024 3.94382 5.81381 4.19291C5.06738 4.442 4.41819 4.91951 3.95808 5.55787C3.49798 6.19623 3.25027 6.96311 3.25 7.75001C3.25 12.2414 9.49005 15.7875 9.75568 15.9363C9.83032 15.9781 9.91445 16 10 16C10.0856 16 10.1697 15.9781 10.2443 15.9363C11.392 15.2641 12.4691 14.4782 13.4595 13.5904C15.6429 11.6253 16.75 9.66032 16.75 7.75001C16.7489 6.75579 16.3534 5.80262 15.6504 5.0996C14.9474 4.39658 13.9942 4.00113 13 4.00001Z"
      fill="#BAB9B8" />
  </svg>
)

export const TerminalMenuIcon = ({ color = "#BAB9B8" }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8.00684 3.62162L13.3896 4.57377C13.9609 4.67533 14.437 4.83402 14.8306 5.04984L14.8242 4.6309C14.7988 3.25346 13.8403 2.5933 12.2725 2.8726L8.00684 3.62162ZM6.07715 15.917L13.0786 17.1485C14.4053 17.3833 15.2241 16.628 15.2241 15.1807V7.51908C15.2241 6.19242 14.5767 5.67826 13.231 5.4434L6.44531 4.24369C5.44873 4.0723 4.76953 4.63724 4.76953 5.62748V14.3999C4.76953 15.2505 5.22021 15.7647 6.07715 15.917ZM7.34033 8.03324C7.09277 7.99515 6.95312 7.80472 6.95312 7.55082C6.95312 7.24613 7.19434 7.04301 7.53076 7.10013L12.4121 7.95707C12.6724 8.00785 12.812 8.16019 12.812 8.43949C12.812 8.74418 12.5898 8.95365 12.2534 8.90287L7.34033 8.03324ZM7.34033 10.6358C7.09912 10.5913 6.95312 10.4073 6.95312 10.1534C6.95312 9.85502 7.19434 9.65189 7.53076 9.70902L12.4121 10.566C12.6724 10.6167 12.812 10.7691 12.812 11.0357C12.812 11.3531 12.5898 11.5625 12.2534 11.5118L7.34033 10.6358Z"
      fill={color} />
  </svg>
)

export const CaretRightIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.18213 5.45401L12.7276 9.99947L8.18213 14.5449" stroke="#929191" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>
)

export const CaretLeftIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" transform="rotate(180)" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.18213 5.45401L12.7276 9.99947L8.18213 14.5449" stroke="#929191" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>
)

export const CaretUpIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5 12L10 7L15 12" stroke="#737372" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const CaretDownIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15 8L10 13L5 8" stroke="#737372" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const ScanQRCodeIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.5 3H16C16.5523 3 17 3.44772 17 4V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path
      d="M5.52002 5.71953C5.52002 5.60907 5.60956 5.51953 5.72002 5.51953H14.28C14.3905 5.51953 14.48 5.60907 14.48 5.71953V14.2795C14.48 14.39 14.3905 14.4795 14.28 14.4795H5.72002C5.60956 14.4795 5.52002 14.39 5.52002 14.2795V5.71953Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.5" strokeLinejoin="round" />
    <path d="M6.5 17H4C3.44772 17 3 16.5523 3 16V13.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M17 13.5V16C17 16.5523 16.5523 17 16 17H13.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M3 6.5V4C3 3.44772 3.44772 3 4 3H6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M3 10L17 10" stroke="#F2F2F2" strokeWidth="1.2" strokeLinecap="round" />
  </svg>
)

export const MagnifierIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.5 15.0001C7.46243 15.0001 5 12.5376 5 9.50006C5 6.4625 7.46243 4.00006 10.5 4.00006C13.5376 4.00006 16 6.4625 16 9.50006C16 12.5376 13.5376 15.0001 10.5 15.0001Z"
      stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M5.61285 13.5383L6.03711 13.114L6.88564 13.9625L6.46137 14.3868L5.61285 13.5383ZM4.42385 16.4243C4.18954 16.6586 3.80964 16.6586 3.57532 16.4243C3.34101 16.19 3.34101 15.8101 3.57532 15.5758L4.42385 16.4243ZM6.46137 14.3868L4.42385 16.4243L3.57532 15.5758L5.61285 13.5383L6.46137 14.3868Z"
      fill="#333332" />
  </svg>
)

export const CoffeeIcon16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.875 2.7998V4.4998" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8 2.7998V4.4998" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.125 2.7998V4.4998" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M2.90039 12.2744H13.1004" stroke="#929191" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path
      d="M2.4751 6.39941V6.47442C2.4751 9.52579 4.94872 11.9994 8.0001 11.9994C11.0515 11.9994 13.5251 9.52579 13.5251 6.47442V6.39941H2.4751Z"
      fill="#929191" stroke="#929191" strokeLinejoin="round" />
  </svg>
)

export const DishesIcon16 = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.59941 3.91836C6.59941 3.58699 6.33078 3.31836 5.99941 3.31836C5.66804 3.31836 5.39941 3.58699 5.39941 3.91836V5.51836C5.39941 5.84973 5.66804 6.11836 5.99941 6.11836C6.33078 6.11836 6.59941 5.84973 6.59941 5.51836V3.91836ZM8.59941 3.91836C8.59941 3.58699 8.33078 3.31836 7.99941 3.31836C7.66804 3.31836 7.39941 3.58699 7.39941 3.91836V5.51836C7.39941 5.84973 7.66804 6.11836 7.99941 6.11836C8.33078 6.11836 8.59941 5.84973 8.59941 5.51836V3.91836ZM9.99941 3.31836C10.3308 3.31836 10.5994 3.58699 10.5994 3.91836V5.51836C10.5994 5.84973 10.3308 6.11836 9.99941 6.11836C9.66804 6.11836 9.39941 5.84973 9.39941 5.51836V3.91836C9.39941 3.58699 9.66804 3.31836 9.99941 3.31836ZM2.39844 7.61875C2.39844 7.39784 2.57752 7.21875 2.79844 7.21875H13.1984C13.4194 7.21875 13.5984 7.39784 13.5984 7.61875C13.5984 9.52385 12.6471 11.2068 11.1936 12.2184H12.7982C13.1296 12.2184 13.3982 12.487 13.3982 12.8184C13.3982 13.1497 13.1296 13.4184 12.7982 13.4184H3.19824C2.86687 13.4184 2.59824 13.1497 2.59824 12.8184C2.59824 12.487 2.86687 12.2184 3.19824 12.2184H4.80326C3.34975 11.2068 2.39844 9.52385 2.39844 7.61875Z"
          fill="#BAB9B8" />
  </svg>
)

export const DishesIcon16Dark = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M6.60039 3.4999C6.60039 3.16853 6.33176 2.8999 6.00039 2.8999C5.66902 2.8999 5.40039 3.16853 5.40039 3.4999V5.0999C5.40039 5.43127 5.66902 5.6999 6.00039 5.6999C6.33176 5.6999 6.60039 5.43127 6.60039 5.0999V3.4999ZM8.60039 3.4999C8.60039 3.16853 8.33176 2.8999 8.00039 2.8999C7.66902 2.8999 7.40039 3.16853 7.40039 3.4999V5.0999C7.40039 5.43127 7.66902 5.6999 8.00039 5.6999C8.33176 5.6999 8.60039 5.43127 8.60039 5.0999V3.4999ZM10.0004 2.8999C10.3318 2.8999 10.6004 3.16853 10.6004 3.4999V5.0999C10.6004 5.43127 10.3318 5.6999 10.0004 5.6999C9.66902 5.6999 9.40039 5.43127 9.40039 5.0999V3.4999C9.40039 3.16853 9.66902 2.8999 10.0004 2.8999ZM2.39941 7.20029C2.39941 6.97938 2.5785 6.80029 2.79941 6.80029H13.1994C13.4203 6.80029 13.5994 6.97938 13.5994 7.20029C13.5994 9.1054 12.6481 10.7883 11.1946 11.7999H12.7992C13.1306 11.7999 13.3992 12.0685 13.3992 12.3999C13.3992 12.7313 13.1306 12.9999 12.7992 12.9999H3.19922C2.86785 12.9999 2.59922 12.7313 2.59922 12.3999C2.59922 12.0685 2.86785 11.7999 3.19922 11.7999H4.80424C3.35073 10.7883 2.39941 9.1054 2.39941 7.20029Z" fill="#929191"/>
  </svg>
)

export const DishesIcon16Active = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.875 2.80005V4.50005" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8 2.80005V4.50005" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.125 2.80005V4.50005" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M2.90002 12.2751H13.1" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M2.47504 6.40015V6.47515C2.47504 9.52652 4.94866 12.0001 8.00004 12.0001C11.0514 12.0001 13.525 9.52652 13.525 6.47515V6.40015H2.47504Z"
      fill="#FAE0DA" stroke="#FAE0DA" strokeLinejoin="round" />
  </svg>
)

export const DishesIcon16Taken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.875 2.80005V4.50005" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8 2.80005V4.50005" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.125 2.80005V4.50005" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M2.90002 12.2751H13.1" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M2.47504 6.40015V6.47515C2.47504 9.52652 4.94866 12.0001 8.00004 12.0001C11.0514 12.0001 13.525 9.52652 13.525 6.47515V6.40015H2.47504Z"
      fill="#BD7269" stroke="#BD7269" strokeLinejoin="round" />
  </svg>
)

export const MartiniIcon16 = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.14171 2.94103C2.01572 2.81504 2.10495 2.59961 2.28314 2.59961H11.7174C11.8956 2.59961 11.9849 2.81504 11.8589 2.94103L7.14171 7.65819C7.06361 7.73629 6.93698 7.73629 6.85887 7.65819L2.14171 2.94103Z"
      fill="#929191" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7 7.7998V11.3998" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5 11.3994H9" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M3.3999 4.19922H10.5999" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const DrinksIcon16 = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M2.71639 4.78365C2.21242 4.27968 2.56935 3.41797 3.28207 3.41797L12.7164 3.41797C13.4291 3.41797 13.786 4.27968 13.2821 4.78365L8.59902 9.4667L8.59902 12.2179H10.499C10.8304 12.2179 11.099 12.4865 11.099 12.8179C11.099 13.1492 10.8304 13.4179 10.499 13.4179L5.49902 13.4179C5.16765 13.4179 4.89902 13.1492 4.89902 12.8179C4.89902 12.4865 5.16765 12.2179 5.49902 12.2179H7.39902L7.39902 9.46629L2.71639 4.78365Z"
          fill="#BAB9B8" />
  </svg>
)

export const DrinksIcon16Dark = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M2.71715 4.36569C2.21318 3.86171 2.57011 3 3.28284 3L12.7171 3C13.4299 3 13.7868 3.86171 13.2828 4.36569L8.59979 9.04873L8.59979 11.7999H10.4998C10.8312 11.7999 11.0998 12.0685 11.0998 12.3999C11.0998 12.7313 10.8312 12.9999 10.4998 12.9999L5.49979 12.9999C5.16842 12.9999 4.89979 12.7313 4.89979 12.3999C4.89979 12.0685 5.16842 11.7999 5.49979 11.7999H7.39979L7.39979 9.04832L2.71715 4.36569Z" fill="#929191"/>
  </svg>
)

export const DrinksIcon16Taken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.14141 3.94152C3.01542 3.81553 3.10465 3.6001 3.28283 3.6001H12.7171C12.8953 3.6001 12.9846 3.81553 12.8586 3.94152L8.14141 8.65868C8.0633 8.73678 7.93667 8.73678 7.85857 8.65868L3.14141 3.94152Z"
      fill="#BD7269" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.99997 8.80005V12.4" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5.99997 12.4001H9.99997" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M4.39996 5.2002H11.6" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const DrinksIcon16Active = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.14141 3.94152C3.01542 3.81553 3.10465 3.6001 3.28283 3.6001H12.7171C12.8953 3.6001 12.9846 3.81553 12.8586 3.94152L8.14141 8.65868C8.0633 8.73678 7.93667 8.73678 7.85857 8.65868L3.14141 3.94152Z"
      fill="#FAE0DA" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8 8.80005V12.4" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6 12.4001H10" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4.39996 5.2002H11.6" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const CoffeeIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.5 4.5V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 4.5V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.5 4.5V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4 15.5H16" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.5 15.5H10.5C13.8137 15.5 16.5 12.8137 16.5 9.5V9H3.5V9.5C3.5 12.8137 6.18629 15.5 9.5 15.5Z"
          fill="#BAB9B8" stroke="#BAB9B8" strokeLinejoin="round" />
  </svg>
)

export const DineIn = ({ color = "#BAB9B8" }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9 4L9.5 7C9.5 7.59674 9.26295 8.16903 8.84099 8.59099C8.41903 9.01295 7.84674 9.25 7.25 9.25C6.65326 9.25 6.08097 9.01295 5.65901 8.59099C5.23705 8.16903 5 7.59674 5 7L5.5 4"
      stroke={color} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M8.84099 8.59099C9.26295 8.16903 9.5 7.59674 9.5 7H5C5 7.59674 5.23705 8.16903 5.65901 8.59099C6.08097 9.01295 6.65326 9.25 7.25 9.25C7.84674 9.25 8.41903 9.01295 8.84099 8.59099Z"
      fill={color} stroke={color} strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.25 4V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.25 9.25V16" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.75 12H11.25C11.25 12 12 4 14.75 4V16" fill={color} />
    <path d="M14.75 12H11.25C11.25 12 12 4 14.75 4V16" stroke={color} strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>
)

export const ShoppingBag = ({ color = "#BAB9B8" }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M3.50063 6.00014C3.50088 4.61929 4.6222 3.50045 6.00305 3.50085C9.01395 3.50175 10.9859 3.50175 13.9964 3.50085C15.3779 3.50045 16.4995 4.62034 16.4991 6.00187C16.4984 8.62223 16.4984 11.3778 16.4991 13.9982C16.4995 15.3797 15.3779 16.4996 13.9964 16.4992C10.9857 16.4983 9.01343 16.4983 6.00235 16.4992C4.6215 16.4996 3.50018 15.3807 3.50007 13.9999C3.49986 11.3784 3.50015 8.62159 3.50063 6.00014ZM8 7.00001C8 6.72387 7.77614 6.50001 7.5 6.50001C7.22386 6.50001 7 6.72387 7 7.00001C7 8.65687 8.34315 10 10 10C11.6569 10 13 8.65687 13 7.00001C13 6.72387 12.7761 6.50001 12.5 6.50001C12.2239 6.50001 12 6.72387 12 7.00001C12 8.10458 11.1046 9.00001 10 9.00001C8.89543 9.00001 8 8.10458 8 7.00001Z"
          fill={color} />
  </svg>
)

export const ReservationsIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.5 15.5C4.5 16.3284 5.17157 17 6 17H14.5C14.7761 17 15 16.7761 15 16.5C15 16.2239 14.7761 16 14.5 16H6C5.72386 16 5.5 15.7761 5.5 15.5C5.5 15.2239 5.72386 15 6 15L14 15C14.8284 15 15.5 14.3284 15.5 13.5L15.5 5.58072C15.5 5.31715 15.5 5.08977 15.4847 4.90249C15.4686 4.70481 15.4329 4.50821 15.3365 4.31902C15.1927 4.03677 14.9632 3.8073 14.681 3.66349C14.4918 3.56709 14.2952 3.53144 14.0975 3.51529C13.9102 3.49998 13.6829 3.49999 13.4193 3.5H13.4193L6.5 3.5C5.96957 3.5 5.46086 3.71072 5.08579 4.08579C4.71071 4.46086 4.5 4.96957 4.5 5.5L4.5 15.5ZM6.75 6.5C6.75 6.22386 6.97386 6 7.25 6L12.75 6C13.0261 6 13.25 6.22386 13.25 6.5C13.25 6.77614 13.0261 7 12.75 7L7.25 7C6.97386 7 6.75 6.77614 6.75 6.5ZM7.25 8.5C6.97386 8.5 6.75 8.72386 6.75 9C6.75 9.27614 6.97386 9.5 7.25 9.5H12.75C13.0261 9.5 13.25 9.27614 13.25 9C13.25 8.72386 13.0261 8.5 12.75 8.5H7.25Z"
          fill="#BAB9B8" />
  </svg>
)

export const ReservationsIconActive = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.5 15.5C4.5 16.3284 5.17157 17 6 17H14.5C14.7761 17 15 16.7761 15 16.5C15 16.2239 14.7761 16 14.5 16H6C5.72386 16 5.5 15.7761 5.5 15.5C5.5 15.2239 5.72386 15 6 15L14 15C14.8284 15 15.5 14.3284 15.5 13.5L15.5 5.58072C15.5 5.31715 15.5 5.08977 15.4847 4.90249C15.4686 4.70481 15.4329 4.50821 15.3365 4.31902C15.1927 4.03677 14.9632 3.8073 14.681 3.66349C14.4918 3.56709 14.2952 3.53144 14.0975 3.51529C13.9102 3.49998 13.6829 3.49999 13.4193 3.5H13.4193L6.5 3.5C5.96957 3.5 5.46086 3.71072 5.08579 4.08579C4.71071 4.46086 4.5 4.96957 4.5 5.5L4.5 15.5ZM6.75 6.5C6.75 6.22386 6.97386 6 7.25 6L12.75 6C13.0261 6 13.25 6.22386 13.25 6.5C13.25 6.77614 13.0261 7 12.75 7L7.25 7C6.97386 7 6.75 6.77614 6.75 6.5ZM7.25 8.5C6.97386 8.5 6.75 8.72386 6.75 9C6.75 9.27614 6.97386 9.5 7.25 9.5H12.75C13.0261 9.5 13.25 9.27614 13.25 9C13.25 8.72386 13.0261 8.5 12.75 8.5H7.25Z"
          fill="#FAE0DA" />
  </svg>
)

export const ReservationsIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.3999 18.6002C5.3999 19.5943 6.20579 20.4002 7.1999 20.4002H17.3999C17.7313 20.4002 17.9999 20.1316 17.9999 19.8002C17.9999 19.4688 17.7313 19.2002 17.3999 19.2002H7.1999C6.86853 19.2002 6.5999 18.9316 6.5999 18.6002C6.5999 18.2688 6.86853 18.0002 7.1999 18.0002H16.7999C17.794 18.0002 18.5999 17.1943 18.5999 16.2002V6.69706C18.5999 6.38077 18.5999 6.10792 18.5816 5.88318C18.5622 5.64597 18.5194 5.41004 18.4037 5.18302C18.2311 4.84432 17.9558 4.56896 17.6171 4.39639C17.3901 4.28071 17.1541 4.23792 16.9169 4.21854C16.6922 4.20018 16.4193 4.20019 16.1031 4.2002H16.103H7.7999C7.16338 4.2002 6.55293 4.45305 6.10285 4.90314C5.65276 5.35323 5.3999 5.96368 5.3999 6.6002V18.6002ZM8.1999 7.8002C8.1999 7.52405 8.42376 7.3002 8.6999 7.3002H15.2999C15.576 7.3002 15.7999 7.52405 15.7999 7.8002C15.7999 8.07634 15.576 8.3002 15.2999 8.3002H8.6999C8.42376 8.3002 8.1999 8.07634 8.1999 7.8002ZM8.6999 10.3002C8.42376 10.3002 8.1999 10.5241 8.1999 10.8002C8.1999 11.0763 8.42376 11.3002 8.6999 11.3002H15.2999C15.576 11.3002 15.7999 11.0763 15.7999 10.8002C15.7999 10.5241 15.576 10.3002 15.2999 10.3002H8.6999Z"
          fill="#BAB9B8" />
  </svg>
)

export const Lightning = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.3235 15.397C9.75041 16.0236 9.46384 16.3369 9.25909 16.3475C9.08239 16.3566 8.91401 16.2717 8.81635 16.1241C8.70318 15.9532 8.78484 15.5365 8.94818 14.7032L9.32054 12.8033C9.36656 12.5686 9.38957 12.4512 9.3657 12.3474C9.34465 12.2559 9.29828 12.1722 9.23188 12.1058C9.15657 12.0306 9.04484 11.9878 8.82138 11.9023L6.34754 10.9557C5.9796 10.8149 5.79563 10.7445 5.70814 10.6223C5.63163 10.5155 5.60026 10.3828 5.6208 10.253C5.6443 10.1046 5.77724 9.95928 6.04313 9.66858L10.6764 4.60301C11.2496 3.9764 11.5362 3.66309 11.7409 3.65251C11.9176 3.64337 12.086 3.72831 12.1837 3.87585C12.2968 4.04681 12.2152 4.46348 12.0518 5.29682L11.6795 7.19664C11.6334 7.43143 11.6104 7.54882 11.6343 7.6526C11.6553 7.74408 11.7017 7.82779 11.7681 7.89415C11.8434 7.96942 11.9552 8.01218 12.1786 8.09768L14.6525 9.0443C15.0204 9.1851 15.2044 9.2555 15.2919 9.37766C15.3684 9.4845 15.3997 9.61716 15.3792 9.74695C15.3557 9.89536 15.2228 10.0407 14.9569 10.3314L10.3235 15.397Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const LightningWhite = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.3235 15.397C9.75041 16.0236 9.46384 16.3369 9.25909 16.3475C9.08239 16.3566 8.91401 16.2717 8.81635 16.1241C8.70318 15.9532 8.78484 15.5365 8.94818 14.7032L9.32054 12.8033C9.36656 12.5686 9.38957 12.4512 9.3657 12.3474C9.34465 12.2559 9.29828 12.1722 9.23188 12.1058C9.15657 12.0306 9.04484 11.9878 8.82138 11.9023L6.34754 10.9557C5.9796 10.8149 5.79563 10.7445 5.70814 10.6223C5.63163 10.5155 5.60026 10.3828 5.6208 10.253C5.6443 10.1046 5.77724 9.95928 6.04313 9.66858L10.6764 4.60301C11.2496 3.9764 11.5362 3.66309 11.7409 3.65251C11.9176 3.64337 12.086 3.72831 12.1837 3.87585C12.2968 4.04681 12.2152 4.46348 12.0518 5.29682L11.6795 7.19664C11.6334 7.43143 11.6104 7.54882 11.6343 7.6526C11.6553 7.74408 11.7017 7.82779 11.7681 7.89415C11.8434 7.96942 11.9552 8.01218 12.1786 8.09768L14.6525 9.0443C15.0204 9.1851 15.2044 9.2555 15.2919 9.37766C15.3684 9.4845 15.3997 9.61716 15.3792 9.74695C15.3557 9.89536 15.2228 10.0407 14.9569 10.3314L10.3235 15.397Z"
      fill="#FFFFFF" stroke="#FFFFFF" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const CustomersIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10 3C6.13401 3 3 6.13401 3 10C3 11.9243 3.7765 13.6673 5.03327 14.9327C6.1126 13.3438 7.9344 12.3 10 12.3C12.0656 12.3 13.8874 13.3438 14.9667 14.9327C16.2235 13.6673 17 11.9243 17 10C17 6.13401 13.866 3 10 3ZM9.94973 11C11.3304 11 12.4497 9.88071 12.4497 8.5C12.4497 7.11929 11.3304 6 9.94973 6C8.56902 6 7.44973 7.11929 7.44973 8.5C7.44973 9.88071 8.56902 11 9.94973 11Z"
          fill="#BAB9B8" />
    <path
      d="M3.5 10C3.5 6.41015 6.41015 3.5 10 3.5C13.5899 3.5 16.5 6.41015 16.5 10C16.5 13.5899 13.5899 16.5 10 16.5C6.41015 16.5 3.5 13.5899 3.5 10Z"
      stroke="#BAB9B8" />
  </svg>
)

export const CustomersIconLight = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10 3C6.13401 3 3 6.13401 3 10C3 11.9243 3.7765 13.6673 5.03327 14.9327C6.1126 13.3438 7.9344 12.3 10 12.3C12.0656 12.3 13.8874 13.3438 14.9667 14.9327C16.2235 13.6673 17 11.9243 17 10C17 6.13401 13.866 3 10 3ZM9.94973 11C11.3304 11 12.4497 9.88071 12.4497 8.5C12.4497 7.11929 11.3304 6 9.94973 6C8.56902 6 7.44973 7.11929 7.44973 8.5C7.44973 9.88071 8.56902 11 9.94973 11Z"
          fill="#EFEFEE" />
    <path
      d="M3.5 10C3.5 6.41015 6.41015 3.5 10 3.5C13.5899 3.5 16.5 6.41015 16.5 10C16.5 13.5899 13.5899 16.5 10 16.5C6.41015 16.5 3.5 13.5899 3.5 10Z"
      stroke="#EFEFEE" />
  </svg>
)

export const ReceiptsIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M12.3 3.50004C13.4201 3.50005 13.9801 3.50005 14.4079 3.71804C14.7843 3.90979 15.0902 4.21575 15.282 4.59207C15.5 5.01989 15.5 5.57966 15.5 6.69918C15.5 9.00684 15.5 12.0468 15.5 13.9957V15.9991C15.5 16.191 15.3906 16.366 15.2183 16.4497C15.046 16.5335 14.8411 16.5113 14.6906 16.3926L13.9403 15.8008C13.8484 15.7283 13.8025 15.692 13.7511 15.6755C13.7056 15.6608 13.6574 15.6573 13.6103 15.6651C13.557 15.6739 13.5063 15.703 13.4048 15.7613L12.2326 16.4338C12.0787 16.5221 11.8895 16.5221 11.7356 16.4338L10.4766 15.7115C10.3897 15.6616 10.3462 15.6367 10.3 15.6269C10.2592 15.6183 10.217 15.6183 10.1761 15.6269C10.1299 15.6367 10.0865 15.6616 9.99952 15.7115L8.74054 16.4338C8.58661 16.5221 8.39749 16.5221 8.24356 16.4338L6.98458 15.7115C6.89764 15.6616 6.85417 15.6367 6.80799 15.6269C6.76713 15.6183 6.72492 15.6183 6.68406 15.6269C6.63788 15.6367 6.59441 15.6616 6.50747 15.7115L5.24849 16.4338C5.09375 16.5225 4.90353 16.522 4.74925 16.4325C4.59497 16.3429 4.5 16.1778 4.5 15.9991C4.5 15.3823 4.50001 14.7857 4.50002 14.2036C4.50007 12.1945 4.50697 9.05146 4.51299 6.69009C4.51583 5.57374 4.51725 5.01557 4.73564 4.58885C4.92768 4.21361 5.23368 3.90838 5.60941 3.7173C6.03669 3.50001 6.59516 3.50001 7.7121 3.50002L12.3 3.50004Z"
          fill="#BAB9B8" />
    <path d="M7.25 6.5H12.75" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.25 9H12.75" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const AppStoreIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.75 9.5C8.26878 9.5 9.5 8.26878 9.5 6.75C9.5 5.23122 8.26878 4 6.75 4C5.23122 4 4 5.23122 4 6.75C4 8.26878 5.23122 9.5 6.75 9.5Z"
      fill="#BAB9B8" />
    <path
      d="M13.25 9.5C14.7688 9.5 16 8.26878 16 6.75C16 5.23122 14.7688 4 13.25 4C11.7312 4 10.5 5.23122 10.5 6.75C10.5 8.26878 11.7312 9.5 13.25 9.5Z"
      fill="#BAB9B8" />
    <path
      d="M6.75 16C8.26878 16 9.5 14.7688 9.5 13.25C9.5 11.7312 8.26878 10.5 6.75 10.5C5.23122 10.5 4 11.7312 4 13.25C4 14.7688 5.23122 16 6.75 16Z"
      fill="#BAB9B8" />
    <path
      d="M13.25 16C14.7688 16 16 14.7688 16 13.25C16 11.7312 14.7688 10.5 13.25 10.5C11.7312 10.5 10.5 11.7312 10.5 13.25C10.5 14.7688 11.7312 16 13.25 16Z"
      fill="#BAB9B8" />
  </svg>
)

export const MyAppsIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    {/*<path d="M6.75 9.5C8.26878 9.5 9.5 8.26878 9.5 6.75C9.5 5.23122 8.26878 4 6.75 4C5.23122 4 4 5.23122 4 6.75C4 8.26878 5.23122 9.5 6.75 9.5Z" fill="#73AF9F"/>*/}
    <path
      d="M13.25 9.5C14.7688 9.5 16 8.26878 16 6.75C16 5.23122 14.7688 4 13.25 4C11.7312 4 10.5 5.23122 10.5 6.75C10.5 8.26878 11.7312 9.5 13.25 9.5Z"
      fill="#BAB9B8" />
    <path
      d="M6.75 16C8.26878 16 9.5 14.7688 9.5 13.25C9.5 11.7312 8.26878 10.5 6.75 10.5C5.23122 10.5 4 11.7312 4 13.25C4 14.7688 5.23122 16 6.75 16Z"
      fill="#BAB9B8" />
    <path
      d="M13.25 16C14.7688 16 16 14.7688 16 13.25C16 11.7312 14.7688 10.5 13.25 10.5C11.7312 10.5 10.5 11.7312 10.5 13.25C10.5 14.7688 11.7312 16 13.25 16Z"
      fill="#BAB9B8" />
  </svg>
)

export const HelpAndSupportIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10.4099 3.41767C8.80577 3.31729 7.22012 3.80597 5.95076 4.79195C4.68139 5.77793 3.81562 7.19339 3.51603 8.77254C3.23284 10.2652 3.47392 11.8071 4.19374 13.1389L3.67074 14.9696C3.61685 15.1582 3.61438 15.3578 3.66358 15.5477C3.71279 15.7376 3.81188 15.9109 3.9506 16.0496C4.08931 16.1883 4.2626 16.2874 4.45249 16.3366C4.64238 16.3858 4.842 16.3833 5.03062 16.3294L6.86124 15.8064C8.19313 16.5263 9.735 16.7673 11.2277 16.4841C12.8068 16.1845 14.2223 15.3188 15.2082 14.0494C16.1942 12.78 16.6829 11.1944 16.5825 9.5902C16.4821 7.98603 15.7996 6.47368 14.663 5.33714C13.5265 4.2006 12.0141 3.51806 10.4099 3.41767ZM9.18349 6.17676C9.6078 6.00101 10.0747 5.95502 10.5252 6.04462C10.9756 6.13422 11.3894 6.35538 11.7141 6.68014C12.0389 7.00489 12.26 7.41865 12.3496 7.8691C12.4392 8.31955 12.3932 8.78645 12.2175 9.21077C12.0417 9.63508 11.7441 9.99775 11.3622 10.2529C11.3363 10.2702 11.3101 10.287 11.2835 10.3032C11.0725 10.4323 10.884 10.5784 10.7529 10.7338C10.6237 10.8869 10.5721 11.0216 10.5721 11.1443V11.4539C10.5721 11.73 10.3483 11.9539 10.0721 11.9539C9.79598 11.9539 9.57213 11.73 9.57213 11.4539V11.1443C9.57213 10.7146 9.76023 10.3596 9.98857 10.089C10.2149 9.82068 10.5017 9.60917 10.7618 9.45009C10.777 9.44085 10.7919 9.4313 10.8067 9.42143C11.0241 9.27616 11.1935 9.06967 11.2936 8.82808C11.3937 8.5865 11.4199 8.32066 11.3688 8.06419C11.3178 7.80773 11.1919 7.57215 11.007 7.38724C10.8221 7.20234 10.5865 7.07642 10.3301 7.02541C10.0736 6.97439 9.80776 7.00057 9.56617 7.10064C9.32458 7.20071 9.1181 7.37017 8.97282 7.58759C8.82754 7.80502 8.75 8.06064 8.75 8.32213C8.75 8.59827 8.52614 8.82213 8.25 8.82213C7.97386 8.82213 7.75 8.59827 7.75 8.32213C7.75 7.86285 7.88619 7.41389 8.14135 7.03202C8.39651 6.65015 8.75917 6.35252 9.18349 6.17676ZM10.0699 13.9333C10.4289 13.9333 10.7199 13.6423 10.7199 13.2833C10.7199 12.9243 10.4289 12.6333 10.0699 12.6333C9.71094 12.6333 9.41992 12.9243 9.41992 13.2833C9.41992 13.6423 9.71094 13.9333 10.0699 13.9333Z"
          fill="#BAB9B8" />
  </svg>

)

export const ReportingIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14.3499 6.50006C13.7899 6.50008 13.5099 6.50009 13.296 6.60909C13.1078 6.70496 12.9549 6.85794 12.859 7.0461C12.75 7.26001 12.75 7.54003 12.75 8.10007L12.75 14.9C12.75 15.4601 12.75 15.7401 12.859 15.954C12.9549 16.1422 13.1079 16.2951 13.296 16.391C13.5099 16.5 13.79 16.5 14.35 16.5H14.65C15.2101 16.5 15.4901 16.5 15.704 16.391C15.8922 16.2951 16.0451 16.1422 16.141 15.954C16.25 15.7401 16.25 15.4601 16.25 14.9V8.10005C16.25 7.53998 16.25 7.25995 16.141 7.04603C16.0451 6.85787 15.8921 6.70489 15.704 6.60902C15.4901 6.50002 15.21 6.50003 14.6499 6.50005L14.3499 6.50006Z"
          fill="#BAB9B8" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M9.84993 3.50008C9.2899 3.5001 9.00989 3.50012 8.79598 3.60911C8.60783 3.70499 8.45486 3.85797 8.35899 4.04613C8.25 4.26004 8.25 4.54005 8.25 5.10008L8.25001 14.9C8.25001 15.4601 8.25001 15.7401 8.35901 15.954C8.45488 16.1422 8.60786 16.2951 8.79602 16.391C9.00993 16.5 9.28996 16.5 9.85001 16.5H10.15C10.7101 16.5 10.9901 16.5 11.204 16.391C11.3922 16.2951 11.5451 16.1422 11.641 15.954C11.75 15.7401 11.75 15.4601 11.75 14.9V5.10007C11.75 4.53999 11.75 4.25996 11.641 4.04604C11.5451 3.85787 11.3921 3.70489 11.204 3.60902C10.99 3.50003 10.71 3.50004 10.1499 3.50007L9.84993 3.50008Z"
          fill="#BAB9B8" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.34997 10.5C4.78993 10.5 4.50991 10.5001 4.296 10.609C4.10784 10.7049 3.95486 10.8579 3.85899 11.0461C3.75 11.26 3.75 11.54 3.75 12.1L3.75001 14.9C3.75001 15.4601 3.75001 15.7401 3.85901 15.954C3.95488 16.1422 4.10786 16.2951 4.29602 16.391C4.50993 16.5 4.78996 16.5 5.35001 16.5H5.65C6.21005 16.5 6.49008 16.5 6.70399 16.391C6.89215 16.2951 7.04513 16.1422 7.14101 15.954C7.25 15.7401 7.25 15.4601 7.25 14.9V12.1C7.25 11.54 7.25 11.2599 7.141 11.046C7.04513 10.8579 6.89214 10.7049 6.70398 10.609C6.49006 10.5 6.21003 10.5 5.64997 10.5L5.34997 10.5Z"
          fill="#BAB9B8" />
  </svg>
)

export const ManagementIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9.6377 17.5264H10.8887C11.0938 17.5264 11.2669 17.4694 11.4082 17.3555C11.5495 17.2461 11.6429 17.0911 11.6885 16.8906L12.0303 15.4346C12.1488 15.3936 12.2627 15.3503 12.3721 15.3047C12.486 15.2637 12.5931 15.2204 12.6934 15.1748L13.9648 15.9473C14.1335 16.0521 14.3089 16.0954 14.4912 16.0771C14.6735 16.0635 14.8353 15.986 14.9766 15.8447L15.8516 14.9766C15.9928 14.8353 16.0726 14.6712 16.0908 14.4844C16.109 14.293 16.0612 14.113 15.9473 13.9443L15.168 12.6865C15.2227 12.5817 15.2705 12.4769 15.3115 12.3721C15.3525 12.2627 15.3913 12.1533 15.4277 12.0439L16.8975 11.6953C17.0934 11.6543 17.2461 11.5632 17.3555 11.4219C17.4694 11.276 17.5264 11.1006 17.5264 10.8955V9.67188C17.5264 9.47135 17.4694 9.30046 17.3555 9.15918C17.2461 9.01335 17.0934 8.91992 16.8975 8.87891L15.4414 8.53027C15.4004 8.40723 15.3571 8.29102 15.3115 8.18164C15.266 8.07227 15.2227 7.96973 15.1816 7.87402L15.9609 6.5957C16.0703 6.42708 16.1182 6.25163 16.1045 6.06934C16.0908 5.88704 16.0111 5.72526 15.8652 5.58398L14.9697 4.70215C14.8285 4.57454 14.6712 4.49935 14.498 4.47656C14.3294 4.45378 14.1608 4.49023 13.9922 4.58594L12.6934 5.38574C12.5977 5.33561 12.4928 5.29004 12.3789 5.24902C12.2695 5.20345 12.1533 5.16016 12.0303 5.11914L11.6885 3.64258C11.6429 3.44661 11.5495 3.29167 11.4082 3.17773C11.2669 3.05924 11.0938 3 10.8887 3H9.6377C9.43717 3 9.264 3.05924 9.11816 3.17773C8.97689 3.29167 8.88346 3.44661 8.83789 3.64258L8.49609 5.10547C8.3776 5.14648 8.26139 5.18978 8.14746 5.23535C8.03353 5.28092 7.92643 5.32878 7.82617 5.37891L6.53418 4.58594C6.36556 4.49023 6.19466 4.45378 6.02148 4.47656C5.85286 4.49479 5.69564 4.57227 5.5498 4.70898L4.66797 5.58398C4.52214 5.72526 4.4401 5.88704 4.42188 6.06934C4.4082 6.25163 4.45605 6.42708 4.56543 6.5957L5.33789 7.87402C5.29688 7.96973 5.25358 8.07227 5.20801 8.18164C5.16243 8.29102 5.12142 8.40723 5.08496 8.53027L3.63574 8.87891C3.43522 8.91992 3.27799 9.01335 3.16406 9.15918C3.05469 9.30046 3 9.47135 3 9.67188V10.8955C3 11.1006 3.05469 11.276 3.16406 11.4219C3.27799 11.5632 3.43522 11.6543 3.63574 11.6953L5.09863 12.0439C5.13053 12.1533 5.16927 12.2627 5.21484 12.3721C5.26042 12.4769 5.30599 12.5817 5.35156 12.6865L4.5791 13.9443C4.46973 14.113 4.42415 14.293 4.44238 14.4844C4.46061 14.6712 4.54036 14.8353 4.68164 14.9766L5.5498 15.8447C5.69108 15.986 5.85286 16.0635 6.03516 16.0771C6.21745 16.0954 6.3929 16.0521 6.56152 15.9473L7.83301 15.1748C7.93327 15.2204 8.03809 15.2637 8.14746 15.3047C8.26139 15.3503 8.3776 15.3936 8.49609 15.4346L8.83789 16.8906C8.88346 17.0911 8.97689 17.2461 9.11816 17.3555C9.264 17.4694 9.43717 17.5264 9.6377 17.5264ZM10.2666 12.6592C9.8291 12.6592 9.42806 12.5521 9.06348 12.3379C8.69889 12.1191 8.40951 11.8275 8.19531 11.4629C7.98112 11.0983 7.87402 10.6973 7.87402 10.2598C7.87402 9.82227 7.98112 9.4235 8.19531 9.06348C8.41406 8.70345 8.70345 8.41634 9.06348 8.20215C9.42806 7.9834 9.8291 7.87402 10.2666 7.87402C10.7041 7.87402 11.1029 7.9834 11.4629 8.20215C11.8275 8.41634 12.1169 8.70345 12.3311 9.06348C12.5452 9.4235 12.6523 9.82227 12.6523 10.2598C12.6523 10.6973 12.5452 11.0983 12.3311 11.4629C12.1169 11.8275 11.8275 12.1191 11.4629 12.3379C11.1029 12.5521 10.7041 12.6592 10.2666 12.6592Z"
      fill="#BAB9B8" />
  </svg>

)

export const MobilePhoneIcon = () => (
  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M15.453 15.4467C15.4754 15.1719 15.4754 14.8361 15.4754 14.4364V14.4364L15.4754 5.37647C15.4754 4.97685 15.4754 4.64107 15.453 4.36631C15.4295 4.07904 15.3786 3.80603 15.2465 3.5469C15.0452 3.15177 14.7239 2.83049 14.3287 2.62918C14.0696 2.49717 13.7966 2.44622 13.5094 2.42276C13.2347 2.40033 12.899 2.40036 12.4995 2.40039L8.25191 2.40051L8.2519 2.40051C7.85221 2.40048 7.51637 2.40046 7.24157 2.42289C6.95427 2.44634 6.68123 2.49729 6.42208 2.62932C6.02688 2.83066 5.70566 3.15187 5.50429 3.54706C5.37225 3.80619 5.32129 4.07921 5.29782 4.36648C5.27537 4.64125 5.27538 4.97704 5.27539 5.37667V14.4368C5.27538 14.8363 5.27537 15.172 5.29782 15.4468C5.32129 15.734 5.37225 16.007 5.50428 16.2661C5.70561 16.6612 6.02687 16.9825 6.42201 17.1838C6.68113 17.3158 6.9541 17.3668 7.24134 17.3903C7.51606 17.4127 7.85178 17.4127 8.25131 17.4127H12.4995C12.899 17.4127 13.2347 17.4127 13.5094 17.3903C13.7967 17.3668 14.0697 17.3158 14.3288 17.1838C14.7239 16.9825 15.0452 16.6612 15.2465 16.2661C15.3785 16.007 15.4295 15.7339 15.453 15.4467ZM8.96861 14.5C8.69247 14.5 8.46861 14.7239 8.46861 15C8.46861 15.2761 8.69247 15.5 8.96861 15.5H11.7811C12.0573 15.5 12.2811 15.2761 12.2811 15C12.2811 14.7239 12.0573 14.5 11.7811 14.5H8.96861Z"
          fill="#BAB9B8" />
  </svg>
)

export const CheersIllustration = () => (
  <svg width="75" height="71" viewBox="0 0 75 71" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21.6152 16.019L42.1764 31.1311L17.7897 58.1558L3.10313 47.3615L21.6152 16.019Z" fill="#BAB9B8" />
    <path d="M64.8008 28.2358L40.2817 35.3035L53.816 69.0951L71.3296 64.0467L64.8008 28.2358Z" fill="#BAB9B8" />
    <path
      d="M57.4276 63.1607C61.9302 64.9643 67.0513 62.7539 68.8659 58.2236L52.5605 51.6924C50.7459 56.2226 52.925 61.3572 57.4276 63.1607Z"
      fill="#737372" />
    <rect x="35.2461" y="1.61523" width="2.2966" height="20.6694" transform="rotate(-6.88787 35.2461 1.61523)"
          fill="#929191" />
    <rect x="55.5488" y="0.333496" width="3.07471" height="27.6724" transform="rotate(23.1121 55.5488 0.333496)"
          fill="#929191" />
    <path
      d="M18.5258 33.4641C17.4408 28.2572 12.3403 24.9158 7.13342 26.0008L11.0625 44.8564C16.2693 43.7715 19.6108 38.6709 18.5258 33.4641Z"
      fill="#929191" />
  </svg>

);

export const SunIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 14.2188C12.33 14.2188 14.2188 12.33 14.2188 10C14.2188 7.67005 12.33 5.78125 10 5.78125C7.67005 5.78125 5.78125 7.67005 5.78125 10C5.78125 12.33 7.67005 14.2188 10 14.2188Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 3.53125V2.125" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5.42552 5.4254L4.43115 4.43103" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M3.53125 10H2.125" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5.42552 14.5736L4.43115 15.568" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M10 16.4688V17.875" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.5737 14.5736L15.5681 15.568" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M16.4688 10H17.875" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.5737 5.4254L15.5681 4.43103" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>

)

export const RevenueIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z"
      fill="#BAB9B8" />
    <path d="M10 6.5V7.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 12.5V13.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M8.5 12.5H10.75C11.0815 12.5 11.3995 12.3683 11.6339 12.1339C11.8683 11.8995 12 11.5815 12 11.25C12 10.9185 11.8683 10.6005 11.6339 10.3661C11.3995 10.1317 11.0815 10 10.75 10H9.25C8.91848 10 8.60054 9.8683 8.36612 9.63388C8.1317 9.39946 8 9.08152 8 8.75C8 8.41848 8.1317 8.10054 8.36612 7.86612C8.60054 7.6317 8.91848 7.5 9.25 7.5H11.5"
      stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const RevenueIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.9998 19.1998C15.9763 19.1998 19.1998 15.9763 19.1998 11.9998C19.1998 8.02335 15.9763 4.7998 11.9998 4.7998C8.02335 4.7998 4.7998 8.02335 4.7998 11.9998C4.7998 15.9763 8.02335 19.1998 11.9998 19.1998Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.9998 7.7998V8.9998" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.9998 15V16.2" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M10.1999 15H12.8999C13.2977 15 13.6792 14.842 13.9605 14.5607C14.2418 14.2794 14.3999 13.8978 14.3999 13.5C14.3999 13.1022 14.2418 12.7206 13.9605 12.4393C13.6792 12.158 13.2977 12 12.8999 12H11.0999C10.702 12 10.3205 11.842 10.0392 11.5607C9.75789 11.2794 9.59985 10.8978 9.59985 10.5C9.59985 10.1022 9.75789 9.72064 10.0392 9.43934C10.3205 9.15804 10.702 9 11.0999 9H13.7999"
      stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const CashJournalIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.5 15.5C4.5 16.3284 5.17157 17 6 17H14.5C14.7761 17 15 16.7761 15 16.5C15 16.2239 14.7761 16 14.5 16H6C5.72386 16 5.5 15.7761 5.5 15.5C5.5 15.2239 5.72386 15 6 15H14C14.8284 15 15.5 14.3284 15.5 13.5V5.58072C15.5 5.31715 15.5 5.08977 15.4847 4.90249C15.4686 4.70481 15.4329 4.5082 15.3365 4.31902C15.1927 4.03677 14.9632 3.8073 14.681 3.66349C14.4918 3.56709 14.2952 3.53144 14.0975 3.51529C13.9102 3.49998 13.6828 3.49999 13.4193 3.5L6.5 3.5C5.96957 3.5 5.46086 3.71071 5.08579 4.08579C4.71071 4.46086 4.5 4.96957 4.5 5.5V15.5Z"
          fill="#BAB9B8" />
    <path d="M10.2146 6V6.85714" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.2146 11.1427V11.9998" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M8.92857 11.143H10.8571C11.1413 11.143 11.4138 11.0301 11.6148 10.8292C11.8157 10.6283 11.9286 10.3557 11.9286 10.0716C11.9286 9.78742 11.8157 9.5149 11.6148 9.31397C11.4138 9.11304 11.1413 9.00016 10.8571 9.00016H9.57143C9.28727 9.00016 9.01475 8.88727 8.81381 8.68634C8.61288 8.48541 8.5 8.21289 8.5 7.92873C8.5 7.64457 8.61288 7.37205 8.81381 7.17111C9.01475 6.97018 9.28727 6.8573 9.57143 6.8573H11.5"
      stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const CashJournalIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.5 15.5C4.5 16.3284 5.17157 17 6 17H14.5C14.7761 17 15 16.7761 15 16.5C15 16.2239 14.7761 16 14.5 16H6C5.72386 16 5.5 15.7761 5.5 15.5C5.5 15.2239 5.72386 15 6 15H14C14.8284 15 15.5 14.3284 15.5 13.5V5.58072C15.5 5.31715 15.5 5.08977 15.4847 4.90249C15.4686 4.70481 15.4329 4.5082 15.3365 4.31902C15.1927 4.03677 14.9632 3.8073 14.681 3.66349C14.4918 3.56709 14.2952 3.53144 14.0975 3.51529C13.9102 3.49998 13.6828 3.49999 13.4193 3.5L6.5 3.5C5.96957 3.5 5.46086 3.71071 5.08579 4.08579C4.71071 4.46086 4.5 4.96957 4.5 5.5V15.5Z"
          fill="#BAB9B8" />
    <path d="M10.2146 6V6.85714" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.2146 11.1427V11.9998" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M8.92857 11.143H10.8571C11.1413 11.143 11.4138 11.0301 11.6148 10.8292C11.8157 10.6283 11.9286 10.3557 11.9286 10.0716C11.9286 9.78742 11.8157 9.5149 11.6148 9.31397C11.4138 9.11304 11.1413 9.00016 10.8571 9.00016H9.57143C9.28727 9.00016 9.01475 8.88727 8.81381 8.68634C8.61288 8.48541 8.5 8.21289 8.5 7.92873C8.5 7.64457 8.61288 7.37205 8.81381 7.17111C9.01475 6.97018 9.28727 6.8573 9.57143 6.8573H11.5"
      stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const DiscountIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M11.1433 3.31234C11.6407 2.89589 12.3593 2.89589 12.8568 3.31234L13.9651 4.24013C14.2283 4.4604 14.564 4.57114 14.9045 4.54987L16.3385 4.46045C16.9821 4.42032 17.5634 4.84896 17.7247 5.48261L18.0841 6.89438C18.1694 7.22957 18.3769 7.51939 18.6646 7.70534L19.8765 8.48842C20.4205 8.83992 20.6425 9.53347 20.406 10.1424L19.8791 11.4988C19.754 11.8209 19.754 12.1791 19.8791 12.5012L20.406 13.8577C20.6425 14.4666 20.4205 15.1601 19.8765 15.5116L18.6646 16.2947C18.3769 16.4806 18.1694 16.7705 18.0841 17.1057L17.7247 18.5174C17.5634 19.1511 16.9821 19.5797 16.3385 19.5396L14.9045 19.4501C14.564 19.4289 14.2283 19.5396 13.9651 19.7599L12.8568 20.6877C12.3593 21.1041 11.6407 21.1041 11.1433 20.6877L10.0349 19.7599C9.77177 19.5396 9.43607 19.4289 9.09558 19.4501L7.66157 19.5396C7.01792 19.5797 6.43661 19.1511 6.27528 18.5174L5.91598 17.1057C5.83066 16.7705 5.62317 16.4806 5.33542 16.2947L4.12351 15.5116C3.57951 15.1601 3.35746 14.4666 3.59399 13.8577L4.12096 12.5012C4.24605 12.1791 4.24605 11.8209 4.12096 11.4988L3.59399 10.1424C3.35746 9.53347 3.57951 8.83992 4.12351 8.48842L5.33542 7.70534C5.62317 7.51939 5.83066 7.22957 5.91598 6.89438L6.27528 5.48261C6.43661 4.84896 7.01792 4.42032 7.66157 4.46045L9.09558 4.54987C9.43607 4.57114 9.77177 4.4604 10.0349 4.24013L11.1433 3.31234ZM15.1661 9.54105C15.3613 9.34579 15.3613 9.02921 15.1661 8.83395C14.9708 8.63868 14.6542 8.63868 14.4589 8.83395L8.83395 14.4589C8.63868 14.6542 8.63868 14.9708 8.83395 15.1661C9.02921 15.3613 9.34579 15.3613 9.54105 15.1661L15.1661 9.54105ZM11.0625 9.96875C11.0625 10.5728 10.5728 11.0625 9.96875 11.0625C9.36469 11.0625 8.875 10.5728 8.875 9.96875C8.875 9.36469 9.36469 8.875 9.96875 8.875C10.5728 8.875 11.0625 9.36469 11.0625 9.96875ZM14.0312 15.125C14.6353 15.125 15.125 14.6353 15.125 14.0312C15.125 13.4272 14.6353 12.9375 14.0312 12.9375C13.4272 12.9375 12.9375 13.4272 12.9375 14.0312C12.9375 14.6353 13.4272 15.125 14.0312 15.125Z"
          fill="#929191" />
  </svg>

)

export const DiscountIcon20 = ({ fill = '#BAB9B8' }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M9.28605 2.76028C9.7006 2.41324 10.2994 2.41324 10.714 2.76028L11.6376 3.53344C11.8569 3.717 12.1367 3.80928 12.4204 3.79156L13.6154 3.71704C14.1518 3.6836 14.6362 4.0408 14.7706 4.56884L15.0701 5.74532C15.1412 6.02464 15.3141 6.26616 15.5538 6.42112L16.5638 7.07368C17.0171 7.3666 17.2021 7.94456 17.005 8.45196L16.5659 9.58236C16.4617 9.85076 16.4617 10.1493 16.5659 10.4177L17.005 11.5481C17.2021 12.0555 17.0171 12.6334 16.5638 12.9264L15.5538 13.5789C15.3141 13.7338 15.1412 13.9754 15.0701 14.2547L14.7706 15.4312C14.6362 15.9592 14.1518 16.3164 13.6154 16.283L12.4204 16.2084C12.1367 16.1908 11.8569 16.283 11.6376 16.4666L10.714 17.2397C10.2994 17.5868 9.7006 17.5868 9.28605 17.2397L8.36244 16.4666C8.14314 16.283 7.86339 16.1908 7.57965 16.2084L6.38464 16.283C5.84827 16.3164 5.36384 15.9592 5.2294 15.4312L4.92998 14.2547C4.85888 13.9754 4.68597 13.7338 4.44618 13.5789L3.43626 12.9264C2.98293 12.6334 2.79788 12.0555 2.99499 11.5481L3.43413 10.4177C3.53838 10.1493 3.53838 9.85076 3.43413 9.58236L2.99499 8.45196C2.79788 7.94456 2.98293 7.3666 3.43626 7.07368L4.44618 6.42112C4.68597 6.26616 4.85888 6.02464 4.92998 5.74532L5.2294 4.56884C5.36384 4.0408 5.84827 3.6836 6.38464 3.71704L7.57965 3.79156C7.86339 3.80928 8.14314 3.717 8.36244 3.53344L9.28605 2.76028ZM12.6973 8.0098C12.8926 7.81454 12.8926 7.49796 12.6973 7.3027C12.502 7.10743 12.1855 7.10743 11.9902 7.3027L7.3027 11.9902C7.10743 12.1855 7.10743 12.502 7.3027 12.6973C7.49796 12.8926 7.81454 12.8926 8.0098 12.6973L12.6973 8.0098ZM9.21875 8.30729C9.21875 8.81068 8.81068 9.21875 8.30729 9.21875C7.80391 9.21875 7.39583 8.81068 7.39583 8.30729C7.39583 7.80391 7.80391 7.39583 8.30729 7.39583C8.81068 7.39583 9.21875 7.80391 9.21875 8.30729ZM11.6927 12.6042C12.1961 12.6042 12.6042 12.1961 12.6042 11.6927C12.6042 11.1893 12.1961 10.7812 11.6927 10.7812C11.1893 10.7812 10.7813 11.1893 10.7813 11.6927C10.7813 12.1961 11.1893 12.6042 11.6927 12.6042Z"
          fill={fill} />
  </svg>
)

export const PayrollIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.5005 16C5.67207 16 5.00049 15.3284 5.0005 14.5L5.00057 5.49986C5.00058 4.67141 5.67159 3.99984 6.50003 3.99987C7.94594 3.99992 10.0613 3.99999 11.0861 4.00001C11.3513 4.00001 11.6055 4.10537 11.793 4.29291L14.7072 7.20712C14.8948 7.39466 15.0001 7.64724 15.0001 7.91246C15.0001 9.60944 15.0001 12.67 15.0001 14.5005C15.0001 15.3289 14.3285 16 13.5001 16H6.5005Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0142 7V7.85714" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0142 12.1429V13" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M8.72838 12.1428H10.6569C10.9411 12.1428 11.2136 12.0299 11.4146 11.829C11.6155 11.6281 11.7284 11.3556 11.7284 11.0714C11.7284 10.7872 11.6155 10.5147 11.4146 10.3138C11.2136 10.1129 10.9411 9.99997 10.6569 9.99997H9.37123C9.08707 9.99997 8.81455 9.88709 8.61362 9.68616C8.41269 9.48523 8.2998 9.21271 8.2998 8.92855C8.2998 8.64438 8.41269 8.37186 8.61362 8.17093C8.81455 7.97 9.08707 7.85712 9.37123 7.85712H11.2998"
      stroke="white" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const GiftCardIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M4.3746 7.78642C4.37452 8.26143 4.37484 8.73643 4.37464 9.21143C4.37449 9.5432 4.64328 9.81246 4.97505 9.81246H15.0248C15.356 9.81246 15.6246 9.54417 15.6247 9.21301C15.6249 8.7377 15.6249 8.26239 15.6247 7.78707C15.6246 7.45579 15.3558 7.18743 15.0246 7.18752C11.6742 7.18842 8.32389 7.18429 4.97356 7.18723C4.64274 7.18752 4.37465 7.45561 4.3746 7.78642Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M14.6875 10C14.6875 11.375 14.6875 12.75 14.6875 14.125C14.6875 14.9534 14.0159 15.625 13.1875 15.625C11.0625 15.625 8.93749 15.625 6.81248 15.625C5.98406 15.625 5.3125 14.9534 5.31249 14.125C5.31249 12.75 5.3125 11.375 5.3125 10"
      fill="#BAB9B8" />
    <path
      d="M14.6875 10C14.6875 11.375 14.6875 12.75 14.6875 14.125C14.6875 14.9534 14.0159 15.625 13.1875 15.625C11.0625 15.625 8.93749 15.625 6.81248 15.625C5.98406 15.625 5.3125 14.9534 5.31249 14.125C5.31249 12.75 5.3125 11.375 5.3125 10"
      stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M12.8406 6.69573C12.1305 7.40588 10 7.40588 10 7.40588C10 7.40588 10 5.27542 10.7102 4.56526C10.9927 4.28301 11.3758 4.12452 11.7752 4.12463C12.1746 4.12475 12.5576 4.28346 12.84 4.56587C13.1224 4.84828 13.2811 5.23128 13.2812 5.63067C13.2814 6.03006 13.1229 6.41315 12.8406 6.69573V6.69573Z"
      stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M7.15938 6.69573C7.86953 7.40588 10 7.40588 10 7.40588C10 7.40588 10 5.27542 9.28984 4.56526C9.00727 4.28301 8.62418 4.12452 8.22479 4.12463C7.8254 4.12475 7.4424 4.28346 7.15998 4.56587C6.87757 4.84828 6.71886 5.23128 6.71875 5.63067C6.71864 6.03006 6.87713 6.41315 7.15938 6.69573V6.69573Z"
      stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 7.1875V15.625" stroke="#F2F2F2" strokeLinecap="square" />
    <path d="M15.2761 10.7498H4.96362" stroke="#F2F2F2" strokeLinecap="square" />
  </svg>
)

export const TeamIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2.5" y="4" width="15" height="12" rx="2.5" fill="#BAB9B8" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M7.37872 10.0636C8.43182 10.0636 9.28552 9.20989 9.28552 8.1568C9.28552 7.1037 8.43182 6.25 7.37872 6.25C6.32563 6.25 5.47192 7.1037 5.47192 8.1568C5.47192 9.20989 6.32563 10.0636 7.37872 10.0636Z"
          fill="#F2F2F2" />
    <path
      d="M7.41616 13.7501C8.50396 13.7501 9.50059 13.3597 10.2736 12.7114C10.4827 12.536 10.4782 12.2188 10.2646 12.0488C9.48301 11.4265 8.49313 11.0547 7.41641 11.0547C6.33956 11.0547 5.34957 11.4266 4.56792 12.049C4.35438 12.2191 4.34986 12.5362 4.55902 12.7116C5.33196 13.3598 6.32848 13.7501 7.41616 13.7501Z"
      fill="#F2F2F2" />
    <path d="M12 10.8037H15" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12 8.55371H15" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const TeamIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="4.7998" width="18" height="14.4" rx="2.5" fill="#BAB9B8" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M8.85432 12.0763C10.118 12.0763 11.1425 11.0519 11.1425 9.78816C11.1425 8.52444 10.118 7.5 8.85432 7.5C7.59061 7.5 6.56616 8.52444 6.56616 9.78816C6.56616 11.0519 7.59061 12.0763 8.85432 12.0763Z"
          fill="#EFEFEE" />
    <path
      d="M8.89944 16.5001C10.2402 16.5001 11.4655 16.0059 12.4031 15.1897C12.609 15.0105 12.6045 14.6944 12.394 14.5206C11.4446 13.7366 10.2272 13.2656 8.89974 13.2656C7.57217 13.2656 6.35462 13.7367 5.40514 14.5208C5.19467 14.6946 5.19016 15.0108 5.39607 15.19C6.33368 16.006 7.55886 16.5001 8.89944 16.5001Z"
      fill="#EFEFEE" />
    <path d="M14.4001 12.9644H18.0001" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.4001 10.2646H18.0001" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const BoxIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <mask id="path-1-inside-1_438_4199" fill="white">
      <path fillRule="evenodd" clipRule="evenodd"
            d="M3.52407 8.70719C3.52452 9.95763 3.52497 11.2081 3.52428 12.4585C3.52387 13.1994 3.93373 13.878 4.57907 14.242C5.1889 14.586 5.79348 14.9408 6.39807 15.2957L6.39809 15.2957L6.39811 15.2957C7.29415 15.8217 8.1902 16.3476 9.10331 16.838C9.22892 16.9055 9.36021 16.9577 9.49463 16.9946L9.56384 10.2921L3.60325 6.96152C3.55109 7.14072 3.52363 7.32867 3.52368 7.52057L3.52407 8.70719ZM10.4947 16.9945L10.5639 10.2915L13.6745 8.51284C13.6889 8.5055 13.703 8.49744 13.7167 8.48868L16.3863 6.96223C16.4352 7.13624 16.461 7.31832 16.461 7.5041V12.4967C16.461 13.2185 16.0716 13.8837 15.4466 14.2448C15.0934 14.4489 14.7405 14.6546 14.3874 14.8605L14.3873 14.8606L14.3871 14.8607L14.387 14.8607C13.2273 15.5367 12.0654 16.2139 10.8826 16.8407C10.758 16.9067 10.6278 16.958 10.4947 16.9945ZM13.4439 7.49275L15.8833 6.09791C15.7547 5.96786 15.6072 5.85442 15.4435 5.76208L10.9774 3.24314C10.3661 2.89838 9.61888 2.89923 9.00839 3.24539L7.43712 4.13635L13.4439 7.49275ZM6.4196 4.71331L4.53701 5.7808C4.3803 5.86965 4.23857 5.97787 4.11414 6.10147L10.0634 9.42575L12.4307 8.07212L6.67985 4.85873L6.4196 4.71331Z" />
    </mask>
    <path fillRule="evenodd" clipRule="evenodd"
          d="M3.52407 8.70719C3.52452 9.95763 3.52497 11.2081 3.52428 12.4585C3.52387 13.1994 3.93373 13.878 4.57907 14.242C5.1889 14.586 5.79348 14.9408 6.39807 15.2957L6.39809 15.2957L6.39811 15.2957C7.29415 15.8217 8.1902 16.3476 9.10331 16.838C9.22892 16.9055 9.36021 16.9577 9.49463 16.9946L9.56384 10.2921L3.60325 6.96152C3.55109 7.14072 3.52363 7.32867 3.52368 7.52057L3.52407 8.70719ZM10.4947 16.9945L10.5639 10.2915L13.6745 8.51284C13.6889 8.5055 13.703 8.49744 13.7167 8.48868L16.3863 6.96223C16.4352 7.13624 16.461 7.31832 16.461 7.5041V12.4967C16.461 13.2185 16.0716 13.8837 15.4466 14.2448C15.0934 14.4489 14.7405 14.6546 14.3874 14.8605L14.3873 14.8606L14.3871 14.8607L14.387 14.8607C13.2273 15.5367 12.0654 16.2139 10.8826 16.8407C10.758 16.9067 10.6278 16.958 10.4947 16.9945ZM13.4439 7.49275L15.8833 6.09791C15.7547 5.96786 15.6072 5.85442 15.4435 5.76208L10.9774 3.24314C10.3661 2.89838 9.61888 2.89923 9.00839 3.24539L7.43712 4.13635L13.4439 7.49275ZM6.4196 4.71331L4.53701 5.7808C4.3803 5.86965 4.23857 5.97787 4.11414 6.10147L10.0634 9.42575L12.4307 8.07212L6.67985 4.85873L6.4196 4.71331Z"
          fill="#BAB9B8" />
    <path
      d="M3.52428 12.4585L4.52428 12.459L3.52428 12.4585ZM3.52407 8.70719L2.52407 8.70752L2.52407 8.70755L3.52407 8.70719ZM4.57907 14.242L4.0878 15.113H4.0878L4.57907 14.242ZM6.39807 15.2957L5.89187 16.1581C5.90199 16.164 5.91222 16.1698 5.92255 16.1754L6.39807 15.2957ZM6.39809 15.2957L6.93715 14.4534C6.91643 14.4402 6.89524 14.4277 6.87361 14.416L6.39809 15.2957ZM6.39811 15.2957L5.85906 16.138C5.86988 16.1449 5.88083 16.1516 5.89191 16.1581L6.39811 15.2957ZM9.10331 16.838L8.63016 17.719L8.63016 17.719L9.10331 16.838ZM9.49463 16.9946L9.22952 17.9588C9.52864 18.0411 9.84899 17.9798 10.0967 17.7931C10.3444 17.6063 10.4914 17.3151 10.4946 17.0049L9.49463 16.9946ZM9.56384 10.2921L10.5638 10.3025C10.5676 9.93648 10.3711 9.59769 10.0516 9.41916L9.56384 10.2921ZM3.60325 6.96152L4.09103 6.08856C3.82507 5.93995 3.50588 5.92068 3.22398 6.03623C2.94208 6.15178 2.72823 6.38953 2.64309 6.68205L3.60325 6.96152ZM3.52368 7.52057L2.52368 7.52084V7.5209L3.52368 7.52057ZM10.5639 10.2915L10.0675 9.4234C9.75928 9.59965 9.56762 9.92613 9.56395 10.2812L10.5639 10.2915ZM10.4947 16.9945L9.49473 16.9842C9.49149 17.2979 9.63565 17.595 9.8841 17.7865C10.1326 17.978 10.4565 18.0419 10.759 17.959L10.4947 16.9945ZM13.6745 8.51284L13.2209 7.62162C13.2065 7.62898 13.1922 7.63669 13.1781 7.64474L13.6745 8.51284ZM13.7167 8.48868L13.2203 7.62058C13.2062 7.62865 13.1923 7.63706 13.1786 7.6458L13.7167 8.48868ZM16.3863 6.96223L17.3488 6.69123C17.2654 6.39482 17.05 6.15338 16.765 6.03674C16.48 5.9201 16.1572 5.94127 15.8899 6.09413L16.3863 6.96223ZM15.4466 14.2448L15.947 15.1107L15.947 15.1107L15.4466 14.2448ZM14.3874 14.8605L14.888 15.7262L14.891 15.7244L14.3874 14.8605ZM14.3871 14.8607L13.8866 13.9949C13.8786 13.9995 13.8708 14.0042 13.863 14.009L14.3871 14.8607ZM14.387 14.8607L14.8906 15.7246C14.8975 15.7206 14.9043 15.7165 14.9111 15.7123L14.387 14.8607ZM10.8826 16.8407L10.4144 15.9571H10.4144L10.8826 16.8407ZM13.4439 7.49275L12.9561 8.36571C13.2623 8.53681 13.6358 8.53497 13.9403 8.36085L13.4439 7.49275ZM15.8833 6.09791L16.3796 6.96601C16.6493 6.81182 16.832 6.5411 16.874 6.23335C16.9161 5.9256 16.8128 5.61577 16.5944 5.39488L15.8833 6.09791ZM15.4435 5.76208L14.9523 6.63309L14.9523 6.63309L15.4435 5.76208ZM10.9774 3.24314L10.4862 4.11416L10.9774 3.24314ZM9.00839 3.24539L8.51514 2.37551V2.37551L9.00839 3.24539ZM7.43712 4.13635L6.94387 3.26646C6.6297 3.44461 6.43599 3.77832 6.43713 4.13949C6.43826 4.50065 6.63405 4.83314 6.94934 5.00931L7.43712 4.13635ZM6.4196 4.71331L6.90739 3.84035C6.6023 3.66988 6.23036 3.67104 5.92635 3.84343L6.4196 4.71331ZM4.53701 5.7808L4.04376 4.91091H4.04376L4.53701 5.7808ZM4.11414 6.10147L3.40942 5.39198C3.18667 5.61322 3.08076 5.92624 3.1234 6.23728C3.16604 6.54832 3.35228 6.82129 3.62635 6.97443L4.11414 6.10147ZM10.0634 9.42575L9.57562 10.2987C9.88183 10.4698 10.2553 10.468 10.5598 10.2938L10.0634 9.42575ZM12.4307 8.07212L12.9271 8.94022C13.24 8.76126 13.4325 8.42769 13.4307 8.06718C13.4289 7.70666 13.2332 7.37501 12.9185 7.19915L12.4307 8.07212ZM6.67985 4.85873L6.19207 5.7317L6.67985 4.85873ZM4.52428 12.459C4.52497 11.2081 4.52452 9.95719 4.52407 8.70683L2.52407 8.70755C2.52452 9.95806 2.52497 11.208 2.52428 12.4579L4.52428 12.459ZM5.07033 13.371C4.72826 13.1781 4.52407 12.8262 4.52428 12.459L2.52428 12.4579C2.52366 13.5726 3.13921 14.578 4.0878 15.113L5.07033 13.371ZM6.90427 14.4333C6.30093 14.0791 5.68892 13.7199 5.07033 13.371L4.0878 15.113C4.68888 15.452 5.28604 15.8025 5.89187 16.1581L6.90427 14.4333ZM6.87361 14.416L6.87358 14.416L5.92255 16.1754L5.92257 16.1754L6.87361 14.416ZM6.93716 14.4534L6.93715 14.4534L5.85904 16.138L5.85906 16.138L6.93716 14.4534ZM9.57647 15.957C8.68284 15.4771 7.80311 14.9609 6.90431 14.4333L5.89191 16.1581C6.7852 16.6825 7.69755 17.2181 8.63016 17.719L9.57647 15.957ZM9.75973 16.0304C9.69692 16.0131 9.63548 15.9887 9.57647 15.957L8.63016 17.719C8.82237 17.8222 9.0235 17.9022 9.22952 17.9588L9.75973 16.0304ZM8.56389 10.2818L8.49468 16.9843L10.4946 17.0049L10.5638 10.3025L8.56389 10.2818ZM3.11546 7.83448L9.07605 11.1651L10.0516 9.41916L4.09103 6.08856L3.11546 7.83448ZM4.52368 7.5203C4.52366 7.42415 4.53739 7.33035 4.5634 7.24099L2.64309 6.68205C2.56478 6.95109 2.5236 7.23319 2.52368 7.52084L4.52368 7.5203ZM4.52407 8.70686L4.52368 7.52024L2.52368 7.5209L2.52407 8.70752L4.52407 8.70686ZM9.56395 10.2812L9.49473 16.9842L11.4946 17.0049L11.5638 10.3018L9.56395 10.2812ZM13.1781 7.64474L10.0675 9.4234L11.0603 11.1596L14.1709 9.38094L13.1781 7.64474ZM13.1786 7.6458C13.1924 7.637 13.2065 7.62894 13.2209 7.62162L14.1281 9.40407C14.1713 9.38205 14.2136 9.35788 14.2548 9.33157L13.1786 7.6458ZM15.8899 6.09413L13.2203 7.62058L14.2131 9.35679L16.8826 7.83033L15.8899 6.09413ZM17.461 7.5041C17.461 7.22568 17.4224 6.95245 17.3488 6.69123L15.4237 7.23323C15.4481 7.32003 15.461 7.41095 15.461 7.5041H17.461ZM17.461 12.4967V7.5041H15.461V12.4967H17.461ZM15.947 15.1107C16.8766 14.5735 17.461 13.5809 17.461 12.4967H15.461C15.461 12.8561 15.2667 13.1939 14.9463 13.379L15.947 15.1107ZM14.891 15.7244C15.2444 15.5184 15.5957 15.3137 15.947 15.1107L14.9463 13.379C14.5912 13.5842 14.2367 13.7909 13.8839 13.9965L14.891 15.7244ZM14.8878 15.7263L14.8879 15.7262L13.8869 13.9947L13.8868 13.9948L14.8878 15.7263ZM14.8876 15.7264L14.8878 15.7263L13.8868 13.9948L13.8866 13.9949L14.8876 15.7264ZM14.9111 15.7123L14.9112 15.7123L13.863 14.009L13.8629 14.009L14.9111 15.7123ZM11.3508 17.7243C12.5545 17.0865 13.7339 16.3989 14.8906 15.7246L13.8835 13.9967C12.7208 14.6744 11.5763 15.3414 10.4144 15.9571L11.3508 17.7243ZM10.759 17.959C10.9624 17.9032 11.161 17.8249 11.3508 17.7243L10.4144 15.9571C10.355 15.9886 10.2932 16.0129 10.2303 16.0301L10.759 17.959ZM13.9403 8.36085L16.3796 6.96601L15.3869 5.22981L12.9475 6.62465L13.9403 8.36085ZM16.5944 5.39488C16.4014 5.19966 16.1801 5.02945 15.9348 4.89107L14.9523 6.63309C15.0344 6.67939 15.108 6.73606 15.1721 6.80094L16.5944 5.39488ZM15.9348 4.89107L11.4687 2.37213L10.4862 4.11416L14.9523 6.63309L15.9348 4.89107ZM11.4687 2.37213C10.5518 1.85498 9.43087 1.85626 8.51514 2.37551L9.50164 4.11528C9.80689 3.9422 10.1805 3.94177 10.4862 4.11416L11.4687 2.37213ZM8.51514 2.37551L6.94387 3.26646L7.93037 5.00624L9.50164 4.11528L8.51514 2.37551ZM6.94934 5.00931L12.9561 8.36571L13.9317 6.61979L7.92491 3.26339L6.94934 5.00931ZM5.92635 3.84343L4.04376 4.91091L5.03026 6.65068L6.91285 5.5832L5.92635 3.84343ZM4.04376 4.91091C3.80892 5.04407 3.59623 5.20643 3.40942 5.39198L4.81885 6.81096C4.88092 6.74931 4.95168 6.69524 5.03026 6.65068L4.04376 4.91091ZM3.62635 6.97443L9.57562 10.2987L10.5512 8.55278L4.60192 5.2285L3.62635 6.97443ZM10.5598 10.2938L12.9271 8.94022L11.9343 7.20402L9.56702 8.55765L10.5598 10.2938ZM12.9185 7.19915L7.16764 3.98577L6.19207 5.7317L11.9429 8.94508L12.9185 7.19915ZM7.16764 3.98577L6.90739 3.84035L5.93182 5.58628L6.19207 5.7317L7.16764 3.98577Z"
      fill="#BAB9B8" mask="url(#path-1-inside-1_438_4199)" />
  </svg>
)

export const DownloadIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.5005 16C5.67207 16 5.00049 15.3284 5.0005 14.5L5.00057 5.49986C5.00058 4.67141 5.67159 3.99984 6.50003 3.99987C7.94594 3.99992 10.0613 3.99999 11.0861 4.00001C11.3513 4.00001 11.6055 4.10537 11.793 4.29291L14.7072 7.20712C14.8948 7.39466 15.0001 7.64724 15.0001 7.91246C15.0001 9.60944 15.0001 12.67 15.0001 14.5005C15.0001 15.3289 14.3285 16 13.5001 16H6.5005Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.87012 10.6289L10.0014 12.7502L12.1298 10.6289" stroke="#F9F9F9" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M10.001 7.75V12.75" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const CalendarBlankIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.0004 4.5H6.00379C4.89774 4.5 4.00176 5.39774 4.00257 6.50379C4.00429 8.83585 4.00204 11.1679 4.00095 13.5C4.00044 14.6046 4.89585 15.5 6.00042 15.5H13.0004C14.105 15.5 15.0004 14.6046 15.0004 13.5V6.5C15.0004 5.39543 14.105 4.5 13.0004 4.5Z"
      stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.0005 3.5V5.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.00049 3.5V5.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M15.0005 8H4.00049V13.5C4.00049 14.6046 4.89592 15.5 6.00049 15.5H13.0005C14.1051 15.5 15.0005 14.6046 15.0005 13.5V8Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="square" strokeLinejoin="round" />
  </svg>

)

export const ArrowLeftIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10 17C11.3845 17 12.7378 16.5895 13.889 15.8203C15.0401 15.0511 15.9373 13.9579 16.4672 12.6788C16.997 11.3997 17.1356 9.99224 16.8655 8.63437C16.5954 7.2765 15.9287 6.02922 14.9497 5.05025C13.9708 4.07129 12.7235 3.4046 11.3656 3.13451C10.0078 2.86441 8.6003 3.00303 7.32122 3.53284C6.04213 4.06266 4.94888 4.95987 4.17971 6.11101C3.41054 7.26215 3 8.61553 3 10C3.00214 11.8559 3.74033 13.6351 5.05262 14.9474C6.36491 16.2597 8.14414 16.9979 10 17ZM9.98375 7.51568C10.1795 7.71048 10.1802 8.02707 9.98541 8.22279L8.71327 9.50093L12.5099 9.50093C12.7861 9.50093 13.0099 9.72478 13.0099 10.0009C13.0099 10.2771 12.7861 10.5009 12.5099 10.5009L8.71355 10.5009L9.98517 11.7767C10.1801 11.9723 10.1796 12.2889 9.984 12.4839C9.78841 12.6788 9.47183 12.6783 9.27689 12.4827L7.15558 10.3543C6.99213 10.1904 6.96604 9.94122 7.07734 9.75004C7.10117 9.70903 7.13067 9.67172 7.16476 9.63918L9.27664 7.51734C9.47145 7.32162 9.78803 7.32088 9.98375 7.51568Z"
          fill="#BAB9B8" />
  </svg>

)

export const BadgeIconGreen16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.4001 9.59961V12.7909C10.4001 13.1626 10.009 13.4043 9.67652 13.2381L8.22336 12.5116C8.08259 12.4413 7.91691 12.4413 7.77615 12.5117L6.32373 13.238C5.99128 13.4042 5.6001 13.1625 5.6001 12.7908V9.59996"
      fill="#4D9985" />
    <path
      d="M10.4001 9.59961V12.7909C10.4001 13.1626 10.009 13.4043 9.67652 13.2381L8.22336 12.5116C8.08259 12.4413 7.91691 12.4413 7.77615 12.5117L6.32373 13.238C5.99128 13.4042 5.6001 13.1625 5.6001 12.7908V9.59996"
      stroke="#4D9985" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M7.99976 10.3999C10.2089 10.3999 11.9998 8.60904 11.9998 6.3999C11.9998 4.19076 10.2089 2.3999 7.99976 2.3999C5.79062 2.3999 3.99976 4.19076 3.99976 6.3999C3.99976 8.60904 5.79062 10.3999 7.99976 10.3999Z"
      fill="#E1EAE8" stroke="#4D9985" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M9.39985 6.4C9.39985 7.1732 8.77305 7.8 7.99985 7.8C7.22665 7.8 6.59985 7.1732 6.59985 6.4C6.59985 5.6268 7.22665 5 7.99985 5C8.77305 5 9.39985 5.6268 9.39985 6.4Z"
      fill="#4D9985" stroke="#4D9985" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const NoItemsIllustration = () => (
  <svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.69052 21.7142H40.8334L35.5273 73.1428H8.99664L3.69052 21.7142Z" fill="#BAB9B8" />
    <path d="M43.6905 21.7142L0.833378 21.7142L4.09868 15.9999L40.4252 16L43.6905 21.7142Z" fill="#929191" />
    <rect x="76.5477" y="20.2858" width="4.28571" height="41.4286" transform="rotate(30 76.5477 20.2858)"
          fill="#C4C4C4" />
    <path d="M5.11908 34.5714H39.4048L37.2619 57.4286H7.26194L5.11908 34.5714Z" fill="#D8D7D6" />
    <path
      d="M52.2619 73.1428C68.0415 73.1428 80.8334 60.351 80.8334 44.5714H23.6905C23.6905 60.351 36.4824 73.1428 52.2619 73.1428Z"
      fill="#929191" />
  </svg>

)

export const NoPendingItemsIllustration = () => (
  <svg width="81" height="58" viewBox="0 0 81 58" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.69049 5.71436L40.8333 5.71436L35.5272 57.1429H8.99661L3.69049 5.71436Z" fill="#BAB9B8" />
    <path d="M43.6905 5.71436L0.833347 5.71435L4.09865 6.99717e-05L40.4252 7.31475e-05L43.6905 5.71436Z"
          fill="#929191" />
    <rect x="76.5477" y="4.28564" width="4.28571" height="41.4286" transform="rotate(30 76.5477 4.28564)"
          fill="#C4C4C4" />
    <path d="M5.11908 18.5713H39.4048L37.2619 41.4284H7.26194L5.11908 18.5713Z" fill="#D8D7D6" />
    <path
      d="M52.2619 57.1427C68.0415 57.1427 80.8333 44.3509 80.8333 28.5713H23.6905C23.6905 44.3509 36.4824 57.1427 52.2619 57.1427Z"
      fill="#929191" />
  </svg>

)

export const AlloIllustration = () => (
  <svg width="79" height="32" viewBox="0 0 79 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M28.7969 31.4005L33.7635 31.4005L33.7635 0.596923L28.7969 0.596924L28.7969 31.4005Z" fill="#F9F9F9" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M62.873 32C71.7096 32 78.873 24.8366 78.873 16C78.873 7.16344 71.7096 0 62.873 0C54.0365 0 46.873 7.16344 46.873 16C46.873 24.8366 54.0365 32 62.873 32ZM62.8725 27.0339C68.9661 27.0339 73.9059 22.0941 73.9059 16.0005C73.9059 9.90699 68.9661 4.96719 62.8725 4.96719C56.779 4.96719 51.8392 9.90699 51.8392 16.0005C51.8392 22.0941 56.779 27.0339 62.8725 27.0339Z"
          fill="#F9F9F9" />
    <path d="M38.1357 31.3547H43.1025V0.596924C40.3594 0.596924 38.1357 2.8206 38.1357 5.56364V31.3547Z"
          fill="#F9F9F9" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M19.4696 10.2786C17.557 8.67721 15.1879 7.57227 12.2143 7.57227C5.46851 7.57227 0 13.0408 0 19.7865C0 26.5323 5.46851 32.0008 12.2143 32.0008C15.1872 32.0008 17.557 30.9055 19.4696 29.3043L19.6148 31.4037H24.4287L24.4287 8.17075L19.6165 8.17075L19.4696 10.2786ZM19.4689 19.787C19.4689 23.7939 16.2207 27.0421 12.2138 27.0421C8.20698 27.0421 4.95877 23.7939 4.95877 19.787C4.95877 15.7801 8.20698 12.5319 12.2138 12.5319C16.2207 12.5319 19.4689 15.7801 19.4689 19.787Z"
          fill="#F9F9F9" />
  </svg>

)

export const MoreOptionsIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M8 4C8.55228 4 9 3.55228 9 3C9 2.44772 8.55228 2 8 2C7.44772 2 7 2.44772 7 3C7 3.55228 7.44772 4 8 4ZM8 9C8.55228 9 9 8.55228 9 8C9 7.44772 8.55228 7 8 7C7.44772 7 7 7.44772 7 8C7 8.55228 7.44772 9 8 9ZM9 13C9 13.5523 8.55228 14 8 14C7.44772 14 7 13.5523 7 13C7 12.4477 7.44772 12 8 12C8.55228 12 9 12.4477 9 13Z"
          fill="#333332" />
  </svg>

)

export const MoreOptionsIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10 6C10.5523 6 11 5.55228 11 5C11 4.44772 10.5523 4 10 4C9.44772 4 9 4.44772 9 5C9 5.55228 9.44772 6 10 6ZM10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11ZM11 15C11 15.5523 10.5523 16 10 16C9.44772 16 9 15.5523 9 15C9 14.4477 9.44772 14 10 14C10.5523 14 11 14.4477 11 15Z"
          fill="#333332" />
  </svg>
)

export const CloseDialogIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.625 4.375L4.375 15.625" stroke="#333332" strokeWidth="2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M15.625 15.625L4.375 4.375" stroke="#333332" strokeWidth="2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>

)

export const CollapseIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M13.6112 15.4046C12.5423 16.1188 11.2856 16.5 10 16.5C8.2767 16.498 6.62456 15.8125 5.406 14.594C4.18745 13.3754 3.50199 11.7233 3.5 10C3.5 8.71442 3.88122 7.45771 4.59545 6.38879C5.30968 5.31987 6.32484 4.48675 7.51256 3.99478C8.70028 3.50281 10.0072 3.37409 11.2681 3.6249C12.529 3.8757 13.6872 4.49476 14.5962 5.40381C15.5052 6.31285 16.1243 7.47104 16.3751 8.73191C16.6259 9.99279 16.4972 11.2997 16.0052 12.4874C15.5132 13.6752 14.6801 14.6903 13.6112 15.4046ZM12.4589 11.1536C12.6542 11.3489 12.9708 11.3489 13.1661 11.1536C13.3613 10.9583 13.3613 10.6418 13.1661 10.4465L10.3536 7.63399C10.1583 7.43873 9.84171 7.43873 9.64645 7.63399L6.83395 10.4465C6.63868 10.6418 6.63868 10.9583 6.83395 11.1536C7.02921 11.3489 7.34579 11.3489 7.54105 11.1536L10 8.69465L12.4589 11.1536Z"
          fill="#929191" />
  </svg>

)

export const UserIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 10.1203 16.997 10.2398 16.991 10.3586C16.9011 12.1404 16.145 13.7464 14.9667 14.9327C13.6987 16.2095 11.9417 17 10 17C8.05835 17 6.30134 16.2095 5.03327 14.9327C3.7765 13.6673 3 11.9243 3 10ZM5.66669 14.15C6.75885 15.2901 8.29652 16 10 16C11.7035 16 13.2411 15.2901 14.3333 14.15C13.2411 13.0099 11.7035 12.3 10 12.3C8.29652 12.3 6.75885 13.0099 5.66669 14.15ZM12.4497 8.5C12.4497 9.88071 11.3304 11 9.94973 11C8.56902 11 7.44973 9.88071 7.44973 8.5C7.44973 7.11929 8.56902 6 9.94973 6C11.3304 6 12.4497 7.11929 12.4497 8.5Z"
          fill="#929191" />
  </svg>

)

export const ReceiptDarkIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14.4079 3.71802C13.9801 3.50003 13.4201 3.50003 12.3 3.50003L7.7121 3.5C6.59516 3.49999 6.03669 3.49999 5.60941 3.71728C5.23368 3.90837 4.92768 4.21359 4.73564 4.58883C4.51725 5.01555 4.51583 5.57372 4.51299 6.69007C4.50697 9.05144 4.50007 12.1945 4.50002 14.2036L4.5 15.9991C4.5 16.1778 4.59497 16.3429 4.74925 16.4324C4.90353 16.522 5.09375 16.5225 5.24849 16.4337L6.50747 15.7115C6.59441 15.6616 6.63788 15.6366 6.68406 15.6269C6.72492 15.6182 6.76713 15.6182 6.80799 15.6269C6.85417 15.6366 6.89764 15.6616 6.98458 15.7115L8.24356 16.4337C8.39749 16.5221 8.58661 16.5221 8.74054 16.4337L9.99952 15.7115C10.0865 15.6616 10.1299 15.6366 10.1761 15.6269C10.217 15.6182 10.2592 15.6182 10.3 15.6269C10.3462 15.6366 10.3897 15.6616 10.4766 15.7115L11.7356 16.4337C11.8895 16.5221 12.0787 16.5221 12.2326 16.4337L13.4048 15.7613C13.5063 15.703 13.557 15.6739 13.6103 15.6651C13.6574 15.6573 13.7056 15.6608 13.7511 15.6755C13.8025 15.692 13.8484 15.7282 13.9403 15.8007L14.6906 16.3926C14.8411 16.5113 15.046 16.5335 15.2183 16.4497C15.3906 16.3659 15.5 16.191 15.5 15.9991V13.9957V6.69916C15.5 5.57964 15.5 5.01988 15.282 4.59205C15.0902 4.21573 14.7843 3.90977 14.4079 3.71802ZM6.75 6.49998C6.75 6.22384 6.97386 5.99998 7.25 5.99998H12.75C13.0261 5.99998 13.25 6.22384 13.25 6.49998C13.25 6.77613 13.0261 6.99998 12.75 6.99998H7.25C6.97386 6.99998 6.75 6.77613 6.75 6.49998ZM6.75 8.99998C6.75 8.72384 6.97386 8.49998 7.25 8.49998H12.75C13.0261 8.49998 13.25 8.72384 13.25 8.99998C13.25 9.27612 13.0261 9.49998 12.75 9.49998H7.25C6.97386 9.49998 6.75 9.27612 6.75 8.99998Z"
          fill="#929191" />
  </svg>

)

export const RevenueDarkIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z"
      fill="#929191" />
    <path d="M10 6.5V7.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 12.5V13.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M8.5 12.5H10.75C11.0815 12.5 11.3995 12.3683 11.6339 12.1339C11.8683 11.8995 12 11.5815 12 11.25C12 10.9185 11.8683 10.6005 11.6339 10.3661C11.3995 10.1317 11.0815 10 10.75 10H9.25C8.91848 10 8.60054 9.8683 8.36612 9.63388C8.1317 9.39946 8 9.08152 8 8.75C8 8.41848 8.1317 8.10054 8.36612 7.86612C8.60054 7.6317 8.91848 7.5 9.25 7.5H11.5"
      stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const SortIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.11101 4.17971C7.26215 3.41054 8.61553 3 10 3C11.8559 3.00214 13.6351 3.74033 14.9474 5.05262C16.2597 6.36491 16.9979 8.14414 17 10C17 11.3845 16.5895 12.7378 15.8203 13.889C15.0511 15.0401 13.9579 15.9373 12.6788 16.4672C11.3997 16.997 9.99224 17.1356 8.63437 16.8655C7.2765 16.5954 6.02922 15.9287 5.05026 14.9497C4.07129 13.9708 3.4046 12.7235 3.13451 11.3656C2.86441 10.0078 3.00303 8.6003 3.53285 7.32122C4.06266 6.04213 4.95987 4.94888 6.11101 4.17971ZM12.2507 6.75C12.1226 6.74992 11.9945 6.79874 11.8968 6.89645L10.2968 8.49645C10.1016 8.69171 10.1016 9.00829 10.2968 9.20355C10.4921 9.39882 10.8087 9.39882 11.0039 9.20355L11.751 8.45652V12.75C11.751 13.0261 11.9748 13.25 12.251 13.25C12.5271 13.25 12.751 13.0261 12.751 12.75V8.45769L13.4968 9.20355C13.6921 9.39882 14.0087 9.39882 14.2039 9.20355C14.3992 9.00829 14.3992 8.69171 14.2039 8.49645L12.6187 6.91117C12.5273 6.81208 12.3964 6.75 12.251 6.75H12.2507ZM8.27344 7.25C8.27344 6.97386 8.04958 6.75 7.77344 6.75C7.49729 6.75 7.27344 6.97386 7.27344 7.25V11.1504H6.15039L7.27344 12.2734V12.75C7.27344 13.0261 7.4973 13.25 7.77344 13.25C8.04958 13.25 8.27344 13.0261 8.27344 12.75V12.2273L9.35039 11.1504H8.27344V7.25Z"
          fill="#BAB9B8" />
  </svg>

)

export const FilterIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M16.2448 5.09217C16.1681 4.91505 16.041 4.76444 15.8792 4.65913C15.7175 4.55382 15.5283 4.49849 15.3353 4.50003H4.65469C4.46201 4.50004 4.27349 4.55601 4.11202 4.66114C3.95056 4.76627 3.82311 4.91604 3.74517 5.09225C3.66723 5.26845 3.64214 5.46351 3.67297 5.6537C3.70379 5.84389 3.7892 6.02104 3.91881 6.1636L7.74589 10.3733C7.91323 10.5574 8.00596 10.7973 8.00596 11.046V15.5052C8.00583 15.6852 8.05461 15.8619 8.14709 16.0164C8.23956 16.1709 8.37226 16.2974 8.53101 16.3824C8.68976 16.4673 8.86861 16.5076 9.04845 16.4988C9.2283 16.4901 9.40238 16.4326 9.55213 16.3326L11.5411 15.0067C11.6775 14.916 11.7893 14.793 11.8666 14.6486C11.9439 14.5042 11.9842 14.3429 11.9841 14.1792V11.0461C11.9841 10.7973 12.0768 10.5575 12.2441 10.3734L16.0712 6.16354C16.2022 6.02176 16.2885 5.84453 16.3194 5.654C16.3502 5.46346 16.3243 5.26806 16.2448 5.09217Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" />
  </svg>


)

export const PropertiesIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 8.75C11.3807 8.75 12.5 7.63071 12.5 6.25C12.5 4.86929 11.3807 3.75 10 3.75C8.61929 3.75 7.5 4.86929 7.5 6.25C7.5 7.63071 8.61929 8.75 10 8.75Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M13.75 15.25C15.1307 15.25 16.25 14.1307 16.25 12.75C16.25 11.3693 15.1307 10.25 13.75 10.25C12.3693 10.25 11.25 11.3693 11.25 12.75C11.25 14.1307 12.3693 15.25 13.75 15.25Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M6.25 15.25C7.63071 15.25 8.75 14.1307 8.75 12.75C8.75 11.3693 7.63071 10.25 6.25 10.25C4.86929 10.25 3.75 11.3693 3.75 12.75C3.75 14.1307 4.86929 15.25 6.25 15.25Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
  </svg>


)

export const FileIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.40302 3.50005L10.3558 3.5C11.0326 3.49999 11.6805 3.7744 12.1514 4.2605L14.3891 6.5704C14.8409 7.03675 15.0935 7.66055 15.0935 8.30986V14.4835C15.0935 15.5972 14.1866 16.4944 13.0739 16.4851C11.3685 16.4709 8.56499 16.4549 6.46023 16.4802C5.33896 16.4937 4.40573 15.5925 4.40553 14.4616C4.40535 13.4488 4.40466 12.4357 4.40396 11.4224C4.4026 9.44718 4.40125 7.47134 4.40372 5.49596C4.40509 4.39258 5.30038 3.50002 6.40302 3.50005Z"
          fill="#BAB9B8" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M12.5004 7.70013H13.2416C13.6051 7.70013 13.7868 7.70013 13.871 7.62825C13.944 7.56588 13.9827 7.47232 13.9752 7.37659C13.9665 7.26625 13.838 7.13774 13.581 6.88072L11.7198 5.01954C11.4628 4.76251 11.3343 4.634 11.2239 4.62532C11.1282 4.61778 11.0346 4.65653 10.9723 4.72956C10.9004 4.81372 10.9004 4.99546 10.9004 5.35895V6.10013C10.9004 6.66018 10.9004 6.94021 11.0094 7.15412C11.1053 7.34228 11.2582 7.49526 11.4464 7.59113C11.6603 7.70013 11.9403 7.70013 12.5004 7.70013ZM7.3125 10.4999C7.3125 10.2238 7.53636 9.99993 7.8125 9.99993H11.6875C11.9636 9.99993 12.1875 10.2238 12.1875 10.4999C12.1875 10.7761 11.9636 10.9999 11.6875 10.9999H7.8125C7.53636 10.9999 7.3125 10.7761 7.3125 10.4999ZM7.8125 11.9999C7.53636 11.9999 7.3125 12.2238 7.3125 12.4999C7.3125 12.7761 7.53636 12.9999 7.8125 12.9999H11.6875C11.9636 12.9999 12.1875 12.7761 12.1875 12.4999C12.1875 12.2238 11.9636 11.9999 11.6875 11.9999H7.8125Z"
          fill="#F9F9F9" />
  </svg>

)

export const TableIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M7.15899 3.1499C6.82762 3.1499 6.55899 3.41853 6.55899 3.7499C6.55899 4.08127 6.82762 4.3499 7.15899 4.3499H9.9999H12.8408C13.1722 4.3499 13.4408 4.08127 13.4408 3.7499C13.4408 3.41853 13.1722 3.1499 12.8408 3.1499H9.9999H7.15899ZM4.3499 7.15899C4.3499 6.82762 4.08127 6.55899 3.7499 6.55899C3.41853 6.55899 3.1499 6.82762 3.1499 7.15899V9.9999V12.8408C3.1499 13.1722 3.41853 13.4408 3.7499 13.4408C4.08127 13.4408 4.3499 13.1722 4.3499 12.8408V9.9999V7.15899ZM16.8499 7.15899C16.8499 6.82762 16.5813 6.55899 16.2499 6.55899C15.9185 6.55899 15.6499 6.82762 15.6499 7.15899V9.9999V12.8408C15.6499 13.1722 15.9185 13.4408 16.2499 13.4408C16.5813 13.4408 16.8499 13.1722 16.8499 12.8408V9.9999V7.15899ZM7.15899 15.6499C6.82762 15.6499 6.55899 15.9185 6.55899 16.2499C6.55899 16.5813 6.82762 16.8499 7.15899 16.8499H9.9999H12.8408C13.1722 16.8499 13.4408 16.5813 13.4408 16.2499C13.4408 15.9185 13.1722 15.6499 12.8408 15.6499H9.9999H7.15899ZM12.6258 5.522C12.3552 5.49989 12.023 5.4999 11.6206 5.4999H11.6206H8.37925H8.37924C7.97676 5.4999 7.64459 5.49989 7.37399 5.522C7.09294 5.54496 6.83459 5.59424 6.59192 5.71789C6.2156 5.90964 5.90964 6.2156 5.71789 6.59192C5.59424 6.83459 5.54496 7.09294 5.522 7.37399C5.49989 7.64459 5.4999 7.97676 5.4999 8.37924V8.37925V11.6206V11.6206C5.4999 12.023 5.49989 12.3552 5.522 12.6258C5.54496 12.9069 5.59424 13.1652 5.71789 13.4079C5.90964 13.7842 6.2156 14.0902 6.59192 14.2819C6.83459 14.4056 7.09294 14.4548 7.37399 14.4778C7.64457 14.4999 7.97673 14.4999 8.37919 14.4999H8.37924H11.6206H11.6206C12.0231 14.4999 12.3552 14.4999 12.6258 14.4778C12.9069 14.4548 13.1652 14.4056 13.4079 14.2819C13.7842 14.0902 14.0902 13.7842 14.2819 13.4079C14.4056 13.1652 14.4548 12.9069 14.4778 12.6258C14.4999 12.3552 14.4999 12.023 14.4999 11.6206V8.37924C14.4999 7.97676 14.4999 7.64458 14.4778 7.37399C14.4548 7.09294 14.4056 6.83459 14.2819 6.59192C14.0902 6.2156 13.7842 5.90964 13.4079 5.71789C13.1652 5.59424 12.9069 5.54496 12.6258 5.522Z"
          fill="#BAB9B8" />
  </svg>
)

export const TileIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.26166 4.5H6.28H7.73834H7.73837C7.94667 4.49999 8.13218 4.49998 8.28616 4.51256C8.45048 4.52599 8.62239 4.5562 8.79019 4.64169C9.0348 4.76633 9.23367 4.9652 9.35831 5.20981C9.44381 5.37761 9.47402 5.54952 9.48744 5.71385C9.50002 5.86783 9.50001 6.05335 9.5 6.26166V6.26167V7.73833V7.73834C9.50001 7.94665 9.50002 8.13217 9.48744 8.28616C9.47402 8.45048 9.44381 8.62239 9.35831 8.79019C9.23368 9.0348 9.0348 9.23367 8.79019 9.35831C8.62239 9.44381 8.45048 9.47402 8.28616 9.48744C8.13217 9.50002 7.94665 9.50001 7.73834 9.5H7.73833H6.26167H6.26166C6.05335 9.50001 5.86783 9.50002 5.71385 9.48744C5.54952 9.47402 5.37761 9.44381 5.20981 9.35831C4.9652 9.23368 4.76633 9.0348 4.64169 8.79019C4.5562 8.62239 4.52599 8.45048 4.51256 8.28616C4.49998 8.13218 4.49999 7.94667 4.5 7.73837V7.73834V6.28V6.26166V6.26164C4.49999 6.05333 4.49998 5.86782 4.51256 5.71385C4.52599 5.54952 4.5562 5.37761 4.64169 5.20981C4.76633 4.9652 4.9652 4.76633 5.20981 4.64169C5.37761 4.5562 5.54952 4.52599 5.71385 4.51256C5.86782 4.49998 6.05333 4.49999 6.26164 4.5H6.26166Z"
      fill="#BAB9B8" />
    <path
      d="M12.2617 4.5H12.28H13.7383H13.7384C13.9467 4.49999 14.1322 4.49998 14.2862 4.51256C14.4505 4.52599 14.6224 4.5562 14.7902 4.64169C15.0348 4.76633 15.2337 4.9652 15.3583 5.20981C15.4438 5.37761 15.474 5.54952 15.4874 5.71385C15.5 5.86783 15.5 6.05335 15.5 6.26166V6.26167V7.73833V7.73834C15.5 7.94665 15.5 8.13217 15.4874 8.28616C15.474 8.45048 15.4438 8.62239 15.3583 8.79019C15.2337 9.0348 15.0348 9.23367 14.7902 9.35831C14.6224 9.44381 14.4505 9.47402 14.2862 9.48744C14.1322 9.50002 13.9467 9.50001 13.7383 9.5H13.7383H12.2617H12.2617C12.0534 9.50001 11.8678 9.50002 11.7138 9.48744C11.5495 9.47402 11.3776 9.44381 11.2098 9.35831C10.9652 9.23368 10.7663 9.0348 10.6417 8.79019C10.5562 8.62239 10.526 8.45048 10.5126 8.28616C10.5 8.13218 10.5 7.94667 10.5 7.73837V7.73834V6.28V6.26166V6.26164C10.5 6.05333 10.5 5.86782 10.5126 5.71385C10.526 5.54952 10.5562 5.37761 10.6417 5.20981C10.7663 4.9652 10.9652 4.76633 11.2098 4.64169C11.3776 4.5562 11.5495 4.52599 11.7138 4.51256C11.8678 4.49998 12.0533 4.49999 12.2616 4.5H12.2617Z"
      fill="#BAB9B8" />
    <path
      d="M6.26166 10.5H6.28H7.73834H7.73837C7.94667 10.5 8.13218 10.5 8.28616 10.5126C8.45048 10.526 8.62239 10.5562 8.79019 10.6417C9.0348 10.7663 9.23367 10.9652 9.35831 11.2098C9.44381 11.3776 9.47402 11.5495 9.48744 11.7138C9.50002 11.8678 9.50001 12.0534 9.5 12.2617V12.2617V13.7383V13.7383C9.50001 13.9467 9.50002 14.1322 9.48744 14.2862C9.47402 14.4505 9.44381 14.6224 9.35831 14.7902C9.23368 15.0348 9.0348 15.2337 8.79019 15.3583C8.62239 15.4438 8.45048 15.474 8.28616 15.4874C8.13217 15.5 7.94665 15.5 7.73834 15.5H7.73833H6.26167H6.26166C6.05335 15.5 5.86783 15.5 5.71385 15.4874C5.54952 15.474 5.37761 15.4438 5.20981 15.3583C4.9652 15.2337 4.76633 15.0348 4.64169 14.7902C4.5562 14.6224 4.52599 14.4505 4.51256 14.2862C4.49998 14.1322 4.49999 13.9467 4.5 13.7384V13.7383V12.28V12.2617V12.2616C4.49999 12.0533 4.49998 11.8678 4.51256 11.7138C4.52599 11.5495 4.5562 11.3776 4.64169 11.2098C4.76633 10.9652 4.9652 10.7663 5.20981 10.6417C5.37761 10.5562 5.54952 10.526 5.71385 10.5126C5.86782 10.5 6.05333 10.5 6.26164 10.5H6.26166Z"
      fill="#BAB9B8" />
    <path
      d="M12.2617 10.5H12.28H13.7383H13.7384C13.9467 10.5 14.1322 10.5 14.2862 10.5126C14.4505 10.526 14.6224 10.5562 14.7902 10.6417C15.0348 10.7663 15.2337 10.9652 15.3583 11.2098C15.4438 11.3776 15.474 11.5495 15.4874 11.7138C15.5 11.8678 15.5 12.0534 15.5 12.2617V12.2617V13.7383V13.7383C15.5 13.9467 15.5 14.1322 15.4874 14.2862C15.474 14.4505 15.4438 14.6224 15.3583 14.7902C15.2337 15.0348 15.0348 15.2337 14.7902 15.3583C14.6224 15.4438 14.4505 15.474 14.2862 15.4874C14.1322 15.5 13.9467 15.5 13.7383 15.5H13.7383H12.2617H12.2617C12.0534 15.5 11.8678 15.5 11.7138 15.4874C11.5495 15.474 11.3776 15.4438 11.2098 15.3583C10.9652 15.2337 10.7663 15.0348 10.6417 14.7902C10.5562 14.6224 10.526 14.4505 10.5126 14.2862C10.5 14.1322 10.5 13.9467 10.5 13.7384V13.7383V12.28V12.2617V12.2616C10.5 12.0533 10.5 11.8678 10.5126 11.7138C10.526 11.5495 10.5562 11.3776 10.6417 11.2098C10.7663 10.9652 10.9652 10.7663 11.2098 10.6417C11.3776 10.5562 11.5495 10.526 11.7138 10.5126C11.8678 10.5 12.0533 10.5 12.2616 10.5H12.2617Z"
      fill="#BAB9B8" />
  </svg>

)

export const ScaleIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 15.75C13.1756 15.75 15.75 13.1756 15.75 10C15.75 6.82436 13.1756 4.25 10 4.25C6.82436 4.25 4.25 6.82436 4.25 10C4.25 13.1756 6.82436 15.75 10 15.75Z"
      stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 4.25V6.75" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4.25 10H6.75" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 15.75V13.25" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M15.75 10H13.25" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const PlusIcon16 = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M8.60037 3.59988C8.60037 3.26851 8.33174 2.99988 8.00037 2.99988C7.669 2.99988 7.40037 3.26851 7.40037 3.59988V7.39988H3.60037C3.269 7.39988 3.00037 7.66851 3.00037 7.99988C3.00037 8.33125 3.269 8.59988 3.60037 8.59988H7.40037V12.3999C7.40037 12.7312 7.669 12.9999 8.00037 12.9999C8.33174 12.9999 8.60037 12.7312 8.60037 12.3999V8.59988H12.4004C12.7317 8.59988 13.0004 8.33125 13.0004 7.99988C13.0004 7.66851 12.7317 7.39988 12.4004 7.39988H8.60037V3.59988Z"
          fill="#333332" />
  </svg>
)

export const MinusIcon16 = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M3.59998 8H12.4" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const PlusIconFilled20 = ({ width = '20', height = '20'}) => (
  <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM10 7C10.2761 7 10.5 7.22386 10.5 7.5V9.5H12.5C12.7761 9.5 13 9.72386 13 10C13 10.2761 12.7761 10.5 12.5 10.5H10.5V12.5C10.5 12.7761 10.2761 13 10 13C9.72386 13 9.5 12.7761 9.5 12.5V10.5H7.5C7.22386 10.5 7 10.2761 7 10C7 9.72386 7.22386 9.5 7.5 9.5H9.5V7.5C9.5 7.22386 9.72386 7 10 7Z"
          fill="#BAB9B8" />
  </svg>

)

export const PlusIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10.6004 4.50039C10.6004 4.16902 10.3318 3.90039 10.0004 3.90039C9.66902 3.90039 9.40039 4.16902 9.40039 4.50039V9.40039H4.50039C4.16902 9.40039 3.90039 9.66902 3.90039 10.0004C3.90039 10.3318 4.16902 10.6004 4.50039 10.6004H9.40039V15.5004C9.40039 15.8318 9.66902 16.1004 10.0004 16.1004C10.3318 16.1004 10.6004 15.8318 10.6004 15.5004V10.6004H15.5004C15.8318 10.6004 16.1004 10.3318 16.1004 10.0004C16.1004 9.66902 15.8318 9.40039 15.5004 9.40039H10.6004V4.50039Z"
          fill="#333332" />
  </svg>

)

export const MinusIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.5 10H15.5" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const ChevronUp20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5 12L10 7L15 12" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const ChevronDown20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15 8L10 13L5 8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const ChevronDown20new = () => (
  <svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11 1L6 6L1 0.999999" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const ChevronUp20new = () => (
  <svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11 1L6 6L1 0.999999" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" transform="rotate(180, 6, 3.5)" />
  </svg>
)

export const BoardIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M14.5 11.9016V8.09925C14.5 7.53966 14.5 7.25986 14.391 7.04594C14.2952 6.85787 14.142 6.70474 13.9539 6.60892C13.74 6.49994 13.4604 6.49996 12.9012 6.5L12.6 6.50004C12.0399 6.50011 11.7598 6.50015 11.5459 6.60916C11.3578 6.70505 11.2048 6.858 11.109 7.04617C11 7.26006 11 7.54013 11 8.10025V11.9016C11 12.4611 11 12.7409 11.109 12.9548C11.2048 13.1428 11.3579 13.296 11.546 13.3918C11.7599 13.5008 12.0394 13.5008 12.5985 13.5009H12.9015C13.4606 13.5008 13.7401 13.5008 13.954 13.3918C14.1421 13.296 14.2952 13.1428 14.391 12.9548C14.5 12.7409 14.5 12.4611 14.5 11.9016Z"
      fill="#BAB9B8" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.40039 4.50039C4.40039 4.16902 4.66902 3.90039 5.00039 3.90039H15.0004C15.3318 3.90039 15.6004 4.16902 15.6004 4.50039C15.6004 4.83176 15.3318 5.10039 15.0004 5.10039H5.00039C4.66902 5.10039 4.40039 4.83176 4.40039 4.50039ZM15.0004 11.9212V11.9213V11.9213C15.0004 12.1846 15.0004 12.4118 14.9851 12.5989C14.969 12.7965 14.9333 12.993 14.8369 13.1822C14.6932 13.4643 14.4636 13.6939 14.1815 13.8377C13.9924 13.934 13.796 13.9697 13.5984 13.9859C13.4114 14.0012 13.1843 14.0012 12.9212 14.0013H12.5795C12.3165 14.0012 12.0894 14.0012 11.9024 13.9859C11.7048 13.9697 11.5084 13.934 11.3193 13.8377C11.0372 13.6939 10.8076 13.4643 10.6639 13.1822C10.5675 12.993 10.5318 12.7965 10.5157 12.5989C10.5004 12.4118 10.5004 12.1845 10.5004 11.9212V11.9212V8.08138V8.08136C10.5004 7.81776 10.5004 7.59036 10.5157 7.40306C10.5318 7.20539 10.5675 7.00877 10.6639 6.81958C10.8076 6.53734 11.0371 6.3079 11.3193 6.16407C11.5084 6.06766 11.7051 6.03198 11.9027 6.01581C12.09 6.00048 12.3174 6.00046 12.5811 6.00043L12.9208 6.00039C13.184 6.00036 13.4111 6.00034 13.5982 6.01563C13.7957 6.03178 13.9922 6.06745 14.1813 6.1638C14.4635 6.30755 14.6931 6.53718 14.8369 6.81933C14.9333 7.00848 14.9689 7.205 14.9851 7.40262C15.0004 7.58979 15.0004 7.817 15.0004 8.08032V8.08033V8.08035V8.08037V11.9212V11.9212ZM8.09772 6.01616C7.91044 6.00086 7.68307 6.00087 7.4195 6.00088H7.41948H7.07898H7.07896C6.81577 6.00087 6.58868 6.00086 6.40159 6.01616C6.20407 6.0323 6.00764 6.06795 5.81856 6.16424C5.5365 6.30789 5.30691 6.53735 5.16308 6.81932C5.06669 7.0083 5.03091 7.20463 5.01464 7.40208C4.99923 7.58907 4.99909 7.81603 4.99894 8.07902V8.07902L4.99893 8.09829C4.99794 9.70313 4.99863 11.308 4.99932 12.9126V12.9128L4.99932 12.9129L4.99992 14.401L4.99992 14.4203C5 14.684 5.00007 14.9114 5.01542 15.0987C5.03163 15.2964 5.06733 15.493 5.16376 15.6822C5.30763 15.9644 5.53702 16.1937 5.81926 16.3375C6.00843 16.4338 6.20503 16.4695 6.40269 16.4856C6.58997 16.5009 6.81736 16.5009 7.08096 16.5009H7.41949C7.68306 16.5009 7.91044 16.5009 8.09772 16.4856C8.2954 16.4694 8.49201 16.4338 8.6812 16.3374C8.96344 16.1936 9.19291 15.9641 9.33672 15.6819C9.43312 15.4927 9.46878 15.2961 9.48493 15.0984C9.50023 14.9111 9.50022 14.6837 9.50021 14.4202V14.4202V8.0816V8.08158C9.50022 7.81802 9.50023 7.59065 9.48493 7.40337C9.46878 7.20569 9.43312 7.00908 9.33672 6.81989C9.19291 6.53765 8.96344 6.30818 8.6812 6.16437C8.49201 6.06797 8.2954 6.03232 8.09772 6.01616Z"
          fill="#BAB9B8" />
  </svg>

)

export const ListViewIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.75 5.05078C4.05964 5.05078 3.5 5.61043 3.5 6.30078C3.5 6.99114 4.05964 7.55078 4.75 7.55078C5.44036 7.55078 6 6.99114 6 6.30078C6 5.61043 5.44036 5.05078 4.75 5.05078ZM7.01921 5.85569C7 5.95229 7 6.06845 7 6.30078C7 6.53311 7 6.64927 7.01921 6.74587C7.09812 7.14256 7.40822 7.45266 7.80491 7.53157C7.90151 7.55078 8.01767 7.55078 8.25 7.55078H15.25C15.4823 7.55078 15.5985 7.55078 15.6951 7.53157C16.0918 7.45266 16.4019 7.14256 16.4808 6.74587C16.5 6.64927 16.5 6.53311 16.5 6.30078C16.5 6.06845 16.5 5.95229 16.4808 5.85569C16.4019 5.459 16.0918 5.1489 15.6951 5.07C15.5985 5.05078 15.4823 5.05078 15.25 5.05078H8.25C8.01767 5.05078 7.90151 5.05078 7.80491 5.07C7.40822 5.1489 7.09812 5.459 7.01921 5.85569ZM7 10.001C7 9.76865 7 9.65249 7.01921 9.55589C7.09812 9.1592 7.40822 8.8491 7.80491 8.77019C7.90151 8.75098 8.01767 8.75098 8.25 8.75098H15.25C15.4823 8.75098 15.5985 8.75098 15.6951 8.77019C16.0918 8.8491 16.4019 9.1592 16.4808 9.55589C16.5 9.65249 16.5 9.76865 16.5 10.001C16.5 10.2333 16.5 10.3495 16.4808 10.4461C16.4019 10.8428 16.0918 11.1529 15.6951 11.2318C15.5985 11.251 15.4823 11.251 15.25 11.251H8.25C8.01767 11.251 7.90151 11.251 7.80491 11.2318C7.40822 11.1529 7.09812 10.8428 7.01921 10.4461C7 10.3495 7 10.2333 7 10.001ZM4.75 8.75098C4.05964 8.75098 3.5 9.31062 3.5 10.001C3.5 10.6913 4.05964 11.251 4.75 11.251C5.44036 11.251 6 10.6913 6 10.001C6 9.31062 5.44036 8.75098 4.75 8.75098ZM7 13.7012C7 13.4688 7 13.3527 7.01921 13.2561C7.09812 12.8594 7.40822 12.5493 7.80491 12.4704C7.90151 12.4512 8.01767 12.4512 8.25 12.4512H15.25C15.4823 12.4512 15.5985 12.4512 15.6951 12.4704C16.0918 12.5493 16.4019 12.8594 16.4808 13.2561C16.5 13.3527 16.5 13.4688 16.5 13.7012C16.5 13.9335 16.5 14.0497 16.4808 14.1463C16.4019 14.543 16.0918 14.8531 15.6951 14.932C15.5985 14.9512 15.4823 14.9512 15.25 14.9512H8.25C8.01767 14.9512 7.90151 14.9512 7.80491 14.932C7.40822 14.8531 7.09812 14.543 7.01921 14.1463C7 14.0497 7 13.9335 7 13.7012ZM4.75 12.4512C4.05964 12.4512 3.5 13.0108 3.5 13.7012C3.5 14.3915 4.05964 14.9512 4.75 14.9512C5.44036 14.9512 6 14.3915 6 13.7012C6 13.0108 5.44036 12.4512 4.75 12.4512Z"
          fill="#BAB9B8" />
  </svg>
)

export const ListIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.71799 4.59202C4.5 5.01984 4.5 5.5799 4.5 6.7V13.3C4.5 14.4201 4.5 14.9802 4.71799 15.408C4.90973 15.7843 5.21569 16.0903 5.59202 16.282C6.01984 16.5 6.57989 16.5 7.7 16.5H12.3C13.4201 16.5 13.9802 16.5 14.408 16.282C14.7843 16.0903 15.0903 15.7843 15.282 15.408C15.5 14.9802 15.5 14.4201 15.5 13.3V6.7C15.5 5.5799 15.5 5.01984 15.282 4.59202C15.0903 4.21569 14.7843 3.90973 14.408 3.71799C13.9802 3.5 13.4201 3.5 12.3 3.5H7.7C6.5799 3.5 6.01984 3.5 5.59202 3.71799C5.21569 3.90973 4.90973 4.21569 4.71799 4.59202ZM7.70039 6.5001C7.70039 6.85908 7.40938 7.1501 7.05039 7.1501C6.69141 7.1501 6.40039 6.85908 6.40039 6.5001C6.40039 6.14111 6.69141 5.8501 7.05039 5.8501C7.40938 5.8501 7.70039 6.14111 7.70039 6.5001ZM8.5 6.5C8.5 6.22386 8.72386 6 9 6H13C13.2761 6 13.5 6.22386 13.5 6.5C13.5 6.77614 13.2761 7 13 7H9C8.72386 7 8.5 6.77614 8.5 6.5ZM9 8.2998C8.72386 8.2998 8.5 8.52366 8.5 8.7998C8.5 9.07595 8.72386 9.2998 9 9.2998H13C13.2761 9.2998 13.5 9.07595 13.5 8.7998C13.5 8.52366 13.2761 8.2998 13 8.2998H9ZM7.70039 8.7999C7.70039 9.15889 7.40938 9.4499 7.05039 9.4499C6.69141 9.4499 6.40039 9.15889 6.40039 8.7999C6.40039 8.44092 6.69141 8.1499 7.05039 8.1499C7.40938 8.1499 7.70039 8.44092 7.70039 8.7999ZM9 10.6001C8.72386 10.6001 8.5 10.824 8.5 11.1001C8.5 11.3762 8.72386 11.6001 9 11.6001H13C13.2761 11.6001 13.5 11.3762 13.5 11.1001C13.5 10.824 13.2761 10.6001 13 10.6001H9ZM7.70039 11.0997C7.70039 11.4587 7.40938 11.7497 7.05039 11.7497C6.69141 11.7497 6.40039 11.4587 6.40039 11.0997C6.40039 10.7407 6.69141 10.4497 7.05039 10.4497C7.40938 10.4497 7.70039 10.7407 7.70039 11.0997Z"
          fill="#BAB9B8" />
  </svg>

)

export const TimelineIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.49954 15.6001C4.16817 15.6001 3.89954 15.3315 3.89954 15.0001L3.89954 5.0001C3.89954 4.66873 4.16816 4.4001 4.49954 4.4001C4.83091 4.4001 5.09954 4.66873 5.09954 5.0001L5.09954 15.0001C5.09954 15.3315 4.83091 15.6001 4.49954 15.6001ZM11.9214 5.0001L11.9214 5.0001L11.9214 5.0001C12.1847 5.00009 12.4119 5.00008 12.599 5.01538C12.7966 5.03154 12.9931 5.06721 13.1823 5.16357C13.4644 5.30731 13.694 5.53692 13.8378 5.81903C13.9342 6.00812 13.9699 6.20453 13.986 6.40206C14.0014 6.58913 14.0014 6.81616 14.0014 7.07924L14.0014 7.42095C14.0014 7.68403 14.0014 7.91107 13.986 8.09814C13.9699 8.29566 13.9342 8.49207 13.8378 8.68116C13.694 8.96327 13.4644 9.19288 13.1823 9.33662C12.9931 9.43299 12.7966 9.46866 12.599 9.48481C12.4119 9.50011 12.1847 9.50011 11.9214 9.5001L11.9213 9.5001L8.0815 9.5001L8.08149 9.5001C7.81788 9.50011 7.59048 9.50011 7.40318 9.48482C7.20551 9.46867 7.00889 9.43302 6.81971 9.33664C6.53746 9.19284 6.30803 8.96344 6.16419 8.68122C6.06778 8.49204 6.0321 8.29543 6.01593 8.09776C6.00061 7.91046 6.00058 7.68305 6.00055 7.41944L6.00052 7.07969C6.00049 6.81653 6.00046 6.58943 6.01575 6.40232C6.0319 6.20476 6.06757 6.00831 6.16392 5.81919C6.30768 5.53702 6.5373 5.30738 6.81945 5.1636C7.00861 5.06722 7.20513 5.03154 7.40274 5.01539C7.58992 5.00008 7.81712 5.00009 8.08045 5.0001L8.08046 5.0001L8.08047 5.0001L8.08049 5.0001L11.9213 5.0001L11.9214 5.0001ZM6.01482 11.9028C5.99952 12.09 5.99953 12.3174 5.99954 12.581L5.99954 12.581L5.99954 12.9215L5.99954 12.9215C5.99953 13.1847 5.99952 13.4118 6.01481 13.5989C6.03096 13.7964 6.0666 13.9928 6.1629 14.1819C6.30655 14.464 6.536 14.6936 6.81798 14.8374C7.00696 14.9338 7.20328 14.9696 7.40073 14.9859C7.58773 15.0013 7.81469 15.0014 8.07767 15.0015L8.07768 15.0015L8.09695 15.0016C9.70178 15.0025 11.3067 15.0019 12.9113 15.0012L12.9114 15.0012L12.9116 15.0012L14.3997 15.0006L14.419 15.0006C14.6826 15.0005 14.9101 15.0004 15.0974 14.9851C15.2951 14.9689 15.4917 14.9332 15.6808 14.8367C15.963 14.6929 16.1923 14.4635 16.3361 14.1812C16.4325 13.9921 16.4681 13.7955 16.4843 13.5978C16.4996 13.4105 16.4995 13.1831 16.4995 12.9195L16.4995 12.581C16.4995 12.3174 16.4996 12.09 16.4843 11.9028C16.4681 11.7051 16.4324 11.5085 16.336 11.3193C16.1922 11.037 15.9628 10.8076 15.6805 10.6638C15.4913 10.5674 15.2947 10.5317 15.0971 10.5156C14.9098 10.5003 14.6824 10.5003 14.4188 10.5003L14.4188 10.5003L8.08025 10.5003L8.08024 10.5003C7.81668 10.5003 7.5893 10.5003 7.40202 10.5156C7.20435 10.5317 7.00774 10.5674 6.81855 10.6638C6.53631 10.8076 6.30684 11.037 6.16303 11.3193C6.06663 11.5085 6.03097 11.7051 6.01482 11.9028Z"
          fill="#BAB9B8" />
  </svg>
)

export const AllOProviderLogo20 = () => (
  <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.417969" width="20" height="20" rx="6" fill="#FF8769" />
    <path
      d="M7.83789 12.6541C7.83789 12.9083 8.04399 13.1144 8.29823 13.1144C8.55247 13.1144 8.75857 12.9083 8.75857 12.6541L8.75857 7.86463C8.75857 7.6104 8.55247 7.4043 8.29823 7.4043C8.04399 7.4043 7.83789 7.6104 7.83789 7.86463L7.83789 12.6541Z"
      fill="#F9F9F9" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14.1554 13.2249C15.7934 13.2249 17.1213 11.897 17.1213 10.2589C17.1213 8.62087 15.7934 7.29297 14.1554 7.29297C12.5174 7.29297 11.1895 8.62087 11.1895 10.2589C11.1895 11.897 12.5174 13.2249 14.1554 13.2249ZM14.1553 12.3043C15.2849 12.3043 16.2006 11.3886 16.2006 10.259C16.2006 9.12944 15.2849 8.21374 14.1553 8.21374C13.0257 8.21374 12.11 9.12944 12.11 10.259C12.11 11.3886 13.0257 12.3043 14.1553 12.3043Z"
          fill="#F9F9F9" />
    <path
      d="M9.56836 12.6456C9.56836 12.8998 9.77446 13.1059 10.0287 13.1059C10.2829 13.1059 10.489 12.8998 10.489 12.6456V8.85057C10.489 8.05181 9.56836 7.52623 9.56836 8.32498V12.6456Z"
      fill="#F9F9F9" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.12757 8.93417C6.11988 9.04446 5.98889 9.10107 5.89679 9.03989C5.58295 8.8314 5.21026 8.69727 4.76418 8.69727C3.51371 8.69727 2.5 9.71097 2.5 10.9614C2.5 12.2119 3.51371 13.2256 4.76418 13.2256C5.21053 13.2256 5.58354 13.0924 5.89758 12.8841C5.98931 12.8232 6.11976 12.8797 6.12735 12.9895C6.13224 13.0601 6.19098 13.1149 6.26179 13.1149H6.5668C6.82173 13.1149 7.02839 12.9083 7.02839 12.6533L7.02839 9.26976C7.02839 9.01485 6.82175 8.80821 6.56684 8.80821L6.26262 8.80821C6.1915 8.80821 6.13251 8.86323 6.12757 8.93417ZM6.10898 10.9615C6.10898 11.7043 5.50686 12.3064 4.7641 12.3064C4.02134 12.3064 3.41922 11.7043 3.41922 10.9615C3.41922 10.2188 4.02134 9.61665 4.7641 9.61665C5.50686 9.61665 6.10898 10.2188 6.10898 10.9615Z"
          fill="#F9F9F9" />
  </svg>
)

export const PaymentPartialBadgeIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="10" cy="10" r="9" fill="#428271" />
    <path d="M3 10C3 13.866 6.13401 17 10 17V3C6.13401 3 3 6.13401 3 10Z" fill="#F2F2F2" />
  </svg>
)

export const CircularProgressBar68 = ({ progress, color }) => (
  <svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="32" width="4" height="4" rx="2" fill={progress >= 24 ? "#E8E7E6" : color} />
    <rect x="40" y="1" width="4" height="4" rx="2" fill={progress >= 23 ? "#E8E7E6" : color} />
    <rect x="48" y="4" width="4" height="4" rx="2" fill={progress >= 22 ? "#E8E7E6" : color} />
    <rect x="55" y="9" width="4" height="4" rx="2" fill={progress >= 21 ? "#E8E7E6" : color} />
    <rect x="60" y="16" width="4" height="4" rx="2" fill={progress >= 20 ? "#E8E7E6" : color} />
    <rect x="63" y="24" width="4" height="4" rx="2" fill={progress >= 19 ? "#E8E7E6" : color} />
    <rect x="64" y="32" width="4" height="4" rx="2" fill={progress >= 18 ? "#E8E7E6" : color} />
    <rect x="63" y="40" width="4" height="4" rx="2" fill={progress >= 17 ? "#E8E7E6" : color} />
    <rect x="60" y="48" width="4" height="4" rx="2" fill={progress >= 16 ? "#E8E7E6" : color} />
    <rect x="55" y="55" width="4" height="4" rx="2" fill={progress >= 15 ? "#E8E7E6" : color} />
    <rect x="48" y="60" width="4" height="4" rx="2" fill={progress >= 14 ? "#E8E7E6" : color} />
    <rect x="40" y="63" width="4" height="4" rx="2" fill={progress >= 13 ? "#E8E7E6" : color} />
    <rect x="32" y="64" width="4" height="4" rx="2" fill={progress >= 12 ? "#E8E7E6" : color} />
    <rect x="24" y="63" width="4" height="4" rx="2" fill={progress >= 11 ? "#E8E7E6" : color} />
    <rect x="16" y="60" width="4" height="4" rx="2" fill={progress >= 10 ? "#E8E7E6" : color} />
    <rect x="9" y="55" width="4" height="4" rx="2" fill={progress >= 9 ? "#E8E7E6" : color} />
    <rect x="4" y="48" width="4" height="4" rx="2" fill={progress >= 8 ? "#E8E7E6" : color} />
    <rect x="1" y="40" width="4" height="4" rx="2" fill={progress >= 7 ? "#E8E7E6" : color} />
    <rect y="32" width="4" height="4" rx="2" fill={progress >= 6 ? "#E8E7E6" : color} />
    <rect x="1" y="24" width="4" height="4" rx="2" fill={progress >= 5 ? "#E8E7E6" : color} />
    <rect x="4" y="16" width="4" height="4" rx="2" fill={progress >= 4 ? "#E8E7E6" : color} />
    <rect x="9" y="9" width="4" height="4" rx="2" fill={progress >= 3 ? "#E8E7E6" : color} />
    <rect x="16" y="4" width="4" height="4" rx="2" fill={progress >= 2 ? "#E8E7E6" : color} />
    <rect x="24" y="1" width="4" height="4" rx="2" fill={progress >= 1 ? "#E8E7E6" : color} />
  </svg>
)

export const CircularProgressBar68Negative = ({ progress, color }) => (
  <svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="32" width="4" height="4" rx="2" fill={progress >= 1 ? "#E8E7E6" : color} />
    <rect x="40" y="1" width="4" height="4" rx="2" fill={progress >= 2 ? "#E8E7E6" : color} />
    <rect x="48" y="4" width="4" height="4" rx="2" fill={progress >= 3 ? "#E8E7E6" : color} />
    <rect x="55" y="9" width="4" height="4" rx="2" fill={progress >= 4 ? "#E8E7E6" : color} />
    <rect x="60" y="16" width="4" height="4" rx="2" fill={progress >= 5 ? "#E8E7E6" : color} />
    <rect x="63" y="24" width="4" height="4" rx="2" fill={progress >= 6 ? "#E8E7E6" : color} />
    <rect x="64" y="32" width="4" height="4" rx="2" fill={progress >= 7 ? "#E8E7E6" : color} />
    <rect x="63" y="40" width="4" height="4" rx="2" fill={progress >= 8 ? "#E8E7E6" : color} />
    <rect x="60" y="48" width="4" height="4" rx="2" fill={progress >= 9 ? "#E8E7E6" : color} />
    <rect x="55" y="55" width="4" height="4" rx="2" fill={progress >= 10 ? "#E8E7E6" : color} />
    <rect x="48" y="60" width="4" height="4" rx="2" fill={progress >= 11 ? "#E8E7E6" : color} />
    <rect x="40" y="63" width="4" height="4" rx="2" fill={progress >= 12 ? "#E8E7E6" : color} />
    <rect x="32" y="64" width="4" height="4" rx="2" fill={progress >= 13 ? "#E8E7E6" : color} />
    <rect x="24" y="63" width="4" height="4" rx="2" fill={progress >= 14 ? "#E8E7E6" : color} />
    <rect x="16" y="60" width="4" height="4" rx="2" fill={progress >= 15 ? "#E8E7E6" : color} />
    <rect x="9" y="55" width="4" height="4" rx="2" fill={progress >= 16 ? "#E8E7E6" : color} />
    <rect x="4" y="48" width="4" height="4" rx="2" fill={progress >= 17 ? "#E8E7E6" : color} />
    <rect x="1" y="40" width="4" height="4" rx="2" fill={progress >= 18 ? "#E8E7E6" : color} />
    <rect y="32" width="4" height="4" rx="2" fill={progress >= 19 ? "#E8E7E6" : color} />
    <rect x="1" y="24" width="4" height="4" rx="2" fill={progress >= 20 ? "#E8E7E6" : color} />
    <rect x="4" y="16" width="4" height="4" rx="2" fill={progress >= 21 ? "#E8E7E6" : color} />
    <rect x="9" y="9" width="4" height="4" rx="2" fill={progress >= 22 ? "#E8E7E6" : color} />
    <rect x="16" y="4" width="4" height="4" rx="2" fill={progress >= 23 ? "#E8E7E6" : color} />
    <rect x="24" y="1" width="4" height="4" rx="2" fill={progress >= 24 ? "#E8E7E6" : color} />
  </svg>
)

export const NoItems80 = () => (
  <svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.69052 21.7142H40.8334L35.5273 73.1428H8.99664L3.69052 21.7142Z" fill="#BAB9B8" />
    <path d="M43.6905 21.7142L0.833378 21.7142L4.09868 15.9999L40.4252 16L43.6905 21.7142Z" fill="#929191" />
    <rect x="76.5477" y="20.2858" width="4.28571" height="41.4286" transform="rotate(30 76.5477 20.2858)"
          fill="#C4C4C4" />
    <path d="M5.11908 34.5714H39.4048L37.2619 57.4286H7.26194L5.11908 34.5714Z" fill="#D8D7D6" />
    <path
      d="M52.2619 73.1428C68.0415 73.1428 80.8334 60.351 80.8334 44.5714H23.6905C23.6905 60.351 36.4824 73.1428 52.2619 73.1428Z"
      fill="#929191" />
  </svg>
)

export const NoTakeaway120x90 = () => (
  <svg width="120" height="90" viewBox="0 0 120 90" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.9339 0H81.4052V90H12.4671L13.9339 0Z" fill="#D8D7D6" />
    <path d="M25.6684 90L13.9343 0L0 90H25.6684Z" fill="#BAB9B8" />
    <path d="M94.6065 90L81.5915 0L68.2047 90H94.6065Z" fill="#D8D7D6" />
    <path d="M13.9341 0H83.9722L85.0723 9.07563H15.0342L13.9341 0Z" fill="#929191" />
    <path d="M60.4653 23.8774H110.258V89.9997H59.3828L60.4653 23.8774Z" fill="#BAB9B8" />
    <path d="M69.1249 89.9997L60.4653 23.8774L50.182 89.9997H69.1249Z" fill="#929191" />
    <path d="M120 89.9997L110.395 23.8774L100.516 89.9997H120Z" fill="#BAB9B8" />
    <path d="M60.4608 23.8774L112.163 23.8774L113.231 32.6938H61.5799L60.4608 23.8774Z" fill="#737372" />
  </svg>
)

export const NoReservation120 = () => (
  <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M74.717 120C99.7261 120 120 99.8528 120 75H29.434C29.434 99.8528 49.7079 120 74.717 120Z" fill="#D8D7D6" />
    <path d="M79.2453 75C92.5835 75 103.396 64.2548 103.396 51H55.0943C55.0943 64.2548 65.9071 75 79.2453 75Z"
          fill="#E8E7E6" />
    <path
      d="M31.2479 0H13.4478L13.4926 25.1679C13.4995 30.6773 11.0251 35.9014 6.74631 39.4112C2.47374 42.9157 0 48.13 0 53.6313V120H44.5283V53.4979C44.5283 48.0405 42.0977 42.8622 37.8881 39.3513C33.6785 35.8403 31.2479 30.6621 31.2479 25.2047V0Z"
      fill="#BAB9B8" />
    <path d="M44.4305 86.25H97.3585L97.4563 120H44.4305V86.25Z" fill="#BAB9B8" />
    <path d="M60.7547 120L44.5283 86.25L28.3019 120H60.7547Z" fill="#929191" />
    <path d="M113.962 120L97.3585 86.25L81.5095 120H113.962Z" fill="#BAB9B8" />
  </svg>
)

export const NoTables100 = () => (
  <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect opacity="0.9" x="20" y="50" width="10" height="50" fill="#929191" />
    <rect y="50" width="10" height="50" fill="#BAB9B8" />
    <rect x="100" y="50" width="10" height="50" transform="rotate(-180 100 50)" fill="#929191" />
    <rect x="60" y="50" width="10" height="60" transform="rotate(90 60 50)" fill="#BAB9B8" />
    <rect x="80" y="50" width="10" height="50" transform="rotate(-180 80 50)" fill="#737372" />
    <rect x="40" y="50" width="10" height="60" transform="rotate(-90 40 50)" fill="#929191" />
    <rect x="40" width="50" height="40" transform="rotate(90 40 0)" fill="#BAB9B8" />
    <rect x="60" y="100" width="50" height="40" transform="rotate(-90 60 100)" fill="#929191" />
    <rect x="50" y="50" width="10" height="50" fill="#BAB9B8" />
    <rect x="50" y="50" width="10" height="50" transform="rotate(-180 50 50)" fill="#929191" />
  </svg>
)

export const Play20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM9.27735 7.58398C9.12392 7.48169 8.92665 7.47215 8.76407 7.55916C8.60149 7.64617 8.5 7.8156 8.5 8V12C8.5 12.1844 8.60149 12.3538 8.76407 12.4408C8.92665 12.5278 9.12392 12.5183 9.27735 12.416L12.2774 10.416C12.4164 10.3233 12.5 10.1672 12.5 10C12.5 9.83282 12.4164 9.67671 12.2774 9.58398L9.27735 7.58398Z"
          fill="#BAB9B8" />
  </svg>
)

export const CloseIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M19.8002 12.0002C19.8002 16.308 16.308 19.8002 12.0002 19.8002C7.69237 19.8002 4.2002 16.308 4.2002 12.0002C4.2002 7.69237 7.69237 4.2002 12.0002 4.2002C16.308 4.2002 19.8002 7.69237 19.8002 12.0002ZM14.7532 9.24654C14.9484 9.44181 14.9484 9.75839 14.7532 9.95365L12.7067 12.0001L14.7532 14.0465C14.9484 14.2418 14.9484 14.5584 14.7532 14.7537C14.5579 14.9489 14.2413 14.9489 14.0461 14.7537L11.9996 12.7072L9.95316 14.7537C9.7579 14.9489 9.44132 14.9489 9.24606 14.7537C9.05079 14.5584 9.05079 14.2418 9.24606 14.0465L11.2925 12.0001L9.24606 9.95365C9.05079 9.75839 9.05079 9.44181 9.24606 9.24654C9.44132 9.05128 9.7579 9.05128 9.95316 9.24654L11.9996 11.293L14.0461 9.24654C14.2413 9.05128 14.5579 9.05128 14.7532 9.24654Z"
          fill="#BAB9B8" />
  </svg>
)

export const ArrowLeft16 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.83323 3.76398C7.06659 2.93987 8.51664 2.5 10 2.5C11.9884 2.5023 13.8947 3.29321 15.3008 4.69924C16.7068 6.10526 17.4977 8.01158 17.5 10C17.5 11.4834 17.0601 12.9334 16.236 14.1668C15.4119 15.4001 14.2406 16.3614 12.8701 16.9291C11.4997 17.4967 9.99168 17.6453 8.53682 17.3559C7.08197 17.0665 5.7456 16.3522 4.6967 15.3033C3.64781 14.2544 2.9335 12.918 2.64411 11.4632C2.35472 10.0083 2.50325 8.50032 3.07091 7.12987C3.63856 5.75943 4.59986 4.58809 5.83323 3.76398ZM13.6247 9.96437C13.6247 10.2405 13.4009 10.4644 13.1247 10.4644H8.07963L9.88145 12.2722C10.0764 12.4677 10.0759 12.7843 9.88028 12.9793C9.6847 13.1742 9.36812 13.1737 9.17318 12.9781L6.53206 10.3282C6.43536 10.2371 6.375 10.1078 6.375 9.96437C6.375 9.81602 6.4396 9.68277 6.54221 9.5912L9.17293 6.94807C9.36773 6.75235 9.68432 6.75161 9.88004 6.94641C10.0758 7.14121 10.0765 7.45779 9.8817 7.65351L8.07934 9.46437H13.1247C13.4009 9.46437 13.6247 9.68823 13.6247 9.96437Z"
          fill="#BAB9B8" />
  </svg>
)

export const RevenueIcon16 = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14 8.41797C14 11.7317 11.3137 14.418 8 14.418C4.68629 14.418 2 11.7317 2 8.41797C2 5.10426 4.68629 2.41797 8 2.41797C11.3137 2.41797 14 5.10426 14 8.41797ZM8.49902 5.61816C8.49902 5.34202 8.27517 5.11816 7.99902 5.11816C7.72288 5.11816 7.49902 5.34202 7.49902 5.61816V5.91797H7.39941C7.00159 5.91797 6.62006 6.076 6.33875 6.35731C6.05745 6.63861 5.89941 7.02014 5.89941 7.41797C5.89941 7.81579 6.05745 8.19732 6.33875 8.47863C6.62006 8.75993 7.00159 8.91797 7.39941 8.91797H8.59941C8.73202 8.91797 8.8592 8.97065 8.95297 9.06442C9.04674 9.15818 9.09941 9.28536 9.09941 9.41797C9.09941 9.55058 9.04674 9.67775 8.95297 9.77152C8.8592 9.86529 8.73202 9.91797 8.59941 9.91797H7.99902H6.79941C6.52327 9.91797 6.29941 10.1418 6.29941 10.418C6.29941 10.6941 6.52327 10.918 6.79941 10.918H7.49902V11.218C7.49902 11.4941 7.72288 11.718 7.99902 11.718C8.27517 11.718 8.49902 11.4941 8.49902 11.218V10.918H8.59941C8.99724 10.918 9.37877 10.7599 9.66007 10.4786C9.94138 10.1973 10.0994 9.81579 10.0994 9.41797C10.0994 9.02014 9.94138 8.63861 9.66007 8.35731C9.37877 8.076 8.99724 7.91797 8.59941 7.91797H7.39941C7.26681 7.91797 7.13963 7.86529 7.04586 7.77152C6.95209 7.67775 6.89941 7.55058 6.89941 7.41797C6.89941 7.28536 6.95209 7.15818 7.04586 7.06442C7.13963 6.97065 7.26681 6.91797 7.39941 6.91797H7.9849L7.99902 6.91816L8.01314 6.91797H9.19941C9.47556 6.91797 9.69941 6.69411 9.69941 6.41797C9.69941 6.14183 9.47556 5.91797 9.19941 5.91797H8.49902V5.61816Z"
          fill="#BAB9B8" />
  </svg>
)

export const RevenueIcon16Dark = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14 8.41797C14 11.7317 11.3137 14.418 8 14.418C4.68629 14.418 2 11.7317 2 8.41797C2 5.10426 4.68629 2.41797 8 2.41797C11.3137 2.41797 14 5.10426 14 8.41797ZM8.49902 5.61816C8.49902 5.34202 8.27517 5.11816 7.99902 5.11816C7.72288 5.11816 7.49902 5.34202 7.49902 5.61816V5.91797H7.39941C7.00159 5.91797 6.62006 6.076 6.33875 6.35731C6.05745 6.63861 5.89941 7.02014 5.89941 7.41797C5.89941 7.81579 6.05745 8.19732 6.33875 8.47863C6.62006 8.75993 7.00159 8.91797 7.39941 8.91797H8.59941C8.73202 8.91797 8.8592 8.97065 8.95297 9.06442C9.04674 9.15818 9.09941 9.28536 9.09941 9.41797C9.09941 9.55058 9.04674 9.67775 8.95297 9.77152C8.8592 9.86529 8.73202 9.91797 8.59941 9.91797H7.99902H6.79941C6.52327 9.91797 6.29941 10.1418 6.29941 10.418C6.29941 10.6941 6.52327 10.918 6.79941 10.918H7.49902V11.218C7.49902 11.4941 7.72288 11.718 7.99902 11.718C8.27517 11.718 8.49902 11.4941 8.49902 11.218V10.918H8.59941C8.99724 10.918 9.37877 10.7599 9.66007 10.4786C9.94138 10.1973 10.0994 9.81579 10.0994 9.41797C10.0994 9.02014 9.94138 8.63861 9.66007 8.35731C9.37877 8.076 8.99724 7.91797 8.59941 7.91797H7.39941C7.26681 7.91797 7.13963 7.86529 7.04586 7.77152C6.95209 7.67775 6.89941 7.55058 6.89941 7.41797C6.89941 7.28536 6.95209 7.15818 7.04586 7.06442C7.13963 6.97065 7.26681 6.91797 7.39941 6.91797H7.9849L7.99902 6.91816L8.01314 6.91797H9.19941C9.47556 6.91797 9.69941 6.69411 9.69941 6.41797C9.69941 6.14183 9.47556 5.91797 9.19941 5.91797H8.49902V5.61816Z"
          fill="#929191" />
  </svg>
)

export const CheckIconGreen16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8ZM11.0952 6.86168C11.295 6.67101 11.3023 6.35451 11.1117 6.15476C10.921 5.95501 10.6045 5.94765 10.4048 6.13832L7.08332 9.30878L5.59524 7.88832C5.39549 7.69765 5.07899 7.70501 4.88832 7.90476C4.69765 8.10451 4.70501 8.42101 4.90476 8.61168L6.73807 10.3617C6.93128 10.5461 7.23534 10.5461 7.42855 10.3617L11.0952 6.86168Z"
          fill="#4D9985" />
  </svg>
)

export const WarningCircle16 = ({ color = "#FF7C5C" }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8ZM8 4.5C8.27614 4.5 8.5 4.72386 8.5 5V8.5C8.5 8.77614 8.27614 9 8 9C7.72386 9 7.5 8.77614 7.5 8.5V5C7.5 4.72386 7.72386 4.5 8 4.5ZM8 11.5C8.41421 11.5 8.75 11.1642 8.75 10.75C8.75 10.3358 8.41421 10 8 10C7.58579 10 7.25 10.3358 7.25 10.75C7.25 11.1642 7.58579 11.5 8 11.5Z"
          fill={color} />
  </svg>
)

export const WarningCircleRed16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8ZM8 4.5C8.27614 4.5 8.5 4.72386 8.5 5V8.5C8.5 8.77614 8.27614 9 8 9C7.72386 9 7.5 8.77614 7.5 8.5V5C7.5 4.72386 7.72386 4.5 8 4.5ZM8 11.5C8.41421 11.5 8.75 11.1642 8.75 10.75C8.75 10.3358 8.41421 10 8 10C7.58579 10 7.25 10.3358 7.25 10.75C7.25 11.1642 7.58579 11.5 8 11.5Z"
          fill="#F06060" />
  </svg>
)

export const ClockIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9.5 3.50024C8.21443 3.50024 6.95772 3.88146 5.8888 4.59569C4.81988 5.30992 3.98676 6.32508 3.49479 7.5128C3.00282 8.70052 2.87409 10.0075 3.1249 11.2683C3.3757 12.5292 3.99477 13.6874 4.90381 14.5964C5.81285 15.5055 6.97104 16.1245 8.23192 16.3753C9.49279 16.6262 10.7997 16.4974 11.9874 16.0055C13.1752 15.5135 14.1903 14.6804 14.9046 13.6114C15.6188 12.5425 16 11.2858 16 10.0002C15.998 8.27695 15.3126 6.6248 14.094 5.40625C12.8754 4.18769 11.2233 3.50224 9.5 3.50024ZM13 10.8232H9.5C9.43433 10.8232 9.3693 10.8103 9.30862 10.7852C9.24794 10.7601 9.1928 10.7233 9.14637 10.6768C9.09993 10.6304 9.0631 10.5752 9.03799 10.5146C9.01287 10.4539 8.99997 10.3889 9 10.3232V6.50024C9 6.36764 9.05268 6.24046 9.14645 6.14669C9.24022 6.05292 9.3674 6.00024 9.5 6.00024C9.63261 6.00024 9.75979 6.05292 9.85356 6.14669C9.94733 6.24046 10 6.36764 10 6.50024V9.82318H13C13.1326 9.82318 13.2598 9.87586 13.3536 9.96963C13.4473 10.0634 13.5 10.1906 13.5 10.3232C13.5 10.4558 13.4473 10.583 13.3536 10.6767C13.2598 10.7705 13.1326 10.8232 13 10.8232Z"
      fill="#BAB9B8" />
  </svg>
)

export const ClockIconLight20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9.5 3.50024C8.21443 3.50024 6.95772 3.88146 5.8888 4.59569C4.81988 5.30992 3.98676 6.32508 3.49479 7.5128C3.00282 8.70052 2.87409 10.0075 3.1249 11.2683C3.3757 12.5292 3.99477 13.6874 4.90381 14.5964C5.81285 15.5055 6.97104 16.1245 8.23192 16.3753C9.49279 16.6262 10.7997 16.4974 11.9874 16.0055C13.1752 15.5135 14.1903 14.6804 14.9046 13.6114C15.6188 12.5425 16 11.2858 16 10.0002C15.998 8.27695 15.3126 6.6248 14.094 5.40625C12.8754 4.18769 11.2233 3.50224 9.5 3.50024ZM13 10.8232H9.5C9.43433 10.8232 9.3693 10.8103 9.30862 10.7852C9.24794 10.7601 9.1928 10.7233 9.14637 10.6768C9.09993 10.6304 9.0631 10.5752 9.03799 10.5146C9.01287 10.4539 8.99997 10.3889 9 10.3232V6.50024C9 6.36764 9.05268 6.24046 9.14645 6.14669C9.24022 6.05292 9.3674 6.00024 9.5 6.00024C9.63261 6.00024 9.75979 6.05292 9.85356 6.14669C9.94733 6.24046 10 6.36764 10 6.50024V9.82318H13C13.1326 9.82318 13.2598 9.87586 13.3536 9.96963C13.4473 10.0634 13.5 10.1906 13.5 10.3232C13.5 10.4558 13.4473 10.583 13.3536 10.6767C13.2598 10.7705 13.1326 10.8232 13 10.8232Z"
      fill="#F9F9F9" fillOpacity="0.8" />
  </svg>
)

export const RevenueIconLight20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z"
      fill="#D1D1D1" stroke="#D1D1D1" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0181 6.25V7.32143" stroke="#242423" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0181 12.6785V13.7499" stroke="#242423" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M8.41071 12.6787H10.8214C11.1766 12.6787 11.5173 12.5376 11.7684 12.2864C12.0196 12.0352 12.1607 11.6946 12.1607 11.3394C12.1607 10.9842 12.0196 10.6435 11.7684 10.3924C11.5173 10.1412 11.1766 10.0001 10.8214 10.0001H9.21429C8.85909 10.0001 8.51843 9.859 8.26727 9.60784C8.0161 9.35667 7.875 9.01602 7.875 8.66082C7.875 8.30562 8.0161 7.96497 8.26727 7.7138C8.51843 7.46264 8.85909 7.32153 9.21429 7.32153H11.625"
      stroke="#242423" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const MandatoryChoices24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M20 8.97065C20 8.54012 19.7495 8.25832 19.1233 7.89041L12.9941 4.32094C12.6184 4.10176 12.3131 4 12 4C11.6791 4 11.3816 4.10176 11.0059 4.32094L4.87671 7.89041C4.25049 8.25832 4 8.54012 4 8.97065C4 9.40117 4.25049 9.68297 4.87671 10.0587L11.0059 13.6204C11.3816 13.8474 11.6791 13.9413 12 13.9413C12.3131 13.9413 12.6184 13.8395 12.9941 13.6204L19.1233 10.0587C19.7495 9.68297 20 9.40117 20 8.97065Z"
      fill="#BAB9B8" />
    <path
      d="M4.42188 12.7079C6.20859 13.7499 7.97547 14.8471 9.77441 15.8796C10.5307 16.3137 10.9089 16.5308 11.3729 16.6285C11.7562 16.7092 12.2347 16.7085 12.6178 16.6267C13.0815 16.5276 13.4646 16.3062 14.2307 15.8635C16.0237 14.8275 17.7916 13.7432 19.5765 12.7002"
      stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M4.42188 16.0077C6.6362 17.2965 8.82008 18.67 11.0723 19.9004C11.654 20.2182 12.3574 20.2134 12.9367 19.891C15.1773 18.6443 17.3638 17.2904 19.5765 16"
      stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="0.01 2" />
  </svg>
)

export const CurrencyCircleDollar = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0178 6.25V7.32143" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0178 12.6785V13.7499" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M8.41071 12.6787H10.8214C11.1766 12.6787 11.5173 12.5376 11.7684 12.2864C12.0196 12.0352 12.1607 11.6946 12.1607 11.3394C12.1607 10.9842 12.0196 10.6435 11.7684 10.3924C11.5173 10.1412 11.1766 10.0001 10.8214 10.0001H9.21429C8.85909 10.0001 8.51843 9.859 8.26727 9.60784C8.0161 9.35667 7.875 9.01602 7.875 8.66082C7.875 8.30562 8.0161 7.96497 8.26727 7.7138C8.51843 7.46264 8.85909 7.32153 9.21429 7.32153H11.625"
      stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const CurrencyCircleDollarTableTaken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z"
      fill="#7A0F19" fillOpacity="0.5" />
    <path d="M8.01428 5V5.85714" stroke="#FFD5B8" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8.01428 10.1431V11.0002" stroke="#FFD5B8" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M6.72862 10.1426H8.65719C8.94135 10.1426 9.21387 10.0298 9.41481 9.82883C9.61574 9.6279 9.72862 9.35538 9.72862 9.07122C9.72862 8.78706 9.61574 8.51454 9.41481 8.3136C9.21387 8.11267 8.94135 7.99979 8.65719 7.99979H7.37148C7.08732 7.99979 6.81479 7.88691 6.61386 7.68598C6.41293 7.48504 6.30005 7.21252 6.30005 6.92836C6.30005 6.6442 6.41293 6.37168 6.61386 6.17075C6.81479 5.96982 7.08732 5.85693 7.37148 5.85693H9.30005"
      stroke="#FFD5B8" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const CurrencyCircleDollarTableActive = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z"
      fill="#F9F9F9" fillOpacity="0.8" />
    <path d="M8.01428 5V5.85714" stroke="#FF7C5C" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8.01428 10.1431V11.0002" stroke="#FF7C5C" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M6.72862 10.1426H8.65719C8.94135 10.1426 9.21387 10.0298 9.41481 9.82883C9.61574 9.6279 9.72862 9.35538 9.72862 9.07122C9.72862 8.78706 9.61574 8.51454 9.41481 8.3136C9.21387 8.11267 8.94135 7.99979 8.65719 7.99979H7.37148C7.08732 7.99979 6.81479 7.88691 6.61386 7.68598C6.41293 7.48504 6.30005 7.21252 6.30005 6.92836C6.30005 6.6442 6.41293 6.37168 6.61386 6.17075C6.81479 5.96982 7.08732 5.85693 7.37148 5.85693H9.30005"
      stroke="#FF7C5C" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const UserCircle20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 10.1203 16.997 10.2398 16.991 10.3586C16.9011 12.1404 16.145 13.7464 14.9667 14.9327C13.6987 16.2095 11.9417 17 10 17C8.05835 17 6.30134 16.2095 5.03327 14.9327C3.7765 13.6673 3 11.9243 3 10ZM5.66669 14.15C6.75885 15.2901 8.29652 16 10 16C11.7035 16 13.2411 15.2901 14.3333 14.15C13.2411 13.0099 11.7035 12.3 10 12.3C8.29652 12.3 6.75885 13.0099 5.66669 14.15ZM12.4497 8.5C12.4497 9.88071 11.3304 11 9.94973 11C8.56902 11 7.44973 9.88071 7.44973 8.5C7.44973 7.11929 8.56902 6 9.94973 6C11.3304 6 12.4497 7.11929 12.4497 8.5Z"
          fill="#BAB9B8" />
  </svg>

)

export const CoffeeIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.34375 3.50024V5.62524" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M10 3.50024V5.62524" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.6562 3.50024V5.62524" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M3.625 15.344H16.375" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M3.09375 8.00024V8.09399C3.09375 8.9357 3.09375 9.35655 3.13592 9.71034C3.46381 12.4614 5.63263 14.6302 8.38365 14.9581C8.73745 15.0002 9.1583 15.0002 10 15.0002C10.8417 15.0002 11.2626 15.0002 11.6163 14.9581C14.3674 14.6302 16.5362 12.4614 16.8641 9.71034C16.9062 9.35655 16.9062 8.9357 16.9062 8.09399V8.00024H3.09375Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeLinejoin="round" />
  </svg>

)

export const CoffeeIconLight20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.34375 3.50024V5.62524" stroke="#F9F9F9" strokeOpacity="0.8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M10 3.50024V5.62524" stroke="#F9F9F9" strokeOpacity="0.8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M12.6562 3.50024V5.62524" stroke="#F9F9F9" strokeOpacity="0.8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path d="M3.625 15.344H16.375" stroke="#F9F9F9" strokeOpacity="0.8" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
    <path
      d="M3.09375 8.00024V8.09399C3.09375 8.9357 3.09375 9.35655 3.13592 9.71034C3.46381 12.4614 5.63263 14.6302 8.38365 14.9581C8.73745 15.0002 9.1583 15.0002 10 15.0002C10.8417 15.0002 11.2626 15.0002 11.6163 14.9581C14.3674 14.6302 16.5362 12.4614 16.8641 9.71034C16.9062 9.35655 16.9062 8.9357 16.9062 8.09399V8.00024H3.09375Z"
      fill="#F9F9F9" stroke="#F9F9F9" strokeLinejoin="round" fillOpacity="0.8" strokeOpacity="0.8" />
  </svg>

)

export const UserCircleLight20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 10.1203 16.997 10.2398 16.991 10.3586C16.9011 12.1404 16.145 13.7464 14.9667 14.9327C13.6987 16.2095 11.9417 17 10 17C8.05835 17 6.30134 16.2095 5.03327 14.9327C3.7765 13.6673 3 11.9243 3 10ZM5.66669 14.15C6.75885 15.2901 8.29652 16 10 16C11.7035 16 13.2411 15.2901 14.3333 14.15C13.2411 13.0099 11.7035 12.3 10 12.3C8.29652 12.3 6.75885 13.0099 5.66669 14.15ZM12.4497 8.5C12.4497 9.88071 11.3304 11 9.94973 11C8.56902 11 7.44973 9.88071 7.44973 8.5C7.44973 7.11929 8.56902 6 9.94973 6C11.3304 6 12.4497 7.11929 12.4497 8.5Z"
          fill="#F9F9F9" stroke="#F9F9F9" strokeLinejoin="round" fillOpacity="0.8" strokeOpacity="0.8" />
  </svg>

)

export const DurationIcon16Active = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M5.38863 4.09214C6.16146 3.57575 7.07004 3.30011 7.99951 3.30005C9.24564 3.30156 10.4403 3.79726 11.3214 4.67841C12.2027 5.55962 12.6984 6.7544 12.6998 8.00063C12.6997 8.93 12.4241 9.83848 11.9077 10.6112C11.3913 11.3841 10.6572 11.9865 9.79842 12.3423C8.93961 12.698 7.99459 12.7911 7.08288 12.6097C6.17117 12.4284 5.33371 11.9808 4.67641 11.3235C4.0191 10.6661 3.57147 9.82868 3.39012 8.91697C3.20877 8.00526 3.30184 7.06025 3.65757 6.20144C4.01331 5.34262 4.61572 4.60858 5.38863 4.09214Z"
      stroke="#FAE0DA" />
    <path
      d="M12.6731 8.49997C12.5926 9.25277 12.3311 9.97762 11.9077 10.6112C11.3913 11.3841 10.6572 11.9865 9.79842 12.3423C8.93961 12.698 7.99459 12.7911 7.08288 12.6097C6.17117 12.4284 5.33371 11.9808 4.67641 11.3235C4.0191 10.6661 3.57147 9.82868 3.39012 8.91697C3.20877 8.00526 3.30184 7.06025 3.65757 6.20144C4.01331 5.34262 4.61572 4.60858 5.38863 4.09214C6.02221 3.6688 6.74704 3.40725 7.49981 3.32672V7.99927V8.49919L7.99973 8.49927L12.6731 8.49997Z"
      fill="#FAE0DA" stroke="#FAE0DA" />
  </svg>

)

export const DurationIcon16Taken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M5.38863 4.09214C6.16146 3.57575 7.07004 3.30011 7.99951 3.30005C9.24564 3.30156 10.4403 3.79726 11.3214 4.67841C12.2027 5.55962 12.6984 6.7544 12.6998 8.00063C12.6997 8.93 12.4241 9.83848 11.9077 10.6112C11.3913 11.3841 10.6572 11.9865 9.79842 12.3423C8.93961 12.698 7.99459 12.7911 7.08288 12.6097C6.17117 12.4284 5.33371 11.9808 4.67641 11.3235C4.0191 10.6661 3.57147 9.82868 3.39012 8.91697C3.20877 8.00526 3.30184 7.06025 3.65757 6.20144C4.01331 5.34262 4.61572 4.60858 5.38863 4.09214Z"
      stroke="#BD7269" />
    <path d="M7.9998 2.80005C5.12792 2.80005 2.7998 5.12817 2.7998 8.00005H7.9998V2.80005Z" fill="#BD7269" />
  </svg>
)

export const UserCircleTaken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M2.40002 8.1199C2.40002 4.96083 4.96096 2.3999 8.12002 2.3999C11.2791 2.3999 13.84 4.96083 13.84 8.1199C13.84 11.279 11.2791 13.8399 8.12002 13.8399C4.96096 13.8399 2.40002 11.279 2.40002 8.1199Z"
          fill="#BD7269" />
    <g clipPath="url(#clip0_565_275829)">
      <mask id="mask0_565_275829" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="3" y="4" width="10"
            height="16">
        <path fillRule="evenodd" clipRule="evenodd"
              d="M9.9598 6.7998C9.9598 7.90437 9.06437 8.7998 7.9598 8.7998C6.85523 8.7998 5.9598 7.90437 5.9598 6.7998C5.9598 5.69524 6.85523 4.7998 7.9598 4.7998C9.06437 4.7998 9.9598 5.69524 9.9598 6.7998ZM8.00001 9.83984C5.34904 9.83984 3.20001 11.9889 3.20001 14.6398C3.20001 17.2908 5.34904 19.4398 8.00001 19.4398C10.651 19.4398 12.8 17.2908 12.8 14.6398C12.8 11.9889 10.651 9.83984 8.00001 9.83984Z"
              fill="#B02525" />
      </mask>
      <g mask="url(#mask0_565_275829)">
        <path fillRule="evenodd" clipRule="evenodd"
              d="M2.27936 8.1199C2.27936 4.96083 4.84029 2.3999 7.99936 2.3999C11.1584 2.3999 13.7194 4.96083 13.7194 8.1199C13.7194 11.279 11.1584 13.8399 7.99936 13.8399C4.84029 13.8399 2.27936 11.279 2.27936 8.1199Z"
              fill="#FFD5B8" />
      </g>
    </g>
    <path
      d="M2.90002 8.1199C2.90002 5.23698 5.2371 2.8999 8.12002 2.8999C11.003 2.8999 13.34 5.23698 13.34 8.1199C13.34 11.0028 11.003 13.3399 8.12002 13.3399C5.2371 13.3399 2.90002 11.0028 2.90002 8.1199Z"
      stroke="#BD7269" />
    <defs>
      <clipPath id="clip0_565_275829">
        <rect width="9.6" height="8.8" fill="white" transform="translate(3.20001 4.7998)" />
      </clipPath>
    </defs>
  </svg>
)

export const UserCircleActive = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M2.40002 8.1199C2.40002 4.96083 4.96096 2.3999 8.12002 2.3999C11.2791 2.3999 13.84 4.96083 13.84 8.1199C13.84 11.279 11.2791 13.8399 8.12002 13.8399C4.96096 13.8399 2.40002 11.279 2.40002 8.1199Z"
          fill="#FAE0DA" />
    <g clipPath="url(#clip0_565_275874)">
      <mask id="mask0_565_275874" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="3" y="4" width="10"
            height="16">
        <path fillRule="evenodd" clipRule="evenodd"
              d="M9.9598 6.7998C9.9598 7.90437 9.06437 8.7998 7.9598 8.7998C6.85523 8.7998 5.9598 7.90437 5.9598 6.7998C5.9598 5.69524 6.85523 4.7998 7.9598 4.7998C9.06437 4.7998 9.9598 5.69524 9.9598 6.7998ZM8.00001 9.83984C5.34904 9.83984 3.20001 11.9889 3.20001 14.6398C3.20001 17.2908 5.34904 19.4398 8.00001 19.4398C10.651 19.4398 12.8 17.2908 12.8 14.6398C12.8 11.9889 10.651 9.83984 8.00001 9.83984Z"
              fill="#B02525" />
      </mask>
      <g mask="url(#mask0_565_275874)">
        <path fillRule="evenodd" clipRule="evenodd"
              d="M2.27936 8.1199C2.27936 4.96083 4.84029 2.3999 7.99936 2.3999C11.1584 2.3999 13.7194 4.96083 13.7194 8.1199C13.7194 11.279 11.1584 13.8399 7.99936 13.8399C4.84029 13.8399 2.27936 11.279 2.27936 8.1199Z"
              fill="#FF7C5C" />
      </g>
    </g>
    <path
      d="M2.90002 8.1199C2.90002 5.23698 5.2371 2.8999 8.12002 2.8999C11.003 2.8999 13.34 5.23698 13.34 8.1199C13.34 11.0028 11.003 13.3399 8.12002 13.3399C5.2371 13.3399 2.90002 11.0028 2.90002 8.1199Z"
      stroke="#FAE0DA" />
    <defs>
      <clipPath id="clip0_565_275874">
        <rect width="9.6" height="8.8" fill="white" transform="translate(3.20001 4.7998)" />
      </clipPath>
    </defs>
  </svg>

)

export const CashIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M2.27248 5.36502C2 5.8998 2 6.59987 2 8V12C2 13.4001 2 14.1002 2.27248 14.635C2.51217 15.1054 2.89462 15.4878 3.36502 15.7275C3.8998 16 4.59987 16 6 16H14C15.4001 16 16.1002 16 16.635 15.7275C17.1054 15.4878 17.4878 15.1054 17.7275 14.635C18 14.1002 18 13.4001 18 12V8C18 6.59987 18 5.8998 17.7275 5.36502C17.4878 4.89462 17.1054 4.51217 16.635 4.27248C16.1002 4 15.4001 4 14 4H6C4.59987 4 3.8998 4 3.36502 4.27248C2.89462 4.51217 2.51217 4.89462 2.27248 5.36502ZM10.5146 7C10.5146 6.72386 10.2908 6.5 10.0146 6.5C9.73851 6.5 9.51465 6.72386 9.51465 7V7.35742H9.37123C8.95446 7.35742 8.55477 7.52298 8.26007 7.81768C7.96537 8.11238 7.7998 8.51208 7.7998 8.92885C7.7998 9.34562 7.96537 9.74532 8.26007 10.04C8.55477 10.3347 8.95446 10.5003 9.37123 10.5003H10.6569C10.8085 10.5003 10.9538 10.5605 11.061 10.6676C11.1682 10.7748 11.2284 10.9202 11.2284 11.0717C11.2284 11.2233 11.1682 11.3686 11.061 11.4758C10.9538 11.5829 10.8085 11.6431 10.6569 11.6431H10.0385C10.0306 11.6428 10.0226 11.6426 10.0146 11.6426C10.0067 11.6426 9.99871 11.6428 9.99082 11.6431H8.72838C8.45223 11.6431 8.22838 11.867 8.22838 12.1431C8.22838 12.4193 8.45223 12.6431 8.72838 12.6431H9.51465V12.9997C9.51465 13.2759 9.73851 13.4997 10.0146 13.4997C10.2908 13.4997 10.5146 13.2759 10.5146 12.9997V12.6431H10.6569C11.0737 12.6431 11.4734 12.4776 11.7681 12.1829C12.0628 11.8882 12.2284 11.4885 12.2284 11.0717C12.2284 10.6549 12.0628 10.2552 11.7681 9.96054C11.4734 9.66584 11.0737 9.50028 10.6569 9.50028H9.37123C9.21968 9.50028 9.07434 9.44007 8.96717 9.33291C8.86001 9.22575 8.7998 9.0804 8.7998 8.92885C8.7998 8.7773 8.86001 8.63195 8.96717 8.52479C9.07434 8.41763 9.21968 8.35742 9.37123 8.35742H11.2998C11.5759 8.35742 11.7998 8.13356 11.7998 7.85742C11.7998 7.58128 11.5759 7.35742 11.2998 7.35742H10.5146V7Z"
          fill="#BAB9B8" />
  </svg>

)

export const CardIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M2.27248 5.36502C2.08303 5.73684 2.0253 6.18855 2.00771 6.8999H17.9923C17.9747 6.18855 17.917 5.73684 17.7275 5.36502C17.4878 4.89462 17.1054 4.51217 16.635 4.27248C16.1002 4 15.4001 4 14 4H6C4.59987 4 3.8998 4 3.36502 4.27248C2.89462 4.51217 2.51217 4.89462 2.27248 5.36502ZM18 8.0999H2V12C2 13.4001 2 14.1002 2.27248 14.635C2.51217 15.1054 2.89462 15.4878 3.36502 15.7275C3.8998 16 4.59987 16 6 16H14C15.4001 16 16.1002 16 16.635 15.7275C17.1054 15.4878 17.4878 15.1054 17.7275 14.635C18 14.1002 18 13.4001 18 12V8.0999ZM4.5 12.8998C4.5 12.5684 4.76863 12.2998 5.1 12.2998H7.9C8.23137 12.2998 8.5 12.5684 8.5 12.8998C8.5 13.2312 8.23137 13.4998 7.9 13.4998H5.1C4.76863 13.4998 4.5 13.2312 4.5 12.8998Z"
          fill="#BAB9B8" />
  </svg>

)

export const PlaceholderIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 3C8.61553 3 7.26215 3.41054 6.11101 4.17971C4.95987 4.94888 4.06266 6.04213 3.53285 7.32122C3.00303 8.6003 2.86441 10.0078 3.13451 11.3656C3.4046 12.7235 4.07129 13.9708 5.05026 14.9497C6.02922 15.9287 7.2765 16.5954 8.63437 16.8655C9.99224 17.1356 11.3997 16.997 12.6788 16.4672C13.9579 15.9373 15.0511 15.0401 15.8203 13.889C16.5895 12.7378 17 11.3845 17 10C16.9979 8.14414 16.2597 6.36491 14.9474 5.05262C13.6351 3.74033 11.8559 3.00214 10 3Z"
      fill="#BAB9B8" />
  </svg>

)

export const DeleteIcon24 = ({ color = "#BAB9B8" }) => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M15.0227 5.40104C12.9815 5.40158 10.9406 5.40212 8.89934 5.40116C8.03092 5.40075 7.21906 5.83374 6.73645 6.55649C6.02842 7.61685 5.32157 8.67809 4.61496 9.73899L4.61369 9.74089L4.0692 10.5583C3.48736 11.4318 3.48738 12.5693 4.0691 13.4427L4.61317 14.2597L4.61666 14.265L4.62075 14.2711L4.62091 14.2714C5.3256 15.3296 6.03058 16.3884 6.73709 17.4459C7.21929 18.1678 8.03008 18.6004 8.89755 18.6004L19.1621 18.6004C20.598 18.6004 21.7621 17.4363 21.7618 16.0002L21.7612 14.1779V14.1775V14.1772C21.7606 12.1188 21.7599 10.0607 21.761 8.00259C21.7617 6.5664 20.5983 5.40051 19.1613 5.40041C17.7815 5.40031 16.402 5.40067 15.0227 5.40104L15.0227 5.40104ZM16.8536 9.39693C17.0489 9.5922 17.0489 9.90878 16.8536 10.104L14.9572 12.0005L16.8536 13.8969C17.0489 14.0922 17.0489 14.4088 16.8536 14.604C16.6583 14.7993 16.3418 14.7993 16.1465 14.604L14.2501 12.7076L12.3536 14.604C12.1583 14.7993 11.8418 14.7993 11.6465 14.604C11.4512 14.4088 11.4512 14.0922 11.6465 13.8969L13.5429 12.0005L11.6465 10.104C11.4512 9.90878 11.4512 9.5922 11.6465 9.39693C11.8418 9.20167 12.1583 9.20167 12.3536 9.39693L14.2501 11.2934L16.1465 9.39693C16.3418 9.20167 16.6583 9.20167 16.8536 9.39693Z"
          fill={color} />
  </svg>

)

export const TrashIcon20 = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.96491 0.899951L5.9658 0.899951C6.81064 0.899915 7.65554 0.89988 8.50044 0.900005C9.384 0.900136 10.1 1.61648 10.1 2.49991V2.50186V2.50382V2.50577V2.50772V2.50968V2.51163V2.51358V2.51554V2.51749V2.51944V2.5214V2.52335V2.5253V2.52726V2.52921V2.53116V2.53312V2.53507V2.53702V2.53898V2.54093V2.54288V2.54484V2.54679V2.54874V2.5507V2.55265V2.5546V2.55656V2.55851V2.56046V2.56242V2.56437V2.56632V2.56828V2.57023V2.57218V2.57414V2.57609V2.57804V2.58V2.58195V2.5839V2.58586V2.58781V2.58976V2.59172V2.59367V2.59562V2.59758V2.59953V2.60148V2.60344V2.60539V2.60734V2.60929V2.61125V2.6132V2.61515V2.61711V2.61906V2.62101V2.62297V2.62492V2.62687V2.62883V2.63078V2.63273V2.63469V2.63664V2.63859V2.64055V2.6425V2.64445V2.64641V2.64836V2.65031V2.65227V2.65422V2.65617V2.65813V2.66008V2.66203V2.66399V2.66594V2.66789V2.66985V2.6718V2.67375V2.67571V2.67766V2.67961V2.68157V2.68352V2.68547V2.68743V2.68938V2.69133V2.69329V2.69524V2.69719V2.69915V2.7011V2.70305V2.70501V2.70696V2.70891V2.71087V2.71282V2.71477V2.71673V2.71868V2.72063V2.72259V2.72454V2.72649V2.72845V2.7304V2.73235V2.73431V2.73626V2.73821V2.74017V2.74212V2.74407V2.74603V2.74798V2.74993V2.75189V2.75384V2.75579V2.75775V2.7597V2.76165V2.76361V2.76556V2.76751V2.76947V2.77142V2.77337V2.77533V2.77728V2.77923V2.78118V2.78314V2.78509V2.78704V2.789V2.79095V2.7929V2.79486V2.79681V2.79876V2.80072V2.80267V2.80462V2.80658V2.80853V2.81048V2.81244V2.81439V2.81634V2.8183V2.82025V2.8222V2.82416V2.82611V2.82806V2.83002V2.83197V2.83392V2.83588V2.83783V2.83978V2.84174V2.84369V2.84564V2.8476V2.84955V2.8515V2.85346V2.85541V2.85736V2.85932V2.86127V2.86322V2.86518V2.86713V2.86908V2.87104V2.87299V2.87494V2.8769V2.87885V2.8808V2.88276V2.88471V2.88666V2.88862V2.89057V2.89252V2.89448V2.89643V2.89838V2.89998L12.5 2.89998C12.8314 2.89998 13.1 3.16861 13.1 3.49998C13.1 3.83135 12.8314 4.09998 12.5 4.09998L11.5 4.09998V12C11.5 12.8284 10.8285 13.5 10 13.5H4.00002C3.1716 13.5 2.50002 12.8284 2.50002 12V4.09999L1.50002 4.09999C1.16865 4.09999 0.900024 3.83136 0.900024 3.49999C0.900024 3.16862 1.16865 2.89999 1.50002 2.89999L3.90006 2.89999L3.90008 2.49944C3.90021 1.61587 4.6166 0.900001 5.4999 0.89997L5.96491 0.899951ZM8.90002 2.89838V2.89998L5.10006 2.89999L5.10008 2.49961C5.10011 2.27897 5.27903 2.09998 5.49994 2.09997L5.96506 2.09995H5.9651H5.96515C6.81023 2.09992 7.65524 2.09988 8.50026 2.10001C8.72102 2.10004 8.90002 2.27902 8.90002 2.49991V2.50186V2.50382V2.50577V2.50772V2.50968V2.51163V2.51358V2.51554V2.51749V2.51944V2.5214V2.52335V2.5253V2.52726V2.52921V2.53116V2.53312V2.53507V2.53702V2.53898V2.54093V2.54288V2.54484V2.54679V2.54874V2.5507V2.55265V2.5546V2.55656V2.55851V2.56046V2.56242V2.56437V2.56632V2.56828V2.57023V2.57218V2.57414V2.57609V2.57804V2.58V2.58195V2.5839V2.58586V2.58781V2.58976V2.59172V2.59367V2.59562V2.59758V2.59953V2.60148V2.60344V2.60539V2.60734V2.60929V2.61125V2.6132V2.61515V2.61711V2.61906V2.62101V2.62297V2.62492V2.62687V2.62883V2.63078V2.63273V2.63469V2.63664V2.63859V2.64055V2.6425V2.64445V2.64641V2.64836V2.65031V2.65227V2.65422V2.65617V2.65813V2.66008V2.66203V2.66399V2.66594V2.66789V2.66985V2.6718V2.67375V2.67571V2.67766V2.67961V2.68157V2.68352V2.68547V2.68743V2.68938V2.69133V2.69329V2.69524V2.69719V2.69915V2.7011V2.70305V2.70501V2.70696V2.70891V2.71087V2.71282V2.71477V2.71673V2.71868V2.72063V2.72259V2.72454V2.72649V2.72845V2.7304V2.73235V2.73431V2.73626V2.73821V2.74017V2.74212V2.74407V2.74603V2.74798V2.74993V2.75189V2.75384V2.75579V2.75775V2.7597V2.76165V2.76361V2.76556V2.76751V2.76947V2.77142V2.77337V2.77533V2.77728V2.77923V2.78118V2.78314V2.78509V2.78704V2.789V2.79095V2.7929V2.79486V2.79681V2.79876V2.80072V2.80267V2.80462V2.80658V2.80853V2.81048V2.81244V2.81439V2.81634V2.8183V2.82025V2.8222V2.82416V2.82611V2.82806V2.83002V2.83197V2.83392V2.83588V2.83783V2.83978V2.84174V2.84369V2.84564V2.8476V2.84955V2.8515V2.85346V2.85541V2.85736V2.85932V2.86127V2.86322V2.86518V2.86713V2.86908V2.87104V2.87299V2.87494V2.8769V2.87885V2.8808V2.88276V2.88471V2.88666V2.88862V2.89057V2.89252V2.89448V2.89643V2.89838ZM4.99963 6.00002C4.99963 5.72387 4.77578 5.50002 4.49963 5.50002C4.22349 5.50002 3.99963 5.72387 3.99963 6.00002V11C3.99963 11.2762 4.22349 11.5 4.49963 11.5C4.77578 11.5 4.99963 11.2762 4.99963 11V6.00002ZM7.49963 6.00002C7.49963 5.72387 7.27578 5.50002 6.99963 5.50002C6.72349 5.50002 6.49963 5.72387 6.49963 6.00002V11C6.49963 11.2762 6.72349 11.5 6.99963 11.5C7.27578 11.5 7.49963 11.2762 7.49963 11V6.00002ZM9.49963 5.50002C9.77578 5.50002 9.99963 5.72387 9.99963 6.00002V11C9.99963 11.2762 9.77578 11.5 9.49963 11.5C9.22349 11.5 8.99963 11.2762 8.99963 11V6.00002C8.99963 5.72387 9.22349 5.50002 9.49963 5.50002Z"
          fill="#BAB9B8" />
  </svg>

)

export const TrashIcon20Red = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.96491 0.899951L5.9658 0.899951C6.81064 0.899915 7.65554 0.89988 8.50044 0.900005C9.384 0.900136 10.1 1.61648 10.1 2.49991V2.50186V2.50382V2.50577V2.50772V2.50968V2.51163V2.51358V2.51554V2.51749V2.51944V2.5214V2.52335V2.5253V2.52726V2.52921V2.53116V2.53312V2.53507V2.53702V2.53898V2.54093V2.54288V2.54484V2.54679V2.54874V2.5507V2.55265V2.5546V2.55656V2.55851V2.56046V2.56242V2.56437V2.56632V2.56828V2.57023V2.57218V2.57414V2.57609V2.57804V2.58V2.58195V2.5839V2.58586V2.58781V2.58976V2.59172V2.59367V2.59562V2.59758V2.59953V2.60148V2.60344V2.60539V2.60734V2.60929V2.61125V2.6132V2.61515V2.61711V2.61906V2.62101V2.62297V2.62492V2.62687V2.62883V2.63078V2.63273V2.63469V2.63664V2.63859V2.64055V2.6425V2.64445V2.64641V2.64836V2.65031V2.65227V2.65422V2.65617V2.65813V2.66008V2.66203V2.66399V2.66594V2.66789V2.66985V2.6718V2.67375V2.67571V2.67766V2.67961V2.68157V2.68352V2.68547V2.68743V2.68938V2.69133V2.69329V2.69524V2.69719V2.69915V2.7011V2.70305V2.70501V2.70696V2.70891V2.71087V2.71282V2.71477V2.71673V2.71868V2.72063V2.72259V2.72454V2.72649V2.72845V2.7304V2.73235V2.73431V2.73626V2.73821V2.74017V2.74212V2.74407V2.74603V2.74798V2.74993V2.75189V2.75384V2.75579V2.75775V2.7597V2.76165V2.76361V2.76556V2.76751V2.76947V2.77142V2.77337V2.77533V2.77728V2.77923V2.78118V2.78314V2.78509V2.78704V2.789V2.79095V2.7929V2.79486V2.79681V2.79876V2.80072V2.80267V2.80462V2.80658V2.80853V2.81048V2.81244V2.81439V2.81634V2.8183V2.82025V2.8222V2.82416V2.82611V2.82806V2.83002V2.83197V2.83392V2.83588V2.83783V2.83978V2.84174V2.84369V2.84564V2.8476V2.84955V2.8515V2.85346V2.85541V2.85736V2.85932V2.86127V2.86322V2.86518V2.86713V2.86908V2.87104V2.87299V2.87494V2.8769V2.87885V2.8808V2.88276V2.88471V2.88666V2.88862V2.89057V2.89252V2.89448V2.89643V2.89838V2.89998L12.5 2.89998C12.8314 2.89998 13.1 3.16861 13.1 3.49998C13.1 3.83135 12.8314 4.09998 12.5 4.09998L11.5 4.09998V12C11.5 12.8284 10.8285 13.5 10 13.5H4.00002C3.1716 13.5 2.50002 12.8284 2.50002 12V4.09999L1.50002 4.09999C1.16865 4.09999 0.900024 3.83136 0.900024 3.49999C0.900024 3.16862 1.16865 2.89999 1.50002 2.89999L3.90006 2.89999L3.90008 2.49944C3.90021 1.61587 4.6166 0.900001 5.4999 0.89997L5.96491 0.899951ZM8.90002 2.89838V2.89998L5.10006 2.89999L5.10008 2.49961C5.10011 2.27897 5.27903 2.09998 5.49994 2.09997L5.96506 2.09995H5.9651H5.96515C6.81023 2.09992 7.65524 2.09988 8.50026 2.10001C8.72102 2.10004 8.90002 2.27902 8.90002 2.49991V2.50186V2.50382V2.50577V2.50772V2.50968V2.51163V2.51358V2.51554V2.51749V2.51944V2.5214V2.52335V2.5253V2.52726V2.52921V2.53116V2.53312V2.53507V2.53702V2.53898V2.54093V2.54288V2.54484V2.54679V2.54874V2.5507V2.55265V2.5546V2.55656V2.55851V2.56046V2.56242V2.56437V2.56632V2.56828V2.57023V2.57218V2.57414V2.57609V2.57804V2.58V2.58195V2.5839V2.58586V2.58781V2.58976V2.59172V2.59367V2.59562V2.59758V2.59953V2.60148V2.60344V2.60539V2.60734V2.60929V2.61125V2.6132V2.61515V2.61711V2.61906V2.62101V2.62297V2.62492V2.62687V2.62883V2.63078V2.63273V2.63469V2.63664V2.63859V2.64055V2.6425V2.64445V2.64641V2.64836V2.65031V2.65227V2.65422V2.65617V2.65813V2.66008V2.66203V2.66399V2.66594V2.66789V2.66985V2.6718V2.67375V2.67571V2.67766V2.67961V2.68157V2.68352V2.68547V2.68743V2.68938V2.69133V2.69329V2.69524V2.69719V2.69915V2.7011V2.70305V2.70501V2.70696V2.70891V2.71087V2.71282V2.71477V2.71673V2.71868V2.72063V2.72259V2.72454V2.72649V2.72845V2.7304V2.73235V2.73431V2.73626V2.73821V2.74017V2.74212V2.74407V2.74603V2.74798V2.74993V2.75189V2.75384V2.75579V2.75775V2.7597V2.76165V2.76361V2.76556V2.76751V2.76947V2.77142V2.77337V2.77533V2.77728V2.77923V2.78118V2.78314V2.78509V2.78704V2.789V2.79095V2.7929V2.79486V2.79681V2.79876V2.80072V2.80267V2.80462V2.80658V2.80853V2.81048V2.81244V2.81439V2.81634V2.8183V2.82025V2.8222V2.82416V2.82611V2.82806V2.83002V2.83197V2.83392V2.83588V2.83783V2.83978V2.84174V2.84369V2.84564V2.8476V2.84955V2.8515V2.85346V2.85541V2.85736V2.85932V2.86127V2.86322V2.86518V2.86713V2.86908V2.87104V2.87299V2.87494V2.8769V2.87885V2.8808V2.88276V2.88471V2.88666V2.88862V2.89057V2.89252V2.89448V2.89643V2.89838ZM4.99963 6.00002C4.99963 5.72387 4.77578 5.50002 4.49963 5.50002C4.22349 5.50002 3.99963 5.72387 3.99963 6.00002V11C3.99963 11.2762 4.22349 11.5 4.49963 11.5C4.77578 11.5 4.99963 11.2762 4.99963 11V6.00002ZM7.49963 6.00002C7.49963 5.72387 7.27578 5.50002 6.99963 5.50002C6.72349 5.50002 6.49963 5.72387 6.49963 6.00002V11C6.49963 11.2762 6.72349 11.5 6.99963 11.5C7.27578 11.5 7.49963 11.2762 7.49963 11V6.00002ZM9.49963 5.50002C9.77578 5.50002 9.99963 5.72387 9.99963 6.00002V11C9.99963 11.2762 9.77578 11.5 9.49963 11.5C9.22349 11.5 8.99963 11.2762 8.99963 11V6.00002C8.99963 5.72387 9.22349 5.50002 9.49963 5.50002Z"
          fill="#F06060" />
  </svg>

)

export const SwitchOnIcon = () => (
  <svg width="36" height="24" viewBox="0 0 36 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_136_25311)">
      <path
        d="M0 12C0 5.37258 5.37258 0 12 0H24C30.6274 0 36 5.37258 36 12V12C36 18.6274 30.6274 24 24 24H12C5.37258 24 0 18.6274 0 12V12Z"
        fill="#73AF9F" />
      <g filter="url(#filter0_ddd_136_25311)">
        <circle cx="24" cy="12" r="10" fill="#F9F9F9" />
      </g>
    </g>
    <defs>
      <filter id="filter0_ddd_136_25311" x="6" y="-2" width="36" height="36" filterUnits="userSpaceOnUse"
              colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha" />
        <feOffset />
        <feGaussianBlur stdDeviation="0.5" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_136_25311" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha" />
        <feOffset />
        <feGaussianBlur stdDeviation="1" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0" />
        <feBlend mode="normal" in2="effect1_dropShadow_136_25311" result="effect2_dropShadow_136_25311" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha" />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="4" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0" />
        <feBlend mode="normal" in2="effect2_dropShadow_136_25311" result="effect3_dropShadow_136_25311" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_136_25311" result="shape" />
      </filter>
      <clipPath id="clip0_136_25311">
        <path
          d="M0 12C0 5.37258 5.37258 0 12 0H24C30.6274 0 36 5.37258 36 12V12C36 18.6274 30.6274 24 24 24H12C5.37258 24 0 18.6274 0 12V12Z"
          fill="white" />
      </clipPath>
    </defs>
  </svg>

)

export const SwitchOffIconDark = () => (
  <svg width="39" height="24" viewBox="0 0 39 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.599976" width="38.4" height="24" rx="12" fill="#333332" fillOpacity="0.08" />
    <rect x="3" y="2.3999" width="19.2" height="19.2" rx="9.6" fill="white" />
  </svg>

)

export const CheckboxOnIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2" y="2" width="16" height="16" rx="6" fill="#FF7C5C" />
    <path d="M13.1429 8L8.95236 12L6.85715 10" stroke="#F9F9F9" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>

)

export const CheckboxOffIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2.5" y="2.5" width="15" height="15" rx="5.5" stroke="#D8D7D6" />
  </svg>

)

export const DeliveryIcon20 = ({ color = "#BAB9B8" }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.3837 5.25569C6.50709 4.25316 7.35863 3.5 8.36872 3.5H16.2385C17.44 3.5 18.3703 4.55183 18.2236 5.74431L17.1159 14.7443C16.9925 15.7468 16.1409 16.5 15.1308 16.5H7.26103C6.05955 16.5 5.12924 15.4482 5.27601 14.2557L6.3837 5.25569ZM9.79959 6.2998C10.0757 6.2998 10.2996 6.52366 10.2996 6.7998C10.2996 7.73869 11.0607 8.4998 11.9996 8.4998C12.9385 8.4998 13.6996 7.73869 13.6996 6.7998C13.6996 6.52366 13.9234 6.2998 14.1996 6.2998C14.4757 6.2998 14.6996 6.52366 14.6996 6.7998C14.6996 8.29097 13.4908 9.4998 11.9996 9.4998C10.5084 9.4998 9.29959 8.29097 9.29959 6.7998C9.29959 6.52366 9.52345 6.2998 9.79959 6.2998ZM0.900391 5.9999C0.900391 5.66853 1.16902 5.3999 1.50039 5.3999H4.50039C4.83176 5.3999 5.10039 5.66853 5.10039 5.9999C5.10039 6.33127 4.83176 6.5999 4.50039 6.5999H1.50039C1.16902 6.5999 0.900391 6.33127 0.900391 5.9999ZM0.900391 8.4999C0.900391 8.16853 1.16902 7.8999 1.50039 7.8999H4.00039C4.33176 7.8999 4.60039 8.16853 4.60039 8.4999C4.60039 8.83127 4.33176 9.0999 4.00039 9.0999H1.50039C1.16902 9.0999 0.900391 8.83127 0.900391 8.4999ZM1.50039 10.3999C1.16902 10.3999 0.900391 10.6685 0.900391 10.9999C0.900391 11.3313 1.16902 11.5999 1.50039 11.5999H3.50039C3.83176 11.5999 4.10039 11.3313 4.10039 10.9999C4.10039 10.6685 3.83176 10.3999 3.50039 10.3999H1.50039ZM0.900391 13.4999C0.900391 13.1685 1.16902 12.8999 1.50039 12.8999H3.00039C3.33176 12.8999 3.60039 13.1685 3.60039 13.4999C3.60039 13.8313 3.33176 14.0999 3.00039 14.0999H1.50039C1.16902 14.0999 0.900391 13.8313 0.900391 13.4999Z"
          fill={color} />
  </svg>
)

export const ExpandIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.11101 4.17971C7.26216 3.41054 8.61553 3 10 3C11.8559 3.00214 13.6351 3.74033 14.9474 5.05262C16.2597 6.36491 16.9979 8.14414 17 10C17 11.3845 16.5895 12.7378 15.8203 13.889C15.0511 15.0401 13.9579 15.9373 12.6788 16.4672C11.3997 16.997 9.99224 17.1356 8.63437 16.8655C7.2765 16.5954 6.02922 15.9287 5.05026 14.9497C4.07129 13.9708 3.4046 12.7235 3.13451 11.3656C2.86441 10.0078 3.00303 8.6003 3.53285 7.32122C4.06266 6.04213 4.95987 4.94888 6.11101 4.17971ZM7.54105 8.84664C7.34579 8.65138 7.02921 8.65138 6.83395 8.84664C6.63868 9.0419 6.63868 9.35849 6.83395 9.55375L9.64645 12.3662C9.84171 12.5615 10.1583 12.5615 10.3536 12.3662L13.1661 9.55375C13.3613 9.35849 13.3613 9.0419 13.1661 8.84664C12.9708 8.65138 12.6542 8.65138 12.4589 8.84664L10 11.3056L7.54105 8.84664Z"
          fill="#929191" />
  </svg>

)

export const CollapseIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.11101 4.17971C7.26215 3.41054 8.61553 3 10 3C11.8559 3.00215 13.6351 3.74033 14.9474 5.05262C16.2597 6.36491 16.9979 8.14414 17 10C17 11.3845 16.5895 12.7378 15.8203 13.889C15.0511 15.0401 13.9579 15.9373 12.6788 16.4672C11.3997 16.997 9.99224 17.1356 8.63437 16.8655C7.2765 16.5954 6.02922 15.9287 5.05026 14.9497C4.07129 13.9708 3.4046 12.7235 3.13451 11.3656C2.86441 10.0078 3.00303 8.6003 3.53285 7.32122C4.06266 6.04213 4.95987 4.94888 6.11101 4.17971ZM12.4589 11.3658C12.6542 11.561 12.9708 11.561 13.1661 11.3658C13.3613 11.1705 13.3613 10.8539 13.1661 10.6587L10.3536 7.84615C10.1583 7.65089 9.84171 7.65089 9.64645 7.84615L6.83395 10.6587C6.63868 10.8539 6.63868 11.1705 6.83395 11.3658C7.02921 11.561 7.34579 11.561 7.54105 11.3658L10 8.90681L12.4589 11.3658Z"
          fill="#929191" />
  </svg>
)

export const CollapseIcon20Grayscale400 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.11101 4.17996C7.26215 3.41079 8.61553 3.00024 10 3.00024C11.8559 3.00239 13.6351 3.74058 14.9474 5.05287C16.2597 6.36516 16.9979 8.14439 17 10.0002C17 11.3847 16.5895 12.7381 15.8203 13.8892C15.0511 15.0404 13.9579 15.9376 12.6788 16.4674C11.3997 16.9972 9.99224 17.1358 8.63437 16.8657C7.2765 16.5956 6.02922 15.929 5.05026 14.95C4.07129 13.971 3.4046 12.7237 3.13451 11.3659C2.86441 10.008 3.00303 8.60054 3.53285 7.32146C4.06266 6.04238 4.95987 4.94913 6.11101 4.17996ZM12.4589 11.366C12.6542 11.5613 12.9708 11.5613 13.1661 11.366C13.3613 11.1707 13.3613 10.8542 13.1661 10.6589L10.3536 7.8464C10.1583 7.65114 9.84171 7.65114 9.64645 7.8464L6.83395 10.6589C6.63868 10.8542 6.63868 11.1707 6.83395 11.366C7.02921 11.5613 7.34579 11.5613 7.54105 11.366L10 8.90706L12.4589 11.366Z"
          fill="#BAB9B8" />
  </svg>
)

export const ClockIcon16 = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.66658 3.42915C5.65328 2.76986 6.81331 2.41797 8 2.41797C9.59074 2.41981 11.1158 3.05254 12.2406 4.17736C13.3654 5.30218 13.9982 6.82723 14 8.41797C14 9.60466 13.6481 10.7647 12.9888 11.7514C12.3295 12.7381 11.3925 13.5071 10.2961 13.9612C9.19975 14.4154 7.99335 14.5342 6.82946 14.3027C5.66557 14.0712 4.59648 13.4997 3.75736 12.6606C2.91825 11.8215 2.3468 10.7524 2.11529 9.58851C1.88378 8.42462 2.0026 7.21822 2.45673 6.12187C2.91085 5.02551 3.67989 4.08844 4.66658 3.42915ZM8.2998 5.61816C8.2998 5.34202 8.07595 5.11816 7.7998 5.11816C7.52366 5.11816 7.2998 5.34202 7.2998 5.61816V8.61816C7.2998 8.89431 7.52366 9.11816 7.7998 9.11816H10.7998C11.0759 9.11816 11.2998 8.89431 11.2998 8.61816C11.2998 8.34202 11.0759 8.11816 10.7998 8.11816H8.2998V5.61816Z"
          fill="#BAB9B8" />
  </svg>
)

export const ClockIcon16Active = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.66658 3.42915C5.65328 2.76986 6.81331 2.41797 8 2.41797C9.59074 2.41981 11.1158 3.05254 12.2406 4.17736C13.3654 5.30218 13.9982 6.82723 14 8.41797C14 9.60466 13.6481 10.7647 12.9888 11.7514C12.3295 12.7381 11.3925 13.5071 10.2961 13.9612C9.19975 14.4154 7.99335 14.5342 6.82946 14.3027C5.66557 14.0712 4.59648 13.4997 3.75736 12.6606C2.91825 11.8215 2.3468 10.7524 2.11529 9.58851C1.88378 8.42462 2.0026 7.21822 2.45673 6.12187C2.91085 5.02551 3.67989 4.08844 4.66658 3.42915ZM8.2998 5.61816C8.2998 5.34202 8.07595 5.11816 7.7998 5.11816C7.52366 5.11816 7.2998 5.34202 7.2998 5.61816V8.61816C7.2998 8.89431 7.52366 9.11816 7.7998 9.11816H10.7998C11.0759 9.11816 11.2998 8.89431 11.2998 8.61816C11.2998 8.34202 11.0759 8.11816 10.7998 8.11816H8.2998V5.61816Z"
          fill="#FAE0DA" />
  </svg>
)

export const ChairIcon16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.69922 2.2002C4.87079 2.2002 4.19922 2.87177 4.19922 3.70019V5.5002C4.19922 6.22616 4.71494 6.83167 5.4 6.97035V8.3999H5.09961C4.27118 8.3999 3.59961 9.07148 3.59961 9.8999V11.5999H3.6002V13.9999C3.6002 14.3313 3.86882 14.5999 4.2002 14.5999C4.53157 14.5999 4.8002 14.3313 4.8002 13.9999V11.5999H11.1998V13.9999C11.1998 14.3313 11.4684 14.5999 11.7998 14.5999C12.1312 14.5999 12.3998 14.3313 12.3998 13.9999V10.3999L12.3996 10.3844V9.8999C12.3996 9.07148 11.728 8.3999 10.8996 8.3999H10.6V6.97003C11.2843 6.83075 11.7992 6.22561 11.7992 5.5002V3.7002C11.7992 2.87177 11.1276 2.2002 10.2992 2.2002H5.69922ZM6.6 8.3999H9.4V7.0002H6.6V8.3999Z"
          fill="#BAB9B8" />
  </svg>
)

export const KeyboardIcon20 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M19.8002 9.81865L19.8001 9.20105C19.7998 8.08037 19.7997 7.52004 19.5816 7.09226C19.3898 6.7159 19.0839 6.41013 18.7074 6.21842C18.2796 6.00052 17.7193 6.00063 16.5986 6.00086C13.5321 6.00148 10.4656 6.00154 7.39913 6.00091C6.27925 6.00068 5.71931 6.00057 5.29138 6.21855C4.9151 6.41021 4.60885 6.71642 4.41714 7.09267C4.19911 7.52058 4.19915 8.0801 4.19924 9.19914C4.19939 11.0652 4.20026 12.9313 4.2003 14.7973C4.20033 15.9187 4.20034 16.4794 4.41828 16.9072C4.61006 17.2836 4.91571 17.5893 5.29211 17.7812C5.71984 17.9992 6.28011 17.9993 7.40063 17.9995C10.4681 18.0001 13.5355 18.0002 16.6029 17.9995C17.7216 17.9993 18.281 17.9992 18.7087 17.7812C19.0847 17.5896 19.3908 17.2834 19.5825 16.9074C19.8004 16.4797 19.8005 15.9205 19.8007 14.8023C19.8009 13.1411 19.8006 11.4799 19.8002 9.81865Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.59766 12.002H17.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.59766 9.36133H17.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.59766 14.6426H7.19766" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.59766 14.6426H14.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M16.7969 14.6426H17.3969" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const KeyboardIconLight20 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M19.8002 9.81865L19.8001 9.20105C19.7998 8.08037 19.7997 7.52004 19.5816 7.09226C19.3898 6.7159 19.0839 6.41013 18.7074 6.21842C18.2796 6.00052 17.7193 6.00063 16.5986 6.00086C13.5321 6.00148 10.4656 6.00154 7.39913 6.00091C6.27925 6.00068 5.71931 6.00057 5.29138 6.21855C4.9151 6.41021 4.60885 6.71642 4.41714 7.09267C4.19911 7.52058 4.19915 8.0801 4.19924 9.19914C4.19939 11.0652 4.20026 12.9313 4.2003 14.7973C4.20033 15.9187 4.20034 16.4794 4.41828 16.9072C4.61006 17.2836 4.91571 17.5893 5.29211 17.7812C5.71984 17.9992 6.28011 17.9993 7.40063 17.9995C10.4681 18.0001 13.5355 18.0002 16.6029 17.9995C17.7216 17.9993 18.281 17.9992 18.7087 17.7812C19.0847 17.5896 19.3908 17.2834 19.5825 16.9074C19.8004 16.4797 19.8005 15.9205 19.8007 14.8023C19.8009 13.1411 19.8006 11.4799 19.8002 9.81865Z"
      fill="none" stroke="rgba(249, 249, 249, 0.8)" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.59766 12.002H17.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.59766 9.36133H17.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.59766 14.6426H7.19766" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.59766 14.6426H14.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M16.7969 14.6426H17.3969" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const KeyboardIcon20New = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M17.6796 5.52042C17.3986 4.99619 16.9506 4.57024 16.3993 4.3032C16.047 4.13259 15.6698 4.06346 15.2521 4.03109C14.8485 3.99981 14.3519 3.9999 13.7447 4.00001L13.7164 4.00002L6.28112 4.00007L6.25279 4.00006C5.646 3.99995 5.14971 3.99987 4.74634 4.03116C4.3288 4.06355 3.95169 4.13274 3.59944 4.3034C3.04828 4.57043 2.5998 4.99695 2.31901 5.52112C2.13958 5.85607 2.06682 6.21455 2.03274 6.61147C1.99982 6.99486 1.99991 7.4665 2.00001 8.04309L2.00002 8.07004L2.00043 9.83479L2.0009 11.9257L2.0009 11.9526C2.00096 12.5306 2.00101 13.0032 2.034 13.3872C2.06814 13.7845 2.14089 14.1434 2.32037 14.4784C2.60127 15.0027 3.04901 15.4285 3.60026 15.6957C3.95241 15.8664 4.32957 15.9356 4.74717 15.9681C5.15066 15.9995 5.64719 15.9996 6.25436 15.9997L6.28271 15.9997C8.76226 16.0001 11.2419 16.0001 13.7215 15.9997L13.7499 15.9997H13.75C14.356 15.9996 14.8517 15.9995 15.2547 15.9681C15.6719 15.9356 16.0487 15.8663 16.4007 15.6957C16.9516 15.4287 17.4 15.0022 17.6806 14.4783C17.86 14.1435 17.9328 13.7853 17.9669 13.3886C17.9999 13.0054 18 12.5341 18 11.958L18 11.931C18.0001 10.6446 17.9998 9.35816 17.9995 8.07184L17.9995 8.04487C17.9994 7.46735 17.9993 6.99508 17.9662 6.6113C17.932 6.21409 17.8592 5.85535 17.6796 5.52042ZM4.89961 7C4.62347 7 4.39961 7.22386 4.39961 7.5C4.39961 7.77614 4.62347 8 4.89961 8H15.1192C15.3953 8 15.6192 7.77614 15.6192 7.5C15.6192 7.22386 15.3953 7 15.1192 7H4.89961ZM4.89961 9.49805C4.62347 9.49805 4.39961 9.7219 4.39961 9.99805C4.39961 10.2742 4.62347 10.498 4.89961 10.498H15.1192C15.3953 10.498 15.6192 10.2742 15.6192 9.99805C15.6192 9.7219 15.3953 9.49805 15.1192 9.49805H4.89961ZM4.39961 12.4971C4.39961 12.2209 4.62347 11.9971 4.89961 11.9971H5.46736C5.74351 11.9971 5.96736 12.2209 5.96736 12.4971C5.96736 12.7732 5.74351 12.9971 5.46736 12.9971H4.89961C4.62347 12.9971 4.39961 12.7732 4.39961 12.4971ZM7.73848 11.9971C7.46233 11.9971 7.23848 12.2209 7.23848 12.4971C7.23848 12.7732 7.46233 12.9971 7.73848 12.9971H12.2805C12.5567 12.9971 12.7805 12.7732 12.7805 12.4971C12.7805 12.2209 12.5567 11.9971 12.2805 11.9971H7.73848ZM14.051 12.4971C14.051 12.2209 14.2748 11.9971 14.551 11.9971H15.1187C15.3949 11.9971 15.6187 12.2209 15.6187 12.4971C15.6187 12.7732 15.3949 12.9971 15.1187 12.9971H14.551C14.2748 12.9971 14.051 12.7732 14.051 12.4971Z"
          fill="#BAB9B8" />
  </svg>
)

export const EditIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M12.3549 3.75C12.063 3.75 11.783 3.86596 11.5766 4.07237L4.07237 11.5766C3.97016 11.6788 3.88909 11.8002 3.83378 11.9337C3.77847 12.0672 3.75 12.2104 3.75 12.3549V15.1494C3.75 15.4413 3.86596 15.7212 4.07237 15.9276C4.27877 16.134 4.55872 16.25 4.85063 16.25H7.85233C8.01155 16.25 8.16425 16.1867 8.27684 16.0742L15.9276 8.42337C16.134 8.21696 16.25 7.93701 16.25 7.64511C16.25 7.3532 16.134 7.07326 15.9276 6.86685L13.1332 4.07237C12.9267 3.86596 12.6468 3.75 12.3549 3.75ZM12.3545 4.99175L15.0074 7.64473L13.8549 8.79725L11.2022 6.14405L12.3545 4.99175Z"
          fill="#BAB9B8" />
  </svg>
)

export const EditIcon20Light = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M12.3549 3.75C12.063 3.75 11.783 3.86596 11.5766 4.07237L4.07237 11.5766C3.97016 11.6788 3.88909 11.8002 3.83378 11.9337C3.77847 12.0672 3.75 12.2104 3.75 12.3549V15.1494C3.75 15.4413 3.86596 15.7212 4.07237 15.9276C4.27877 16.134 4.55872 16.25 4.85063 16.25H7.85233C8.01155 16.25 8.16425 16.1867 8.27684 16.0742L15.9276 8.42337C16.134 8.21696 16.25 7.93701 16.25 7.64511C16.25 7.3532 16.134 7.07326 15.9276 6.86685L13.1332 4.07237C12.9267 3.86596 12.6468 3.75 12.3549 3.75ZM12.3545 4.99175L15.0074 7.64473L13.8549 8.79725L11.2022 6.14405L12.3545 4.99175Z"
          fill="#F9F9F9" fillOpacity="0.8" />
  </svg>
)

export const CopyIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.75 13.4999H13.3C14.4201 13.4999 14.9802 13.4999 15.408 13.2819C15.7843 13.0902 16.0903 12.7842 16.282 12.4079C16.5 11.9801 16.5 11.42 16.5 10.2999V6.69991C16.5 5.5798 16.5 5.01975 16.282 4.59193C16.0903 4.2156 15.7843 3.90964 15.408 3.7179C14.9802 3.49991 14.4201 3.49991 13.3 3.49991H9.7C8.57989 3.49991 8.01984 3.49991 7.59202 3.7179C7.21569 3.90964 6.90973 4.2156 6.71799 4.59193C6.5 5.01975 6.5 5.5798 6.5 6.69991V7.24991"
      stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M10.3 6.49991H6.7C5.5799 6.49991 5.01984 6.49991 4.59202 6.7179C4.21569 6.90964 3.90973 7.2156 3.71799 7.59193C3.5 8.01975 3.5 8.5798 3.5 9.69991V13.2999C3.5 14.42 3.5 14.9801 3.71799 15.4079C3.90973 15.7842 4.21569 16.0902 4.59202 16.2819C5.01984 16.4999 5.5799 16.4999 6.7 16.4999H10.3C11.4201 16.4999 11.9802 16.4999 12.408 16.2819C12.7843 16.0902 13.0903 15.7842 13.282 15.4079C13.5 14.9801 13.5 14.42 13.5 13.2999V9.69991C13.5 8.5798 13.5 8.01975 13.282 7.59193C13.0903 7.2156 12.7843 6.90964 12.408 6.7179C11.9802 6.49991 11.4201 6.49991 10.3 6.49991Z"
      fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const UserIcon16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8C14 9.64935 13.3345 11.1433 12.2574 12.2279C11.1704 13.3223 9.66436 14 8 14C6.33581 14 4.82986 13.3225 3.74296 12.2282C2.66564 11.1436 2 9.64952 2 8ZM4.38614 11.4554C5.29645 12.4072 6.579 13 8 13C9.42115 13 10.7038 12.4071 11.6141 11.4552C10.6853 10.5377 9.40893 9.9714 8.00028 9.9714C6.59148 9.9714 5.31499 10.5379 4.38614 11.4554ZM10.1 6.71422C10.1 7.89768 9.14064 8.85707 7.95717 8.85707C6.7737 8.85707 5.81431 7.89768 5.81431 6.71422C5.81431 5.53075 6.7737 4.57136 7.95717 4.57136C9.14064 4.57136 10.1 5.53075 10.1 6.71422Z"
          fill="#BAB9B8" />
  </svg>
)

export const TableIcon16 = () => (
  <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1">
    <g id="surface1dd">
      <path
        style={{ stroke: "none", fillRule: "evenodd", fill: "rgb(72.941176%,72.54902%,72.156863%)", fillOpacity: 1 }}
        d="M 5.726562 2.519531 C 5.460938 2.519531 5.246094 2.734375 5.246094 3 C 5.246094 3.265625 5.460938 3.480469 5.726562 3.480469 L 10.273438 3.480469 C 10.539062 3.480469 10.753906 3.265625 10.753906 3 C 10.753906 2.734375 10.539062 2.519531 10.273438 2.519531 Z M 3.480469 5.726562 C 3.480469 5.460938 3.265625 5.246094 3 5.246094 C 2.734375 5.246094 2.519531 5.460938 2.519531 5.726562 L 2.519531 10.273438 C 2.519531 10.539062 2.734375 10.753906 3 10.753906 C 3.265625 10.753906 3.480469 10.539062 3.480469 10.273438 Z M 13.480469 5.726562 C 13.480469 5.460938 13.265625 5.246094 13 5.246094 C 12.734375 5.246094 12.519531 5.460938 12.519531 5.726562 L 12.519531 10.273438 C 12.519531 10.539062 12.734375 10.753906 13 10.753906 C 13.265625 10.753906 13.480469 10.539062 13.480469 10.273438 Z M 5.726562 12.519531 C 5.460938 12.519531 5.246094 12.734375 5.246094 13 C 5.246094 13.265625 5.460938 13.480469 5.726562 13.480469 L 10.273438 13.480469 C 10.539062 13.480469 10.753906 13.265625 10.753906 13 C 10.753906 12.734375 10.539062 12.519531 10.273438 12.519531 Z M 10.101562 4.417969 C 9.882812 4.398438 9.617188 4.398438 9.296875 4.398438 L 6.703125 4.398438 C 6.382812 4.398438 6.117188 4.398438 5.898438 4.417969 C 5.675781 4.4375 5.46875 4.476562 5.273438 4.574219 C 4.972656 4.726562 4.726562 4.972656 4.574219 5.273438 C 4.476562 5.46875 4.4375 5.675781 4.417969 5.898438 C 4.398438 6.117188 4.398438 6.382812 4.398438 6.703125 L 4.398438 9.296875 C 4.398438 9.617188 4.398438 9.882812 4.417969 10.101562 C 4.4375 10.324219 4.476562 10.53125 4.574219 10.726562 C 4.726562 11.027344 4.972656 11.273438 5.273438 11.425781 C 5.46875 11.523438 5.675781 11.5625 5.898438 11.582031 C 6.117188 11.601562 6.382812 11.601562 6.703125 11.601562 L 9.296875 11.601562 C 9.617188 11.601562 9.882812 11.601562 10.101562 11.582031 C 10.324219 11.5625 10.53125 11.523438 10.726562 11.425781 C 11.027344 11.273438 11.273438 11.027344 11.425781 10.726562 C 11.523438 10.53125 11.5625 10.324219 11.582031 10.101562 C 11.601562 9.882812 11.601562 9.617188 11.601562 9.296875 L 11.601562 6.703125 C 11.601562 6.382812 11.601562 6.117188 11.582031 5.898438 C 11.5625 5.675781 11.523438 5.46875 11.425781 5.273438 C 11.273438 4.972656 11.027344 4.726562 10.726562 4.574219 C 10.53125 4.476562 10.324219 4.4375 10.101562 4.417969 Z M 10.101562 4.417969 " />
    </g>
  </svg>
)

export const PhoneIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M7.06012 3.52725C7.27499 3.50125 7.4925 3.54578 7.67986 3.65414C7.86696 3.76234 8.01388 3.92834 8.09856 4.12717L9.35396 7.05643C9.41892 7.20799 9.44538 7.37327 9.43101 7.53754C9.41663 7.70182 9.36185 7.85998 9.27155 7.99796L8.23177 9.58669C8.70057 10.5419 9.47512 11.3131 10.4324 11.7775L11.9972 10.7341C12.1356 10.6418 12.2949 10.5855 12.4605 10.5703C12.6262 10.5551 12.793 10.5814 12.9459 10.647L15.8718 11.9009C16.0707 11.9856 16.2374 12.1328 16.3457 12.3199C16.454 12.5073 16.4986 12.7248 16.4726 12.9397L16.4722 12.943C16.3642 13.7877 15.9521 14.564 15.313 15.1267C14.6738 15.6894 13.8515 15.9998 13 15.9998C10.6131 15.9998 8.32387 15.0516 6.63604 13.3638C4.94821 11.676 4 9.38677 4 6.99982C4.00004 6.1483 4.31047 5.32597 4.87314 4.68685C5.43581 4.04772 6.21216 3.63561 7.0568 3.52766L7.06012 3.52725Z"
      fill="#BAB9B8" />
  </svg>
)

export const EmailIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M3.15504 5.34526C2.99204 5.64373 2.89937 5.98612 2.89931 6.35014L2.89894 8.12772L2.89894 8.12775C2.89852 9.96589 2.89809 11.8043 2.89887 13.6426C2.89936 14.8019 3.83949 15.7411 4.99855 15.7411H14.9984C16.1582 15.7411 17.0984 14.8009 17.0984 13.6411V6.35024C17.0984 5.98599 17.0056 5.6434 16.8425 5.34479L10.3861 10.8084C10.1624 10.9977 9.83462 10.9977 9.61091 10.8084L3.15504 5.34526ZM4.01053 4.49719L9.99849 9.56434L15.9868 4.49692C15.6922 4.33949 15.3557 4.25024 14.9984 4.25024H4.99944C4.64189 4.25024 4.30522 4.3396 4.01053 4.49719Z"
          fill="#BAB9B8" />
  </svg>

)

export const EnvelopeIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M3.51708 4.8643C3.13645 5.24439 2.90096 5.76978 2.90087 6.35014L2.9005 8.12772L2.9005 8.12775C2.90007 9.96589 2.89965 11.8043 2.90043 13.6426C2.90091 14.8019 3.84105 15.7411 5.00011 15.7411H14.9999C16.1597 15.7411 17.0999 14.8009 17.0999 13.6411V6.35024C17.0999 5.76963 16.8643 5.24406 16.4835 4.86392L10.0001 10.3503L3.51708 4.8643ZM3.53119 4.85034H16.4697C16.091 4.47913 15.5722 4.25024 14.9999 4.25024H5.001C4.42874 4.25024 3.90997 4.47913 3.53119 4.85034Z"
          fill="#BAB9B8" />
  </svg>

)

export const CloseIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM12.3536 7.64645C12.5488 7.84171 12.5488 8.15829 12.3536 8.35355L10.7071 10L12.3536 11.6464C12.5488 11.8417 12.5488 12.1583 12.3536 12.3536C12.1583 12.5488 11.8417 12.5488 11.6464 12.3536L10 10.7071L8.35355 12.3536C8.15829 12.5488 7.84171 12.5488 7.64645 12.3536C7.45118 12.1583 7.45118 11.8417 7.64645 11.6464L9.29289 10L7.64645 8.35355C7.45118 8.15829 7.45118 7.84171 7.64645 7.64645C7.84171 7.45118 8.15829 7.45118 8.35355 7.64645L10 9.29289L11.6464 7.64645C11.8417 7.45118 12.1583 7.45118 12.3536 7.64645Z"
          fill="#BAB9B8" />
  </svg>
)

export const CheckIcon20Green = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z"
      fill="#4D9985" />
    <path d="M12.75 8.25L9.08331 11.75L7.25 10" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const CheckIcon20 = ({ color = "#BAB9B8" }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z"
      fill={color} />
    <path d="M12.75 8.25L9.08331 11.75L7.25 10" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const CheckIcon70Green = () => (
  <svg width="70" height="70" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z"
      fill="#4D9985" />
    <path d="M12.75 8.25L9.08331 11.75L7.25 10" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const WarningIcon20Red = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z"
      fill="#F06060" />
    <path d="M10 6.5V10.5" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M10 14C10.4142 14 10.75 13.6642 10.75 13.25C10.75 12.8358 10.4142 12.5 10 12.5C9.58579 12.5 9.25 12.8358 9.25 13.25C9.25 13.6642 9.58579 14 10 14Z"
      fill="#F9F9F9" />
  </svg>
)

export const GlobeSimpleIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M1.90039 10C1.90039 5.52652 5.52688 1.90002 10.0004 1.90002C14.4739 1.90002 18.1004 5.52652 18.1004 10C18.1004 14.4735 14.4739 18.1 10.0004 18.1C5.52688 18.1 1.90039 14.4735 1.90039 10Z"
          fill="#BAB9B8" />
    <mask id="mask0_2866_57127" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="1" y="1" width="18"
          height="18">
      <path fillRule="evenodd" clipRule="evenodd"
            d="M1.90039 10C1.90039 5.52652 5.52688 1.90002 10.0004 1.90002C14.4739 1.90002 18.1004 5.52652 18.1004 10C18.1004 14.4735 14.4739 18.1 10.0004 18.1C5.52688 18.1 1.90039 14.4735 1.90039 10Z"
            fill="#333332" />
    </mask>
    <g mask="url(#mask0_2866_57127)">
      <path fillRule="evenodd" clipRule="evenodd"
            d="M7.38183 9.50012C7.4325 7.65306 7.76148 6.01087 8.25272 4.81616C8.52078 4.16424 8.82732 3.66962 9.14212 3.3458C9.45541 3.02353 9.74514 2.90002 10 2.90002C10.2549 2.90002 10.5446 3.02353 10.8579 3.3458C11.1727 3.66962 11.4792 4.16424 11.7473 4.81616C12.2385 6.01087 12.5675 7.65306 12.6182 9.50012L7.38183 9.50012ZM6.38148 9.50012H1.87109V10.5001H6.38149C6.43226 12.4519 6.77855 14.2283 7.32786 15.5642C7.62531 16.2876 7.99216 16.906 8.4251 17.3513C8.85955 17.7982 9.39191 18.1 10 18.1C10.6081 18.1 11.1404 17.7982 11.5749 17.3513C12.0078 16.906 12.3747 16.2876 12.6721 15.5642C13.2215 14.2283 13.5677 12.4519 13.6185 10.5001H18.1289V9.50012L13.6185 9.50012C13.5678 7.54827 13.2215 5.77183 12.6721 4.43587C12.3747 3.71246 12.0078 3.09409 11.5749 2.64874C11.1404 2.20185 10.6081 1.90002 10 1.90002C9.39191 1.90002 8.85955 2.20185 8.4251 2.64874C7.99216 3.09409 7.62531 3.71246 7.32786 4.43587C6.77853 5.77183 6.43224 7.54827 6.38148 9.50012ZM12.6182 10.5001C12.5675 12.3471 12.2385 13.9892 11.7473 15.1839C11.4792 15.8358 11.1727 16.3304 10.8579 16.6543C10.5446 16.9765 10.2549 17.1 10 17.1C9.74514 17.1 9.45541 16.9765 9.14212 16.6543C8.82732 16.3304 8.52078 15.8358 8.25272 15.1839C7.7615 13.9892 7.43252 12.3471 7.38184 10.5001L12.6182 10.5001Z"
            fill="white" />
    </g>
  </svg>
)

export const AllOLogo64 = () => (
  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="32" cy="32" r="32" fill="#FF7C5C" />
    <path d="M26.3984 39.7016L28.8818 39.7016L28.8818 24.2998L26.3984 24.2998L26.3984 39.7016Z" fill="#F9F9F9" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M43.4355 40C47.8538 40 51.4355 36.4183 51.4355 32C51.4355 27.5817 47.8538 24 43.4355 24C39.0173 24 35.4355 27.5817 35.4355 32C35.4355 36.4183 39.0173 40 43.4355 40ZM43.4353 37.5169C46.4821 37.5169 48.952 35.047 48.952 32.0003C48.952 28.9535 46.4821 26.4836 43.4353 26.4836C40.3885 26.4836 37.9186 28.9535 37.9186 32.0003C37.9186 35.047 40.3885 37.5169 43.4353 37.5169Z"
          fill="#F9F9F9" />
    <path d="M31.0654 39.6787H33.5488V24.2998C32.1773 24.2998 31.0654 25.4116 31.0654 26.7832V39.6787Z"
          fill="#F9F9F9" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M21.7348 29.1383C20.7785 28.3376 19.594 27.7852 18.1071 27.7852C14.7343 27.7852 12 30.5194 12 33.8923C12 37.2652 14.7343 39.9994 18.1071 39.9994C19.5936 39.9994 20.7785 39.4518 21.7348 38.6512L21.8074 39.7009H24.2144L24.2144 28.0844L21.8083 28.0844L21.7348 29.1383ZM21.7345 33.8925C21.7345 35.896 20.1104 37.5201 18.1069 37.5201C16.1035 37.5201 14.4794 35.896 14.4794 33.8925C14.4794 31.8891 16.1035 30.265 18.1069 30.265C20.1104 30.265 21.7345 31.8891 21.7345 33.8925Z"
          fill="#F9F9F9" />
  </svg>
)

export const AllOLogo32 = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="16" cy="16" r="16" fill="#FF7C5C" />
    <path d="M13.1992 19.8507L14.4409 19.8507L14.4409 12.1498L13.1992 12.1498L13.1992 19.8507Z" fill="#F9F9F9" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M21.7176 20C23.9267 20 25.7176 18.2091 25.7176 16C25.7176 13.7909 23.9267 12 21.7176 12C19.5084 12 17.7176 13.7909 17.7176 16C17.7176 18.2091 19.5084 20 21.7176 20ZM21.7174 18.7585C23.2408 18.7585 24.4758 17.5235 24.4758 16.0001C24.4758 14.4767 23.2408 13.2418 21.7174 13.2418C20.1941 13.2418 18.9591 14.4767 18.9591 16.0001C18.9591 17.5235 20.1941 18.7585 21.7174 18.7585Z"
          fill="#F9F9F9" />
    <path d="M15.5328 19.8393H16.7745V12.1498C16.0887 12.1498 15.5328 12.7057 15.5328 13.3915V19.8393Z"
          fill="#F9F9F9" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10.8674 14.5693C10.3893 14.169 9.79698 13.8928 9.05356 13.8928C7.36713 13.8928 6 15.2599 6 16.9463C6 18.6328 7.36713 19.9999 9.05356 19.9999C9.79679 19.9999 10.3893 19.7261 10.8674 19.3258L10.9037 19.8506H12.1072L12.1072 14.0424L10.9041 14.0424L10.8674 14.5693ZM10.8672 16.9464C10.8672 17.9482 10.0552 18.7602 9.05346 18.7602C8.05174 18.7602 7.23969 17.9482 7.23969 16.9464C7.23969 15.9447 8.05174 15.1327 9.05346 15.1327C10.0552 15.1327 10.8672 15.9447 10.8672 16.9464Z"
          fill="#F9F9F9" />
  </svg>

)

export const EnvelopeIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M3.157 5.34513C2.99399 5.64361 2.90132 5.986 2.90126 6.35002L2.90089 8.1276L2.90089 8.12763C2.90047 9.96577 2.90005 11.8041 2.90082 13.6424C2.90131 14.8018 3.84144 15.741 5.00051 15.741H15.0003C16.1601 15.741 17.1003 14.8008 17.1003 13.641V6.35012C17.1003 5.98587 17.0076 5.64328 16.8444 5.34467L10.388 10.8082C10.1643 10.9975 9.83658 10.9975 9.61286 10.8082L3.157 5.34513ZM4.01248 4.49707L10.0004 9.56422L15.9887 4.4968C15.6942 4.33937 15.3577 4.25012 15.0003 4.25012H5.0014C4.64385 4.25012 4.30717 4.33948 4.01248 4.49707Z"
          fill="#BAB9B8" />
  </svg>

)

export const PhoneIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M7.06012 3.52725C7.27499 3.50125 7.4925 3.54578 7.67986 3.65414C7.86696 3.76234 8.01388 3.92834 8.09856 4.12717L9.35396 7.05643C9.41892 7.20799 9.44538 7.37327 9.43101 7.53754C9.41663 7.70182 9.36185 7.85998 9.27155 7.99796L8.23177 9.58669C8.70057 10.5419 9.47512 11.3131 10.4324 11.7775L11.9972 10.7341C12.1356 10.6418 12.2949 10.5855 12.4605 10.5703C12.6262 10.5551 12.793 10.5814 12.9459 10.647L15.8718 11.9009C16.0707 11.9856 16.2374 12.1328 16.3457 12.3199C16.454 12.5073 16.4986 12.7248 16.4726 12.9397L16.4722 12.943C16.3642 13.7877 15.9521 14.564 15.313 15.1267C14.6738 15.6894 13.8515 15.9998 13 15.9998C10.6131 15.9998 8.32387 15.0516 6.63604 13.3638C4.94821 11.676 4 9.38677 4 6.99982C4.00004 6.1483 4.31047 5.32597 4.87314 4.68685C5.43581 4.04772 6.21216 3.63561 7.0568 3.52766L7.06012 3.52725Z"
      fill="#BAB9B8" />
  </svg>

)

export const ArrowLeftGray20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.83323 3.76422C7.06659 2.94011 8.51664 2.50024 10 2.50024C11.9884 2.50254 13.8947 3.29346 15.3008 4.69948C16.7068 6.10551 17.4977 8.01182 17.5 10.0002C17.5 11.4836 17.0601 12.9337 16.236 14.167C15.4119 15.4004 14.2406 16.3617 12.8701 16.9293C11.4997 17.497 9.99168 17.6455 8.53682 17.3561C7.08197 17.0667 5.7456 16.3524 4.6967 15.3035C3.64781 14.2546 2.9335 12.9183 2.64411 11.4634C2.35472 10.0086 2.50325 8.50056 3.07091 7.13012C3.63856 5.75967 4.59986 4.58833 5.83323 3.76422ZM13.6247 9.96461C13.6247 10.2408 13.4009 10.4646 13.1247 10.4646L8.07963 10.4646L9.88145 12.2724C10.0764 12.468 10.0759 12.7846 9.88028 12.9795C9.6847 13.1744 9.36812 13.1739 9.17318 12.9783L6.53206 10.3285C6.43536 10.2373 6.375 10.108 6.375 9.96461C6.375 9.81627 6.4396 9.68301 6.54221 9.59144L9.17293 6.94832C9.36773 6.7526 9.68432 6.75185 9.88004 6.94665C10.0758 7.14146 10.0765 7.45804 9.8817 7.65376L8.07934 9.46461L13.1247 9.46461C13.4009 9.46461 13.6247 9.68847 13.6247 9.96461Z"
          fill="#BAB9B8" />
  </svg>

)

export const MagnifierIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M15.3996 9.4999C15.3996 6.79371 13.2058 4.5999 10.4996 4.5999C7.79341 4.5999 5.59961 6.79371 5.59961 9.4999C5.59961 12.2061 7.79341 14.3999 10.4996 14.3999C13.2058 14.3999 15.3996 12.2061 15.3996 9.4999ZM10.4996 3.3999C13.8685 3.3999 16.5996 6.13097 16.5996 9.4999C16.5996 12.8688 13.8685 15.5999 10.4996 15.5999C9.03135 15.5999 7.68425 15.0812 6.63158 14.2169L6.46196 14.3866L4.42444 16.4241C4.19012 16.6584 3.81022 16.6584 3.57591 16.4241C3.34159 16.1898 3.34159 15.8099 3.57591 15.5756L5.61343 13.538L5.783 13.3685C4.91853 12.3157 4.39961 10.9684 4.39961 9.4999C4.39961 6.13097 7.13067 3.3999 10.4996 3.3999Z"
          fill="#333332" />
  </svg>

)

export const LoaderIcon20White = () => (
  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle opacity="0.4" cx="10.9971" cy="10" r="6.5" stroke="#F9F9F9" strokeWidth="2" />
    <path
      d="M4.49707 10C4.49707 6.41015 7.40722 3.5 10.9971 3.5C14.5869 3.5 17.4971 6.41015 17.4971 10C17.4971 11.731 16.8205 13.3039 15.7173 14.4687"
      stroke="#F9F9F9" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const LoaderIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle opacity="0.4" cx="10" cy="10" r="6.5" stroke="#BAB9B8" strokeWidth="2" />
    <path
      d="M3.5 10C3.5 6.41015 6.41015 3.5 10 3.5C13.5899 3.5 16.5 6.41015 16.5 10C16.5 11.731 15.8234 13.3039 14.7202 14.4688"
      stroke="#BAB9B8" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const GreenCheckboxOnDarkIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" fill="#333332" />
    <circle cx="10" cy="10" r="9" fill="#428271" />
    <path d="M6.5 10.1923L8.71053 12.5L13.5 7.5" stroke="white" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>

)

export const AlloPayLogo20 = () => (
  <svg width="71" height="21" viewBox="0 0 71 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.0626 19.6876H17.6604L17.6604 3.576L15.0626 3.576L15.0626 19.6876Z" fill="#333332" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M32.8842 20.0001C37.5061 20.0001 41.2529 16.2533 41.2529 11.6314C41.2529 7.00948 37.5061 3.2627 32.8842 3.2627C28.2623 3.2627 24.5155 7.00948 24.5155 11.6314C24.5155 16.2533 28.2623 20.0001 32.8842 20.0001ZM32.8839 17.4026C36.0711 17.4026 38.6549 14.8188 38.6549 11.6317C38.6549 8.44447 36.0711 5.86075 32.8839 5.86075C29.6968 5.86075 27.113 8.44447 27.113 11.6317C27.113 14.8188 29.6968 17.4026 32.8839 17.4026Z"
          fill="#333332" />
    <path d="M19.9452 19.6636H22.543V3.576C21.1083 3.576 19.9452 4.73907 19.9452 6.1738V19.6636Z" fill="#333332" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10.1834 8.63842C9.18306 7.80084 7.94392 7.22291 6.38857 7.22291C2.86026 7.22291 0 10.0832 0 13.6115C0 17.1398 2.86026 20.0001 6.38857 20.0001C7.94354 20.0001 9.18308 19.4272 10.1834 18.5897L10.2594 19.6878L12.7773 19.6878L12.7773 7.53594L10.2603 7.53594L10.1834 8.63842ZM10.1831 13.6117C10.1831 15.7075 8.48412 17.4064 6.38836 17.4064C4.2926 17.4064 2.59365 15.7075 2.59365 13.6117C2.59365 11.516 4.2926 9.81702 6.38836 9.81702C8.48412 9.81702 10.1831 11.516 10.1831 13.6117Z"
          fill="#333332" />
    <path
      d="M45.2965 11.2078C44.7342 8.0186 44.453 6.42401 45.111 5.18058C45.2699 4.88024 45.4655 4.60081 45.6934 4.34875C46.6368 3.30523 48.2314 3.02406 51.4206 2.46172L57.9468 1.31098C61.136 0.748639 62.7306 0.467469 63.974 1.1254C64.2743 1.28432 64.5537 1.47997 64.8058 1.70785C65.8493 2.65126 66.1305 4.24585 66.6928 7.43503C67.2552 10.6242 67.5363 12.2188 66.8784 13.4622C66.7195 13.7626 66.5238 14.042 66.296 14.2941C65.3526 15.3376 63.758 15.6187 60.5688 16.1811L54.0426 17.3318C50.8534 17.8942 49.2588 18.1753 48.0154 17.5174C47.7151 17.3585 47.4356 17.1628 47.1836 16.935C46.1401 15.9915 45.8589 14.397 45.2965 11.2078Z"
      fill="#FF8769" />
    <path
      d="M45.2965 11.2078C44.7342 8.0186 44.453 6.42401 45.111 5.18058C45.2699 4.88024 45.4655 4.60081 45.6934 4.34875C46.6368 3.30523 48.2314 3.02406 51.4206 2.46172L57.9468 1.31098C61.136 0.748639 62.7306 0.467469 63.974 1.1254C64.2743 1.28432 64.5537 1.47997 64.8058 1.70785C65.8493 2.65126 66.1305 4.24585 66.6928 7.43503C67.2552 10.6242 67.5363 12.2188 66.8784 13.4622C66.7195 13.7626 66.5238 14.042 66.296 14.2941C65.3526 15.3376 63.758 15.6187 60.5688 16.1811L54.0426 17.3318C50.8534 17.8942 49.2588 18.1753 48.0154 17.5174C47.7151 17.3585 47.4356 17.1628 47.1836 16.935C46.1401 15.9915 45.8589 14.397 45.2965 11.2078Z"
      fill="white" fillOpacity="0.6" />
    <path
      d="M44.0635 10.3157C44.0635 8.02003 44.0635 6.87222 44.5102 5.99541C44.9032 5.22414 45.5303 4.59708 46.3015 4.2041C47.1784 3.75734 48.3262 3.75734 50.6218 3.75734H63.4652C65.7608 3.75734 66.9086 3.75734 67.7854 4.2041C68.5567 4.59708 69.1837 5.22414 69.5767 5.99541C70.0235 6.87222 70.0235 8.02003 70.0235 10.3157V13.5948C70.0235 15.8904 70.0235 17.0383 69.5767 17.9151C69.1837 18.6863 68.5567 19.3134 67.7854 19.7064C66.9086 20.1531 65.7608 20.1531 63.4652 20.1531H50.6218C48.3262 20.1531 47.1784 20.1531 46.3015 19.7064C45.5303 19.3134 44.9032 18.6863 44.5102 17.9151C44.0635 17.0383 44.0635 15.8904 44.0635 13.5948V10.3157Z"
      fill="#FF8769" />
    <path
      d="M48.1409 15.2179H49.8216V12.6405H51.2345C53.0627 12.6405 54.1263 11.5498 54.1263 9.96222C54.1263 8.38241 53.0821 7.2684 51.2772 7.2684H48.1409V15.2179ZM49.8216 11.2936V8.64248H50.955C51.9254 8.64248 52.3951 9.17037 52.3951 9.96222C52.3951 10.7502 51.9254 11.2936 50.9628 11.2936H49.8216Z"
      fill="white" />
    <path
      d="M56.6176 15.3304C57.4987 15.3304 58.0693 14.9462 58.3605 14.3911H58.407V15.2179H59.9752V11.1966C59.9752 9.7759 58.7719 9.17814 57.4444 9.17814C56.016 9.17814 55.0766 9.8613 54.8476 10.9481L56.377 11.0723C56.4895 10.6764 56.8428 10.3853 57.4366 10.3853C57.9995 10.3853 58.3216 10.6687 58.3216 11.1577V11.181C58.3216 11.5653 57.9141 11.6158 56.8777 11.7167C55.6977 11.8254 54.638 12.2213 54.638 13.5527C54.638 14.7327 55.4803 15.3304 56.6176 15.3304ZM57.0912 14.1893C56.5827 14.1893 56.2178 13.9525 56.2178 13.4983C56.2178 13.0325 56.6021 12.8035 57.1843 12.722C57.5453 12.6716 58.1353 12.5862 58.3333 12.4542V13.0869C58.3333 13.7118 57.817 14.1893 57.0912 14.1893Z"
      fill="white" />
    <path
      d="M61.9561 17.4537C63.1866 17.4537 63.8387 16.8248 64.1764 15.8622L66.4937 9.25577H64.7431L63.4971 13.6342H63.435L62.2007 9.25577H60.4617L62.6005 15.3887L62.5034 15.641C62.2861 16.1999 61.8707 16.2271 61.2885 16.0485L60.9159 17.2829C61.1526 17.3838 61.5369 17.4537 61.9561 17.4537Z"
      fill="white" />
  </svg>
)

export const AllOPayIllustration = () => (
  <svg width="355" height="150" viewBox="0 0 355 150" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_302_1101)">
      <rect width="355" height="150" fill="#4D9985" />
      <rect x="26" y="44" width="175" height="106" fill="#C0DAD3" />
      <rect x="26" y="65" width="175" height="25" fill="#2B574B" />
      <ellipse cx="232.5" cy="118" rx="31.5" ry="32" fill="#FFD36E" />
      <circle cx="296" cy="118" r="32" fill="#C0DAD3" />
      <circle cx="264" cy="62" r="32" fill="white" />
    </g>
    <defs>
      <clipPath id="clip0_302_1101">
        <rect width="355" height="150" rx="8" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export const AlloPayIllustration140 = () => (
  <svg width="140" height="140" viewBox="0 0 140 140" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1193_44596)">
      <rect width="355" height="150" fill="#4D9985" />
      <rect x="-95.4165" y="42.8333" width="160.417" height="97.1667" fill="#C0DAD3" />
      <rect x="-95.4165" y="62.0833" width="160.417" height="22.9167" fill="#2B574B" />
      <ellipse cx="93.8752" cy="110.667" rx="28.875" ry="29.3333" fill="#FFD36E" />
      <circle cx="152.084" cy="110.667" r="29.3333" fill="#C0DAD3" />
      <circle cx="122.75" cy="59.3333" r="29.3333" fill="white" />
    </g>
    <defs>
      <clipPath id="clip0_1193_44596">
        <rect width="140" height="140" rx="8" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export const EuroSymbolIcon20Dark = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z"
      stroke="#626260" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <g clipPath="url(#clip0_302_1121)">
      <path
        d="M12.5996 7.5C12.1776 7.01767 11.643 6.69141 11.0627 6.56213C10.4825 6.43285 9.88243 6.50631 9.33782 6.7733C8.79321 7.04029 8.32829 7.48892 8.00135 8.06294C7.67441 8.63695 7.5 9.31081 7.5 10C7.5 10.6892 7.67441 11.363 8.00135 11.9371C8.32829 12.5111 8.79321 12.9597 9.33782 13.2267C9.88243 13.4937 10.4825 13.5672 11.0627 13.4379C11.643 13.3086 12.1776 12.9823 12.5996 12.5"
        stroke="#626260" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M10.5 9H6.5M6.5 11H10.5" stroke="#626260" strokeLinecap="round" strokeLinejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_302_1121">
        <rect width="12" height="12" fill="white" transform="translate(4 4)" />
      </clipPath>
    </defs>
  </svg>
)

export const EuroSymbolIcon20Light = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M11.2612 5.17484C10.5047 5.02436 9.72056 5.10159 9.00792 5.39677C8.29529 5.69196 7.6862 6.19183 7.25766 6.83318C6.94121 7.30678 6.73371 7.84182 6.64682 8.3999H10.5004C10.8318 8.3999 11.1004 8.66853 11.1004 8.9999C11.1004 9.33127 10.8318 9.5999 10.5004 9.5999H6.60039V10.8999H9.50039C9.83176 10.8999 10.1004 11.1685 10.1004 11.4999C10.1004 11.8313 9.83176 12.0999 9.50039 12.0999H6.75873C6.86949 12.4767 7.03712 12.8366 7.25766 13.1666C7.6862 13.808 8.29529 14.3079 9.00792 14.603C9.72056 14.8982 10.5047 14.9754 11.2612 14.825C12.0178 14.6745 12.7127 14.303 13.2581 13.7576C13.4924 13.5233 13.8723 13.5233 14.1066 13.7576C14.3409 13.9919 14.3409 14.3718 14.1066 14.6062C13.3934 15.3194 12.4847 15.8051 11.4953 16.0019C10.506 16.1987 9.48061 16.0977 8.5487 15.7117C7.6168 15.3257 6.82029 14.672 6.2599 13.8333C5.90608 13.3038 5.65641 12.7155 5.52043 12.0999H4.50039C4.16902 12.0999 3.90039 11.8313 3.90039 11.4999C3.90039 11.1685 4.16902 10.8999 4.50039 10.8999H5.40039V9.5999H4.50039C4.16902 9.5999 3.90039 9.33127 3.90039 8.9999C3.90039 8.66853 4.16902 8.3999 4.50039 8.3999H5.4358C5.53013 7.60366 5.8113 6.83787 6.2599 6.1665C6.82029 5.32781 7.6168 4.67413 8.5487 4.28812C9.48061 3.90211 10.506 3.80112 11.4953 3.9979C12.4847 4.19468 13.3934 4.68041 14.1066 5.39366C14.3409 5.62797 14.3409 6.00787 14.1066 6.24219C13.8723 6.4765 13.4924 6.4765 13.2581 6.24219C12.7127 5.69676 12.0178 5.32532 11.2612 5.17484Z"
          fill="#BAB9B8" />
  </svg>
)

export const EuroCurrencyIcon20 = ({ color = '#BAB9B8' }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M11.2612 5.17484C10.5047 5.02436 9.72056 5.10159 9.00792 5.39677C8.29529 5.69196 7.6862 6.19183 7.25766 6.83318C6.94121 7.30678 6.73371 7.84182 6.64682 8.3999H10.5004C10.8318 8.3999 11.1004 8.66853 11.1004 8.9999C11.1004 9.33127 10.8318 9.5999 10.5004 9.5999H6.60039V10.8999H9.50039C9.83176 10.8999 10.1004 11.1685 10.1004 11.4999C10.1004 11.8313 9.83176 12.0999 9.50039 12.0999H6.75873C6.86949 12.4767 7.03712 12.8366 7.25766 13.1666C7.6862 13.808 8.29529 14.3079 9.00792 14.603C9.72056 14.8982 10.5047 14.9754 11.2612 14.825C12.0178 14.6745 12.7127 14.303 13.2581 13.7576C13.4924 13.5233 13.8723 13.5233 14.1066 13.7576C14.3409 13.9919 14.3409 14.3718 14.1066 14.6062C13.3934 15.3194 12.4847 15.8051 11.4953 16.0019C10.506 16.1987 9.48061 16.0977 8.5487 15.7117C7.6168 15.3257 6.82029 14.672 6.2599 13.8333C5.90608 13.3038 5.65641 12.7155 5.52043 12.0999H4.50039C4.16902 12.0999 3.90039 11.8313 3.90039 11.4999C3.90039 11.1685 4.16902 10.8999 4.50039 10.8999H5.40039V9.5999H4.50039C4.16902 9.5999 3.90039 9.33127 3.90039 8.9999C3.90039 8.66853 4.16902 8.3999 4.50039 8.3999H5.4358C5.53013 7.60366 5.8113 6.83787 6.2599 6.1665C6.82029 5.32781 7.6168 4.67413 8.5487 4.28812C9.48061 3.90211 10.506 3.80112 11.4953 3.9979C12.4847 4.19468 13.3934 4.68041 14.1066 5.39366C14.3409 5.62797 14.3409 6.00787 14.1066 6.24219C13.8723 6.4765 13.4924 6.4765 13.2581 6.24219C12.7127 5.69676 12.0178 5.32532 11.2612 5.17484Z"
          fill={color} />
  </svg>
)

export const PercentageForCurrencyIcon20 = ({ color = "#BAB9B8" }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.13165 14.7791C5.81449 14.4608 5.81534 13.9458 6.13357 13.6286L13.9119 5.87616C14.2301 5.55899 14.7452 5.55985 15.0623 5.87807C15.3795 6.1963 15.3786 6.71138 15.0604 7.02854L7.28212 14.781C6.9639 15.0981 6.44882 15.0973 6.13165 14.7791Z"
      fill={color} />
    <path d="M9 7C9 8.10457 8.10457 9 7 9C5.89543 9 5 8.10457 5 7C5 5.89543 5.89543 5 7 5C8.10457 5 9 5.89543 9 7Z"
          fill={color} />
    <path
      d="M16 14C16 15.1046 15.1046 16 14 16C12.8954 16 12 15.1046 12 14C12 12.8954 12.8954 12 14 12C15.1046 12 16 12.8954 16 14Z"
      fill={color} />
  </svg>
);

export const ContractIcon20Dark = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 13C12.7614 13 15 10.7614 15 8C15 5.23858 12.7614 3 10 3C7.23858 3 5 5.23858 5 8C5 10.7614 7.23858 13 10 13Z"
      stroke="#626260" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M10 10C11.1046 10 12 9.10457 12 8C12 6.89543 11.1046 6 10 6C8.89543 6 8 6.89543 8 8C8 9.10457 8.89543 10 10 10Z"
      stroke="#626260" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path
      d="M13 12V15.3821C13 16.1255 12.2178 16.609 11.5529 16.2766L10.4468 15.7236C10.1652 15.5829 9.83389 15.5829 9.55238 15.7237L8.44724 16.2763C7.78234 16.6088 7 16.1253 7 15.3819V12.0004"
      stroke="#626260" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const CreditCardIcon20Dark = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M15 4.1665H5C3.61929 4.1665 2.5 5.28579 2.5 6.6665V13.3332C2.5 14.7139 3.61929 15.8332 5 15.8332H15C16.3807 15.8332 17.5 14.7139 17.5 13.3332V6.6665C17.5 5.28579 16.3807 4.1665 15 4.1665Z"
      stroke="#626260" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M2.5 8.3335H17.5" stroke="#626260" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5.83301 12.5H5.84134" stroke="#626260" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.16699 12.5H10.8337" stroke="#626260" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const CreateRestaurant120Illustration = () => (
  <svg width="121" height="120" viewBox="0 0 121 120" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1145_45808)">
      <rect x="0.25" width="120" height="120" rx="60" fill="#FFF2D4" />
      <rect x="26.2285" y="109.791" width="67.7571" height="13.2086" fill="#FFD36E" />
      <path
        d="M26.2285 52.554C26.2285 35.1272 40.3557 21 57.7825 21H62.4317C79.8584 21 93.9856 35.1272 93.9856 52.554H26.2285Z"
        fill="#FFD36E" />
      <rect x="26.2285" y="85.8418" width="67.7571" height="13.2086" fill="#2B564B" />
      <rect x="26.2285" y="63.0935" width="67.7571" height="13.2086" fill="#FF8769" />
    </g>
    <defs>
      <clipPath id="clip0_1145_45808">
        <rect x="0.25" width="120" height="120" rx="60" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const JoinRestaurant120Illustration = () => (
  <svg width="121" height="120" viewBox="0 0 121 120" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1145_45818)">
      <rect x="0.75" width="120" height="120" rx="60" fill="#CCE5FF" />
      <rect x="19" y="44.6999" width="44.7999" height="76.2999" fill="#4F93DB" />
      <rect x="32.3008" y="29.3001" width="6.99999" height="84.6999" fill="#1A3047" />
      <rect x="39.2998" y="29.3441" width="6.99999" height="25.2" transform="rotate(-65 39.2998 29.3441)"
            fill="#1A3047" />
      <rect x="32.3008" y="44.6999" width="6.99999" height="76.2999" fill="white" />
      <rect x="51.7246" y="108.4" width="65.0999" height="12.6" fill="#FFD36E" />
      <path
        d="M51.7246 83.1997C51.7246 66.5759 65.2008 53.0997 81.8246 53.0997H86.7245C103.348 53.0997 116.825 66.5759 116.825 83.1997H51.7246Z"
        fill="#FFD36E" />
      <rect x="51.7246" y="95.7999" width="65.0999" height="12.6" fill="#2B564B" />
      <rect x="51.7246" y="83.2" width="65.0999" height="12.6" fill="#FF8769" />
    </g>
    <defs>
      <clipPath id="clip0_1145_45818">
        <rect x="0.75" width="120" height="120" rx="60" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export const ToggleCheckedIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2" y="2" width="16" height="16" rx="8" fill="#FF7C5C" />
    <path d="M13.1426 8L8.95215 12L6.85693 10" stroke="#F9F9F9" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>
)

export const PaymentIllustration160 = () => (
  <svg width="160" height="187" viewBox="0 0 160 187" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3.48965" y="71.549" width="104" height="64" transform="rotate(-15 3.48965 71.549)" fill="#C0DAD3" />
    <rect x="6.85428" y="84.106" width="104" height="15" transform="rotate(-15 6.85428 84.106)" fill="#2B574B" />
    <path
      d="M90.2244 45.0984C91.7083 39.5604 97.4117 36.2769 102.963 37.7645L148.756 50.0346C154.307 51.5221 157.605 57.2174 156.121 62.7553L147.022 96.7131C146.162 99.923 144.105 102.682 141.275 104.422L137.87 106.516C134.604 108.524 132.231 111.707 131.238 115.411L115.357 174.68C113.873 180.218 108.17 183.501 102.618 182.014L66.8779 172.437C61.3263 170.949 58.0288 165.254 59.5127 159.716L90.2244 45.0984Z"
      fill="#A9A7A5" />
    <path
      d="M73.5358 40.6267C75.0197 35.0888 80.7231 31.8053 86.2747 33.2928L132.067 45.5629C137.619 47.0504 140.916 52.7457 139.433 58.2837L108.721 172.902C107.237 178.439 101.534 181.723 95.9819 180.235L50.1893 167.965C44.6377 166.478 41.3402 160.782 42.8241 155.245L73.5358 40.6267Z"
      fill="#E8E7E6" />
    <mask id="mask0_1071_81294" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="45" y="36" width="92"
          height="142">
      <path
        d="M76.3832 42.2854C77.5373 37.9781 81.9733 35.4242 86.2912 36.5812L130.408 48.4024C134.726 49.5594 137.291 53.9891 136.137 58.2963L105.761 171.661C104.607 175.968 100.171 178.522 95.853 177.365L51.7357 165.544C47.4178 164.387 44.8531 159.957 46.0072 155.65L76.3832 42.2854Z"
        fill="#C4C4C4" />
    </mask>
    <g mask="url(#mask0_1071_81294)">
      <rect x="75.0337" y="54.1387" width="58.3333" height="97.6667" transform="rotate(15 75.0337 54.1387)"
            fill="white" />
      <ellipse cx="104.662" cy="56.9011" rx="2" ry="2" transform="rotate(15 104.662 56.9011)" fill="#4B4A49" />
      <path
        d="M78.4073 56.9814C78.084 56.8947 77.7941 57.0552 77.7126 57.3594L77.712 57.3614C77.6358 57.6459 77.78 57.9093 78.0662 57.986C78.2707 58.0408 78.429 57.9708 78.5154 57.8671L78.5361 57.8727C78.533 57.8841 78.5289 57.8952 78.5259 57.9067C78.4377 58.1902 78.2752 58.3959 78.0386 58.3325C77.9075 58.2973 77.8339 58.2042 77.8227 58.0888L77.8224 58.0776L77.56 58.0073L77.559 58.0193C77.5451 58.2614 77.7059 58.4792 77.9797 58.5526C78.3547 58.6531 78.661 58.4147 78.7962 57.9101L78.7968 57.908C78.9414 57.3682 78.7255 57.0666 78.4073 56.9814ZM78.1874 57.7981C78.0179 57.7528 77.9284 57.5952 77.9749 57.4219L77.9754 57.4198C78.0202 57.2526 78.1867 57.1559 78.351 57.1999C78.5163 57.2442 78.6095 57.4127 78.5636 57.5841L78.563 57.5861C78.5177 57.7554 78.3557 57.8433 78.1874 57.7981Z"
        fill="white" />
      <path
        d="M79.2777 57.82C79.3727 57.8454 79.4614 57.7913 79.4859 57.6999C79.5107 57.6075 79.4606 57.5174 79.3656 57.4919C79.2716 57.4667 79.1822 57.5195 79.1574 57.6119C79.1329 57.7033 79.1837 57.7948 79.2777 57.82ZM79.0713 58.5903C79.1663 58.6158 79.2547 58.5627 79.2795 58.4703C79.3042 58.3779 79.2542 58.2877 79.1592 58.2622C79.0652 58.2371 78.9757 58.2899 78.951 58.3823C78.9262 58.4747 78.9773 58.5651 79.0713 58.5903Z"
        fill="white" />
      <path
        d="M80.1675 59.0988L80.4226 59.1672L80.4997 58.8796L80.7001 58.9333L80.7593 58.7122L80.5589 58.6585L80.824 57.6691L80.447 57.568C80.1616 57.8232 79.8575 58.1111 79.5739 58.3968L79.5152 58.6158L80.2445 58.8113L80.1675 59.0988ZM79.8207 58.4674L79.8249 58.4518C80.0377 58.2362 80.2845 58.0052 80.5002 57.8115L80.5157 57.8157L80.3062 58.5974L79.8207 58.4674Z"
        fill="white" />
      <path
        d="M81.1981 59.3749L81.4647 59.4463L81.8661 57.9482L81.6006 57.8771L81.1374 58.0478L81.0698 58.3001L81.5113 58.1358L81.5289 58.1405L81.1981 59.3749Z"
        fill="white" />
      <path fillRule="evenodd" clipRule="evenodd"
            d="M119.661 67.8764L119.51 67.8359C119.427 67.8135 119.341 67.8634 119.318 67.9473L118.966 69.2635C118.943 69.3474 118.993 69.4335 119.076 69.4559L119.227 69.4964C119.311 69.5188 119.397 69.4689 119.419 69.385L119.772 68.0687C119.794 67.9849 119.745 67.8988 119.661 67.8764ZM118.71 68.0013L118.861 68.0418C118.945 68.0642 118.994 68.1503 118.972 68.2342L118.714 69.196C118.692 69.2799 118.606 69.3298 118.522 69.3074L118.371 69.2669C118.288 69.2445 118.238 69.1585 118.261 69.0746L118.518 68.1127C118.541 68.0288 118.627 67.9789 118.71 68.0013ZM118.061 68.2072L117.91 68.1667C117.827 68.1444 117.741 68.1943 117.718 68.2781L117.555 68.8856C117.533 68.9695 117.582 69.0556 117.666 69.078L117.817 69.1185C117.9 69.1408 117.986 69.091 118.009 69.0071L118.172 68.3996C118.194 68.3157 118.145 68.2296 118.061 68.2072ZM117.274 68.322L117.123 68.2815C117.04 68.2592 116.954 68.309 116.932 68.3929L116.85 68.6967C116.828 68.7805 116.877 68.8667 116.961 68.889L117.112 68.9295C117.195 68.9519 117.281 68.902 117.303 68.8181L117.385 68.5144C117.407 68.4305 117.358 68.3444 117.274 68.322Z"
            fill="white" />
      <path fillRule="evenodd" clipRule="evenodd"
            d="M121.636 68.7518C121.971 68.8415 122.258 69.0571 122.439 69.354C122.452 69.3768 122.481 69.3844 122.504 69.3711L122.726 69.2407C122.738 69.2339 122.746 69.2228 122.75 69.2098C122.753 69.1967 122.751 69.1829 122.745 69.1713C122.272 68.3872 121.267 68.1179 120.466 68.5607C120.454 68.5674 120.445 68.5784 120.442 68.5914C120.438 68.6044 120.44 68.6182 120.447 68.6299L120.574 68.8539C120.587 68.8769 120.616 68.8849 120.639 68.8719C120.944 68.705 121.301 68.6621 121.636 68.7518ZM121.481 69.3282C121.665 69.3775 121.824 69.4938 121.927 69.6544C121.941 69.677 121.97 69.6844 121.993 69.6711L122.215 69.5407C122.227 69.5339 122.235 69.5226 122.239 69.5095C122.242 69.4964 122.24 69.4824 122.233 69.4708C121.922 68.9713 121.279 68.7989 120.759 69.0759C120.748 69.0825 120.739 69.0936 120.735 69.1067C120.732 69.1197 120.733 69.1337 120.74 69.1455L120.867 69.3694C120.88 69.3923 120.909 69.4004 120.932 69.3878C121.102 69.3003 121.297 69.279 121.481 69.3282ZM121.727 69.8097C121.724 69.823 121.715 69.8343 121.703 69.8409L121.32 70.0662C121.308 70.0728 121.295 70.0747 121.282 70.0713C121.27 70.0679 121.259 70.0596 121.252 70.0482L121.033 69.6612C121.026 69.6495 121.024 69.6355 121.028 69.6224C121.032 69.6094 121.041 69.5984 121.053 69.5922C121.289 69.48 121.572 69.5557 121.721 69.7712C121.728 69.7826 121.73 69.7965 121.727 69.8097Z"
            fill="white" />
      <path opacity="0.4" fillRule="evenodd" clipRule="evenodd"
            d="M124.007 69.1494L126.526 69.8242C126.665 69.8615 126.747 70.005 126.71 70.1448L126.466 71.0561C126.428 71.1959 126.285 71.279 126.146 71.2417L123.628 70.5669C123.488 70.5296 123.406 70.3861 123.443 70.2463L123.688 69.335C123.725 69.1952 123.868 69.1121 124.007 69.1494ZM123.537 69.2945C123.596 69.0709 123.825 68.9379 124.048 68.9975L126.567 69.6723C126.789 69.732 126.921 69.9616 126.861 70.1853L126.617 71.0966C126.557 71.3202 126.328 71.4532 126.105 71.3936L123.587 70.7188C123.364 70.6591 123.232 70.4294 123.292 70.2058L123.537 69.2945ZM127.091 70.7352C127.055 70.8678 126.944 70.9663 126.809 70.9852L126.971 70.3777C127.079 70.4617 127.126 70.6026 127.091 70.7352Z"
            fill="white" />
      <path
        d="M126.384 69.949L124.067 69.3282C123.956 69.2983 123.841 69.3648 123.811 69.4767L123.621 70.1854C123.591 70.2973 123.657 70.4121 123.769 70.4419L126.086 71.0628C126.197 71.0926 126.311 71.0261 126.341 70.9143L126.531 70.2055C126.561 70.0937 126.495 69.9788 126.384 69.949Z"
        fill="white" />
      <rect x="99.0741" y="42.9806" width="17.6667" height="1.66667" rx="0.833333"
            transform="rotate(15 99.0741 42.9806)" fill="#D9D9D9" />
      <rect x="78.1725" y="59.811" width="22.6667" height="22.6667" transform="rotate(15 78.1725 59.811)"
            fill="#4D9985" />
      <rect x="104.092" y="66.756" width="22.6667" height="22.6667" rx="11.3333" transform="rotate(15 104.092 66.756)"
            fill="#E8E7E6" />
      <rect x="71.2705" y="85.569" width="22.6667" height="49.3333" rx="11.3333" transform="rotate(15 71.2705 85.569)"
            fill="#FFD36E" />
      <rect x="97.0285" y="92.4708" width="22.6667" height="22.6667" rx="11.3333" transform="rotate(15 97.0285 92.4708)"
            fill="#8977B5" />
      <rect x="90.1267" y="118.229" width="22.6667" height="22.6667" rx="11.3333" transform="rotate(15 90.1267 118.229)"
            fill="#FF8769" />
    </g>
  </svg>
)

export const ChairsAndRoundTableIllustration425 = () => (
  <svg width="289" height="591" viewBox="0 0 289 591" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="459.583" y="251.067" width="243.357" height="242.932" rx="121.466" transform="rotate(120 459.583 251.067)"
          fill="#FF8769" />
    <path
      d="M76.0001 344.574L89.2737 344.574L89.2737 326.833L155.642 335.704L155.642 255.87L89.2737 264.741L89.2737 247L76.0001 247L76.0001 344.574Z"
      fill="#6EA8E4" />
    <rect x="76.0854" y="344.506" width="97.574" height="13.7131" transform="rotate(-90 76.0854 344.506)"
          fill="#193047" />
    <path
      d="M227.077 508.439L233.714 496.944L218.35 488.073L259.216 435.032L190.078 395.116L164.576 457.027L149.212 448.157L142.576 459.652L227.077 508.439Z"
      fill="#6EA8E4" />
    <rect x="227.077" y="508.439" width="97.5741" height="13.7131" transform="rotate(-150 227.077 508.439)"
          fill="#193047" />
    <path
      d="M142.56 131.777L149.196 143.273L164.56 134.402L190.062 196.314L259.2 156.397L218.334 103.356L233.698 94.4857L227.061 82.9905L142.56 131.777Z"
      fill="#6EA8E4" />
    <rect x="142.56" y="131.778" width="97.574" height="13.7131" transform="rotate(-30 142.56 131.778)"
          fill="#193047" />
  </svg>
)

export const SaladBowlsIllustrations = () => (
  <svg width="199" height="343" viewBox="0 0 199 343" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M35.6965 352.303C112.626 352.303 174.99 290.153 174.99 213.488L-103.597 213.488C-103.597 290.153 -41.2333 352.303 35.6965 352.303Z"
      fill="#6EA8E4" />
    <path
      d="M17.6883 197.464C92.0229 217.382 168.43 173.268 188.347 98.9337L-80.8418 26.8047C-100.76 101.139 -56.6463 177.546 17.6883 197.464Z"
      fill="#428271" />
    <ellipse cx="107.5" cy="26" rx="25.5" ry="26" fill="#FF9378" />
  </svg>
)

export const PointOfSaleAndHandIllustration = () => (
  <svg width="461" height="255" viewBox="0 0 461 255" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0 79.7344H166.659L214.875 189.94L42.7383 189.94L0 79.7344Z" fill="#FFD36E" />
    <rect width="57.6114" height="57.6114" rx="28.8057" transform="matrix(1 0 0.35576 0.934578 34.4663 126.667)"
          fill="#FF9378" />
    <rect width="64.3012" height="35.3571" rx="17.6786" transform="matrix(1 0 0.35576 0.934578 85.699 93.5271)"
          fill="#4D9985" />
    <rect width="36.8854" height="36.8854" transform="matrix(1 0 0.35576 0.934578 139.342 143.184)" fill="#6092C7" />
    <path d="M0 189.793H214.876V247.37H0V189.793Z" fill="#193047" />
    <path d="M0 189.939V79.8307L42.7376 189.939H0Z" fill="#665292" />
    <path d="M110.083 200.069H166.663L178.713 227.312L128.188 237.433L110.083 200.069Z" fill="white" />
    <path d="M128.188 237.432L184.769 237.336L195.717 210.094H140.237L128.188 237.432Z" fill="#403754" />
    <path d="M109.962 213.967L110.083 200.069L116.797 213.967H109.962Z" fill="#CC6C54" />
    <path d="M139.579 210.094H196.16L214.761 247.457H157.685L139.579 210.094Z" fill="white" />
    <path
      d="M165.161 152.617L186.449 94.3039C188.219 89.4547 191.384 85.2375 195.545 82.1827L255.565 38.1224L297.385 24.3127L315.298 58.2223L287.381 77.1557L286.959 78.8884C284.005 91.0252 276.934 101.762 266.951 109.271L253.364 119.491L254.064 125.86C254.479 129.639 252.283 133.218 248.727 134.56C245.603 135.739 242.078 134.938 239.77 132.526L239.12 131.846C239.142 133.829 237.72 135.535 235.766 135.87L231.86 136.541C228.944 137.041 225.974 136.024 223.978 133.84L221.134 130.73C221.162 133.258 219.466 135.481 217.02 136.121L216.534 136.248C213.997 136.912 211.318 135.895 209.861 133.715L199.532 118.266L178.805 158.586C177.26 161.592 173.888 163.183 170.585 162.466C166.166 161.505 163.611 156.865 165.161 152.617Z"
      fill="#FFA787" />
    <path d="M287.489 77.2521L255.166 37.921L306.349 -20.649L429.857 -12.4957L287.489 77.2521Z" fill="#4D9985" />
    <rect y="247.292" width="228.348" height="7.70794" fill="#FF9378" />
  </svg>

)

export const ArrowRight20NoBackground = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5 10L15 10" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11 6L15 10L11 14" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const PlayVideoButton = () => (
  <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_b_1145_67797)">
      <path
        d="M24 42C33.9411 42 42 33.9411 42 24C42 14.0589 33.9411 6 24 6C14.0589 6 6 14.0589 6 24C6 33.9411 14.0589 42 24 42Z"
        fill="#F9F9F9" fillOpacity="0.2" />
    </g>
    <path
      d="M20.6338 18.142C21.024 17.9332 21.4974 17.9561 21.8656 18.2015L29.0656 23.0015C29.3995 23.2241 29.6 23.5988 29.6 24C29.6 24.4012 29.3995 24.7759 29.0656 24.9985L21.8656 29.7985C21.4974 30.0439 21.024 30.0668 20.6338 29.858C20.2436 29.6492 20 29.2426 20 28.8V19.2C20 18.7574 20.2436 18.3508 20.6338 18.142Z"
      fill="#F9F9F9" />
    <defs>
      <filter id="filter0_b_1145_67797" x="-14" y="-14" width="76" height="76" filterUnits="userSpaceOnUse"
              colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="10" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1145_67797" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1145_67797" result="shape" />
      </filter>
    </defs>
  </svg>
)

export const TableSquare20 = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M5.29493 1.1H8.70313C9.55311 1.1 10.1545 1.10047 10.6247 1.13888C11.0878 1.17672 11.3705 1.24837 11.5927 1.36158C12.0443 1.59168 12.4115 1.95883 12.6416 2.41042C12.7548 2.63261 12.8264 2.91531 12.8643 3.37847C12.9027 3.84862 12.9031 4.45002 12.9031 5.3V8.7C12.9031 9.54998 12.9027 10.1514 12.8643 10.6215C12.8264 11.0847 12.7548 11.3674 12.6416 11.5896C12.4115 12.0412 12.0443 12.4083 11.5927 12.6384C11.3705 12.7516 11.0878 12.8233 10.6247 12.8611C10.1545 12.8995 9.55311 12.9 8.70314 12.9H5.30314C4.45295 12.9 3.85137 12.8995 3.38111 12.8611C2.91781 12.8233 2.6351 12.7517 2.41301 12.6386L2.14071 13.1732L2.41301 12.6386C1.96135 12.4086 1.59453 12.0419 1.36424 11.5904C1.25102 11.3684 1.17924 11.0857 1.14116 10.6222C1.10252 10.1519 1.10172 9.5501 1.10126 8.69967C1.10064 7.56346 1.10009 6.42735 1.10001 5.29128C1.09995 4.44423 1.10038 3.84512 1.13875 3.37653C1.17653 2.9151 1.2481 2.63295 1.36142 2.41053C1.59115 1.95959 1.95935 1.59137 2.41026 1.36161C2.63258 1.24832 2.91498 1.1767 3.37717 1.13888C3.84646 1.10047 4.44658 1.1 5.29493 1.1Z"
      stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const TableSquare20Dark = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M5.29493 1.1H8.70313C9.55311 1.1 10.1545 1.10047 10.6247 1.13888C11.0878 1.17672 11.3705 1.24837 11.5927 1.36158C12.0443 1.59168 12.4115 1.95883 12.6416 2.41042C12.7548 2.63261 12.8264 2.91531 12.8643 3.37847C12.9027 3.84862 12.9031 4.45002 12.9031 5.3V8.7C12.9031 9.54998 12.9027 10.1514 12.8643 10.6215C12.8264 11.0847 12.7548 11.3674 12.6416 11.5896C12.4115 12.0412 12.0443 12.4083 11.5927 12.6384C11.3705 12.7516 11.0878 12.8233 10.6247 12.8611C10.1545 12.8995 9.55311 12.9 8.70314 12.9H5.30314C4.45295 12.9 3.85137 12.8995 3.38111 12.8611C2.91781 12.8233 2.6351 12.7517 2.41301 12.6386L2.14071 13.1732L2.41301 12.6386C1.96135 12.4086 1.59453 12.0419 1.36424 11.5904C1.25102 11.3684 1.17924 11.0857 1.14116 10.6222C1.10252 10.1519 1.10172 9.5501 1.10126 8.69967C1.10064 7.56346 1.10009 6.42735 1.10001 5.29128C1.09995 4.44423 1.10038 3.84512 1.13875 3.37653C1.17653 2.9151 1.2481 2.63295 1.36142 2.41053C1.59115 1.95959 1.95935 1.59137 2.41026 1.36161C2.63258 1.24832 2.91498 1.1767 3.37717 1.13888C3.84646 1.10047 4.44658 1.1 5.29493 1.1Z"
      stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const TableRound20 = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.4 7C13.4 10.5346 10.5346 13.4 7 13.4C3.46538 13.4 0.6 10.5346 0.6 7C0.6 3.46538 3.46538 0.6 7 0.6C10.5346 0.6 13.4 3.46538 13.4 7Z"
      stroke="#929191" strokeWidth="1.2" strokeMiterlimit="10" />
  </svg>
)

export const TableRound20Dark = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.4 7C13.4 10.5346 10.5346 13.4 7 13.4C3.46538 13.4 0.6 10.5346 0.6 7C0.6 3.46538 3.46538 0.6 7 0.6C10.5346 0.6 13.4 3.46538 13.4 7Z"
      stroke="#333332" strokeWidth="1.2" strokeMiterlimit="10" />
  </svg>
)

export const TableSelectionS1 = () => (
  <svg width="90" height="89" viewBox="0 0 90 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="89" height="89" fill="url(#pattern0)" />
    <defs>
      <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217648" transform="translate(0.0197564 0.139272) scale(0.0038764)" />
      </pattern>
      <image id="image0_732_217648" width="232" height="200"
             xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOgAAADICAYAAADiBa1OAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAcoSURBVHgB7d3vcRNHHIfx3+458ZE3KG8IZzyTUwWYCjAVxFQAqSBOBZgOoIKYCnAqACoAKpBgLA78BvMKGaHd3MrI2EgCQ2DuK+v5zOA/suyBGT3s7t3e2dkZUVVVGcJgLXNLZbBQZi47Hy2WFmPLnGuNn+fMWrH+Y8BXql87+/VrZ//YQ90PX9gPITzz5rvD+L4bo++urq4+se/A2ZzqdDqtc8vLN83bVRdtneigZBSzs4fxffi3PxjstNvt/W/8OfMlhflLnv9lFjeJEvPCmdsOZreLouh+3ffNkVdVdYswMc/qULd+K4rbp3/+HEjryxiGO977ywbMv240d+00o6k3cXtVdcNbfEycOEPKeib4uNd7vvGlJ0oHmqa0weI2U1qcNelsQuaX7h8u2z77PE3pL16fJtky4Iz73LpUMtBer7eReXffgAXhzd28UBT3Pn1cLtB0QCitOZnWYpEcboBwVz49cCS3BnUWHxAnFk1ak7oYJmaNUoF+WDCXBiwi59aqXm/rxEMmIk1t69GzY8ACS1Pdfv+gPd4aKDSChi0DFlya6uY//7x57PPmjTa+58uvDcCJUVRiBM3z/Is7KoBFkUbR5XSllolMcWMMBAqctJ7eSASaOXfVABxxzkZNNL4G3d3dXVvK/GMDcEI01258BHUulAZggg9hrfFA0z2EDMCEoXNl44GGwAgKTFW30fxBIu9KAzAhy7LzjQfqnTtvACak28bK3/IEWFT1qZaWQqClAZgQo0agAGYgUEBY8xsVuHsCMFW6s0jjgXJ7E2A6pxAogNkIFBBGoIAwAgWEESggjEABYQQKCCNQQBiBAsIIFBBGoIAwAgWEESggjEABYQQKCCNQQBiBAsIIFBBGoIAwAgWEESggjEABYQQKCCNQQBiBAsIIFBC2dNondjqdVp7/tOEtuxpjWHPOlenXNlwsVpwB+CG+GGhVVWWMw83M+RspyJh+m4pz6S2AH2xmoGnEPLe8fMssbjrnCRJowNRA06hZj5EPjN9+DTRq4iDR3t7uGnECGk6MoGnkDMN434gTkHA0gqY1JyMnoOUo0MMDQsQJKBkFmqa25mzTAEj5MIKGLQMgxx+uPd0NAyDH53m+YQAk1VPcsG4AJHnv3GUDICltsi0NgCSfrlAxAJK4YBsQRqCAMAIFhBEoIIxAAWEECggjUEAYgQLCCBQQRqCAMAIFhBEoIIxAAWEECggjUEAYgQLCCBQQRqCAMAIFhBEoIIxAAWEECggjUEAYgQLCCBQQRqCAMAIFhBEoIIxAAWEECggjUEAYgQLCCBQQRqCAMAIFhBEoIIxAAWEECggjUEAYgQLCCBQQRqCAMAIFhBEoIIxAAWEECggjUEAYgQLCCBQQRqCAMAIFhBEoIIxAAWEECggjUEAYgQLCCBQQ5p3ZvgGQ5M1Z1wBI8mEYnxoASd68f2gAJPl+v7/DOhTQ5Nvt9v5wGO4ZADmj0ywuy+4YADmjQIui6IZhuGsApBxtVDgYDLbqd10DIOMo0LQWjeauGZECMk5s9UtTXZ+F60akgISJvbgXLqw+YSQFNEzdLJ9G0rf9gyscOAKaNfNqlrQmXVld3axH03a0yHlSoAHutE/sdDqtPM83zMK6d+6yRSujWetisXLqnzHNy+pFNABT/a+4vgcCBWbjgm1AGIECwggUEEaggDACBYQRKCCMQAFhBAoII1BAGIECwggUEEaggDACBYQRKCCMQAFhBAoII1BAGIECwggUEEaggDACBYQRKCCMQAFhBAoII1BAGIECwhoP1JntG4AJsW6j8UAjgQJTOYVAAcxGoICw5tegjikuMEO3+TVoJFBgluYDDfGZAZgQor0RGEEjIygwTQjNT3G9910DMKluo/FAh/F91wBMyGJUGEF/emIAJgzqQJ0JeFW9eB3NWgZgJO2wK4qVXyU2KgxjfGQAjtSnH0dNSATqzT00AEdcCDvpvUSgbw8OtrmqBfioPxjoBNput/dDiHcNgDlz26mJ9LHMZvn+u3d3GEUBs2B2e/yxTKCMokA6ehtvF0XRHX8ucZrluFcvXzyuj2CtGbB4uheLlfbxB+SuBw3RXWeqi0WTXvPR3LVPH5cLNA3vwcLfBiyQ9yH+eXxqOyZ5R4WiWN1Oc3EDFkB6rV+6dGln2tfk1qDHVVVvqz7kfMuAM+rwoNClrVlflw406fWebyz5pX/Yq4uzJK0501IuzRa/8Dx9VVWVzuKD+sPSgDkXQnjqfLYxbc35qbkIdIwpL+bZ4agZ735uSjvle+ZLGk3r/4NSqDcMmAPjMPv9d3fGW/i+4nvnU6fTaeV5vuFd/KNeaa+zRoWS0bl8Zw9jsEfpYpCvDfPYzzkb9vZ21waDUGZuqawX32Xm/e8xHkVbjp9X/4NbxIxvcbiZ4OMmmnRP53Tb2Ho21x3G4Rtvvptu4ZPuEnKa9eVp/AdQWfiTzUtxrAAAAABJRU5ErkJggg==" />
    </defs>
  </svg>
)

export const TableSelectionS2 = () => (
  <svg width="89" height="89" viewBox="0 0 89 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3.05176e-05" width="89" height="89" fill="url(#pattern2)" />
    <defs>
      <pattern id="pattern2" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217653" transform="translate(0 0.166156) scale(0.00378788)" />
      </pattern>
      <image id="image0_732_217653" width="264" height="200"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const TableSelectionS3 = () => (
  <svg width="90" height="89" viewBox="0 0 90 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="1" width="89" height="89" fill="url(#pattern3)" />
    <defs>
      <pattern id="pattern3" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217654" transform="translate(0 0.0606061) scale(0.00378788)" />
      </pattern>
      <image id="image0_732_217654" width="264" height="232"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const TableSelectionS4a = () => (
  <svg width="91" height="89" viewBox="0 0 91 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.666687" width="89" height="89" fill="url(#pattern4a)" />
    <defs>
      <pattern id="pattern4a" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217658" transform="scale(0.00378788)" />
      </pattern>
      <image id="image0_732_217658" width="264" height="264"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const TableSelectionS4b = () => (
  <svg width="90" height="89" viewBox="0 0 90 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="89.3333" y="1.90735e-06" width="89" height="89" transform="rotate(90 89.3333 1.90735e-06)"
          fill="url(#pattern4b)" />
    <defs>
      <pattern id="pattern4b" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217661" transform="translate(0.17) scale(0.0025)" />
      </pattern>
      <image id="image0_732_217661" width="264" height="400"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const TableSelectionS5 = () => (
  <svg width="90" height="89" viewBox="0 0 90 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="90" y="1.90735e-06" width="89" height="89" transform="rotate(90 90 1.90735e-06)" fill="url(#pattern5)" />
    <defs>
      <pattern id="pattern5" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217664" transform="translate(0.194444) scale(0.00231481)" />
      </pattern>
      <image id="image0_732_217664" width="264" height="432"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const TableSelectionS6a = () => (
  <svg width="91" height="89" viewBox="0 0 91 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="89.6667" width="89" height="89" transform="rotate(90 89.6667 0)" fill="url(#pattern6a)" />
    <defs>
      <pattern id="pattern6a" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217668" transform="translate(0.215517) scale(0.00215517)" />
      </pattern>
      <image id="image0_732_217668" width="264" height="464"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>

)

export const TableSelectionS6b = () => (
  <svg width="90" height="89" viewBox="0 0 90 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="89.3333" width="89" height="89" transform="rotate(90 89.3333 0)" fill="url(#pattern6b)" />
    <defs>
      <pattern id="pattern6b" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217671" transform="translate(0.28) scale(0.00166667)" />
      </pattern>
      <image id="image0_732_217671" width="264" height="600"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const TableSelectionS7 = () => (
  <svg width="90" height="89" viewBox="0 0 90 89" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="90" width="89" height="89" transform="rotate(90 90 0)" fill="url(#pattern7)" />
    <defs>
      <pattern id="pattern7" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217674" transform="translate(0.291139) scale(0.00158228)" />
      </pattern>
      <image id="image0_732_217674" width="264" height="632"
             xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQgAAAJ4CAYAAACZPH2jAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAABLtSURBVHgB7d1fUltXnsDx37nCbXny0MyLJyKuGrGCkBXYWcE4K3BmBWFWEHsFSVYwzgriWUHiFYSsAHUXBrdfmn6YCjTRPaMrA+0IfomnE6wL+nxeUCHkKmSfr885949K0Au7u7vrw+Gth00M7tfabpVSxjViPVZAiTistU5KaXba6fT50cnJs83NzcNg6UqwVAcHB+Nap9uD0jxalSC8jRLlaRvxZDQaTYKlEYgl6WYMd27f/nz2N7AdpJoSX/7vj8dPzCiWQyCWoJs1lKjfzh6Og7cxqVE+Npt495rgnXr1am9LHP7f5kHd29vbCt4pM4h3yMzhNzOTeMfMIN6Rbs9BHH6zeWC79zJ4JwTiHZlvSIrD72H83p35e8k7YInxDpwuLXaD381sqbFpqXH1zCDeifZx8Luq06nDw++AGcQVm5/vMLz91+B3VSMOj46ON50fcbXMIK7YcDh8GPzuZv+zrQ9v3fLeXjGBuHLtg+BKNIPB/eBKCcQVa0r5MLgapTpx6ooJxFWrDm1eldZ7e+UE4oq5QvPqFO8tAAAAAAAAAAAAAAAAAAAAACvmxtzVuvvsibY92RqUtXEb7XhQBn+sUcdR63qUcn5jke4mI27iwj9j9m/nsLub9hvfmpw+cdi27Z+aaCbT+tOk1mZy7969nbgBrm0g5reTv33702jifqnxwKCnT+YxKfFd/an9n6OTk2fX9fb81y4QXRj+ZTj8LKJuiwLXRYnytI14ct0+DexaBeIvBwefCwPX2SwUj/9tNHoS18S1CES3v1Db6bOmadxCnptgUqN8fB1mE72/q/Wrg4NHTdTvxYEbZDybCX//4sWfe//JYL0ORLekaKM+taTgpumOpg2atW9eL5v7q7dLjO6Nmx2mfBxww/V5X6KXgXjx4sXDQVO+CVgRTZRP745GX0fP9C4Q3YZkt+dgWcEqeX0CVvmobxuXvduDKFG/FQdWTbcnUWrbu1lzrwJxumEzDlhFpWwdvHjxOHqkN0uMbmkxmz3sBqywbqlxdHS82ZdTs3s0g2gfB6y4bqkx/MMftqMnejGDmF94Nbz91wB6NYvoxQxiOBz2/owyeFe6WcTt7krlHuhFIGptBQJ+7kH0QC8CMSjlfgDnSolejImlB2Jvb2/LeQ/wc90yozuyF0u29ECU0o4DuKBp261YsqUHoruHZAAXTEsZx5ItPRBtawYBl+rB2Fj+JmVTxgFcMBgM/hhLtvRANKUs/U2APpp/bMOS9f6Wc7CqZoc6l350rw+BGAdwQa0CAfSYQACp5Z8o5SxKuFQfzjBeeiCcZg2X68N/npYYQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggNTa2/7g7u7u+nB462ETg/u1tlullHH3sXnvjzZKAL308mC/zgboYa11Ukqz006nz49OTp5tbm4evs3rf3VwHxwcjGudbg9K8+iyz9H8rYHofoEALnVV46tEedpGPBmNRpNfen26xOhmDC/3978oUXeb0nzmQ3bh5qhRP+3G9quX+190Yz37uUsD0c0a7gxvfz/LzHYAN1ZbY7sb692Yv+z5C4F49Wpva1aWb2cPxwGsgnE35vf29rYWn/hZILqKtNPmmxAHWDXjtUHzzeJM4jwQ3TrEzAFW2nwm8eaexHkg7ty+/XmIA6y68Xt35i2YmwdiPq2wIQnE643Ls6XG6QyifRwAp+p0Op8wNK/3HsqjADhVBs2jrg3NcDh8GABvKBHrw1vdpRXRPgiABc1gcL9pSvkwABaVutVEdWgTuGh2NGPcuAgLuEy3D+GGMUBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBAqikRhwGwoM7a0ESJSQAsmM0eJk07rT8EwKJadppomu8CYEE7nT5vjo6OntmHABYdnZw8azY3Nw+n0/brADhVojzt2jA/zFkGgy8D4FQb8aT7Og/EaDSatNP2qwBWXlvbr7omdI/PT5Q6Pjl5PPsyCWCVTY6P5y2YOw9Et96oUT4OkYBVNeka0LXg7Bs/O9W6m1Y0g/aTEAlYNbOxv/bJ2dLizIVrMe7evbdjJgErZT5zuHv37s7iE5derNVV5Mej449sXMLN1m1IdmN9ceZwJr2as1uHbNy7tz0ry2aN6jwJuCG6EyO7MHRje2Pj3vabew6L1n7tDzsty6e7u7vbw+Hw4aw5D5pSPowa4xqxHkCvzc+U7i7KrGWnjenzH49Onv1SFBZeu1wvD/ZrAJd6f7Sx1DHqhjFASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJBae9sf3N3dXR8Obz1sYnC/1narlDKuEevvjzZKAL308mC/zgboYa11Ukqz006nz49OTp5tbm4evs3rf3VwHxwcjGudbg9K86gLwuLzvzUQ3S8QwKWuanyVKE/biCej0WjyS69PlxjdjOHl/v4XJepuU5rPLosDcD3VqJ92Y/vVy/0vurGe/dylgehmDXeGt7+fZWY7gBurrbHdjfVuzF/2/IVAvHq1tzUry7ezh+MAVsG4G/N7e3tbi0/8LBBdRdpp802IA6ya8dqg+WZxJnEeiG4dYuYAK20+k3hzT+I8EHdu3/48xAFW3fi9O/MWzM0DMZ9W2JAE4vXG5dlS43QG0T4OgFN1Op1PGJrXew/lUQCcKoPmUdeGZjgcPgyAN5SI9eGt7tKKaB8EwIJmMLjfNKV8GACLSt1qojq0CVw0O5oxblyEBVym24dwwxggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIINWUiMMAWFBnbWiixCQAFsxmD5OmndYfAmBRLTtNNM13AbCgnU6fN0dHR8/sQwCLjk5OnjWbm5uH02n7dQCcKlGedm2YH+Ysg8GXAXCqjXjSfZ0HYjQaTdpp+1UAK6+t7VddE7rH5ydKHZ+cPJ59mQSwyibHx/MWzJ0Holtv1Cgfh0jAqpp0DehacPaNn51q3U0rmkH7SYgErJrZ2F/75GxpcebCtRh3797bMZOAlTKfOdy9e3dn8YlLL9bqKvLj0fFHNi7hZus2JLuxvjhzOJNezdmtQzbu3duelWWzRnWeBNwQ3YmRXRi6sb2xcW/7zT2HRWu/9oedluXT3d3d7eFw+HDWnAdNKR9GjXGNWA+g1+ZnSncXZday08b0+Y9HJ89+KQoLr12ulwf7NYBLvT/aWOoYdcMYICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJIrb3tD+7u7q4Ph7ceNjG4X2u7VUoZ14j190cbJYBeenmwX2cD9LDWOiml2Wmn0+dHJyfPNjc3D9/m9b86uA8ODsa1TrcHpXnUBWHx+d8aiO4XCOBSVzW+SpSnbcST0Wg0+aXXp0uMbsbwcn//ixJ1tynNZ5fFAbieatRPu7H96uX+F91Yz37u0kB0s4Y7w9vfzzKzHcCN1dbY7sZ6N+Yve/5CIF692tualeXb2cNxAKtg3I35vb29rcUnfhaIriLttPkmxAFWzXht0HyzOJM4D0S3DjFzgJU2n0m8uSdxHog7t29/HuIAq2783p15C+bmgZhPK2xIAvF64/JsqXE6g2gfB8CpOp3OJwzN672H8igATpVB86hrQzMcDh8GwBtKxPrwVndpRbQPAmBBMxjcb5pSPgyARaVuNVEd2gQumh3NGDcuwgIu0+1DuGEMkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJBqSsRhACyoszY0UWISAAtms4dJ007rDwGwqJadJprmuwBY0E6nz5ujo6Nn9iGARUcnJ8+azc3Nw+m0/ToATpUoT7s2zA9zlsHgywA41UY86b7OAzEajSbttP0qgJXX1varrgnd4/MTpY5PTh7PvkwCWGWT4+N5C+bOA9GtN2qUj0MkYFVNugZ0LTj7xs9Ote6mFc2g/SREAlbNbOyvfXK2tDhz4VqMu3fv7ZhJwEqZzxzu3r27s/jEpRdrdRX58ej4IxuXcLN1G5LdWF+cOZxJr+bs1iEb9+5tz8qyWaM6TwJuiO7EyC4M3dje2Li3/eaew6K1X/vDTsvy6e7u7vZwOHw4a86DppQPo8a4RqwH0GvzM6W7izJr2Wlj+vzHo5NnvxSFhdcu18uD/RrApd4fbSx1jLphDJASCCAlEEBKIICUQAApgQBSAgGkBAJICQSQEgggJRBASiCAlEAAKYEAUgIBpAQCSAkEkBIIICUQQEoggJRAACmBAFICAaQEAkgJBJASCCC19EDMPzcQuKD2YGwsPRB9eBOgj/rwn6clBpASCCC1/D2IYokBiUks2fL3IKpAQF8tPxBt/VMAF7Q1/hZL1oMZRDWDgMu07SSWbOmBaJpmEsBFPRgbSw/EtP40CeCCQa2TWLIezCBu7QRwwUkPAlGiB/5ysP/XGrEewFx3hvFotPGvsWS9OFFqWuvzAM7NDv/3Ykz0IhBNlO8COFfa9ln0QC8C8ePx8VNXdcI/HJ2cCMSZzc3Nw7atXwUw2xgsT7sxET3Qm4u1jv7+9y/NIiCijXgSPdGbQJhFQHf0oj4ZjUaT6IleHOZ8019e7n8/28HdClg9k/dHG5vRI727H0RbyyeWGqya7t98jfJx9EzvAtFNr9po/ytghfzU1v/s09LiTC/vKDUa3XvarcUCVkD3b/2DDz7oxWHNRb3bg3jTwcGLx7NDPp8H3FCvNyU/eBw91etAdF68+PPDtWbtv12rwU3S7Tl0S+luthw91vtAdA4ODsYl6rezh+OAa65t2x9KM3jYxz2HRdciEGcsObjOXs8a6ld9XlIsulaB6HSziVmDu1A8CrgGzsJwdPT3L/tyCvXbunaBOLO7u7s+HA4fNqX+x2yn54E9Cvpkfi5Pie9qG8+7ixGvWxjOXNtALHr1am/r5KQdD8raeLb5Mx40zb/Xeh6N8dnPzX7hdTHhn/H6ZKZ/nMTXfaZL97ENs9nsZFqnf2uimXS3UOzuknYd9hfexv8BKzTqlb7UWUYAAAAASUVORK5CYII=" />
    </defs>
  </svg>
)

export const TableSelectionS8a = () => (
  <svg width="90" height="91" viewBox="0 0 90 91" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="89.3333" y="0.666687" width="89" height="89" transform="rotate(90 89.3333 0.666687)"
          fill="url(#pattern8a)" />
    <defs>
      <pattern id="pattern8a" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217678" transform="translate(0.301205) scale(0.00150602)" />
      </pattern>
      <image id="image0_732_217678" width="264" height="664"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const TableSelectionS8b = () => (
  <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="89.6667" width="89" height="89" transform="rotate(90 89.6667 0)" fill="url(#pattern8b)" />
    <defs>
      <pattern id="pattern8b" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217681" transform="translate(0.335) scale(0.00125)" />
      </pattern>
      <image id="image0_732_217681" width="264" height="800"
             xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQgAAAMgCAYAAADflvyNAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAABYlSURBVHgB7d1RchtHgufhrAI9grYfhvvCNdiKWPAEpk9g+QQrn0DaE5h7AksnsHWClU8g7QlsncD0CYiZkEi1XobzZKrZqFokRHIkkH9bux09LBLfF+EgggAUQYXy58ysLLApt8TR0dG06053R83GtCvddNSM/rkv/bT0/WZpms3z1zWlbPaL/wr8P1r82zle/Ns5/uBbs7Mnjruu+5e2tLN5/7dZ37eze/fu7ZdboCk31MHBwebdO3celbZ81fTlvkHPkCxj0pSf+791/+fk9PTFzs7OcbmBblwgahj+y3j8bSn9nihwUzSledaV8mQymczKDXKjAvGXo6PvhIGbbBGKx/9tMnlSbogbEYi6v9B38xdt235R4Oab9aX5+ibMJtoycG+Pjh62pf9FHLhFpouZ8C+vX//rgzJwgw5EXVJ0pX9mScFtU6+mjdqN5++XzcM12CVG/YtbXKZ8XOCWG/K+xCAD8fr16wejtnleYE20pXm0NZn8WAZmcIGoG5J1z8GygnXy/gBW8+XQNi4HtwfRlP4ncWDd1D2Jpu8GN2seVCDONmymBdZR0+wevX79uAzIYJYYdWmxmD0cFFhjdalxcvJuZyhHswc0g+geF1hzdakx/qd/2isDMYgZxPLGq/GdfyvAoGYRg5hBjMfjwZ8og/8sdRZxp96pPACDCETfdwIBH7tfBmAQgRg1zVcFuNA0ZRBj4toD8erVq13nHuBjdZlRr+yVa3btgWiablqAS9qu2y3X7NoDUT9DsgCXzJtmWq7ZtQei68wg4EoDGBvXv0nZNtMCXDIajf65XLNrD0TbNNf+lwBDtPy1Ddds8B85B+tqcanz2q/uDSEQ0wJc0vcCAQyYQADR9R+UcooSrjSEE8bXHgjHrOFqQ/ifpyUGEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEAkEEG186gsPDg42x+PPHrRl9FXfd7tN00zrr837fLLdFGCQ3hwd9osBetz3/axp2v1uPn95cnr6Ymdn5/hT3v+Hg/vo6Gja9/O9UdM+vOr3aP69gag/QAGu9I8aX01pnnWlPJlMJrPfe39cYtQZw5vDw++b0h+0TfutX7ILt0df+kd1bL99c/h9HevpdVcGos4a7o7v/LLIzF4Bbq2uL3t1rNcxf9XzlwLx9u2r3UVZflo8nBZgHUzrmH/16tXu6hMfBaJWpJu3z4s4wLqZboza56sziYtA1HWImQOsteVM4sM9iYtA3L1z57siDrDupn+6u2zB0jIQy2mFDUmgvN+4PF9qnM0guscF4Ew/ny8nDO37vYfmYQE404zah7UN7Xg8flAAPtCUsjn+rN5aUbr7BWBFOxp91bZN80UBWNX0u23pXdoELltczZi2bsICrlL3IXxgDBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBAJBBC1TSnHBWBFv2hDW5oyKwArFrOHWdvN+18LwKq+2W9L2/5cAFZ08/nL9uTk5IV9CGDVyenpi3ZnZ+d4Pu9+LABnmtI8q21YXuZsRqMfCsCZrpQn9esyEJPJZNbNu6cFWHtd3z2tTaiPLw5KvTs9fbz4MivAOpu9e7dswdJFIOp6oy/N10UkYF3NagNqC86/8dFR6zqtaEfdN0UkYN0sxv7GN+dLi3OX7sXY2rq3byYBa2U5c9ja2tpffeLKm7VqRX47efeljUu43eqGZB3rqzOHc/FuzroO2b53b29Rlp2+9M5JwC1RD0bWMNSxvb19b+/DPYdVG3/0h52V5dHBwcHeeDx+sGjO/bZpvih9mfalbBZg0JYnpetNmX2z35X5y99OTl/8XhRW3nu93hwd9gW40ueT7Wsdoz4wBogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAogEAog2PvWFBwcHm+PxZw/aMvqq77vdpmmmfSmbn0+2mwIM0pujw34xQI/7vp81TbvfzecvT05PX+zs7Bx/yvv/cHAfHR1N+36+N2rahzUIq8//vYGoP0ABrvSPGl9NaZ51pTyZTCaz33t/XGLUGcObw8Pvm9IftE377VVxAG6mvvSP6th+++bw+zrW0+uuDESdNdwd3/llkZm9AtxaXV/26livY/6q5y8F4u3bV7uLsvy0eDgtwDqY1jH/6tWr3dUnPgpErUg3b58XcYB1M90Ytc9XZxIXgajrEDMHWGvLmcSHexIXgbh75853RRxg3U3/dHfZgqVlIJbTChuSQHm/cXm+****************************************************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" />
    </defs>
  </svg>
)

export const TableSelectionS9 = () => (
  <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="90" y="0.333313" width="89" height="89" transform="rotate(90 90 0.333313)" fill="url(#pattern9)" />
    <defs>
      <pattern id="pattern9" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217684" transform="translate(0.341346) scale(0.00120192)" />
      </pattern>
      <image id="image0_732_217684" width="264" height="832"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const TableSelectionS10 = () => (
  <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="89.3333" y="3.05176e-05" width="89" height="89" transform="rotate(90 89.3333 3.05176e-05)"
          fill="url(#pattern10)" />
    <defs>
      <pattern id="pattern10" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_217688" transform="translate(0.347222) scale(0.00115741)" />
      </pattern>
      <image id="image0_732_217688" width="264" height="864"
             xlinkHref="data:image/png;base64,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" />
    </defs>
  </svg>
)

export const ArrowDownloadRound20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.11101 4.17971C7.26216 3.41054 8.61553 3 10 3C11.8559 3.00214 13.6351 3.74033 14.9474 5.05262C16.2597 6.36491 16.9979 8.14414 17 10C17 11.3845 16.5895 12.7378 15.8203 13.889C15.0511 15.0401 13.9579 15.9373 12.6788 16.4672C11.3997 16.997 9.99224 17.1356 8.63437 16.8655C7.2765 16.5954 6.02922 15.9287 5.05026 14.9497C4.07129 13.9708 3.4046 12.7235 3.13451 11.3656C2.86441 10.0078 3.00303 8.6003 3.53285 7.32122C4.06266 6.04213 4.95987 4.94888 6.11101 4.17971ZM8.21307 9.02452C8.01735 8.82972 7.70077 8.83046 7.50597 9.02619C7.31117 9.22191 7.31191 9.53849 7.50763 9.73329L9.62948 11.8452C9.66202 11.8793 9.69932 11.9088 9.74032 11.9326C9.9315 12.0439 10.1806 12.0178 10.3446 11.8544L12.473 9.73304C12.6686 9.5381 12.6691 9.22152 12.4741 9.02594C12.2792 8.83035 11.9626 8.82983 11.767 9.02477L10.4912 10.2964V6.5C10.4912 6.22386 10.2674 6 9.99121 6C9.71507 6 9.49121 6.22386 9.49121 6.5V10.2967L8.21307 9.02452ZM8 13C7.72386 13 7.5 13.2239 7.5 13.5C7.5 13.7761 7.72386 14 8 14H12C12.2761 14 12.5 13.7761 12.5 13.5C12.5 13.2239 12.2761 13 12 13H8Z"
          fill="#BAB9B8" />
  </svg>
)

export const UploadIcon = ({ color = '#BAB9B8' }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.49994 3.39986C5.33982 3.39982 4.40047 4.34033 4.40046 5.49984L4.40039 14.5C4.40038 15.6598 5.34059 16.6 6.50039 16.6H13.5C14.6596 16.6 15.6 15.6605 15.6 14.5005V7.91245C15.6 7.48697 15.4306 7.0821 15.1314 6.78284L12.2172 3.86863C11.9172 3.56866 11.5105 3.4 11.086 3.39999L6.49994 3.39986ZM9.99902 13.2504C9.72288 13.2504 9.49902 13.0266 9.49902 12.7504V8.95406L8.2232 10.2257C8.02762 10.4206 7.71104 10.4201 7.5161 10.2245C7.32116 10.0289 7.32168 9.71234 7.51727 9.5174L9.6456 7.39609C9.80959 7.23264 10.0587 7.20655 10.2499 7.31785C10.2909 7.34168 10.3282 7.37117 10.3608 7.40526L12.4826 9.51715C12.6783 9.71195 12.6791 10.0285 12.4843 10.2243C12.2895 10.42 11.9729 10.4207 11.7772 10.2259L10.499 8.95378V12.7504C10.499 13.0266 10.2752 13.2504 9.99902 13.2504Z"
          fill={color} />
  </svg>
)

export const DineInIllustration80 = () => (
  <svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1177_43723)">
      <rect x="0.5" width="80" height="80" rx="40" fill="#ADD1F6" />
      <rect opacity="0.9" x="21" y="47.5" width="6.5" height="32.5" fill="#3D72A9" />
      <rect x="7.99902" y="47.5" width="6.5" height="32.5" fill="#4F93DB" />
      <rect x="72.999" y="47.5" width="6.5" height="32.5" transform="rotate(-180 72.999 47.5)" fill="#2B5178" />
      <rect x="47.001" y="47.5" width="6.5" height="39" transform="rotate(90 47.001 47.5)" fill="#4F93DB" />
      <rect x="60.001" y="47.5" width="6.5" height="32.5" transform="rotate(-180 60.001 47.5)" fill="#193047" />
      <rect x="33.999" y="47.5" width="6.5" height="39" transform="rotate(-90 33.999 47.5)" fill="#2B5178" />
      <rect x="33.999" y="15" width="32.5" height="26" transform="rotate(90 33.999 15)" fill="#4F93DB" />
      <rect x="47.001" y="80" width="32.5" height="26" transform="rotate(-90 47.001 80)" fill="#2B5178" />
      <rect x="40.499" y="47.5" width="6.5" height="32.5" fill="#4F93DB" />
      <rect x="40.499" y="47.5" width="6.5" height="32.5" transform="rotate(-180 40.499 47.5)" fill="#2B5178" />
    </g>
    <defs>
      <clipPath id="clip0_1177_43723">
        <rect x="0.5" width="80" height="80" rx="40" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export const ReservationIllustration80 = () => (
  <svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1177_43739)">
      <rect x="0.5" width="80" height="80" rx="40" fill="#524072" />
      <path d="M68 79C88.9868 79 106 62.4345 106 42H30C30 62.4345 47.0132 79 68 79Z" fill="#271B33" />
      <path
        d="M57.4245 41.625C64.6494 41.625 70.5063 35.8047 70.5063 28.625H44.3428C44.3428 35.8047 50.1997 41.625 57.4245 41.625Z"
        fill="#8977B5" />
      <path
        d="M29.426 15H19.7842L19.8085 28.6326C19.8123 31.6169 18.472 34.4466 16.1542 36.3477C13.8399 38.246 12.5 41.0704 12.5 44.0503V80H36.6195V43.978C36.6195 41.0219 35.3029 38.217 33.0227 36.3153C30.7425 34.4135 29.426 31.6086 29.426 28.6525V15Z"
        fill="#D8CCE9" />
      <path d="M36.5654 61.7188H65.2347L65.2877 80H36.5654V61.7188Z" fill="#BAACD4" />
      <path d="M45.4077 80L36.6184 61.7188L27.8291 80H45.4077Z" fill="#665292" />
      <path d="M74.229 80L65.2353 61.7188L56.6504 80H74.229Z" fill="#BAACD4" />
    </g>
    <defs>
      <clipPath id="clip0_1177_43739">
        <rect x="0.5" width="80" height="80" rx="40" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export const TakeawayIllustration80 = () => (
  <svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1177_43752)">
      <rect x="0.5" width="80" height="80" rx="40" fill="#FFD36E" />
      <path d="M13.7293 15H62.4585V80H12.6699L13.7293 15Z" fill="#FCE3AA" />
      <path d="M22.2043 80L13.7297 15L3.66602 80H22.2043Z" fill="#CFAB59" />
      <path d="M71.9938 80L62.594 15L52.9258 80H71.9938Z" fill="#FCE3AA" />
      <path d="M13.7305 15H64.3136L65.1081 21.5546H14.525L13.7305 15Z" fill="#6E5B2F" />
      <path d="M47.3355 32.2451H83.2969V80.0001H46.5537L47.3355 32.2451Z" fill="#FFD36E" />
      <path d="M53.5892 80.0001L47.335 32.2451L39.9082 80.0001H53.5892Z" fill="#9E8344" />
      <path d="M90.3326 80.0001L83.3958 32.2451L76.2607 80.0001H90.3326Z" fill="#BAB9B8" />
      <path d="M47.3311 32.2451L84.6713 32.2451L85.4431 38.6125H48.1393L47.3311 32.2451Z" fill="#3D331A" />
    </g>
    <defs>
      <clipPath id="clip0_1177_43752">
        <rect x="0.5" width="80" height="80" rx="40" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export const ExpressOrderingIllustration80 = () => (
  <svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1177_43770)">
      <rect x="0.5" width="80" height="80" rx="40" fill="#4D9985" />
      <path d="M12.5938 21.9717H55.308L49.206 81.1145H18.6958L12.5938 21.9717Z" fill="#9AC5B9" />
      <path d="M58.5938 21.9717L9.30803 21.9717L13.0631 15.4003L54.8386 15.4003L58.5938 21.9717Z" fill="#1F3D35" />
      <rect width="4.92857" height="47.6429" transform="matrix(-0.866025 0.5 0.5 0.866025 64.8252 30.3291)"
            fill="#E1EAE8" />
      <path d="M14.2354 36.7578H53.6639L51.1996 63.0435H16.6996L14.2354 36.7578Z" fill="#C0DAD3" />
      <path
        d="M68.4509 81.115C86.5974 81.115 101.308 66.4043 101.308 48.2578H35.5938C35.5938 66.4043 50.3044 81.115 68.4509 81.115Z"
        fill="#2B574B" />
    </g>
    <defs>
      <clipPath id="clip0_1177_43770">
        <rect x="0.5" width="80" height="80" rx="40" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export const ChatIconWhite20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10.4561 2.66987C8.66962 2.55808 6.9038 3.10229 5.49019 4.20031C4.07659 5.29833 3.11244 6.87463 2.7788 8.63322C2.46153 10.3055 2.73517 12.0333 3.54808 13.5226L2.95128 15.6115C2.89433 15.8108 2.89172 16.0218 2.94372 16.2225C2.99572 16.4231 3.10044 16.6063 3.24704 16.7529C3.39364 16.8995 3.57677 17.0042 3.77745 17.0562C3.97813 17.1082 4.18909 17.1056 4.38842 17.0486L6.47735 16.4518C7.96668 17.2647 9.69443 17.5384 11.3667 17.2211C13.1253 16.8875 14.7016 15.9233 15.7996 14.5097C16.8976 13.0961 17.4418 11.3303 17.33 9.54379C17.2182 7.75734 16.4581 6.07314 15.1924 4.80745C13.9267 3.54177 12.2425 2.78166 10.4561 2.66987ZM6.99963 8.74976C6.99963 8.47361 7.22349 8.24976 7.49963 8.24976H12.4996C12.7758 8.24976 12.9996 8.47361 12.9996 8.74976C12.9996 9.0259 12.7758 9.24976 12.4996 9.24976H7.49963C7.22349 9.24976 6.99963 9.0259 6.99963 8.74976ZM6.99963 11.2498C6.99963 10.9736 7.22349 10.7498 7.49963 10.7498H12.4996C12.7758 10.7498 12.9996 10.9736 12.9996 11.2498C12.9996 11.5259 12.7758 11.7498 12.4996 11.7498H7.49963C7.22349 11.7498 6.99963 11.5259 6.99963 11.2498Z"
          fill="#F9F9F9" />
  </svg>
)

export const SaladBowlIllustration = () => (
  <svg width="127" height="222" viewBox="0 0 127 222" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="67.6594" y="44.4841" width="5.68195" height="36.9827" transform="rotate(-3.3251 67.6594 44.4841)"
          fill="#FFD36E" />
    <path
      d="M-30.9564 130.005C-10.7491 150.958 22.6179 151.562 43.5709 131.355L-29.6065 55.4773C-50.5594 75.6847 -51.1638 109.052 -30.9564 130.005Z"
      fill="#2B574B" />
    <rect x="38.4521" y="8.05566" width="5.68195" height="36.9827" transform="rotate(54.3701 38.4521 8.05566)"
          fill="#FFD36E" />
    <path
      d="M28.9324 82.1286C-2.68488 88.2744 -23.3336 118.887 -17.1878 150.505L97.3084 128.249C91.1626 96.6315 60.5496 75.9828 28.9324 82.1286Z"
      fill="#2B574B" />
    <path
      d="M74.5316 65.2957C64.4483 55.284 48.1582 55.3421 38.1466 65.4253L74.6612 101.681C84.6729 91.5974 84.6148 75.3073 74.5316 65.2957Z"
      fill="#4D9985" />
    <path
      d="M-15.9605 121.157C-9.19593 148.474 18.4329 165.135 45.7503 158.371L21.2536 59.446C-6.06377 66.2106 -22.7251 93.8395 -15.9605 121.157Z"
      fill="#4D9985" />
    <circle cx="36.5076" cy="43.3046" r="8.52293" transform="rotate(16.0377 36.5076 43.3046)" fill="#8977B5" />
    <circle cx="93.9739" cy="93.5801" r="6.73416" transform="rotate(16.0377 93.9739 93.5801)" fill="#8977B5" />
    <path
      d="M74.5557 162.468C97.6551 157.978 112.741 135.612 108.251 112.513L24.6007 128.773C29.0907 151.872 51.4564 166.958 74.5557 162.468Z"
      fill="#4D9985" />
    <rect x="96.8134" y="107.506" width="5.68195" height="63.177" transform="rotate(49 96.8134 107.506)"
          fill="#FFD36E" />
    <path
      d="M4.94323 194.99C53.198 208.861 103.561 180.988 117.432 132.733L-57.3137 82.501C-71.1849 130.756 -43.3115 181.119 4.94323 194.99Z"
      fill="#FF7C5C" />
    <circle cx="48.8832" cy="96.7435" r="4.97171" transform="rotate(16.0377 48.8832 96.7435)" fill="#FFD36E" />
  </svg>
)

export const SaladBowlIllustrationSmall = () => (
  <svg width="74" height="77" viewBox="0 0 74 77" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Illustration/ Salad">
      <rect id="Carrot" width="2.28778" height="14.8907"
            transform="matrix(-0.998316 -0.0580015 -0.0580015 0.998316 29.9189 5.21289)" fill="#FFD36E" />
      <path id="Lettuce leaf"
            d="M66.6383 39.6463C58.502 48.0827 45.0671 48.3261 36.6307 40.1898L66.0947 9.63867C74.5312 17.775 74.7745 31.2098 66.6383 39.6463Z"
            fill="#2B574B" />
      <rect id="Carrot_2" width="2.28778" height="14.8907"
            transform="matrix(-0.987665 -0.15658 -0.15658 0.987665 62.5547 11.625)" fill="#FFD36E" />
      <rect id="Carrot_3" width="2.28778" height="14.8907"
            transform="matrix(-0.582547 0.812797 0.812797 0.582547 41.6787 -9.45508)" fill="#FFD36E" />
      <path id="Lettuce leaf_2"
            d="M42.524 20.3697C55.2544 22.8442 63.5683 35.1702 61.0938 47.9005L14.9932 38.9395C17.4677 26.2091 29.7937 17.8951 42.524 20.3697Z"
            fill="#2B574B" />
      <path id="Lettuce leaf_3"
            d="M24.1645 13.5922C28.2244 9.56109 34.7835 9.58446 38.8145 13.6444L24.1123 28.2422C20.0812 24.1823 20.1046 17.6232 24.1645 13.5922Z"
            fill="#4D9985" />
      <path id="Lettuce leaf_4"
            d="M60.6001 36.0855C57.8764 47.0845 46.7519 53.793 35.7529 51.0693L45.6162 11.2383C56.6152 13.962 63.3237 25.0864 60.6001 36.0855Z"
            fill="#4D9985" />
      <circle id="Ball" cx="3.43166" cy="3.43166" r="3.43166"
              transform="matrix(-0.96108 0.27627 0.27627 0.96108 44.8115 0.490234)" fill="#D8CCE9" />
      <circle id="Ball_2" cx="2.71144" cy="2.71144" r="2.71144"
              transform="matrix(-0.96108 0.27627 0.27627 0.96108 21.1797 21.625)" fill="#D8CCE9" />
      <path id="Lettuce leaf_5"
            d="M20.8676 52.7193C11.5669 50.9114 5.49274 41.9062 7.30062 32.6055L40.9814 39.1523C39.1736 48.453 30.1683 54.5272 20.8676 52.7193Z"
            fill="#4D9985" />
      <circle id="Ball_3" cx="1.71583" cy="1.71583" r="1.71583"
              transform="matrix(-0.96108 0.27627 0.27627 0.96108 60.9609 -0.919922)" fill="#D8CCE9" />
      <rect id="Carrot_4" width="2.28778" height="25.4375"
            transform="matrix(-0.656059 0.75471 0.75471 0.656059 18.1797 30.5879)" fill="#FFD36E" />
      <circle id="Ball_4" cx="2.0018" cy="2.0018" r="2.0018"
              transform="matrix(-0.96108 0.27627 0.27627 0.96108 38.8506 23.7773)" fill="#FFD36E" />
      <path id="Salmon Bowl"
            d="M48.8968 65.812C29.4675 71.3971 9.18938 60.1742 3.60429 40.7449L73.9639 20.5195C79.549 39.9488 68.326 60.2269 48.8968 65.812Z"
            fill="#FF8769" />
    </g>
  </svg>
)

export const FriesIllustration = () => (
  <svg width="72" height="154" viewBox="0 0 72 154" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="9.86974" height="125.542" transform="matrix(-0.97451 0.224344 0.224344 0.97451 77.285 5.64062)"
          fill="#FFD36E" />
    <rect width="9.86974" height="114.606" transform="matrix(-0.909606 0.415473 0.415473 0.909606 66.2457 15.5354)"
          fill="#FFD36E" />
    <rect width="9.86974" height="94.4191" transform="matrix(-0.909606 0.415473 0.415473 0.909606 26.0846 53.0203)"
          fill="#FFD36E" />
    <rect width="9.86974" height="110.433" transform="matrix(-0.909606 0.415473 0.415473 0.909606 39.6215 32.5198)"
          fill="#FFD36E" />
    <rect x="37.8602" y="24.3809" width="9.86974" height="117.191" transform="rotate(-20.0117 37.8602 24.3809)"
          fill="#FFD36E" />
    <rect x="46.8337" y="27.1396" width="9.86974" height="98.0955" transform="rotate(-30.2466 46.8337 27.1396)"
          fill="#FFD36E" />
    <rect x="8.93958" y="53.6233" width="9.86974" height="87.8554" transform="rotate(-30.2466 8.93958 53.6233)"
          fill="#FFD36E" />
    <rect x="15.4292" y="33.9246" width="9.86974" height="92.3952" transform="rotate(-30.2466 15.4292 33.9246)"
          fill="#FFD36E" />
    <path d="M13.071 67.4674L96.6716 33.2136L123.337 123.044L57.1027 150.182L13.071 67.4674Z" fill="#FF7C5C" />
  </svg>
)

export const BurgerIllustration = () => (
  <svg width="91" height="93" viewBox="0 0 91 93" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M53.1026 7.14963C73.7719 10.8292 87.4485 31.1084 83.6502 52.4444L8.80017 39.1194C12.5985 17.7834 32.4334 3.47003 53.1026 7.14963Z"
      fill="#FFD36E" />
    <rect width="76.0269" height="7.35744" transform="matrix(-0.981312 -0.192423 -0.192423 0.981312 80.9908 56.4253)"
          fill="#4D9985" />
    <rect width="76.0269" height="9.80991" transform="matrix(-0.984521 -0.175267 -0.175267 0.984521 81.745 67.0457)"
          fill="#FF7C5C" />
    <path
      d="M78.4203 78.834L3.15079 68.1293L1.98743 76.3094C1.67638 78.4965 3.19725 80.5217 5.38438 80.8328L72.7336 90.411C74.9207 90.7221 76.9459 89.2012 77.2569 87.0141L78.4203 78.834Z"
      fill="#FFD36E" />
  </svg>
)

export const TerminalPaymentIllustration = () => (
  <svg width="375" height="211" viewBox="0 0 375 211" fill="none" xmlns="http://www.w3.org/2000/svg"
       style={{ borderTopLeftRadius: 16, borderTopRightRadius: 16 }}>
    <g clipPath="url(#clip0_1587_77441)">
      <rect width="375" height="211" fill="#1F3D35" />
      <path
        d="M228.122 37.0222C230.447 28.3446 239.384 23.1996 248.083 25.5305L319.837 44.7569C328.536 47.0878 333.703 56.0119 331.378 64.6895L316.939 118.577C315.708 123.171 312.764 127.12 308.712 129.611L302.09 133.683C297.415 136.558 294.018 141.114 292.598 146.415L267.504 240.067C265.179 248.745 256.242 253.89 247.543 251.559L191.54 236.553C182.841 234.222 177.674 225.298 179.999 216.621L228.122 37.0222Z"
        fill="#A9A7A5" />
      <path
        d="M201.973 30.0154C204.298 21.3378 213.235 16.1928 221.934 18.5237L293.688 37.7501C302.387 40.0809 307.554 49.005 305.229 57.6826L257.105 237.281C254.78 245.959 245.843 251.104 237.145 248.773L165.391 229.546C156.692 227.215 151.525 218.291 153.85 209.614L201.973 30.0154Z"
        fill="#E8E7E6" />
      <mask id="mask0_1587_77441" style={{ "mask-type": "alpha" }} maskUnits="userSpaceOnUse" x="158" y="23" width="143"
            height="222">
        <path
          d="M206.433 32.6143C208.242 25.8651 215.193 21.8634 221.958 23.6764L291.087 42.1994C297.853 44.0123 301.872 50.9532 300.063 57.7025L252.466 235.337C250.658 242.086 243.707 246.088 236.941 244.275L167.812 225.752C161.047 223.939 157.028 216.998 158.836 210.249L206.433 32.6143Z"
          fill="#C4C4C4" />
      </mask>
      <g mask="url(#mask0_1587_77441)">
        <rect x="204.32" y="51.1877" width="91.4044" height="153.037" transform="rotate(15 204.32 51.1877)"
              fill="white" />
        <ellipse cx="250.743" cy="55.5162" rx="3.13386" ry="3.13386" transform="rotate(15 250.743 55.5162)"
                 fill="#4B4A49" />
        <path
          d="M209.607 55.6421C209.1 55.5064 208.646 55.7578 208.518 56.2345L208.517 56.2377C208.398 56.6834 208.624 57.0962 209.072 57.2163C209.393 57.3022 209.641 57.1926 209.776 57.0301L209.809 57.0388C209.804 57.0566 209.797 57.0741 209.793 57.092C209.654 57.5362 209.4 57.8586 209.029 57.7593C208.824 57.7042 208.708 57.5582 208.691 57.3774L208.69 57.3598L208.279 57.2497L208.278 57.2684C208.256 57.6479 208.508 57.9892 208.937 58.1041C209.524 58.2616 210.004 57.888 210.216 57.0974L210.217 57.0942C210.444 56.2482 210.105 55.7757 209.607 55.6421ZM209.262 56.922C208.997 56.8508 208.856 56.604 208.929 56.3324L208.93 56.3291C209 56.0672 209.261 55.9157 209.519 55.9846C209.778 56.054 209.924 56.3181 209.852 56.5865L209.851 56.5898C209.78 56.8549 209.526 56.9927 209.262 56.922Z"
          fill="white" />
        <path
          d="M210.972 56.9563C211.121 56.9962 211.26 56.9114 211.298 56.7683C211.337 56.6235 211.259 56.4822 211.11 56.4423C210.962 56.4028 210.822 56.4855 210.783 56.6303C210.745 56.7735 210.825 56.9169 210.972 56.9563ZM210.648 58.1634C210.797 58.2033 210.936 58.1201 210.975 57.9753C211.013 57.8306 210.935 57.6892 210.786 57.6493C210.639 57.6099 210.499 57.6926 210.46 57.8374C210.421 57.9822 210.501 58.124 210.648 58.1634Z"
          fill="white" />
        <path
          d="M212.364 58.9601L212.764 59.0672L212.885 58.6166L213.199 58.7007L213.292 58.3542L212.978 58.2701L213.393 56.7197L212.802 56.5614C212.355 56.9612 211.879 57.4124 211.434 57.86L211.342 58.2033L212.485 58.5095L212.364 58.9601ZM211.821 57.9706L211.828 57.9462C212.161 57.6084 212.548 57.2464 212.886 56.943L212.91 56.9495L212.582 58.1744L211.821 57.9706Z"
          fill="white" />
        <path
          d="M213.981 59.3929L214.399 59.5048L215.028 57.1573L214.612 57.0458L213.886 57.3134L213.78 57.7088L214.472 57.4513L214.5 57.4586L213.981 59.3929Z"
          fill="white" />
        <path fillRule="evenodd" clipRule="evenodd"
              d="M274.249 72.7139L274.012 72.6504C273.881 72.6154 273.747 72.6935 273.711 72.825L273.159 74.8874C273.124 75.0189 273.201 75.1538 273.332 75.1889L273.569 75.2523C273.7 75.2874 273.834 75.2092 273.869 75.0778L274.422 73.0153C274.457 72.8839 274.38 72.749 274.249 72.7139ZM272.758 72.9097L272.995 72.9731C273.126 73.0081 273.203 73.1431 273.168 73.2745L272.764 74.7817C272.729 74.9132 272.594 74.9913 272.464 74.9562L272.227 74.8928C272.096 74.8577 272.019 74.7228 272.054 74.5914L272.458 73.0842C272.493 72.9528 272.627 72.8746 272.758 72.9097ZM271.741 73.2323L271.504 73.1689C271.374 73.1339 271.239 73.212 271.204 73.3434L270.949 74.2953C270.914 74.4268 270.991 74.5617 271.122 74.5967L271.359 74.6602C271.489 74.6952 271.624 74.6171 271.659 74.4857L271.914 73.5337C271.95 73.4023 271.872 73.2673 271.741 73.2323ZM270.509 73.4122L270.272 73.3487C270.141 73.3137 270.007 73.3918 269.971 73.5233L269.844 73.9992C269.809 74.1307 269.886 74.2656 270.017 74.3006L270.254 74.3641C270.385 74.3992 270.519 74.321 270.554 74.1896L270.682 73.7136C270.717 73.5822 270.64 73.4473 270.509 73.4122Z"
              fill="white" />
        <path fillRule="evenodd" clipRule="evenodd"
              d="M277.342 74.0856C277.867 74.2262 278.317 74.564 278.6 75.0293C278.621 75.065 278.667 75.0769 278.702 75.056L279.051 74.8517C279.069 74.8411 279.082 74.8236 279.087 74.8032C279.093 74.7828 279.09 74.7612 279.079 74.743C278.339 73.5142 276.764 73.0923 275.508 73.7862C275.49 73.7966 275.477 73.8139 275.471 73.8342C275.466 73.8546 275.468 73.8762 275.479 73.8946L275.678 74.2456C275.699 74.2816 275.744 74.2942 275.78 74.2738C276.258 74.0123 276.817 73.945 277.342 74.0856ZM277.1 74.9888C277.388 75.0661 277.637 75.2483 277.799 75.5C277.82 75.5353 277.866 75.5469 277.902 75.5261L278.25 75.3218C278.268 75.3111 278.281 75.2934 278.287 75.2729C278.292 75.2523 278.289 75.2304 278.278 75.2122C277.79 74.4295 276.782 74.1595 275.969 74.5935C275.95 74.6038 275.936 74.6212 275.931 74.6416C275.925 74.6621 275.928 74.684 275.938 74.7025L276.137 75.0533C276.158 75.0892 276.203 75.102 276.239 75.0821C276.505 74.945 276.812 74.9117 277.1 74.9888ZM277.485 75.7433C277.48 75.7641 277.466 75.7817 277.448 75.7922L276.846 76.1452C276.829 76.1556 276.807 76.1584 276.788 76.1532C276.768 76.1479 276.751 76.1349 276.741 76.117L276.397 75.5107C276.386 75.4923 276.383 75.4703 276.389 75.4498C276.395 75.4294 276.409 75.4122 276.428 75.4025C276.799 75.2267 277.241 75.3452 277.475 75.6829C277.486 75.7008 277.49 75.7226 277.485 75.7433Z"
              fill="white" />
        <path opacity="0.4" fillRule="evenodd" clipRule="evenodd"
              d="M281.057 74.7087L285.004 75.7661C285.222 75.8245 285.351 76.0494 285.292 76.2685L284.91 77.6963C284.851 77.9154 284.627 78.0457 284.409 77.9872L280.462 76.9298C280.244 76.8714 280.115 76.6465 280.174 76.4274L280.556 74.9996C280.615 74.7805 280.839 74.6503 281.057 74.7087ZM280.32 74.9361C280.414 74.5856 280.772 74.3773 281.121 74.4707L285.068 75.5281C285.416 75.6216 285.623 75.9815 285.529 76.3319L285.146 77.7598C285.052 78.1103 284.694 78.3187 284.345 78.2252L280.399 77.1678C280.05 77.0743 279.843 76.7145 279.937 76.364L280.32 74.9361ZM285.889 77.1936C285.833 77.4014 285.659 77.5557 285.447 77.5853L285.702 76.6334C285.871 76.7651 285.945 76.9858 285.889 77.1936Z"
              fill="white" />
        <path
          d="M284.783 75.9615L281.152 74.9886C280.978 74.9419 280.798 75.0461 280.751 75.2214L280.454 76.3319C280.407 76.5072 280.51 76.6871 280.685 76.7338L284.315 77.7067C284.49 77.7534 284.669 77.6492 284.716 77.474L285.014 76.3634C285.06 76.1881 284.957 76.0082 284.783 75.9615Z"
          fill="white" />
        <rect x="241.99" y="33.7039" width="27.6825" height="2.61155" rx="1.30578" transform="rotate(15 241.99 33.7039)"
              fill="#D9D9D9" />
        <rect x="209.24" y="60.0759" width="35.5171" height="35.5171" transform="rotate(15 209.24 60.0759)"
              fill="#4D9985" />
        <rect x="249.853" y="70.9583" width="35.5171" height="35.5171" rx="16.2208"
              transform="rotate(15 249.853 70.9583)" fill="#E8E7E6" />
        <rect x="198.424" y="100.437" width="35.5171" height="77.302" rx="17.7586"
              transform="rotate(15 198.424 100.437)" fill="#FFD36E" />
        <rect x="238.784" y="111.252" width="35.5171" height="35.5171" rx="17.7586"
              transform="rotate(15 238.784 111.252)" fill="#8977B5" />
        <rect x="227.969" y="151.613" width="35.5171" height="35.5171" rx="16.2208"
              transform="rotate(15 227.969 151.613)" fill="#FF8769" />
      </g>
      <rect x="20.8877" y="110.485" width="148.85" height="91.6" transform="rotate(-35.3784 20.8877 110.485)"
            fill="#C0DAD3" />
      <rect x="31.6599" y="125.655" width="148.85" height="21.4688" transform="rotate(-35.3784 31.6599 125.655)"
            fill="#2B574B" />
      <rect y="165" width="375" height="46" fill="url(#paint0_linear_1587_77441)" />
    </g>
    <defs>
      <linearGradient id="paint0_linear_1587_77441" x1="187.5" y1="165" x2="187.5" y2="211"
                      gradientUnits="userSpaceOnUse">
        <stop stopColor="#1F3D35" stopOpacity="0" />
        <stop offset="1" stopColor="#1F3D35" stopOpacity="0.2" />
      </linearGradient>
      <clipPath id="clip0_1587_77441">
        <rect width="375" height="211" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export const BowlsAndBallIllustration = ({ style }) => (
  <svg width="639" height="704" viewBox="0 0 639 704" fill="none" xmlns="http://www.w3.org/2000/svg" style={style}>
    <path d="M318.85 704C479.611 704 609.933 574.84 609.933 415.512H27.7671C27.7671 574.84 158.089 704 318.85 704Z"
          fill="#2B574B" />
    <path
      d="M356.921 394.209C512.236 353.168 604.253 195.166 562.447 41.3012L0 189.922C41.8067 343.787 201.606 435.249 356.921 394.209Z"
      fill="#FF8769" />
    <rect x="15.3368" y="0.868256" width="149.461" height="149.931" rx="74.7305" fill="#FFD36E" />
  </svg>
)

export const ArrowLeftDark20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M8.57573 14.4243C8.81004 14.6586 9.18994 14.6586 9.42426 14.4243C9.65857 14.19 9.65857 13.8101 9.42426 13.5758L6.44852 10.6L15 10.6C15.3314 10.6 15.6 10.3314 15.6 10C15.6 9.66865 15.3314 9.40002 15 9.40002L6.44852 9.40002L9.42426 6.42429C9.65857 6.18997 9.65857 5.81008 9.42426 5.57576C9.18994 5.34145 8.81005 5.34145 8.57573 5.57576L4.57698 9.57451C4.573 9.57847 4.56907 9.58248 4.5652 9.58656C4.46283 9.69418 4.39999 9.83976 4.39999 10C4.39999 10.0814 4.41618 10.1589 4.44552 10.2297C4.4748 10.3005 4.5182 10.3668 4.57573 10.4243L8.57573 14.4243Z"
          fill="#333332" />
  </svg>
)

export const ArrowLeft32 = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M14.6741 22.7802C14.967 23.0731 15.4418 23.0731 15.7347 22.7802C16.0276 22.4873 16.0276 22.0124 15.7347 21.7195L10.7577 16.7425L23.2503 16.7425C23.6645 16.7425 24.0003 16.4067 24.0003 15.9925C24.0003 15.5783 23.6645 15.2425 23.2503 15.2425L10.7571 15.2425L15.7347 10.2648C16.0276 9.97192 16.0276 9.49705 15.7347 9.20415C15.4418 8.91126 14.967 8.91126 14.6741 9.20416L8.41639 15.4619C8.12794 15.7503 8.12356 16.2153 8.40327 16.5091C8.40874 16.5148 8.41429 16.5205 8.41993 16.5261L14.6741 22.7802Z"
          fill="#333332" />
  </svg>
)

export const MagnifierIconWhite20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M15.3996 9.4999C15.3996 6.79371 13.2058 4.5999 10.4996 4.5999C7.79341 4.5999 5.59961 6.79371 5.59961 9.4999C5.59961 12.2061 7.79341 14.3999 10.4996 14.3999C13.2058 14.3999 15.3996 12.2061 15.3996 9.4999ZM10.4996 3.3999C13.8685 3.3999 16.5996 6.13097 16.5996 9.4999C16.5996 12.8688 13.8685 15.5999 10.4996 15.5999C9.03135 15.5999 7.68425 15.0812 6.63158 14.2169L6.46196 14.3866L4.42444 16.4241C4.19012 16.6584 3.81022 16.6584 3.57591 16.4241C3.34159 16.1898 3.34159 15.8099 3.57591 15.5756L5.61343 13.538L5.783 13.3685C4.91853 12.3157 4.39961 10.9684 4.39961 9.4999C4.39961 6.13097 7.13067 3.3999 10.4996 3.3999Z"
          fill="white" />
  </svg>
)

export const DoubleCheckmarkIcons20 = ({ color = '#BAB9B8' }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M11.7639 7.42417C11.9982 7.18985 11.9982 6.80995 11.7639 6.57564C11.5295 6.34132 11.1496 6.34132 10.9153 6.57564L5.44678 12.0442L2.92465 9.52216C2.69033 9.28786 2.31043 9.28786 2.07612 9.52218C1.84181 9.7565 1.84182 10.1364 2.07614 10.3707L5.02254 13.317C5.25685 13.5513 5.63675 13.5513 5.87106 13.317L11.7639 7.42417ZM17.9246 7.42417C18.1589 7.18985 18.1589 6.80995 17.9246 6.57564C17.6903 6.34132 17.3104 6.34132 17.0761 6.57564L11.6075 12.0442L10.4666 10.9034C10.2323 10.669 9.85242 10.6691 9.61811 10.9034C9.3838 11.1377 9.38381 11.5176 9.61813 11.7519L11.1833 13.317C11.4176 13.5513 11.7975 13.5513 12.0318 13.317L17.9246 7.42417Z"
          fill={color} />
  </svg>
)

export const ArrowRight20 = () => (
  <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.11101 4.47415C7.26216 3.70498 8.61553 3.29443 10 3.29443C11.8559 3.29658 13.6351 4.03477 14.9474 5.34705C16.2597 6.65934 16.9979 8.43858 17 10.2944C17 11.6789 16.5895 13.0323 15.8203 14.1834C15.0511 15.3346 13.9579 16.2318 12.6788 16.7616C11.3997 17.2914 9.99224 17.43 8.63437 17.1599C7.2765 16.8898 6.02922 16.2231 5.05026 15.2442C4.07129 14.2652 3.4046 13.0179 3.13451 11.6601C2.86441 10.3022 3.00303 8.89473 3.53285 7.61565C4.06266 6.33657 4.95987 5.24332 6.11101 4.47415ZM6.99023 10.2935C6.99023 10.0173 7.21409 9.79346 7.49023 9.79346H11.2866L10.015 8.51764C9.82006 8.32205 9.82059 8.00547 10.0162 7.81053C10.2118 7.61559 10.5283 7.61612 10.7233 7.8117L12.8446 9.94003C13.008 10.104 13.0341 10.3532 12.9228 10.5443C12.899 10.5853 12.8695 10.6227 12.8354 10.6552L10.7235 12.777C10.5287 12.9728 10.2121 12.9735 10.0164 12.7787C9.8207 12.5839 9.81995 12.2673 10.0148 12.0716L11.2869 10.7935H7.49023C7.21409 10.7935 6.99023 10.5696 6.99023 10.2935Z"
          fill="#BAB9B8" />
  </svg>
)

export const FlameCircleIcon16 = ({ color = "#FF7C5C" }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.66658 3.01118C5.65328 2.35189 6.81331 2 8 2C9.59074 2.00184 11.1158 2.63457 12.2406 3.75939C13.3654 4.88421 13.9982 6.40926 14 8C14 9.18669 13.6481 10.3467 12.9888 11.3334C12.3295 12.3201 11.3925 13.0891 10.2961 13.5433C9.19974 13.9974 7.99335 14.1162 6.82946 13.8847C5.66557 13.6532 4.59648 13.0818 3.75736 12.2426C2.91825 11.4035 2.3468 10.3344 2.11529 9.17054C1.88378 8.00666 2.0026 6.80026 2.45673 5.7039C2.91085 4.60754 3.67989 3.67047 4.66658 3.01118ZM9.68281 10.3318C10.1291 9.88552 10.3798 9.2802 10.3798 8.64904C10.3798 6.61945 8.43212 5.0812 8.34921 5.01674C8.32229 4.99579 8.2908 4.98151 8.2573 4.97506C8.22381 4.96861 8.18927 4.97016 8.15649 4.9796C8.12372 4.98904 8.09364 5.0061 8.06871 5.02938C8.04378 5.05266 8.02471 5.0815 8.01306 5.11355L7.24875 7.21543L6.55224 6.67882C6.52846 6.66048 6.50111 6.64731 6.47195 6.64012C6.44279 6.63294 6.41245 6.63191 6.38287 6.63709C6.35329 6.64227 6.32511 6.65355 6.30013 6.67022C6.27515 6.68689 6.25391 6.70858 6.23777 6.73391C5.828 7.37684 5.62023 8.02118 5.62023 8.64904C5.62023 9.2802 5.87096 9.88552 6.31726 10.3318C6.76356 10.7781 7.36887 11.0288 8.00004 11.0288C8.6312 11.0288 9.23651 10.7781 9.68281 10.3318Z"
          fill={color} />
  </svg>
)

export const DollarCircleIconWhite20 = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM10.5 6.5C10.5 6.22386 10.2761 6 10 6C9.72386 6 9.5 6.22386 9.5 6.5V7H9.25C8.78587 7 8.34075 7.18437 8.01256 7.51256C7.68437 7.84075 7.5 8.28587 7.5 8.75C7.5 9.21413 7.68437 9.65925 8.01256 9.98744C8.34075 10.3156 8.78587 10.5 9.25 10.5H10.75C10.9489 10.5 11.1397 10.579 11.2803 10.7197C11.421 10.8603 11.5 11.0511 11.5 11.25C11.5 11.4489 11.421 11.6397 11.2803 11.7803C11.1397 11.921 10.9489 12 10.75 12H10H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H9.5V13.5C9.5 13.7761 9.72386 14 10 14C10.2761 14 10.5 13.7761 10.5 13.5V13H10.75C11.2141 13 11.6592 12.8156 11.9874 12.4874C12.3156 12.1592 12.5 11.7141 12.5 11.25C12.5 10.7859 12.3156 10.3408 11.9874 10.0126C11.6592 9.68437 11.2141 9.5 10.75 9.5H9.25C9.05109 9.5 8.86032 9.42098 8.71967 9.28033C8.57902 9.13968 8.5 8.94891 8.5 8.75C8.5 8.55109 8.57902 8.36032 8.71967 8.21967C8.86032 8.07902 9.05109 8 9.25 8H10H11.5C11.7761 8 12 7.77614 12 7.5C12 7.22386 11.7761 7 11.5 7H10.5V6.5Z"
          fill="#F9F9F9" fillOpacity="0.8" />
  </svg>
)

export const ArrowDownCircleIconWhite20 = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10 3C8.61553 3 7.26216 3.41054 6.11101 4.17971C4.95987 4.94888 4.06266 6.04213 3.53285 7.32122C3.00303 8.6003 2.86441 10.0078 3.13451 11.3656C3.4046 12.7235 4.07129 13.9708 5.05026 14.9497C6.02922 15.9287 7.2765 16.5954 8.63437 16.8655C9.99224 17.1356 11.3997 16.997 12.6788 16.4672C13.9579 15.9373 15.0511 15.0401 15.8203 13.889C16.5895 12.7378 17 11.3845 17 10C16.9979 8.14414 16.2597 6.36491 14.9474 5.05262C13.6351 3.74033 11.8559 3.00214 10 3ZM7.50597 10.0262C7.70077 9.83046 8.01735 9.82972 8.21307 10.0245L9.49121 11.2967V7.5C9.49121 7.22386 9.71507 7 9.99121 7C10.2674 7 10.4912 7.22386 10.4912 7.5V11.2964L11.767 10.0248C11.9626 9.82983 12.2792 9.83035 12.4741 10.0259C12.6691 10.2215 12.6686 10.5381 12.473 10.733L10.3446 12.8544C10.1806 13.0178 9.9315 13.0439 9.74032 12.9326C9.69932 12.9088 9.66202 12.8793 9.62948 12.8452L7.50763 10.7333C7.31191 10.5385 7.31117 10.2219 7.50597 10.0262Z"
          fill="#F9F9F9" fillOpacity="0.8" />
  </svg>
)

export const EditItemIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M12.5004 3.60693C12.2087 3.60693 11.9289 3.72283 11.7226 3.92912L4.22257 11.4291C4.12043 11.5313 4.0394 11.6525 3.98412 11.786C3.92884 11.9194 3.90039 12.0625 3.90039 12.2069V14.9998C3.90039 15.2916 4.01628 15.5714 4.22257 15.7776C4.42886 15.9839 4.70865 16.0998 5.00039 16.0998H8.00039C8.15952 16.0998 8.31213 16.0366 8.42465 15.9241L16.0711 8.27764C16.2774 8.07135 16.3933 7.79156 16.3933 7.49983C16.3933 7.20809 16.2774 6.9283 16.0711 6.72201L13.2782 3.92912C13.0719 3.72283 12.7921 3.60693 12.5004 3.60693ZM12.5003 4.84814L15.1517 7.49962L13.9999 8.65148L11.3486 5.99979L12.5003 4.84814Z"
          fill="#BAB9B8" />
    <path d="M15.5004 15.6001L5 15.6L15.5004 15.6001Z" fill="#BAB9B8" />
    <path d="M15.5004 15.6001L5 15.6" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const TableIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M7.15908 3.14999C6.82771 3.14999 6.55908 3.41862 6.55908 3.74999C6.55908 4.08136 6.82771 4.34999 7.15908 4.34999H9.99999L12.8409 4.34999C13.1723 4.34999 13.4409 4.08136 13.4409 3.74999C13.4409 3.41862 13.1723 3.14999 12.8409 3.14999L9.99999 3.14999L7.15908 3.14999ZM4.34999 7.15908C4.34999 6.82771 4.08136 6.55908 3.74999 6.55908C3.41862 6.55908 3.14999 6.82771 3.14999 7.15908L3.14999 9.99999L3.14999 12.8409C3.14999 13.1723 3.41862 13.4409 3.74999 13.4409C4.08136 13.4409 4.34999 13.1723 4.34999 12.8409L4.34999 9.99999V7.15908ZM16.85 7.15908C16.85 6.82771 16.5814 6.55908 16.25 6.55908C15.9186 6.55908 15.65 6.82771 15.65 7.15908L15.65 9.99999V12.8409C15.65 13.1723 15.9186 13.4409 16.25 13.4409C16.5814 13.4409 16.85 13.1723 16.85 12.8409V9.99999V7.15908ZM7.15908 15.65C6.82771 15.65 6.55908 15.9186 6.55908 16.25C6.55908 16.5814 6.82771 16.85 7.15908 16.85H9.99999H12.8409C13.1723 16.85 13.4409 16.5814 13.4409 16.25C13.4409 15.9186 13.1723 15.65 12.8409 15.65H9.99999L7.15908 15.65ZM12.6259 5.52209C12.3553 5.49998 12.0231 5.49999 11.6207 5.49999H11.6206L8.37934 5.49999H8.37934C7.97685 5.49999 7.64468 5.49998 7.37408 5.52209C7.09303 5.54505 6.83468 5.59434 6.59201 5.71798C6.21569 5.90973 5.90973 6.21569 5.71798 6.59201C5.59434 6.83468 5.54505 7.09303 5.52209 7.37408C5.49998 7.64468 5.49999 7.97685 5.49999 8.37934V8.37934L5.49999 11.6206V11.6207C5.49999 12.0231 5.49998 12.3553 5.52209 12.6259C5.54505 12.907 5.59434 13.1653 5.71798 13.408C5.90973 13.7843 6.21569 14.0903 6.59201 14.282C6.83468 14.4057 7.09303 14.4549 7.37408 14.4779C7.64466 14.5 7.97682 14.5 8.37928 14.5H8.37933L11.6207 14.5H11.6207C12.0232 14.5 12.3553 14.5 12.6259 14.4779C12.907 14.4549 13.1653 14.4057 13.408 14.282C13.7843 14.0903 14.0903 13.7843 14.282 13.408C14.4057 13.1653 14.4549 12.907 14.4779 12.6259C14.5 12.3553 14.5 12.0231 14.5 11.6207L14.5 8.37933C14.5 7.97685 14.5 7.64468 14.4779 7.37408C14.4549 7.09303 14.4057 6.83468 14.282 6.59201C14.0903 6.21569 13.7843 5.90973 13.408 5.71798C13.1653 5.59434 12.907 5.54505 12.6259 5.52209Z"
          fill="#929191" />
  </svg>
)

export const LightningIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.7151 3.15317C12.0685 3.1349 12.4053 3.30477 12.6006 3.59986C12.6948 3.74216 12.7234 3.89597 12.7324 4.01544C12.7415 4.1375 12.733 4.2676 12.7181 4.39564C12.6891 4.64485 12.6248 4.97294 12.5469 5.37065L12.5469 5.37069L12.1701 7.29281C12.1457 7.41755 12.133 7.48413 12.1272 7.53277L12.1266 7.53771L12.1311 7.53984C12.1754 7.56074 12.2386 7.58527 12.3573 7.6307L14.8496 8.58439C15.0177 8.64867 15.1739 8.70844 15.2975 8.76903C15.4269 8.83249 15.5829 8.92534 15.6984 9.08654C15.8514 9.30021 15.9141 9.56554 15.8731 9.82513C15.8421 10.021 15.7441 10.1739 15.6569 10.2886C15.5735 10.3981 15.4606 10.5215 15.3392 10.6543L10.6772 15.7512C10.4036 16.0503 10.178 16.297 9.99475 16.4684C9.90062 16.5564 9.80106 16.6406 9.69746 16.7058C9.59606 16.7696 9.45534 16.838 9.28491 16.8468C8.9315 16.8651 8.59476 16.6952 8.39942 16.4001C8.30522 16.2578 8.27659 16.104 8.26764 15.9845C8.25849 15.8625 8.26704 15.7324 8.28192 15.6043C8.31087 15.3551 8.37518 15.027 8.45314 14.6293L8.45315 14.6293L8.82988 12.7072C8.85433 12.5824 8.86705 12.5159 8.87283 12.4672L8.87339 12.4623L8.86891 12.4601C8.82461 12.4392 8.76142 12.4147 8.6427 12.3693L6.1504 11.4156C5.98236 11.3513 5.8261 11.2915 5.70254 11.231C5.57313 11.1675 5.41708 11.0746 5.30164 10.9134C5.14862 10.6998 5.08586 10.4344 5.12696 10.1749C5.15796 9.97903 5.25589 9.82611 5.34316 9.71141C5.42649 9.60188 5.53941 9.47845 5.66086 9.34569L10.3229 4.24877C10.5964 3.94971 10.822 3.70299 11.0053 3.5316C11.0994 3.44354 11.1989 3.35934 11.3026 3.29416C11.404 3.23036 11.5447 3.16198 11.7151 3.15317Z"
      fill="#929191" />
  </svg>
)

export const QrCode20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M13.4989 2.3999C13.1676 2.3999 12.8989 2.66853 12.8989 2.9999C12.8989 3.33127 13.1676 3.5999 13.4989 3.5999H14.9986C15.7718 3.5999 16.3986 4.2267 16.3986 4.9999V6.49958C16.3986 6.83095 16.6672 7.09958 16.9986 7.09958C17.33 7.09958 17.5986 6.83095 17.5986 6.49958V4.9999C17.5986 3.56396 16.4345 2.3999 14.9986 2.3999L13.4989 2.3999ZM15.1347 13.6035V10.5243L4.86318 10.5243V13.6035C4.86318 14.4491 5.54869 15.1346 6.39429 15.1346L13.6036 15.1346C14.4492 15.1346 15.1347 14.4491 15.1347 13.6035ZM4.86318 6.39429C4.86318 5.54869 5.54868 4.86318 6.39429 4.86318L13.6036 4.86318C14.4492 4.86318 15.1347 5.54868 15.1347 6.39429L15.1347 9.47453L4.86318 9.47453L4.86318 6.39429ZM3.5999 13.5004C3.5999 13.169 3.33127 12.9004 2.9999 12.9004C2.66853 12.9004 2.3999 13.169 2.3999 13.5004L2.3999 15.0001C2.3999 16.436 3.56396 17.6001 4.9999 17.6001H6.49958C6.83095 17.6001 7.09958 17.3314 7.09958 17.0001C7.09958 16.6687 6.83095 16.4001 6.49958 16.4001H4.9999C4.2267 16.4001 3.5999 15.7733 3.5999 15.0001V13.5004ZM16.9986 12.9004C17.33 12.9004 17.5986 13.169 17.5986 13.5004V15.0001C17.5986 16.436 16.4345 17.6001 14.9986 17.6001H13.4989C13.1676 17.6001 12.8989 17.3314 12.8989 17.0001C12.8989 16.6687 13.1676 16.4001 13.4989 16.4001H14.9986C15.7718 16.4001 16.3986 15.7733 16.3986 15.0001V13.5004C16.3986 13.169 16.6672 12.9004 16.9986 12.9004ZM3.5999 4.9999C3.5999 4.2267 4.2267 3.5999 4.9999 3.5999L6.49958 3.5999C6.83095 3.5999 7.09958 3.33127 7.09958 2.9999C7.09958 2.66853 6.83095 2.3999 6.49958 2.3999H4.9999C3.56396 2.3999 2.3999 3.56396 2.3999 4.9999V6.49958C2.3999 6.83095 2.66853 7.09958 2.9999 7.09958C3.33127 7.09958 3.5999 6.83095 3.5999 6.49958L3.5999 4.9999Z"
          fill="#929191" />
  </svg>
)

export const TrashIcon20White = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M8.96491 2.89994L8.9658 2.89994C9.81064 2.8999 10.6555 2.89986 11.5004 2.89999C12.384 2.90012 13.1 3.61647 13.1 4.4999V4.50185V4.5038V4.50575V4.50771V4.50966V4.51161V4.51357V4.51552V4.51747V4.51943V4.52138V4.52333V4.52529V4.52724V4.52919V4.53115V4.5331V4.53505V4.53701V4.53896V4.54091V4.54287V4.54482V4.54677V4.54873V4.55068V4.55263V4.55459V4.55654V4.55849V4.56045V4.5624V4.56435V4.56631V4.56826V4.57021V4.57217V4.57412V4.57607V4.57803V4.57998V4.58193V4.58389V4.58584V4.58779V4.58975V4.5917V4.59365V4.59561V4.59756V4.59951V4.60147V4.60342V4.60537V4.60733V4.60928V4.61123V4.61319V4.61514V4.61709V4.61905V4.621V4.62295V4.62491V4.62686V4.62881V4.63077V4.63272V4.63467V4.63663V4.63858V4.64053V4.64249V4.64444V4.64639V4.64835V4.6503V4.65225V4.65421V4.65616V4.65811V4.66007V4.66202V4.66397V4.66593V4.66788V4.66983V4.67179V4.67374V4.67569V4.67764V4.6796V4.68155V4.6835V4.68546V4.68741V4.68936V4.69132V4.69327V4.69522V4.69718V4.69913V4.70108V4.70304V4.70499V4.70694V4.7089V4.71085V4.7128V4.71476V4.71671V4.71866V4.72062V4.72257V4.72452V4.72648V4.72843V4.73038V4.73234V4.73429V4.73624V4.7382V4.74015V4.7421V4.74406V4.74601V4.74796V4.74992V4.75187V4.75382V4.75578V4.75773V4.75968V4.76164V4.76359V4.76554V4.7675V4.76945V4.7714V4.77336V4.77531V4.77726V4.77922V4.78117V4.78312V4.78508V4.78703V4.78898V4.79094V4.79289V4.79484V4.7968V4.79875V4.8007V4.80266V4.80461V4.80656V4.80852V4.81047V4.81242V4.81438V4.81633V4.81828V4.82024V4.82219V4.82414V4.8261V4.82805V4.83V4.83196V4.83391V4.83586V4.83782V4.83977V4.84172V4.84368V4.84563V4.84758V4.84953V4.85149V4.85344V4.85539V4.85735V4.8593V4.86125V4.86321V4.86516V4.86711V4.86907V4.87102V4.87297V4.87493V4.87688V4.87883V4.88079V4.88274V4.88469V4.88665V4.8886V4.89055V4.89251V4.89446V4.89641V4.89837V4.89997L15.5 4.89997C15.8314 4.89997 16.1 5.1686 16.1 5.49997C16.1 5.83134 15.8314 6.09997 15.5 6.09997L14.5 6.09997V14C14.5 14.8284 13.8285 15.5 13 15.5H7.00002C6.1716 15.5 5.50002 14.8284 5.50002 14V6.09997L4.50002 6.09997C4.16865 6.09997 3.90002 5.83134 3.90002 5.49997C3.90002 5.1686 4.16865 4.89997 4.50002 4.89997L6.90006 4.89997L6.90008 4.49943C6.90021 3.61585 7.6166 2.89999 8.4999 2.89995L8.96491 2.89994ZM11.9 4.89837V4.89997L8.10006 4.89997L8.10008 4.4996C8.10011 4.27896 8.27903 4.09996 8.49994 4.09995L8.96506 4.09994H8.9651H8.96515C9.81023 4.0999 10.6552 4.09986 11.5003 4.09999C11.721 4.10002 11.9 4.27901 11.9 4.4999V4.50185V4.5038V4.50575V4.50771V4.50966V4.51161V4.51357V4.51552V4.51747V4.51943V4.52138V4.52333V4.52529V4.52724V4.52919V4.53115V4.5331V4.53505V4.53701V4.53896V4.54091V4.54287V4.54482V4.54677V4.54873V4.55068V4.55263V4.55459V4.55654V4.55849V4.56045V4.5624V4.56435V4.56631V4.56826V4.57021V4.57217V4.57412V4.57607V4.57803V4.57998V4.58193V4.58389V4.58584V4.58779V4.58975V4.5917V4.59365V4.59561V4.59756V4.59951V4.60147V4.60342V4.60537V4.60733V4.60928V4.61123V4.61319V4.61514V4.61709V4.61905V4.621V4.62295V4.62491V4.62686V4.62881V4.63077V4.63272V4.63467V4.63663V4.63858V4.64053V4.64249V4.64444V4.64639V4.64835V4.6503V4.65225V4.65421V4.65616V4.65811V4.66007V4.66202V4.66397V4.66593V4.66788V4.66983V4.67179V4.67374V4.67569V4.67764V4.6796V4.68155V4.6835V4.68546V4.68741V4.68936V4.69132V4.69327V4.69522V4.69718V4.69913V4.70108V4.70304V4.70499V4.70694V4.7089V4.71085V4.7128V4.71476V4.71671V4.71866V4.72062V4.72257V4.72452V4.72648V4.72843V4.73038V4.73234V4.73429V4.73624V4.7382V4.74015V4.7421V4.74406V4.74601V4.74796V4.74992V4.75187V4.75382V4.75578V4.75773V4.75968V4.76164V4.76359V4.76554V4.7675V4.76945V4.7714V4.77336V4.77531V4.77726V4.77922V4.78117V4.78312V4.78508V4.78703V4.78898V4.79094V4.79289V4.79484V4.7968V4.79875V4.8007V4.80266V4.80461V4.80656V4.80852V4.81047V4.81242V4.81438V4.81633V4.81828V4.82024V4.82219V4.82414V4.8261V4.82805V4.83V4.83196V4.83391V4.83586V4.83782V4.83977V4.84172V4.84368V4.84563V4.84758V4.84953V4.85149V4.85344V4.85539V4.85735V4.8593V4.86125V4.86321V4.86516V4.86711V4.86907V4.87102V4.87297V4.87493V4.87688V4.87883V4.88079V4.88274V4.88469V4.88665V4.8886V4.89055V4.89251V4.89446V4.89641V4.89837ZM7.99963 8C7.99963 7.72386 7.77578 7.5 7.49963 7.5C7.22349 7.5 6.99963 7.72386 6.99963 8V13C6.99963 13.2761 7.22349 13.5 7.49963 13.5C7.77578 13.5 7.99963 13.2761 7.99963 13V8ZM10.4996 8C10.4996 7.72386 10.2758 7.5 9.99963 7.5C9.72349 7.5 9.49963 7.72386 9.49963 8V13C9.49963 13.2761 9.72349 13.5 9.99963 13.5C10.2758 13.5 10.4996 13.2761 10.4996 13V8ZM12.4996 7.5C12.7758 7.5 12.9996 7.72386 12.9996 8V13C12.9996 13.2761 12.7758 13.5 12.4996 13.5C12.2235 13.5 11.9996 13.2761 11.9996 13V8C11.9996 7.72386 12.2235 7.5 12.4996 7.5Z"
          fill="#FFFFFF" />
  </svg>
)

export const CourseIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M9.99844 3.8999C10.3298 3.8999 10.5984 4.16853 10.5984 4.4999V5.09682C14.144 5.40102 16.9269 8.37541 16.9269 11.9998V12.9969C16.9269 13.3141 16.6698 13.5712 16.3526 13.5712H3.64398C3.32684 13.5712 3.06973 13.3141 3.06973 12.9969V11.9998C3.06973 8.37531 5.85275 5.40087 9.39844 5.0968V4.4999C9.39844 4.16853 9.66707 3.8999 9.99844 3.8999ZM2.49844 14.6144C2.16707 14.6144 1.89844 14.883 1.89844 15.2144C1.89844 15.5457 2.16707 15.8144 2.49844 15.8144L17.4984 15.8144C17.8298 15.8144 18.0984 15.5457 18.0984 15.2144C18.0984 14.883 17.8298 14.6144 17.4984 14.6144L2.49844 14.6144Z"
          fill="#BAB9B8" />
  </svg>
)

export const LightningIcon20 = () => (
  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.0897 3.15319C12.4431 3.13492 12.7799 3.3048 12.9752 3.59988C13.0694 3.74218 13.098 3.89599 13.107 4.01546C13.1161 4.13752 13.1076 4.26763 13.0927 4.39567C13.0637 4.64487 12.9994 4.97297 12.9215 5.37068L12.9215 5.37071L12.5447 7.29283C12.5203 7.41757 12.5076 7.48416 12.5018 7.5328L12.5012 7.53773L12.5057 7.53987C12.55 7.56076 12.6132 7.58529 12.7319 7.63072L15.2242 8.58441C15.3923 8.6487 15.5485 8.70847 15.6721 8.76906C15.8015 8.83251 15.9575 8.92537 16.073 9.08656C16.226 9.30024 16.2887 9.56557 16.2477 9.82515C16.2167 10.021 16.1187 10.1739 16.0315 10.2886C15.9481 10.3981 15.8352 10.5215 15.7138 10.6543L11.0518 15.7512C10.7782 16.0503 10.5526 16.297 10.3694 16.4684C10.2752 16.5565 10.1757 16.6407 10.0721 16.7058C9.97066 16.7696 9.82994 16.838 9.65951 16.8468C9.30611 16.8651 8.96936 16.6952 8.77403 16.4002C8.67983 16.2578 8.65119 16.104 8.64224 15.9846C8.63309 15.8625 8.64165 15.7324 8.65652 15.6044C8.68547 15.3552 8.74979 15.0271 8.82775 14.6293L8.82775 14.6293L9.20449 12.7072C9.22894 12.5825 9.24165 12.5159 9.24743 12.4672L9.248 12.4623L9.24352 12.4602C9.19921 12.4393 9.13602 12.4147 9.0173 12.3693L6.525 11.4156C6.35696 11.3513 6.20071 11.2916 6.07714 11.231C5.94774 11.1675 5.79168 11.0747 5.67624 10.9135C5.52322 10.6998 5.46047 10.4345 5.50156 10.1749C5.53256 9.97905 5.63049 9.82613 5.71776 9.71143C5.80109 9.6019 5.91402 9.47847 6.03546 9.34572L10.6975 4.24879C10.971 3.94973 11.1966 3.70301 11.3799 3.53162C11.474 3.44357 11.5736 3.35936 11.6772 3.29418C11.7786 3.23039 11.9193 3.162 12.0897 3.15319Z"
      fill="#BAB9B8" />
  </svg>
)

export const KeyboardIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M20.4424 6.56979C20.193 6.08055 19.7952 5.683 19.3059 5.43379C18.9932 5.27457 18.6584 5.21006 18.2877 5.17986C17.9295 5.15068 17.4888 5.15078 16.9498 5.15089L16.9247 5.1509C13.6416 5.15158 10.3585 5.15165 7.07545 5.15096L7.05031 5.15095C6.51173 5.15083 6.07123 5.15073 5.71321 5.17992C5.34261 5.21014 5.00789 5.27468 4.69525 5.43393C4.20604 5.68312 3.80799 6.08111 3.55873 6.57028C3.39946 6.88285 3.33485 7.21742 3.30457 7.58786C3.27532 7.94569 3.27535 8.38589 3.27539 8.92406L3.2754 8.94921C3.27545 9.90226 3.2757 10.8548 3.27595 11.8073L3.27595 11.8079L3.27595 11.8082C3.27623 12.8877 3.27651 13.9672 3.27651 15.0473L3.27651 15.0724C3.27651 15.6118 3.2765 16.0528 3.30574 16.4111C3.33601 16.7819 3.40055 17.1169 3.55983 17.4295C3.80911 17.9188 4.20653 18.3163 4.69581 18.5657C5.00837 18.725 5.34313 18.7897 5.71379 18.82C6.07192 18.8493 6.51262 18.8494 7.05154 18.8495L7.07668 18.8495C10.3608 18.8502 13.645 18.8502 16.9292 18.8495L16.9544 18.8495C17.4924 18.8494 17.9324 18.8493 18.2901 18.82C18.6604 18.7897 18.9948 18.725 19.3073 18.5658C19.7962 18.3166 20.1941 17.9187 20.4432 17.4298C20.6024 17.1174 20.6671 16.783 20.6974 16.4128C20.7267 16.0552 20.7268 15.6154 20.7269 15.0777V15.0776L20.7269 15.0524C20.7272 13.2792 20.7268 11.5061 20.7264 9.73315L20.7262 8.92578C20.7261 8.38681 20.7261 7.94605 20.6967 7.58789C20.6664 7.21718 20.6017 6.88238 20.4424 6.56979ZM6.375 8.74951C6.09886 8.74951 5.875 8.97337 5.875 9.24951C5.875 9.52565 6.09886 9.74951 6.375 9.74951H17.625C17.9011 9.74951 18.125 9.52565 18.125 9.24951C18.125 8.97337 17.9011 8.74951 17.625 8.74951H6.375ZM6.375 11.5C6.09886 11.5 5.875 11.7239 5.875 12C5.875 12.2761 6.09886 12.5 6.375 12.5H17.625C17.9011 12.5 18.125 12.2761 18.125 12C18.125 11.7239 17.9011 11.5 17.625 11.5H6.375ZM5.875 14.75C5.875 14.4739 6.09886 14.25 6.375 14.25H7C7.27614 14.25 7.5 14.4739 7.5 14.75C7.5 15.0261 7.27614 15.25 7 15.25H6.375C6.09886 15.25 5.875 15.0261 5.875 14.75ZM9.5 14.25C9.22386 14.25 9 14.4739 9 14.75C9 15.0261 9.22386 15.25 9.5 15.25H14.5C14.7761 15.25 15 15.0261 15 14.75C15 14.4739 14.7761 14.25 14.5 14.25H9.5ZM16.5 14.75C16.5 14.4739 16.7239 14.25 17 14.25H17.625C17.9011 14.25 18.125 14.4739 18.125 14.75C18.125 15.0261 17.9011 15.25 17.625 15.25H17C16.7239 15.25 16.5 15.0261 16.5 14.75Z"
          fill="#BAB9B8" />
  </svg>
)

export const MegaphoneIcon20 = ({ color = "#BAB9B8", height = 20, width = 20 }) => (
  <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.35642 4.00978C5.13017 3.98164 4.90004 4.01453 4.69309 4.1046C4.48614 4.19466 4.31097 4.33815 4.18819 4.51819C4.06541 4.69823 4.00012 4.9077 4 5.12128V13.5155C4.00012 13.729 4.06541 13.9381 4.18819 14.1182C4.31097 14.2982 4.48614 14.4417 4.69309 14.5318C4.90004 14.6218 5.13017 14.6547 5.35642 14.6266C5.58225 14.5985 5.79483 14.5108 5.96927 14.3737C7.38124 13.2655 8.81869 12.7129 9.90007 12.4372C10.4407 12.2994 10.8904 12.2313 11.2 12.1977L11.2 6.43865C10.8904 6.40506 10.4407 6.33697 9.90007 6.19914C8.81881 5.92347 7.38158 5.37098 5.96976 4.26308C5.79523 4.12577 5.58246 4.0379 5.35642 4.00978ZM16.1089 7.30498C15.5383 6.77104 14.7644 6.47108 13.9574 6.47108H12.0933L12.1135 12.1653H13.9574C14.7644 12.1653 15.5383 11.8653 16.1089 11.3314C16.6794 10.7975 17 10.0733 17 9.31818C17 8.56308 16.6794 7.83891 16.1089 7.30498ZM13.8891 15.5113C14.0103 15.4023 14.0966 15.2717 14.141 15.1305L14.6364 13.5553L11.0909 13.4545V14.4861C11.0909 14.6356 11.1373 14.7827 11.226 14.9145C11.3147 15.0462 11.443 15.1585 11.5994 15.2414L12.3999 15.6657C12.5521 15.7464 12.7265 15.7968 12.9084 15.8127C13.0903 15.8286 13.2743 15.8096 13.445 15.7571C13.6157 15.7046 13.768 15.6203 13.8891 15.5113Z"
          fill={color} />
  </svg>

)

export const RushHourIcon20 = ({ color = "#BAB9B8" }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M8 2.8999C7.66863 2.8999 7.4 3.16853 7.4 3.4999C7.4 3.83127 7.66863 4.0999 8 4.0999H12C12.3314 4.0999 12.6 3.83127 12.6 3.4999C12.6 3.16853 12.3314 2.8999 12 2.8999H8ZM3.90039 10.9998C3.90039 7.63087 6.63145 4.8998 10.0004 4.8998C13.3693 4.8998 16.1004 7.63087 16.1004 10.9998C16.1004 14.3687 13.3693 17.0998 10.0004 17.0998C6.63145 17.0998 3.90039 14.3687 3.90039 10.9998ZM12.8284 8.17125C13.0237 8.36651 13.0237 8.6831 12.8284 8.87836L10.3536 11.3532C10.1583 11.5485 9.84171 11.5485 9.64645 11.3532C9.45118 11.158 9.45118 10.8414 9.64645 10.6461L12.1213 8.17125C12.3166 7.97599 12.6332 7.97599 12.8284 8.17125Z"
          fill={color} />
  </svg>
)

export const SparksIcon20 = ({ color = '#BAB9B8' }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10.5645 18.2014C10.4429 18.0873 10.3746 17.9408 10.3535 17.7774C10.2412 17.0198 10.1225 16.3855 9.99785 15.8729C9.87464 15.3711 9.71774 14.9703 9.53211 14.6638C9.35069 14.3643 9.11174 14.1276 8.81288 13.9508L8.80929 13.9486C8.5085 13.7639 8.11353 13.6095 7.61693 13.4911C7.11985 13.3714 6.49783 13.262 5.74988 13.1638C5.58129 13.1439 5.42661 13.0784 5.30845 12.9524L5.30329 12.9469C5.18981 12.8182 5.13481 12.6617 5.13481 12.4916C5.13481 12.3231 5.19205 12.1691 5.30845 12.0449C5.42729 11.9187 5.57977 11.8469 5.75085 11.8263L5.75269 11.8261C6.50089 11.7419 7.12511 11.6443 7.62723 11.5337C8.12219 11.4201 8.51867 11.2684 8.82371 11.0838C9.12363 10.8977 9.36347 10.6536 9.54545 10.3489L9.54675 10.3467C9.73632 10.0365 9.89302 9.63223 10.0115 9.1266L10.0119 9.12452C10.1364 8.61297 10.2504 7.97697 10.3533 7.21463C10.3741 7.0505 10.4424 6.90333 10.5645 6.78885L10.57 6.78369C10.6943 6.67406 10.8446 6.61521 11.0112 6.61521C11.1797 6.61521 11.3372 6.66972 11.4607 6.79155C11.5763 6.90108 11.6475 7.03994 11.68 7.19591L11.6818 7.20463L11.683 7.21346C11.786 7.97624 11.8977 8.61303 12.0177 9.12555C12.1407 9.63101 12.2974 10.036 12.4829 10.3472C12.6699 10.6483 12.9105 10.8908 13.2064 11.0772C13.5116 11.2617 13.9082 11.4134 14.4033 11.5269C14.9047 11.6419 15.5285 11.7419 16.2767 11.8261L16.2786 11.8263C16.4487 11.8467 16.6007 11.918 16.7163 12.0474C16.8307 12.1703 16.8946 12.3214 16.8946 12.4916C16.8946 12.6651 16.8348 12.8224 16.7163 12.9498C16.5993 13.0808 16.4449 13.1498 16.2731 13.1645C15.5265 13.2486 14.9013 13.3485 14.3957 13.4634L14.3941 13.4638C13.8986 13.5729 13.5022 13.7225 13.1976 13.907C12.8983 14.0883 12.6588 14.3296 12.4769 14.6342C12.2911 14.9453 12.1341 15.3529 12.0108 15.8641C11.8908 16.3811 11.7814 17.0177 11.6832 17.7755L11.682 17.7844C11.6552 17.9454 11.5847 18.089 11.4649 18.2014C11.3386 18.3197 11.1813 18.375 11.0112 18.375C10.8446 18.375 10.6944 18.3161 10.5701 18.2064L10.5645 18.2014ZM5.60314 10.0619C5.54701 9.59885 5.48938 9.24384 5.43109 8.99275C5.37602 8.75128 5.2934 8.60149 5.2041 8.51527L5.20094 8.51222C5.10789 8.41917 4.94824 8.33131 4.69413 8.2667C4.43172 8.19884 4.06552 8.1248 3.59443 8.04544C3.47441 8.02896 3.34957 7.9861 3.25365 7.89018C3.15175 7.78828 3.11328 7.65762 3.11328 7.52963C3.11328 7.41119 3.14579 7.28834 3.23502 7.18829C3.32212 7.09063 3.43829 7.04282 3.55158 7.02218C4.03341 6.93329 4.40811 6.85454 4.67806 6.78598C4.9336 6.71647 5.09556 6.62807 5.18996 6.53693L5.193 6.53399C5.28706 6.4462 5.37019 6.30131 5.42425 6.0726L5.42523 6.06843C5.4873 5.82459 5.54707 5.47404 5.60314 5.01151L5.60355 5.00811C5.62003 4.88614 5.66506 4.7597 5.76584 4.66408C5.86966 4.56559 6.00055 4.53007 6.12698 4.53007C6.24945 4.53007 6.37746 4.56347 6.48013 4.65803C6.58056 4.75053 6.62612 4.87351 6.64307 4.9921L6.6439 4.99795C6.69989 5.46923 6.75739 5.83356 6.81578 6.09461C6.87555 6.34122 6.96068 6.49717 7.053 6.58948C7.14456 6.68105 7.30201 6.76645 7.55403 6.82649C7.82179 6.8854 8.1933 6.94982 8.67114 7.01952C8.7979 7.03059 8.91267 7.08538 8.99839 7.18504C9.08957 7.28073 9.13362 7.40112 9.13362 7.52963C9.13362 7.64843 9.10222 7.77228 9.01406 7.8738C8.92747 7.97351 8.81104 8.02289 8.69643 8.04395C8.20931 8.13764 7.83266 8.22102 7.56366 8.29397L7.56083 8.29474C7.31208 8.35907 7.15388 8.44666 7.06007 8.54047L7.05697 8.54357C6.96553 8.63185 6.88116 8.78097 6.82189 9.01806C6.75982 9.2619 6.69983 9.6135 6.64376 10.076L6.64261 10.0856L6.64072 10.095C6.61773 10.2099 6.56536 10.3145 6.48047 10.3994C6.3834 10.4965 6.25951 10.5433 6.12698 10.5433C6.00055 10.5433 5.86966 10.5078 5.76584 10.4093C5.66506 10.3137 5.62005 10.1873 5.60357 10.0653L5.60314 10.0619ZM9.13155 5.23697C9.08049 4.95414 9.03489 4.73628 8.99491 4.5808L8.99323 4.57427C8.9619 4.43722 8.91705 4.36127 8.87648 4.3207L8.87143 4.31565C8.83462 4.27668 8.7582 4.22758 8.61172 4.18689L8.60364 4.18464C8.45493 4.13791 8.23248 4.08818 7.93009 4.037L7.91632 4.03467C7.82871 4.01476 7.73513 3.97204 7.66433 3.88838C7.59192 3.80279 7.5663 3.70106 7.5663 3.60673C7.5663 3.51056 7.59574 3.41022 7.67089 3.32824C7.7421 3.25056 7.83356 3.21214 7.9164 3.19331L7.92638 3.19104C8.22857 3.13525 8.45431 3.08554 8.60772 3.04185C8.75436 2.99678 8.83294 2.94579 8.8715 2.90495L8.87641 2.89976C8.91828 2.85788 8.96281 2.78298 8.99368 2.6518L8.99486 2.64678C9.03484 2.4913 9.08049 2.27346 9.13155 1.99063C9.1444 1.90347 9.17972 1.80544 9.26248 1.72959C9.34901 1.65027 9.45472 1.625 9.54804 1.625C9.6419 1.625 9.74552 1.65046 9.83159 1.7262C9.91578 1.80029 9.95466 1.89749 9.97071 1.98578L9.97149 1.99005C10.0181 2.27452 10.0614 2.49236 10.1012 2.6468C10.1368 2.78554 10.182 2.86574 10.2222 2.90946C10.2688 2.95494 10.3511 3.00529 10.4885 3.04892C10.6457 3.09233 10.8725 3.13974 11.173 3.1906L11.1761 3.19111C11.2618 3.2067 11.3579 3.24358 11.4322 3.32471C11.5096 3.40906 11.5368 3.51205 11.5368 3.60673C11.5368 3.70106 11.5112 3.80279 11.4388 3.88838C11.368 3.97204 11.2743 4.01438 11.1867 4.03429L11.1731 4.03739C10.8708 4.08854 10.645 4.13789 10.4905 4.1851L10.4861 4.18643C10.3482 4.22584 10.2686 4.27619 10.2243 4.32293C10.1836 4.36612 10.1375 4.44668 10.1012 4.58787L10.1007 4.58958C10.0613 4.73836 10.0181 4.95306 9.97145 5.23754L9.97075 5.24182C9.95469 5.33011 9.91578 5.42731 9.83159 5.5014C9.74552 5.57714 9.6419 5.6026 9.54804 5.6026C9.45472 5.6026 9.34901 5.57733 9.26248 5.49801C9.17973 5.42216 9.1444 5.32413 9.13155 5.23697Z"
          fill={color} />
  </svg>
)

export const SparksIcon16 = ({ color = '#BAB9B8' }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M8.45123 14.5609C8.35389 14.4696 8.29926 14.3524 8.28243 14.2217C8.1926 13.6157 8.09764 13.1082 7.99789 12.6981C7.89932 12.2966 7.7738 11.9761 7.6253 11.7309C7.48016 11.4912 7.289 11.3018 7.04991 11.1604L7.04704 11.1587C6.80641 11.0109 6.49043 10.8874 6.09316 10.7927C5.69549 10.6969 5.19787 10.6094 4.59951 10.5308C4.46464 10.5149 4.3409 10.4625 4.24637 10.3617L4.24224 10.3573C4.15145 10.2544 4.10746 10.1292 4.10746 9.99306C4.10746 9.85827 4.15325 9.73507 4.24637 9.63574C4.34144 9.53473 4.46342 9.47729 4.60029 9.46086L4.60176 9.46069C5.20032 9.39335 5.6997 9.31521 6.10139 9.2268C6.49736 9.13591 6.81455 9.01451 7.05858 8.86688C7.29851 8.718 7.49038 8.52271 7.63597 8.27894L7.63701 8.2772C7.78866 8.02904 7.91402 7.70558 8.00877 7.30108L8.00916 7.29942C8.10871 6.89018 8.19991 6.38138 8.28225 5.77151C8.29887 5.6402 8.35354 5.52247 8.45123 5.43089L8.45563 5.42676C8.55503 5.33905 8.67527 5.29198 8.80855 5.29198C8.9434 5.29198 9.0694 5.33558 9.16815 5.43305C9.26062 5.52067 9.31763 5.63176 9.34362 5.75653L9.34507 5.76351L9.34603 5.77057C9.42839 6.3808 9.51778 6.89023 9.61378 7.30025C9.7122 7.70461 9.83753 8.0286 9.98593 8.27753C10.1355 8.51845 10.328 8.71247 10.5647 8.86156C10.8089 9.00914 11.1262 9.13051 11.5222 9.22135C11.9233 9.31335 12.4224 9.39335 13.021 9.46069L13.0225 9.46086C13.1586 9.4772 13.2802 9.53424 13.3726 9.63776C13.4641 9.73604 13.5153 9.85692 13.5153 9.99306C13.5153 10.1318 13.4675 10.2577 13.3726 10.3597C13.279 10.4644 13.1556 10.5197 13.0181 10.5314C12.4208 10.5987 11.9206 10.6786 11.5162 10.7705L11.5149 10.7708C11.1185 10.8581 10.8014 10.9778 10.5577 11.1254C10.3183 11.2704 10.1266 11.4635 9.98112 11.7072C9.83252 11.956 9.70689 12.2821 9.60823 12.6911C9.51226 13.1047 9.42475 13.6139 9.34616 14.2202L9.34523 14.2274C9.32377 14.3561 9.26737 14.471 9.17152 14.5609C9.07049 14.6556 8.94461 14.6998 8.80855 14.6998C8.67527 14.6998 8.55509 14.6527 8.45569 14.565L8.45123 14.5609ZM4.48212 8.04932C4.43722 7.67888 4.39111 7.39488 4.34448 7.19401C4.30042 7.00083 4.23433 6.881 4.16289 6.81202L4.16036 6.80958C4.08592 6.73514 3.9582 6.66485 3.75492 6.61317C3.54499 6.55887 3.25202 6.49965 2.87516 6.43616C2.77914 6.42298 2.67927 6.38869 2.60253 6.31195C2.52101 6.23043 2.49023 6.1259 2.49023 6.02351C2.49023 5.92875 2.51624 5.83047 2.58763 5.75044C2.6573 5.67231 2.75024 5.63406 2.84087 5.61755C3.22634 5.54644 3.5261 5.48343 3.74206 5.42859C3.94649 5.37298 4.07606 5.30226 4.15158 5.22935L4.15401 5.227C4.22926 5.15677 4.29576 5.04085 4.33901 4.85789L4.3398 4.85455C4.38945 4.65948 4.43727 4.37904 4.48212 4.00901L4.48245 4.00629C4.49563 3.90872 4.53166 3.80756 4.61228 3.73107C4.69534 3.65228 4.80005 3.62386 4.9012 3.62386C4.99917 3.62386 5.10158 3.65058 5.18371 3.72623C5.26406 3.80023 5.30051 3.89862 5.31406 3.99349L5.31473 3.99817C5.35953 4.37519 5.40552 4.66665 5.45223 4.87549C5.50005 5.07278 5.56816 5.19754 5.64201 5.27139C5.71526 5.34464 5.84122 5.41296 6.04283 5.461C6.25704 5.50813 6.55425 5.55966 6.93652 5.61542C7.03793 5.62428 7.12975 5.66811 7.19832 5.74783C7.27127 5.82439 7.3065 5.9207 7.3065 6.02351C7.3065 6.11855 7.28139 6.21763 7.21086 6.29884C7.14158 6.37861 7.04844 6.41812 6.95675 6.43496C6.56706 6.50992 6.26573 6.57662 6.05054 6.63498L6.04827 6.6356C5.84927 6.68706 5.72271 6.75713 5.64766 6.83218L5.64518 6.83466C5.57203 6.90528 5.50454 7.02458 5.45712 7.21425C5.40746 7.40933 5.35947 7.6906 5.31462 8.06063L5.31369 8.06825L5.31219 8.07578C5.2938 8.16774 5.25189 8.25143 5.18398 8.31935C5.10633 8.397 5.00722 8.43447 4.9012 8.43447C4.80005 8.43447 4.69534 8.40605 4.61228 8.32726C4.53166 8.25077 4.49565 8.14961 4.48247 8.05203L4.48212 8.04932ZM7.30485 4.18938C7.264 3.96311 7.22752 3.78883 7.19554 3.66445L7.19419 3.65922C7.16913 3.54958 7.13325 3.48882 7.10079 3.45636L7.09675 3.45232C7.06731 3.42115 7.00617 3.38187 6.88899 3.34932L6.88252 3.34752C6.76356 3.31013 6.5856 3.27035 6.34368 3.22941L6.33266 3.22754C6.26257 3.21161 6.18771 3.17744 6.13108 3.11051C6.07314 3.04204 6.05265 2.96066 6.05265 2.88519C6.05265 2.80825 6.0762 2.72798 6.13632 2.6624C6.19329 2.60025 6.26646 2.56951 6.33273 2.55445L6.34071 2.55264C6.58247 2.50801 6.76306 2.46823 6.88579 2.43328C7.0031 2.39723 7.06596 2.35643 7.09681 2.32377L7.10073 2.31961C7.13424 2.28611 7.16986 2.22619 7.19455 2.12124L7.19549 2.11723C7.22748 1.99285 7.264 1.81857 7.30485 1.59231C7.31513 1.52258 7.34339 1.44416 7.40959 1.38347C7.47882 1.32002 7.56339 1.2998 7.63804 1.2998C7.71313 1.2998 7.79602 1.32017 7.86488 1.38076C7.93224 1.44004 7.96334 1.5178 7.97618 1.58843L7.9768 1.59185C8.0141 1.81942 8.04877 1.99369 8.08054 2.11724C8.10908 2.22824 8.14524 2.29239 8.17735 2.32737C8.21465 2.36376 8.2805 2.40404 8.39038 2.43894C8.51617 2.47367 8.69763 2.5116 8.93805 2.55228L8.94045 2.55269C9.00908 2.56517 9.08592 2.59467 9.14541 2.65957C9.20727 2.72705 9.22908 2.80945 9.22908 2.88519C9.22908 2.96066 9.20859 3.04204 9.15065 3.11051C9.09402 3.17744 9.01909 3.21131 8.949 3.22724L8.9381 3.22971C8.69626 3.27064 8.5156 3.31012 8.392 3.34788L8.38851 3.34895C8.27817 3.38048 8.21447 3.42076 8.17905 3.45815C8.14652 3.4927 8.10959 3.55715 8.08054 3.6701L8.08019 3.67147C8.04868 3.79049 8.01408 3.96225 7.97677 4.18984L7.97621 4.19326C7.96337 4.26389 7.93224 4.34165 7.86488 4.40093C7.79602 4.46152 7.71313 4.48188 7.63804 4.48188C7.56339 4.48188 7.47882 4.46167 7.40959 4.39821C7.34339 4.33753 7.31513 4.25911 7.30485 4.18938Z"
          fill={color} />
  </svg>

)

export const KitchenCulteries20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.09149 4.09864C6.14597 3.77177 5.92516 3.46264 5.5983 3.40816C5.27144 3.35368 4.9623 3.57449 4.90782 3.90136L4.40782 6.90136C4.40239 6.93396 4.39966 6.96695 4.39966 7C4.39966 7.75586 4.69993 8.48077 5.2344 9.01525C5.62515 9.40599 6.11767 9.67156 6.64966 9.78613V16C6.64966 16.3314 6.91829 16.6 7.24966 16.6C7.58103 16.6 7.84966 16.3314 7.84966 16V9.78613C7.88001 9.7796 7.91023 9.77257 7.94032 9.76505C8.43767 9.64088 8.89676 9.3837 9.2653 9.01515C9.79978 8.48067 10.1 7.75577 10.1 6.9999C10.1 6.95765 10.0957 6.91642 10.0874 6.87663L9.59149 3.90136C9.53702 3.57449 9.22788 3.35368 8.90102 3.40816C8.57416 3.46264 8.35334 3.77177 8.40782 4.09864L8.79137 6.3999H7.84966V4C7.84966 3.66863 7.58103 3.4 7.24966 3.4C6.91829 3.4 6.64966 3.66863 6.64966 4V6.3999H5.70795L6.09149 4.09864Z"
          fill="white" />
    <path d="M14.75 12H11.25C11.25 12 12 4 14.75 4V16" fill="white" />
    <path d="M14.75 12H11.25C11.25 12 12 4 14.75 4V16" stroke="white" strokeWidth="1.2" strokeLinecap="round"
          strokeLinejoin="round" />
  </svg>

)

export const RushHour20 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.40005 2.20001C6.06868 2.20001 5.80005 2.46864 5.80005 2.80001C5.80005 3.13138 6.06868 3.40001 6.40005 3.40001H9.60005C9.93142 3.40001 10.2 3.13138 10.2 2.80001C10.2 2.46864 9.93142 2.20001 9.60005 2.20001H6.40005ZM3.12036 8.79993C3.12036 6.10478 5.30521 3.91993 8.00036 3.91993C10.6955 3.91993 12.8804 6.10478 12.8804 8.79993C12.8804 11.4951 10.6955 13.6799 8.00036 13.6799C5.30521 13.6799 3.12036 11.4951 3.12036 8.79993ZM10.3335 6.46638C10.5288 6.66164 10.5288 6.97823 10.3335 7.17349L8.3536 9.15339C8.15834 9.34865 7.84176 9.34865 7.6465 9.15339C7.45123 8.95812 7.45123 8.64154 7.6465 8.44628L9.62639 6.46638C9.82166 6.27112 10.1382 6.27112 10.3335 6.46638Z"
          fill="white" />
  </svg>

)

export const ClockWhite16 = ({ color = "white" }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.66683 3.01137C5.65352 2.35208 6.81356 2.00018 8.00025 2.00018C9.59098 2.00202 11.116 2.63475 12.2409 3.75957C13.3657 4.88439 13.9984 6.40945 14.0002 8.00018C14.0002 9.18687 13.6484 10.3469 12.9891 11.3336C12.3298 12.3203 11.3927 13.0893 10.2963 13.5435C9.19999 13.9976 7.99359 14.1164 6.8297 13.8849C5.66582 13.6534 4.59672 13.0819 3.75761 12.2428C2.91849 11.4037 2.34705 10.3346 2.11554 9.17072C1.88402 8.00684 2.00284 6.80044 2.45697 5.70408C2.9111 4.60773 3.68013 3.67065 4.66683 3.01137ZM8.30005 5.20038C8.30005 4.92424 8.07619 4.70038 7.80005 4.70038C7.52391 4.70038 7.30005 4.92424 7.30005 5.20038V8.20038C7.30005 8.47652 7.52391 8.70038 7.80005 8.70038H10.8C11.0762 8.70038 11.3 8.47652 11.3 8.20038C11.3 7.92424 11.0762 7.70038 10.8 7.70038H8.30005V5.20038Z"
          fill={color} />
  </svg>

)

export const Switcher24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M16.4697 9.53033C16.7626 9.82322 17.2374 9.82322 17.5303 9.53033C17.8232 9.23744 17.8232 8.76256 17.5303 8.46967L12.5303 3.46967C12.2374 3.17678 11.7626 3.17678 11.4697 3.46967L6.46967 8.46967C6.17678 8.76256 6.17678 9.23744 6.46967 9.53033C6.76256 9.82322 7.23744 9.82322 7.53033 9.53033L12 5.06066L16.4697 9.53033ZM7.53033 14.4697C7.23744 14.1768 6.76256 14.1768 6.46967 14.4697C6.17678 14.7626 6.17678 15.2374 6.46967 15.5303L11.4697 20.5303C11.7626 20.8232 12.2374 20.8232 12.5303 20.5303L17.5303 15.5303C17.8232 15.2374 17.8232 14.7626 17.5303 14.4697C17.2374 14.1768 16.7626 14.1768 16.4697 14.4697L12 18.9393L7.53033 14.4697Z"
          fill="#BAB9B8" />
  </svg>
)

export const SliderIcon = () => (
  <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.5" width="18" height="18" rx="3" fill="#BAB9B8" />
    <path
      d="M9.5 3.8C9.5 3.05005 9.5 2.67508 9.69098 2.41221C9.75266 2.32732 9.82732 2.25266 9.91221 2.19098C10.1751 2 10.5501 2 11.3 2H14.7C15.4499 2 15.8249 2 16.0878 2.19098C16.1727 2.25266 16.2473 2.32732 16.309 2.41221C16.5 2.67508 16.5 3.05005 16.5 3.8V14.2C16.5 14.9499 16.5 15.3249 16.309 15.5878C16.2473 15.6727 16.1727 15.7473 16.0878 15.809C15.8249 16 15.4499 16 14.7 16H11.3C10.5501 16 10.1751 16 9.91221 15.809C9.82732 15.7473 9.75266 15.6727 9.69098 15.5878C9.5 15.3249 9.5 14.9499 9.5 14.2V3.8Z"
      fill="#F9F9F9" />
    <path d="M11.5 4H14.5" stroke="#BAB9B8" strokeLinecap="round" />
    <path d="M11.5 7H14.5" stroke="#BAB9B8" strokeLinecap="round" />
    <path d="M11.5 10H14.5" stroke="#BAB9B8" strokeLinecap="round" />
  </svg>

)

export const KitchenMonitorItemStatusOnGoing = ({ color = '#BAB9B8' , width = 24, height = 24 }) => (
  <svg
    width={width }
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.5 10.3C3.5 9.55005 3.5 9.17508 3.69098 8.91221C3.75266 8.82732 3.82732 8.75266 3.91221 8.69098C4.17508 8.5 4.55005 8.5 5.3 8.5H18.7C19.4499 8.5 19.8249 8.5 20.0878 8.69098C20.1727 8.75266 20.2473 8.82732 20.309 8.91221C20.5 9.17508 20.5 9.55005 20.5 10.3V13.3C20.5 16.2998 20.5 17.7997 19.7361 18.8511C19.4893 19.1907 19.1907 19.4893 18.8511 19.7361C17.7997 20.5 16.2998 20.5 13.3 20.5H10.7C7.70021 20.5 6.20032 20.5 5.14886 19.7361C4.80928 19.4893 4.51065 19.1907 4.26393 18.8511C3.5 17.7997 3.5 16.2998 3.5 13.3V10.3Z"
      fill={color}
    />
    <path d="M19 14L21.8284 11.1716" stroke={color} strokeWidth="2" strokeLinecap="round" />
    <path d="M2 11L4.82843 13.8284" stroke={color} strokeWidth="2" strokeLinecap="round" />
    <path d="M8 6.5L8 3.5" stroke={color} strokeWidth="2" strokeLinecap="round" />
    <path d="M12 6.5L12 3.5" stroke={color} strokeWidth="2" strokeLinecap="round" />
    <path d="M16 6.5L16 3.5" stroke={color} strokeWidth="2" strokeLinecap="round" />
  </svg>
);


export const KitchenMonitorItemStatusCanceled = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.5 10.3C3.5 9.55005 3.5 9.17508 3.69098 8.91221C3.75266 8.82732 3.82732 8.75266 3.91221 8.69098C4.17508 8.5 4.55005 8.5 5.3 8.5H18.7C19.4499 8.5 19.8249 8.5 20.0878 8.69098C20.1727 8.75266 20.2473 8.82732 20.309 8.91221C20.5 9.17508 20.5 9.55005 20.5 10.3V13.3C20.5 16.2998 20.5 17.7997 19.7361 18.8511C19.4893 19.1907 19.1907 19.4893 18.8511 19.7361C17.7997 20.5 16.2998 20.5 13.3 20.5H10.7C7.70021 20.5 6.20032 20.5 5.14886 19.7361C4.80928 19.4893 4.51065 19.1907 4.26393 18.8511C3.5 17.7997 3.5 16.2998 3.5 13.3V10.3Z"
      fill="#E04649" />
    <path d="M19 14L21.8284 11.1716" stroke="#E04649" strokeWidth="2" strokeLinecap="round" />
    <path d="M2 11L4.82843 13.8284" stroke="#E04649" strokeWidth="2" strokeLinecap="round" />
    <path d="M8 6.5L8 3.5" stroke="#E04649" strokeWidth="2" strokeLinecap="round" />
    <path d="M12 6.5L12 3.5" stroke="#E04649" strokeWidth="2" strokeLinecap="round" />
    <path d="M16 6.5L16 3.5" stroke="#E04649" strokeWidth="2" strokeLinecap="round" />
  </svg>

)

export const KitchenMonitorItemStatusServed = ({ color = '#BAB9B8' , width = 24, height = 24 }) => (
  <svg width={width } height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M9.99844 3.8999C10.3298 3.8999 10.5984 4.16853 10.5984 4.4999V5.09682C14.144 5.40102 16.9269 8.37541 16.9269 11.9998V12.9969C16.9269 13.3141 16.6698 13.5712 16.3526 13.5712H3.64398C3.32684 13.5712 3.06973 13.3141 3.06973 12.9969V11.9998C3.06973 8.37531 5.85275 5.40087 9.39844 5.0968V4.4999C9.39844 4.16853 9.66707 3.8999 9.99844 3.8999ZM2.49844 14.6144C2.16707 14.6144 1.89844 14.883 1.89844 15.2144C1.89844 15.5457 2.16707 15.8144 2.49844 15.8144L17.4984 15.8144C17.8298 15.8144 18.0984 15.5457 18.0984 15.2144C18.0984 14.883 17.8298 14.6144 17.4984 14.6144L2.49844 14.6144Z"
          fill={color} />
  </svg>
)

export const KitchenMonitorItemStatusCompleted = ({ fill = "#428271", color = '#BAB9B8' , width = 24, height = 24 }) => (
  <svg width={width } height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 20.4C16.6392 20.4 20.4 16.6392 20.4 12C20.4 7.36078 16.6392 3.59998 12 3.59998C7.36078 3.59998 3.59998 7.36078 3.59998 12C3.59998 16.6392 7.36078 20.4 12 20.4Z" fill={fill}/>
    <path d="M15.3 9.90002L10.9 14.1L8.70001 12" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

);

export const KitchenMonitorItemStatusNew = ({ color = '#333332' , width = 24, height = 24 }) => (
  <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M7.66653 5.51451C8.94914 4.6575 10.4571 4.20004 11.9996 4.19998C14.0677 4.20246 16.0503 5.02507 17.5126 6.48739C18.975 7.94978 19.7976 9.93253 19.8 12.0007C19.7998 13.5431 19.3424 15.0509 18.4854 16.3334C17.6284 17.6161 16.4102 18.6159 14.9849 19.2062C13.5596 19.7966 11.9913 19.9511 10.4783 19.6501C8.96522 19.3491 7.5754 18.6063 6.48455 17.5154C5.3937 16.4246 4.65082 15.0347 4.34985 13.5217C4.04889 12.0086 4.20336 10.4403 4.79372 9.01504C5.38408 7.58978 6.38383 6.37159 7.66653 5.51451Z"
      stroke={color} strokeOpacity="0.4" strokeWidth="1.2" strokeLinecap="round" />
  </svg>

)

export const KitchenMonitorItemStatusStarted = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17ZM9.99913 15.4446C6.99225 15.4446 4.55469 13.007 4.55469 10.0001C4.55469 6.99323 6.99225 4.55566 9.99913 4.55566V15.4446Z"
          fill="#333332" fillOpacity="0.5" />
  </svg>
);

export const KitchenMonitorSummarySubtract16 = () => (
  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M12 6C12 9.31371 9.31371 12 6 12C2.68629 12 0 9.31371 0 6C0 2.68629 2.68629 0 6 0C9.31371 0 12 2.68629 12 6ZM6 2C6.55228 2 7 2.44772 7 3V6C7 6.55228 6.55228 7 6 7C5.44772 7 5 6.55228 5 6V3C5 2.44772 5.44772 2 6 2ZM6 10C6.55228 10 7 9.55228 7 9C7 8.44772 6.55228 8 6 8C5.44772 8 5 8.44772 5 9C5 9.55228 5.44772 10 6 10Z"
          fill="#737372" />
  </svg>

)

export const KitchenMonitorPrinterIcon = ({ color = "#ffffff"}) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6 7.5V3.75H18V7.5" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M18 14.25H6V20.25H18V14.25Z" stroke={color} strokeWidth="1.5" strokeLinecap="round" />
    <path
      fillRule="evenodd" clipRule="evenodd"
      d="M6 17.25C6.41421 17.25 6.75 16.9142 6.75 16.5H17.25C17.25 16.9142 17.5858 17.25 18 17.25H21.75C22.1642 17.25 22.5 16.9142 22.5 16.5V9C22.5 7.70164 21.3791 6.75 20.1253 6.75H3.87469C2.62094 6.75 1.5 7.70164 1.5 9V16.5C1.5 16.9142 1.83579 17.25 2.25 17.25H6ZM18.75 10.875C18.75 11.4963 18.2463 12 17.625 12C17.0037 12 16.5 11.4963 16.5 10.875C16.5 10.2537 17.0037 9.75 17.625 9.75C18.2463 9.75 18.75 10.2537 18.75 10.875Z"
      fill={color}
    />
  </svg>
)

export const KitchenMonitorFlameCircle16 = () => (
  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M2.66658 1.01118C3.65328 0.351894 4.81331 0 6 0C7.59074 0.00183854 9.11579 0.63457 10.2406 1.75939C11.3654 2.88421 11.9982 4.40926 12 6C12 7.18669 11.6481 8.34672 10.9888 9.33342C10.3295 10.3201 9.39246 11.0891 8.2961 11.5433C7.19974 11.9974 5.99335 12.1162 4.82946 11.8847C3.66557 11.6532 2.59648 11.0818 1.75736 10.2426C0.918247 9.40352 0.346802 8.33443 0.115291 7.17054C-0.11622 6.00666 0.00259972 4.80026 0.456725 3.7039C0.910851 2.60754 1.67989 1.67047 2.66658 1.01118ZM7.68281 8.33182C8.12911 7.88552 8.37984 7.2802 8.37984 6.64904C8.37984 4.61945 6.43212 3.0812 6.34921 3.01674C6.32229 2.99579 6.2908 2.98151 6.2573 2.97506C6.22381 2.96861 6.18927 2.97016 6.15649 2.9796C6.12372 2.98904 6.09364 3.0061 6.06871 3.02938C6.04378 3.05266 6.02471 3.0815 6.01306 3.11355L5.24875 5.21543L4.55224 4.67882C4.52846 4.66048 4.50111 4.64731 4.47195 4.64012C4.44279 4.63294 4.41245 4.63191 4.38287 4.63709C4.35329 4.64227 4.32511 4.65355 4.30013 4.67022C4.27515 4.68689 4.25391 4.70858 4.23777 4.73391C3.828 5.37684 3.62023 6.02118 3.62023 6.64904C3.62023 7.2802 3.87096 7.88552 4.31726 8.33182C4.76356 8.77812 5.36887 9.02885 6.00004 9.02885C6.6312 9.02885 7.23651 8.77812 7.68281 8.33182Z"
          fill="#737372" />
  </svg>

)

export const KitchenMonitorSettingsCog22 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.2422 20.7075H12.7434C12.9895 20.7075 13.1973 20.6392 13.3668 20.5024C13.5364 20.3712 13.6485 20.1853 13.7031 19.9446L14.1133 18.1974C14.2555 18.1481 14.3922 18.0962 14.5235 18.0415C14.6602 17.9923 14.7887 17.9403 14.909 17.8856L16.4348 18.8126C16.6371 18.9384 16.8477 18.9903 17.0664 18.9685C17.2852 18.9521 17.4793 18.8591 17.6489 18.6896L18.6989 17.6478C18.8684 17.4782 18.9641 17.2813 18.986 17.0571C19.0078 16.8274 18.9504 16.6114 18.8137 16.4091L17.8785 14.8997C17.9442 14.7739 18.0016 14.6481 18.0508 14.5224C18.1 14.3911 18.1465 14.2599 18.1903 14.1286L19.9539 13.7103C20.1891 13.661 20.3723 13.5517 20.5035 13.3821C20.6403 13.2071 20.7086 12.9966 20.7086 12.7505V11.2821C20.7086 11.0415 20.6403 10.8364 20.5035 10.6669C20.3723 10.4919 20.1891 10.3798 19.9539 10.3306L18.2067 9.91221C18.1574 9.76455 18.1055 9.6251 18.0508 9.49385C17.9961 9.3626 17.9442 9.23955 17.8949 9.12471L18.8301 7.59072C18.9614 7.38838 19.0188 7.17783 19.0024 6.95908C18.986 6.74033 18.8903 6.54619 18.7153 6.37666L17.6406 5.31846C17.4711 5.16533 17.2824 5.0751 17.0746 5.04775C16.8723 5.02041 16.6699 5.06416 16.4676 5.179L14.909 6.13877C14.7942 6.07861 14.6684 6.02393 14.5317 5.97471C14.4004 5.92002 14.261 5.86807 14.1133 5.81885L13.7031 4.04697C13.6485 3.81182 13.5364 3.62588 13.3668 3.48916C13.1973 3.34697 12.9895 3.27588 12.7434 3.27588H11.2422C11.0016 3.27588 10.7938 3.34697 10.6188 3.48916C10.4492 3.62588 10.3371 3.81182 10.2824 4.04697L9.87229 5.80244C9.7301 5.85166 9.59065 5.90361 9.45393 5.9583C9.31721 6.01299 9.1887 6.07041 9.06838 6.13057L7.51799 5.179C7.31565 5.06416 7.11057 5.02041 6.90276 5.04775C6.70042 5.06963 6.51174 5.1626 6.33674 5.32666L5.27854 6.37666C5.10354 6.54619 5.0051 6.74033 4.98323 6.95908C4.96682 7.17783 5.02424 7.38838 5.15549 7.59072L6.08245 9.12471C6.03323 9.23955 5.98127 9.3626 5.92659 9.49385C5.8719 9.6251 5.82268 9.76455 5.77893 9.91221L4.03987 10.3306C3.79924 10.3798 3.61057 10.4919 3.47385 10.6669C3.3426 10.8364 3.27698 11.0415 3.27698 11.2821V12.7505C3.27698 12.9966 3.3426 13.2071 3.47385 13.3821C3.61057 13.5517 3.79924 13.661 4.03987 13.7103L5.79534 14.1286C5.83362 14.2599 5.8801 14.3911 5.93479 14.5224C5.98948 14.6481 6.04417 14.7739 6.09885 14.8997L5.1719 16.4091C5.04065 16.6114 4.98596 16.8274 5.00784 17.0571C5.02971 17.2813 5.12542 17.4782 5.29495 17.6478L6.33674 18.6896C6.50627 18.8591 6.70042 18.9521 6.91917 18.9685C7.13792 18.9903 7.34846 18.9384 7.55081 18.8126L9.07659 17.8856C9.1969 17.9403 9.32268 17.9923 9.45393 18.0415C9.59065 18.0962 9.7301 18.1481 9.87229 18.1974L10.2824 19.9446C10.3371 20.1853 10.4492 20.3712 10.6188 20.5024C10.7938 20.6392 11.0016 20.7075 11.2422 20.7075ZM11.9969 14.8669C11.4719 14.8669 10.9906 14.7384 10.5531 14.4813C10.1156 14.2188 9.76838 13.8688 9.51135 13.4313C9.25432 12.9938 9.12581 12.5126 9.12581 11.9876C9.12581 11.4626 9.25432 10.9841 9.51135 10.5521C9.77385 10.12 10.1211 9.77549 10.5531 9.51846C10.9906 9.25596 11.4719 9.12471 11.9969 9.12471C12.5219 9.12471 13.0004 9.25596 13.4324 9.51846C13.8699 9.77549 14.2172 10.12 14.4742 10.5521C14.7313 10.9841 14.8598 11.4626 14.8598 11.9876C14.8598 12.5126 14.7313 12.9938 14.4742 13.4313C14.2172 13.8688 13.8699 14.2188 13.4324 14.4813C13.0004 14.7384 12.5219 14.8669 11.9969 14.8669Z"
      fill="#929191" />
  </svg>

)

export const KitchenMonitorTakeAwayIcon = ({ color = "white" }) => (
  <svg width="19" height="16" viewBox="0 0 19 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.88812 2.27176C6.0041 1.26214 6.85879 0.5 7.87505 0.5H16.6641C17.8591 0.5 18.7874 1.54106 18.6511 2.72824L17.3875 13.7282C17.2715 14.7379 16.4168 15.5 15.4005 15.5H6.61146C5.41647 15.5 4.48815 14.4589 4.62453 13.2718L5.88812 2.27176ZM9.53759 3.55364C9.81373 3.55364 10.0376 3.77749 10.0376 4.05364C10.0376 5.08598 10.8745 5.92287 11.9068 5.92287C12.9392 5.92287 13.776 5.08598 13.776 4.05364C13.776 3.77749 13.9999 3.55364 14.276 3.55364C14.5522 3.55364 14.776 3.77749 14.776 4.05364C14.776 5.63827 13.4914 6.92287 11.9068 6.92287C10.3222 6.92287 9.03759 5.63827 9.03759 4.05364C9.03759 3.77749 9.26144 3.55364 9.53759 3.55364ZM0 4.19222C0 3.86085 0.268629 3.59222 0.6 3.59222H3.83077C4.16214 3.59222 4.43077 3.86085 4.43077 4.19222C4.43077 4.52359 4.16214 4.79222 3.83077 4.79222H0.6C0.268629 4.79222 0 4.52359 0 4.19222ZM0 6.88453C0 6.55316 0.268629 6.28453 0.6 6.28453H3.29231C3.62368 6.28453 3.89231 6.55316 3.89231 6.88453C3.89231 7.2159 3.62368 7.48453 3.29231 7.48453H0.6C0.268629 7.48453 0 7.2159 0 6.88453ZM0.6 8.97684C0.268629 8.97684 0 9.24547 0 9.57684C0 9.90821 0.268629 10.1768 0.6 10.1768H2.75385C3.08522 10.1768 3.35385 9.90821 3.35385 9.57684C3.35385 9.24547 3.08522 8.97684 2.75385 8.97684H0.6ZM0 12.2691C0 11.9378 0.268629 11.6691 0.6 11.6691H2.21538C2.54676 11.6691 2.81538 11.9378 2.81538 12.2691C2.81538 12.6005 2.54676 12.8691 2.21538 12.8691H0.6C0.268629 12.8691 0 12.6005 0 12.2691Z"
          fill={color} />
  </svg>
)

export const KitchenMonitorExpressIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.241 3.00137C11.6019 2.98269 11.9458 3.15633 12.1453 3.45796C12.2415 3.60342 12.2707 3.76064 12.2799 3.88275C12.2892 4.00752 12.2805 4.14051 12.2653 4.27139C12.2357 4.52612 12.1701 4.8615 12.0904 5.26804L12.0904 5.26805L11.7057 7.23279C11.6807 7.3603 11.6677 7.42836 11.6618 7.47808L11.6612 7.48312L11.6658 7.4853C11.7111 7.50666 11.7756 7.53174 11.8968 7.57817L14.4422 8.55301C14.6138 8.61872 14.7734 8.67982 14.8996 8.74175C15.0318 8.80661 15.1911 8.90152 15.309 9.06629C15.4653 9.28471 15.5294 9.55592 15.4874 9.82126C15.4558 10.0214 15.3558 10.1777 15.2666 10.295C15.1815 10.4069 15.0662 10.5331 14.9422 10.6688L10.181 15.8787L10.1809 15.8787C9.90158 16.1844 9.67111 16.4366 9.48399 16.6118C9.38785 16.7018 9.28618 16.7879 9.18037 16.8545C9.07681 16.9197 8.9331 16.9896 8.75904 16.9986C8.39811 17.0173 8.05419 16.8437 7.8547 16.542C7.75849 16.3966 7.72925 16.2394 7.72011 16.1172C7.71076 15.9925 7.7195 15.8595 7.73469 15.7286C7.76426 15.4739 7.82994 15.1385 7.90956 14.732L7.90957 14.7319L8.29432 12.7672C8.31929 12.6397 8.33228 12.5716 8.33818 12.5219L8.33876 12.5169L8.33419 12.5147C8.28894 12.4933 8.2244 12.4683 8.10315 12.4218L5.5578 11.447C5.38617 11.3813 5.22659 11.3202 5.1004 11.2583C4.96824 11.1934 4.80886 11.0985 4.69096 10.9337C4.53468 10.7153 4.4706 10.4441 4.51257 10.1787C4.54423 9.97857 4.64424 9.82226 4.73337 9.70502C4.81848 9.59306 4.9338 9.46689 5.05783 9.33119L9.81907 4.12126C10.0984 3.81556 10.3289 3.56338 10.516 3.38819C10.6122 3.29818 10.7138 3.21211 10.8196 3.14548C10.9232 3.08027 11.0669 3.01037 11.241 3.00137Z"
      fill="white" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

)

export const KitchenMonitorCancelledItemIcon = ({ height = 24, width = 24, color = "#333332"  }) => (
  <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.0003 3.60004C10.339 3.60004 8.71493 4.09269 7.33355 5.01569C5.95218 5.9387 4.87553 7.2506 4.23976 8.78549C3.60398 10.3204 3.43763 12.0094 3.76175 13.6388C4.08587 15.2682 4.88589 16.765 6.06065 17.9397C7.23541 19.1145 8.73215 19.9145 10.3616 20.2386C11.991 20.5627 13.68 20.3964 15.2149 19.7606C16.7498 19.1248 18.0617 18.0482 18.9847 16.6668C19.9077 15.2855 20.4003 13.6614 20.4003 12C20.3978 9.77301 19.5119 7.63793 17.9372 6.06318C16.3624 4.48843 14.2274 3.60261 12.0003 3.60004Z"
      fill={color} fill-opacity="0.5" />
    <line x1="8.75" y1="12.25" x2="15.25" y2="12.25" stroke="#F9F9F9" strokeWidth="1.5" strokeLinecap="round" />
  </svg>

)

export const KitchenMonitorNotificationBell = ({ color = "#FFC107", height= 24, width= 24 }) => (
  <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12 21C13.1 21 14 20.1 14 19H10C10 20.1 10.9 21 12 21Z"
      fill={color}
    />
    <path
      d="M18 16H6C5.45 16 5 15.55 5 15C5 14.45 5.45 14 6 14H6.73C6.45 13.33 6.26 12.59 6.16 11.84C6.03 10.73 6 9.6 6 8.5C6 6.22 7.79 4.43 10.06 4.05C10.5 3.97 10.85 3.64 10.93 3.2C11.16 2 12 1 12 1C12 1 12.84 2 13.07 3.2C13.15 3.64 13.5 3.97 13.94 4.05C16.21 4.43 18 6.22 18 8.5C18 9.6 17.97 10.73 17.84 11.84C17.74 12.59 17.55 13.33 17.27 14H18C18.55 14 19 14.45 19 15C19 15.55 18.55 16 18 16Z"
      fill={color}
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChainedTablesHorizontalIcon2 = () => (
  <svg width="19" height="58" viewBox="0 0 19 58" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M16 3H13.6C10.2397 3 8.55953 3 7.27606 3.65396C6.14708 4.2292 5.2292 5.14708 4.65396 6.27606C4 7.55953 4 9.23969 4 12.6V45.4C4 48.7603 4 50.4405 4.65396 51.7239C5.2292 52.8529 6.14708 53.7708 7.27606 54.346C8.55953 55 10.2397 55 13.6 55H16"
      stroke="#FF7C5C" strokeWidth="1.2" />
    <circle cx="16" cy="3" r="2.4" fill="#F9F9F9" stroke="#FF7C5C" strokeWidth="1.2" />
    <circle cx="16" cy="55" r="2.4" fill="#F9F9F9" stroke="#FF7C5C" strokeWidth="1.2" />
    <path
      d="M0.999756 27.4999L0.999756 24.9999C0.999756 24.2043 1.31583 23.4412 1.87844 22.8787C2.44105 22.3161 3.20411 22 3.99976 22C4.79541 22 5.55847 22.3161 6.12108 22.8787C6.68369 23.4413 6.99976 24.2043 6.99976 24.9999L6.99976 27.4999" fill="#F2F2F2"/>
    <path d="M0.999756 27.4999L0.999756 24.9999C0.999756 24.2043 1.31583 23.4412 1.87844 22.8787C2.44105 22.3161 3.20411 22 3.99976 22C4.79541 22 5.55847 22.3161 6.12108 22.8787C6.68369 23.4413 6.99976 24.2043 6.99976 24.9999L6.99976 27.4999" stroke="#FF7C5C" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M0.999756 30.5L0.999756 32.9999C0.999756 33.7955 1.31583 34.5586 1.87844 35.1212C2.44105 35.6837 3.20411 35.9998 3.99976 35.9998C4.79541 35.9998 5.55847 35.6837 6.12108 35.1212C6.68369 34.5586 6.99976 33.7955 6.99976 32.9999L6.99976 30.5" fill="#F2F2F2"/>
    <path d="M0.999756 30.5L0.999756 32.9999C0.999756 33.7955 1.31583 34.5586 1.87844 35.1212C2.44105 35.6837 3.20411 35.9998 3.99976 35.9998C4.79541 35.9998 5.55847 35.6837 6.12108 35.1212C6.68369 34.5586 6.99976 33.7955 6.99976 32.9999L6.99976 30.5" stroke="#FF7C5C" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.99996 26.0005L3.99996 32.0003" stroke="#FF7C5C" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const ChainedTableVerticalEllipseGreyIcon = () => (
  <svg width="8" height="16" viewBox="0 0 8 16" fill="none" xmlns="http://www.w3.org/2000/svg" transform="rotate(90)">
    <path d="M0.999756 6.49985L0.999756 3.99992C0.999756 3.20429 1.31583 2.44125 1.87844 1.87866C2.44105 1.31606 3.20411 1 3.99976 1C4.79541 1 5.55847 1.31606 6.12108 1.87866C6.68369 2.44125 6.99976 3.20429 6.99976 3.99992L6.99976 6.49985" fill="#F2F2F2"/>
    <path d="M0.999756 6.49985L0.999756 3.99992C0.999756 3.20429 1.31583 2.44125 1.87844 1.87866C2.44105 1.31606 3.20411 1 3.99976 1C4.79541 1 5.55847 1.31606 6.12108 1.87866C6.68369 2.44125 6.99976 3.20429 6.99976 3.99992L6.99976 6.49985" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M0.999756 9.5L0.999756 11.9999C0.999756 12.7955 1.31583 13.5586 1.87844 14.1212C2.44105 14.6837 3.20411 14.9998 3.99976 14.9998C4.79541 14.9998 5.55847 14.6837 6.12108 14.1212C6.68369 13.5586 6.99976 12.7955 6.99976 11.9999L6.99976 9.5" fill="#F2F2F2"/>
    <path d="M0.999756 9.5L0.999756 11.9999C0.999756 12.7955 1.31583 13.5586 1.87844 14.1212C2.44105 14.6837 3.20411 14.9998 3.99976 14.9998C4.79541 14.9998 5.55847 14.6837 6.12108 14.1212C6.68369 13.5586 6.99976 12.7955 6.99976 11.9999L6.99976 9.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.99996 5.00049L3.99996 11.0003" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const TeosScaleItemIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M11.0821 4.64541C11.1624 4.32392 10.9669 3.9982 10.6454 3.91789C10.3239 3.83758 9.9982 4.03309 9.91789 4.35459L9.41828 6.35459C9.33797 6.67608 9.53349 7.0018 9.85498 7.08211C10.1765 7.16242 10.5022 6.96691 10.5825 6.64541L11.0821 4.64541ZM8.58211 4.64542C8.66242 4.32392 8.46691 3.9982 8.14541 3.91789C7.82392 3.83758 7.4982 4.0331 7.41789 4.35459L6.91828 6.35459C6.83797 6.67608 7.03348 7.0018 7.35498 7.08211C7.67647 7.16242 8.00219 6.96691 8.0825 6.64541L8.58211 4.64542ZM13.1454 3.91789C13.4669 3.9982 13.6624 4.32392 13.5821 4.64542L13.0825 6.64541C13.0022 6.96691 12.6765 7.16242 12.355 7.08211C12.0335 7.0018 11.838 6.67608 11.9183 6.35458L12.4179 4.35459C12.4982 4.03309 12.8239 3.83758 13.1454 3.91789ZM2.57143 9C2.25584 9 2 9.23878 2 9.53333V10.0667C2 13.8958 5.32588 17 9.42857 17H10.5714C14.6741 17 18 13.8958 18 10.0667V9.53333C18 9.23878 17.7442 9 17.4286 9H2.57143Z" fill="#BAB9B8"/>
  </svg>

)

export const PrinterIcon20 = () => (
  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M6.5 2.75C5.67157 2.75 5 3.42157 5 4.25H16C16 3.42157 15.3284 2.75 14.5 2.75H6.5ZM2.5 8.45C2.5 7.3299 2.5 6.76984 2.71799 6.34202C2.90973 5.96569 3.21569 5.65973 3.59202 5.46799C4.01984 5.25 4.5799 5.25 5.7 5.25H15.3C16.4201 5.25 16.9802 5.25 17.408 5.46799C17.7843 5.65973 18.0903 5.96569 18.282 6.34202C18.5 6.76984 18.5 7.3299 18.5 8.45V12.05C18.5 13.1701 18.5 13.7302 18.282 14.158C18.0903 14.5343 17.7843 14.8403 17.408 15.032C16.9802 15.25 16.4201 15.25 15.3 15.25H5.7C4.5799 15.25 4.01984 15.25 3.59202 15.032C3.21569 14.8403 2.90973 14.5343 2.71799 14.158C2.5 13.7302 2.5 13.1701 2.5 12.05V8.45ZM16 7.75C16 8.30228 15.5523 8.75 15 8.75C14.4477 8.75 14 8.30228 14 7.75C14 7.19772 14.4477 6.75 15 6.75C15.5523 6.75 16 7.19772 16 7.75Z"
          fill="#BAB9B8" />
    <rect x="5.5" y="9.75" width="10" height="7.5" rx="1" fill="#E8E7E6" stroke="#929191" />
    <path fillRule="evenodd" clipRule="evenodd"
          d="M7.99844 11.6504C7.66707 11.6504 7.39844 11.919 7.39844 12.2504C7.39844 12.5818 7.66707 12.8504 7.99844 12.8504H12.9984C13.3298 12.8504 13.5984 12.5818 13.5984 12.2504C13.5984 11.919 13.3298 11.6504 12.9984 11.6504H7.99844ZM7.99844 14.1504C7.66707 14.1504 7.39844 14.419 7.39844 14.7504C7.39844 15.0818 7.66707 15.3504 7.99844 15.3504H12.9984C13.3298 15.3504 13.5984 15.0818 13.5984 14.7504C13.5984 14.419 13.3298 14.1504 12.9984 14.1504H7.99844Z"
          fill="#BAB9B8" />
  </svg>

)

export const Confirm20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M11.5004 3.8999C11.169 3.8999 10.9004 4.16853 10.9004 4.4999C10.9004 4.83127 11.169 5.0999 11.5004 5.0999H15.0004C15.7736 5.0999 16.4004 5.7267 16.4004 6.4999V9.4999C16.4004 10.2731 15.7736 10.8999 15.0004 10.8999H4.94892L7.92465 7.92417C8.15897 7.68985 8.15897 7.30995 7.92465 7.07564C7.69034 6.84132 7.31044 6.84132 7.07613 7.07564L3.07613 11.0756C2.84181 11.31 2.84181 11.6899 3.07613 11.9242L7.07613 15.9242C7.31044 16.1585 7.69034 16.1585 7.92465 15.9242C8.15897 15.6899 8.15897 15.31 7.92465 15.0756L4.94892 12.0999H15.0004C16.4363 12.0999 17.6004 10.9358 17.6004 9.4999V6.4999C17.6004 5.06396 16.4363 3.8999 15.0004 3.8999H11.5004Z"
          fill="#BAB9B8" />
  </svg>

)

export const WebIcon20 = () => (
  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M4.5007 3.1499C3.3406 3.1499 2.40037 4.09055 2.40057 5.25052C2.40087 7.01108 2.4007 8.77162 2.40053 10.5323L2.40053 10.5324L2.40039 12.0497C2.4003 13.2095 3.34054 14.1498 4.50034 14.1498H8.35059V15.7496H7.2002C6.86882 15.7496 6.6002 16.0182 6.6002 16.3496C6.6002 16.681 6.86882 16.9496 7.2002 16.9496H8.35059V16.9496H11.6506V16.9496H12.8001C13.1315 16.9496 13.4001 16.681 13.4001 16.3496C13.4001 16.0182 13.1315 15.7496 12.8001 15.7496H11.6506V14.1498H15.5002C16.66 14.1498 17.6002 13.2095 17.6002 12.0498V5.2499C17.6002 4.0901 16.66 3.1499 15.5002 3.1499H4.5007ZM7.90039 4.8252C7.62425 4.8252 7.40039 5.04905 7.40039 5.3252C7.40039 5.60134 7.62425 5.8252 7.90039 5.8252H12.1003C12.3765 5.8252 12.6003 5.60134 12.6003 5.3252C12.6003 5.04905 12.3765 4.8252 12.1003 4.8252H7.90039Z"
          fill="#BAB9B8" />
  </svg>

)

export const KioskIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd"
          d="M5.21799 3.09202C5 3.51984 5 4.0799 5 5.2V10.8C5 11.9201 5 12.4802 5.21799 12.908C5.40973 13.2843 5.71569 13.5903 6.09202 13.782C6.51984 14 7.0799 14 8.2 14H8.3501V15.75H6.9999C6.66853 15.75 6.3999 16.0186 6.3999 16.35C6.3999 16.6814 6.66853 16.95 6.9999 16.95H12.9999C13.3313 16.95 13.5999 16.6814 13.5999 16.35C13.5999 16.0186 13.3313 15.75 12.9999 15.75H11.6501V14H11.8C12.9201 14 13.4802 14 13.908 13.782C14.2843 13.5903 14.5903 13.2843 14.782 12.908C15 12.4802 15 11.9201 15 10.8V5.2C15 4.0799 15 3.51984 14.782 3.09202C14.5903 2.71569 14.2843 2.40973 13.908 2.21799C13.4802 2 12.9201 2 11.8 2H8.2C7.0799 2 6.51984 2 6.09202 2.21799C5.71569 2.40973 5.40973 2.71569 5.21799 3.09202ZM8.5 11.5C8.22386 11.5 8 11.7239 8 12C8 12.2761 8.22386 12.5 8.5 12.5H11.5C11.7761 12.5 12 12.2761 12 12C12 11.7239 11.7761 11.5 11.5 11.5H8.5Z"
          fill="#BAB9B8" />
  </svg>
)

export const AddressIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16.5 15.5V8.9276C16.5 8.3393 16.241 7.78083 15.7919 7.40083L11.2919 3.59313C10.5462 2.96216 9.45381 2.96216 8.70812 3.59313L4.20812 7.40083C3.75901 7.78083 3.5 8.3393 3.5 8.9276V15.5C3.5 16.0523 3.94772 16.5 4.5 16.5H7C7.55228 16.5 8 16.0523 8 15.5V11.5C8 11.2239 8.22386 11 8.5 11H11.5C11.7761 11 12 11.2239 12 11.5V15.5C12 16.0523 12.4477 16.5 13 16.5H15.5C16.0523 16.5 16.5 16.0523 16.5 15.5Z" fill="#BAB9B8"/>
  </svg>
)

export const DriverIcon20 = ({ color = "#BAB9B8" }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.25 9.375H18.75" stroke={color} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16.25 14.375C16.25 15 16.25 15.625 16.25 16.25C16.25 16.5952 15.9702 16.875 15.625 16.875C15.2083 16.875 14.7916 16.875 14.3749 16.875C14.0299 16.875 13.7501 16.5954 13.75 16.2504C13.7499 15.6253 13.75 15.0001 13.75 14.375" fill={color}/>
    <path d="M16.25 14.375C16.25 15 16.25 15.625 16.25 16.25C16.25 16.5952 15.9702 16.875 15.625 16.875C15.2083 16.875 14.7916 16.875 14.3749 16.875C14.0299 16.875 13.7501 16.5954 13.75 16.2504C13.7499 15.6253 13.75 15.0001 13.75 14.375" stroke={color} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.24976 14.375C6.24976 14.9999 6.24966 15.6248 6.24978 16.2497C6.24985 16.595 5.96998 16.875 5.62468 16.875C5.20808 16.875 4.79148 16.875 4.37489 16.875C4.02988 16.875 3.75012 16.5954 3.75002 16.2504C3.74985 15.6253 3.75 15.0001 3.75 14.375" fill={color}/>
    <path d="M6.24977 14.375C6.24977 14.9999 6.24966 15.6248 6.24978 16.2497C6.24985 16.595 5.96998 16.875 5.62468 16.875C5.20808 16.875 4.79148 16.875 4.37489 16.875C4.02988 16.875 3.75012 16.5954 3.75002 16.2504C3.74985 15.6253 3.75 15.0001 3.75 14.375" stroke={color} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M17.5 9.375H2.5V15H17.5V9.375Z" fill={color}/>
    <path d="M5 11.875H6.25" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M13.75 11.875H15" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M17.5 9.375V13.75C17.5 14.4404 16.9404 15 16.25 15H3.75C3.05964 15 2.5 14.4404 2.5 13.75V9.375" stroke={color} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16.875 9.37514C16.3225 8.01901 15.77 6.66287 15.2175 5.30675C14.834 4.3655 13.9189 3.75011 12.9025 3.75006C10.9675 3.74998 9.03251 3.74998 7.09751 3.75006C6.08113 3.75011 5.16595 4.3655 4.78247 5.30676C4.22997 6.66288 3.6775 8.01901 3.125 9.37514" stroke={color} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)