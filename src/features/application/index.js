import React from "react";
import {useSelector} from "react-redux";
import { accountSelectors, applicationSelectors, restaurantSelectors } from "../../../redux/selectors";
import {onboardingStates} from "../../utils/const";
import Notification from "../../components/Notification";
import Layout from "../../components/Administration/Layout";
import SoundNotification from "../../components/SoundNotification";

const Application = (props) => {
	const onboardingState = useSelector(accountSelectors.getOnboardingStateMemo);
	const restaurantOnboardingState = useSelector(restaurantSelectors.getOnboardingStateMemo);
	
	const showApplication = restaurantOnboardingState === onboardingStates.REQUESTING_APPLICATION
		|| onboardingState === onboardingStates.REQUESTING_APPLICATION;
	
	return (
		<div id="_app">
			{showApplication && (
				<Layout {...props} />
			)}
			<Notification />
			<SoundNotification />
		</div>
	)
}

export default Application;
