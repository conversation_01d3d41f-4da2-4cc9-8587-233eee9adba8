import React, {useEffect, useMemo, useState} from 'react';
import {useDispatch, useSelector} from "react-redux";
import debounce from 'lodash.debounce';
import { restaurantSelectors, uiSelectors } from "../../../../redux/selectors";
import { ButtonBase, Typography } from "@material-ui/core";
import {Autocomplete} from "@material-ui/lab";
import isEmpty from "../../../utils/isEmpty";
import { searchCRMCompanies, updateCRMSettings } from "../../../api";
import { accountActions, restaurantActions, uiActions } from "../../../../redux/actions";
import Avatar from "@material-ui/core/Avatar";
import axios from "axios";
import Field from "../../../components/form/Field";
import {MainButton} from "../../../components/Buttons";
import useStyles from "./styles";
import LanguageSelector from "../../../components/LanguageSelector";
import FlexDiv from "../../../components/_div/FlexDiv";
import { ArrowLeftGray20, NoTables100 } from "../../../utils/icons";
import typography from "../../../../styles/typography";
import { defaultLogoUrl, onboardingStateForRestaurant, onboardingStates } from "../../../utils/const";
import palette from "../../../../styles/palette";
import { elementIds } from "../../../../redux/elements";
import EmptyScreen from "../../../components/_placeholder/EmptyScreen";
import { controlStringLength } from "../../../utils/sliceString";
import { withTranslation } from "../../../../i18n";

let request = null;

const ConnectToCRM = ({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const restaurantName = useSelector(restaurantSelectors.getRestaurantName)
  const onboarding = useSelector(restaurantSelectors.getOnboardingStateMemo)

  const [query, setQuery] = useState(restaurantName || "");
  const [results, setResults] = React.useState([]);
  const [loadingResults, setLoadingResults] = useState(false);
  const [selectedResultId, setSelectedResultId] = useState(null);
  const { loading } = useSelector((state) => uiSelectors.getState(elementIds["update-crm-btn"], state));

  const goBack = () => {
    dispatch(restaurantActions.setOnboardingState(onboardingStates.CREATING_RESTAURANT, restaurantId))
  }
  
  const updateRestaurantWithCRMResult = () => {
    dispatch(uiActions.setElementState(elementIds["update-crm-btn"], { loading: true }))
    updateCRMSettings(restaurantId, selectedResultId).then(() => {
      const nextState = onboardingStateForRestaurant(onboarding, onboardingStates.CONNECT_RESTAURANT_TO_CRM) || "";
      dispatch(restaurantActions.setOnboardingState(nextState, restaurantId))
    }).catch(() => {
      dispatch(uiActions.setElementState(elementIds["update-crm-btn"], { loading: false }))
    })
  }
  
  const onSearch = (value) => {
    // cancel  previous ajax if exists
    if (request ) {
      request.cancel();
    }
    
    request = axios.CancelToken.source();
    searchCRMCompanies(value, request.token).then(({ data = {} }) => {
      setSelectedResultId(null)
      if (!isEmpty(data?.items)) {
        setResults(data.items)
      } else {
        setResults([])
      }
    })
      .catch((err) => {
        console.log('Error setting gmaps results')
      })
      .finally(() => setLoadingResults(false))
  }
  
  const onSelect = (value) => {
    if (!value) {
      return
    }
  }
  
  const debouncedOnSearch = useMemo(() => debounce(onSearch, 300), []);
  
  useEffect(() => {
    return () => {
      debouncedOnSearch.cancel();
    }
  }, []);

  useEffect(() => {
    setQuery(restaurantName);
    onSearch(restaurantName);
  }, [restaurantName]);
  
  const updateQuery = (value) => {
    setSelectedResultId(null)
    if (!value) {
      setResults([]);
    }
    setQuery(value)
  }
  
  const canSubmit = !isEmpty(selectedResultId)
  
  return (
    <div style={{ height: "100%", position: "relative" }}>
      <div style={{ height: "100%" }}>
        <div className={classes.content}>
          <div style={{ margin: 'auto', maxHeight: '100%', overflow: 'auto', width: "100%" }}>
            <div style={{
              width: 460,
              maxWidth: "calc(100% - 24px)",
              margin: "48px auto"
            }}>
              <Typography style={{ ...typography.large.semiBold, marginBottom: 10 }}>
                {t('connect-to-crm')}
              </Typography>
              <div style={{ marginTop: 32 }}>
                <form>
                  <div>
                    <Autocomplete
                      freeSolo
                      disableClearable
                      id="restaurant-hubspot-search"
                      inputValue={query}
                      open={loadingResults}
                      onOpen={() => {}}
                      onClose={() => {
                        if (!query) {
                          setResults([])
                        }
                      }}
                      loadingText={(
                        <div style={{ paddingTop: 2, paddingBottom: 2 }}>
                          <Typography style={{ color: palette.grayscale["600"] }}>
                            {t('fetching-data')}
                          </Typography>
                        </div>
                      )}
                      renderOption={(option) => {
                      }}
                      onChange={(event, value) => onSelect(value)}
                      loading={loadingResults}
                      disabled={loading}
                      options={[]}
                      getOptionLabel={(option) => option ? option.name : query}
                      renderInput={(params) => {
                        const { InputLabelProps, InputProps, ref, ...rest } = params;
                        return (
                          <Field
                            type="text"
                            placeholder={t('type-to-search-on-hubspot')}
                            label={t('search-hubspot')}
                            onChange={(e) => {
                              const value = e.target.value;
                              updateQuery(value)
                              debouncedOnSearch(value)
                              setLoadingResults(true)
                            }}
                            required
                            {...InputProps}
                            {...rest}
                            refs={ref}
                            value={query}
                          />
                        )
                      }}
                    />
                  </div>
                  {isEmpty(results) ? (
                    <div style={{ marginTop: 32 }}>
                      <EmptyScreen
                        icon={<NoTables100 />}
                        titleI18nKey={"no-matches-found"}
                        descriptionI18nKey={"try-typing-address-hubspot-id-or-tax-number"}
                      />
                    </div>
                  ) : (
                    <div style={{ marginTop: 8 }}>
                      {results.map(result => {
                        const isSelected = selectedResultId && (result?.id === selectedResultId)
                        return (
                          <ButtonBase
                            key={result?.id}
                            style={{
                              display: "block",
                              textAlign: "left",
                              width: "100%",
                              marginBottom: 8,
                              border: `1px solid ${isSelected ? palette.primary["500"] : palette.grayscale["350"]}`,
                              backgroundColor: palette.grayscale["100"],
                              borderRadius: 12,
                              padding: 10,
                              position: "relative",
                            }}
                            onClick={() => setSelectedResultId(result?.id)}
                          >
                            {isSelected && (
                              <div style={{ position: "absolute", top: 8, right: 8 }}>
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                  <rect x="2" y="2" width="16" height="16" rx="8" fill="#FF7C5C" />
                                  <path d="M13.1429 8L8.95239 12L6.85718 10" stroke="#F9F9F9" strokeWidth="1.2"
                                        strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                              </div>
                            )}
                            <div style={{ display: "flex", alignItems: "center" }}>
                              <Avatar
                                alt={"allO-logo"}
                                src={defaultLogoUrl}
                                style={{
                                  width: 32,
                                  height: 32,
                                  fontSize: 12,
                                  lineHeight: "12px",
                                  fontWeight: 500,
                                  textTransform: "uppercase",
                                  borderRadius: 12
                                }}
                                defaultValue="allO"
                              />
                              <Typography
                                style={{ ...typography.body.medium, whiteSpace: "break-spaces", marginLeft: 6 }}>
                                {controlStringLength(result?.name, 50)}
                              </Typography>
                            </div>
                            {!isEmpty(result?.address) && (
                              <div style={{ marginTop: 10 }}>
                                <Typography
                                  style={{ ...typography.extraSmall.medium, color: palette.grayscale["600"] }}>
                                  {t("address")}
                                </Typography>
                                <Typography style={{ ...typography.body.regular }}>
                                  {result?.address?.street}
                                </Typography>
                                <Typography style={{ ...typography.body.regular }}>
                                  {result?.address?.zipCode}
                                </Typography>
                              </div>
                            )}
                          </ButtonBase>
                        )
                      })}
                    </div>
                  )}
                  <div style={{ marginBottom: 24, marginTop: 24, border: `1px dashed ${palette.grayscale.divider}` }} />
                  <Typography style={{ ...typography.body.medium }}>
                    {t("cant-find-what-you-are-looking-for")}
                  </Typography>
                  <a
                    href={"https://app.hubspot.com/contacts/48291892/objects/0-2/views/all/list"}
                    style={{ display: "flex", alignItems: "center", marginTop: 4, textDecoration: "none" }}
                  >
                    <Typography style={{ ...typography.body.medium, marginRight: 2, color: palette.grayscale["600"] }}>
                      {t("create-company-on-hubspot")}
                    </Typography>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10 2H14V6" stroke="#BAB9B8" stroke-width="1.2" strokeLinecap="round"
                            strokeLinejoin="round" />
                      <path d="M6.66663 9.33333L14 2" stroke="#BAB9B8" stroke-width="1.2" strokeLinecap="round"
                            strokeLinejoin="round" />
                      <path
                        d="M12 8.66667V12.6667C12 13.0203 11.8595 13.3594 11.6095 13.6095C11.3594 13.8595 11.0203 14 10.6667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V5.33333C2 4.97971 2.14048 4.64057 2.39052 4.39052C2.64057 4.14048 2.97971 4 3.33333 4H7.33333"
                        stroke="#BAB9B8" stroke-width="1.2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </a>
                  <MainButton
                    color="primary"
                    variant="contained"
                    className={classes.ctaButton}
                    onClick={updateRestaurantWithCRMResult}
                    loading={loading}
                    disabled={!canSubmit}
                    style={{
                      display: "flex",
                      width: "auto",
                      paddingLeft: 24,
                      paddingRight: 24,
                      minWidth: 110,
                      marginTop: 24
                    }}
                  >
                    {t("confirm")}
                  </MainButton>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style={{
        position: "absolute",
        left: 12,
        top: 6
      }}>
        <ButtonBase onClick={goBack} style={{ padding: 6, borderRadius: 12 }} disabled={loading}>
          <FlexDiv>
            <ArrowLeftGray20 />
            <Typography style={{
              ...typography.body.medium,
              marginLeft: 2,
              color: palette.grayscale["600"]
            }}>{t("back")}</Typography>
          </FlexDiv>
        </ButtonBase>
      </div>
      <div style={{
        position: "absolute",
        left: 16,
        bottom: 16
      }}>
        <LanguageSelector disabled={loading} />
      </div>
    </div>
  )
};

export default withTranslation("common")(ConnectToCRM);
