import React, { useEffect, useState } from "react";
import {useDispatch, useSelector} from "react-redux";
import {MainButton} from "../../../components/Buttons";
import {authActions} from "../../../../redux/actions";
import { grantTypes } from "../../../utils/const";
import { authSelectors, uiSelectors } from "../../../../redux/selectors";
import OtpInput from "react-otp-input";
import {withTranslation} from "../../../../i18n";
import useStyles from "./styles";
import { AllOLogo32, AllOLogo64, ArrowLeftGray20} from "../../../utils/icons";
import LanguageSelector from "../../../components/LanguageSelector";
import { ButtonBase, Typography, useMediaQuery } from "@material-ui/core";
import typography from "../../../../styles/typography";
import isEmpty from "../../../utils/isEmpty";
import palette from "../../../../styles/palette";
import { elementIds } from "../../../../redux/elements";
import FlexDiv from "../../../components/_div/FlexDiv";
import Keyboard from "../../../components/Keyboard";
import clsx from "clsx";

let timer = null;

const ValidateOneTimePassword = ({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const [updatedIllustration, setUpdatedIllustration] = useState(false)
  
  useEffect(() => {
    setTimeout(() => setUpdatedIllustration(true), 300)
  }, [])
  
  const isMobile = useMediaQuery('(max-width:700px)');
  const isTablet = useMediaQuery('(max-width:1200px)');
  
  const { grantType } = useSelector(authSelectors.getGrantType);
  const { meta = {} } = useSelector(authSelectors.getMeta);
  let account = meta;
  const { email = "", mobile = "", countryCode = "" } = account;
  const isMobileGrantType = grantType === grantTypes.MOBILE
  const isEmailGrantType = grantType === grantTypes.EMAIL
  
  const { error, errorI18nKey } = useSelector((state) => uiSelectors.getState(elementIds["verification-continue-btn"], state));
  
  const [secondsToResend, setSecondsToResend] = useState(60);
  
  useEffect(() => {
    timer = setInterval(() => {
      if (secondsToResend === 0) {
        clearInterval(timer);
      } else {
        setSecondsToResend(secondsToResend - 1);
      }
    }, 1000);
    
    return () => clearInterval(timer);
  }, [secondsToResend])
  
  const { flowState = {} } = useSelector(authSelectors.getFlowState);
  const { loading } = flowState;
  
  const [otp, setOtp] = useState('');
  
  const canContinue = !!otp && otp.length === 6
  
  const reset = () => setOtp('')
  
  const handleChange = (otp) => {
    setOtp(otp)
  };
  
  const setNumberViaKeyboard = (char) => {
    if (!otp || otp.length < 6) {
      setOtp(`${otp}${char}`)
    }
  }
  
  const deleteNumberViaKeyboard = () => {
    if (otp) {
      setOtp(otp.slice(0, -1))
    }
  }
  
  const setEmailType = () => {
    reset()
    dispatch(authActions.setGrantType(grantTypes.EMAIL))
  }
  
  const authenticate = () => {
    setSecondsToResend(60);
    setOtp('');
    if (!grantType) {
      return;
    }
    if (!email && !(mobile && countryCode)) {
      console.log(" not dispatching")
      return;
    }
    
    if (grantType === grantTypes.MOBILE) {
      dispatch(authActions.loginByMobile(countryCode, mobile));
    }
    if (grantType === grantTypes.EMAIL) {
      console.log("dispatching")
      dispatch(authActions.loginByEmail(email));
    }
  };
  
  const validate = () => {
    if (isMobileGrantType) dispatch(authActions.verifyOneTimePasswordForMobile(otp))
    if (isEmailGrantType) dispatch(authActions.verifyOneTimePasswordForEmail(otp))
  }
  
  const getVerificationCodeInfoByGrantType = () => {
    if (isEmpty(account)) {
      return "";
    }
    
    if (isMobileGrantType) {
      return `+${countryCode}${mobile}`;
    }
    if (isEmailGrantType) {
      return email;
    }
  };
  
  const submit = (event) => {
    event.preventDefault();
    if (!loading) {
      validate();
    }
  }
  
  const virtualKeyboard = false; //isTablet && !isMobile;
  
  return (
    <div style={{ height: "100%", position: "relative" }}>
      <div style={{ height: "100%" }}>
        <div className={classes.content}>
          <div className={clsx(classes.illustration, { [classes.updatedIllustration]: updatedIllustration })}/>
          <div className={classes.formSection}>
            <div style={{ margin: 'auto', maxHeight: '100%', overflow: 'auto', width: "100%" }}>
              <div style={{
                position: "absolute",
                left: 16,
                top: 16
              }}>
                <ButtonBase onClick={setEmailType} style={{ padding: 6 }}>
                  <FlexDiv>
                    <ArrowLeftGray20 />
                    <Typography style={{ ...typography.body.medium, marginLeft: 2, color: palette.grayscale["600"] }}>{t("back")}</Typography>
                  </FlexDiv>
                </ButtonBase>
              </div>
              <div className={classes.form}>
                <div style={{ marginBottom: isTablet ? 16 : 32, display: "flex" }}>
                  {isTablet ? <AllOLogo32 /> : <AllOLogo64 />}
                </div>
                <div style={{ marginBottom: isTablet ? 16 : 32 }}>
                  <Typography style={{ ...typography.large.semiBold, marginBottom: 12 }}>{t("continue-to-allo")}</Typography>
                  <Typography style={{ ...typography.body.regular }}>{t("enter-the-6-digit-code-you-received-at")}</Typography>
                  <Typography style={{ ...typography.body.medium }}>{getVerificationCodeInfoByGrantType()}</Typography>
                </div>
                <form onSubmit={submit}>
                  <Typography style={{ marginBottom: 8, ...typography.body.medium }}>{t("your-6-digit-code")}</Typography>
                  <OtpInput
                    containerStyle={classes.otpFields}
                    inputStyle={classes.otpField}
                    numInputs={6}
                    isDisabled={loading}
                    // hasErrored={hasErrored}
                    errorStyle="error"
                    onChange={virtualKeyboard ? () => {} : handleChange}
                    isInputNum
                    shouldAutoFocus
                    value={otp}
                  />
                  {error && errorI18nKey && (
                    <Typography
                      style={{
                        ...typography.body.regular,
                        color: palette.negative["600"],
                        marginTop: 12,
                      }}
                    >
                      {t(errorI18nKey)}
                    </Typography>
                  )}
                  {virtualKeyboard && (
                    <div style={{ marginBottom: 24 }}>
                      <Keyboard onClick={setNumberViaKeyboard} onDelete={deleteNumberViaKeyboard} specialChar={null} />
                    </div>
                  )}
                  <MainButton color="primary" variant="contained" type="submit" className={classes.ctaButton} onClick={validate} loading={loading} disabled={!canContinue}>{t('common-continue')}</MainButton>
                </form>
                <div style={{ marginTop: 24, display: "flex", justifyContent: "center" }}>
                  <ButtonBase
                    style={{ padding: 6 }}
                    disabled={!!secondsToResend || !!loading}
                    onClick={authenticate}
                  >
                    <Typography style={{ ...typography.body.medium, color: !secondsToResend ? palette.primary["500"] : palette.grayscale["600"] }}>{secondsToResend ? `${t("resend-code")} 0:${secondsToResend}` : t("resend-code")}</Typography>
                  </ButtonBase>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style={{
        position: "absolute",
        left: 16,
        top: isMobile ? null : 16,
        bottom: isMobile ? 16 : null
      }}>
        <LanguageSelector />
      </div>
    </div>
  )
};

export default withTranslation('common')(ValidateOneTimePassword);
