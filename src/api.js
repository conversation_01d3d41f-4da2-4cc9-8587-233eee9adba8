import axios from "axios";
import getConfig from 'next/config';
import { billingPlanPeriod, grantTypes, noop } from "./utils/const";
import isEmpty from "./utils/isEmpty";
import { operationViews, orderTypes } from "../redux/constants";

const clientId = "gluttony-api";

const { publicRuntimeConfig } = getConfig();
const isProd = true;
const restaurantServiceBasePath = isProd ? publicRuntimeConfig.API_URL : 'http://localhost:8090';
const orderServiceBasePath = isProd ? publicRuntimeConfig.API_URL : 'http://localhost:8080';
const marketingServiceBasePath = isProd ? publicRuntimeConfig.API_URL : 'http://localhost:8091';
const userServiceBasePath = isProd ? publicRuntimeConfig.API_URL : 'http://localhost:8091';

const clientSecret = publicRuntimeConfig.CLIENT_SECRET;

const path = `${publicRuntimeConfig.API_URL}/gluttony-api/api`;
const gluttonyService = `${publicRuntimeConfig.API_URL}/gluttony-api`;
const uiService = `${publicRuntimeConfig.API_URL}/ui-service`;
const restaurantService = `${restaurantServiceBasePath}/restaurant-api`;
const reservationService = `${publicRuntimeConfig.API_URL}/reservation-service`;
const orderService = `${orderServiceBasePath}/gluttony-api/api`;
const marketingService = `${marketingServiceBasePath}/marketing-service`;
const marketplaceService = `${marketingServiceBasePath}/marketplace-service`;
const userService = `${userServiceBasePath}/user-service`;
const fiskalizationService = `${userServiceBasePath}/fiskal-service`;
const secured = `${path}/s`;

export const log = req =>
  axios.post('/log', { message: req }, {}).then(noop).catch(noop);

export const logAsync = req =>
  axios.post('/log', { message: req }, {})

export const getRestaurantById = id =>
  axios.get(`${restaurantService}/restaurants/${id}`, {});

export const getRestaurantConfiguration = id =>
  axios.get(`${restaurantService}/restaurants/${id}/configuration`, {});

export const createRestaurantConfiguration = (id, configuration) =>
  axios.post(`${restaurantService}/restaurants/${id}/configuration`, configuration, {});

export const updateRestaurantConfigurationById = (id, configuration) =>
  axios.put(`${restaurantService}/restaurants/${id}/configuration`, configuration, {});

export const patchRestaurantConfigurationById = (restaurantId, configuration) =>
  axios.patch(`${restaurantService}/restaurants/${restaurantId}/configuration`, configuration, {});

export const updateBranchRestaurants = (restaurantId, branchRestaurantIds) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/branch-restaurants`, { branchRestaurantIds }, {})

export const reserve = tableCode =>
  axios.post(`${secured}/tables/_reserve`, { tableCode }, {});

export const reserveAsWaiter = tableId =>
  axios.post(`${secured}/administration/tables/${tableId}/reserve`, {}, {});

export const getPaymentState = (restaurantId, orderId) =>
  axios.get(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/payment-state`, {});

export const approveOrderParticipant = (orderId, participantId) =>
  axios.post(`${path}/orders/${orderId}/participants/${participantId}/approve`, {});

export const promoteParticipant = (orderId, participantId) =>
  axios.post(
    `${path}/orders/${orderId}/participants/${participantId}/promote`,
    {}
  );

export const clearOrder = orderId =>
  axios.post(
    `${secured}/administration/orders/${orderId}/clear`,
    {}
  );

export const clearOrderItem = (orderId, orderItemId) =>
  axios.post(
    `${secured}/administration/orders/${orderId}/items/${orderItemId}/clear`,
    {}
  );

export const cancelOrderItem = (orderId, orderItemId, qtd = 1, notes, selectedNotes) =>
  axios.post(
    `${secured}/administration/orders/${orderId}/items/${orderItemId}/cancel`,
    {
      qtd, notes, selectedNotes
    }
  );

export const confirmOrderItems = orderId => axios.post(`${secured}/orders/${orderId}/items/confirm`, {}, {});

export const confirmOfflineOrderItems = (orderId, courseNumber) => axios.post(`${secured}/administration/orders/${orderId}/items/confirm`, { courseNumber }, {});

export const printOrderItem = (orderId, itemId) => axios.post(`${secured}/administration/orders/${orderId}/items/${itemId}/print`, {}, {});

export const printPayment = (orderId, paymentId, refundable = false) => axios.post(`${secured}/administration/orders/${orderId}/payments/${paymentId}/print?refundable=${refundable}`, {}, {});

export const printTerminalPaymentReceipt = (orderId, paymentId, refundable = false) => axios.post(`${gluttonyService}/api/orders/${orderId}/payments/${paymentId}/_printTerminalPaymentReceipt`, {}, {});

export const confirmPickupOrderItems = orderId => axios.post(`${secured}/administration/orders/${orderId}/pickup/items/confirm`, {}, {});

export const acceptOrder = (restaurantId, orderId, estimatedPreparationTime) => axios.post(`${gluttonyService}/restaurants/${restaurantId}/_accept-order`, { orderId, estimatedPreparationTime }, {});

export const payOrderItemsByWaiter = (orderId, customerId, paymentChannel, paymentOption, amount, tipOption, tipAmount, selectedOrderItems, cardId, terminalId, receiptOption, splitOptionNumberOfParts, splitOptionPayingParts, paymentDiscountId, openInvoiceData) => {
  return axios.patch(
    `${secured}/administration/orders/${orderId}/payment/${customerId}/confirm`,
    {
      channel: paymentChannel,
      option: paymentOption,
      amount,
      tipOption,
      tipAmount,
      selectedOrderItems,
      cardId,
      terminalId,
      receiptOption,
      splitOptionNumberOfParts,
      splitOptionPayingParts,
      paymentDiscountId,
      openInvoice: openInvoiceData
    },
    {}
  )
};

export const getPaymentPartsForSplitPayment = (restaurantId, orderId, numberOfParts) =>
  axios.get(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/payment-parts`, { params: { numberOfParts } });

export const getPaymentsReport = restaurantId =>
  axios.get(`${secured}/manage/restaurant/${restaurantId}/payments/report`, {});

export const getPaymentsReportForUser = restaurantId =>
  axios.get(`${secured}/manage/restaurant/${restaurantId}/payments/report/user`, {});

export const getPaymentsReportForAllUsers = restaurantId =>
  axios.get(`${secured}/manage/restaurant/${restaurantId}/payments/report/users`, {});

export const getTerminalMenus = (restaurantId, orderType, partnerId) =>
  axios.get(`${restaurantService}/business/restaurants/${restaurantId}/restaurant/menus`, { params: { orderType, partnerId } });

export const getVersionedTerminalMenus = (restaurantId, orderType, version, partnerId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/versioned-menus`, { params: { orderType, partnerId, version } });

export const getTerminalMenuItemGroups = (restaurantId, orderType, partnerId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/menu-item-groups`, { params: { orderType, partnerId } });

export const getTables = (restaurantId, include) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/tables?sortBy=LABEL`, { params: {include} });

export const getOrdersAndCarts = (restaurantId, params) =>
  axios.get(`${secured}/restaurants/${restaurantId}/tables/ordersAndCarts`, { params });

export const getHistoricalOrders = (restaurantId, cancelToken, dateFrom, dateTo, filters, sorting, hideCancelledOrdersWithoutPayments = false ) => {
  const preparedFilters = filters ? `${filters.orderTypes ? '&' : ''}${(filters.orderTypes || []).map((n, index) => `selectedOrderTypes=${n}`).join('&')}${filters.accountIds ? '&' : ''}${(filters.accountIds || []).map((n, index) => `selectedAccountIds=${n}`).join('&')}${filters.tableIds ? '&' : ''}${(filters.tableIds || []).map((n, index) => `tableIds=${n}`).join('&')}` : '';
  const preparedSorting = sorting && sorting.sortField && sorting.sortDirection ? `&sortField=${sorting.sortField}&sortDirection=${sorting.sortDirection}` : '';
  return axios.get(`${secured}/restaurants/${restaurantId}/tables/ordersAndCarts/history?hideCancelledOrdersWithoutPayments=${hideCancelledOrdersWithoutPayments}&${dateFrom ? `dateFrom=${dateFrom}${dateTo ? `&dateTo=${dateTo}` : ''}` : ''}${preparedFilters}${preparedSorting}`, { cancelToken } );
}

export const getGroupedOrder = (restaurantId, orderId, groupBy = 'NONE', paymentGroupBy = "NONE", includeMovedItems = false) =>
  axios.get(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/grouped`, { params: { groupBy, paymentGroupBy, includeMovedItems } });

export const getTable = (restaurantId, tableId) =>
  // axios.get(`${restaurantService}/tables/${tableId}`, {});
  axios.post(
    `${uiService}/graphql`,
    {
      query: `
        query {
          getTable(request: {
            restaurantId: "${restaurantId}",
            tableId: "${tableId}",
          }) {
            id
            code
            label
            floorId
            orderId
            status
            minimumCapacity
            capacity
            reservation {
              id
              status
              startsInMinutes
              startTime
              endTime
              formattedStartLocalTime
              formattedEndLocalTime
              formattedStartEndLocalTime
              people
              customers {
                id
                fullName
              }
            }
            unconnectedReservation {
              id
              status
              startsInMinutes
              startTime
              endTime
              formattedStartLocalTime
              formattedEndLocalTime
              formattedStartEndLocalTime
              people
              customers {
                id
                fullName
              }
            }
          }
        }
      `,
    },
    {}
)

export const updateLayout = layout =>
  axios.post(`${restaurantService}/tables/_update-layout`, { layout }, {});

export const addItemToOrder = (orderId, code, qtd = 1) =>
  axios.post(`${secured}/orders/${orderId}/items`,  {
    "code": code,
    "qtd": qtd
  }, {});

export const orderItemOffline = (orderId, itemRequest) =>
  axios.post(`${secured}/administration/orders/${orderId}/items`,  itemRequest, {});

export const orderOngoingItemOffline = (orderId, itemRequest) =>
  axios.post(`${secured}/administration/orders/${orderId}/items/ongoing`,  itemRequest, {});

export const getPayments = (orderId) =>
  axios.get(`${secured}/orders/${orderId}/payments`, {});

export const subscribe = (channelId) =>
  axios.post(`${secured}/events/_subscribe`, {}, {});

export const getEvents = (orderId, timestamp) =>
  axios.get(`${secured}/events?channelId=${orderId}&timestamp=${timestamp}`, {});

export const getReports = (restaurantId, limit = 10, offset = 0) =>
  axios.get(`${secured}/restaurants/${restaurantId}/reports`, { params: { limit, offset } });

export const getReportsAsOptions = (restaurantId) =>
  axios.get(`${secured}/restaurants/${restaurantId}/reports/options`, {});

export const createReport = restaurantId =>
  axios.post(`${secured}/restaurants/${restaurantId}/reports`, {}, {});

export const printReport = (restaurantId, reportId = null, targetAccountId) =>
  axios.get(`${secured}/restaurants/${restaurantId}/reports/${reportId ? `${reportId}/print`: 'print'}${targetAccountId ? `?targetAccountId=${targetAccountId}` : ''}`, {});

export const printWaiterReport = (restaurantId, reportId = null) =>
  axios.get(`${secured}/restaurants/${restaurantId}/my-report/${reportId ? `${reportId}/print`: 'print'}`, {});

export const regenerateReport = (restaurantId, reportId = null) =>
  axios.get(`${secured}/restaurants/${restaurantId}/reports/${reportId}/regenerate`, {});

export const closeReport = (restaurantId, closingDateTime) =>
  axios.post(`${secured}/restaurants/${restaurantId}/reports/close`, {
    closingDateTime
  }, {});

export const emailReport = (restaurantId, reportId = null, sendReportEmail = null, targetAccountId) =>
  axios.post(`${gluttonyService}/restaurants/${restaurantId}/reports/_trigger-daily-report-email`, {
    reportId,
    sendReportEmail,
    targetAccountId
  }, {});

export const emailMonthlyReport = (restaurantId, yearMonth = null, sendReportEmail = null) =>
  axios.post(`${gluttonyService}/restaurants/${restaurantId}/reports/_trigger-monthly-report-email`, {
    yearMonth,
    sendReportEmail
  }, {});

/**
 * DSFin-Vk and Fiskaly
 */

export const getFiskalizationConfig = (restaurantId) =>
  axios.get(`${fiskalizationService}/fiskal-configs?restaurantId=${restaurantId}`,
    {}
  );

export const createTSEConfiguration = (restaurantId, restaurantName, apiKey, apiSecret) =>
  axios.post(
    `${fiskalizationService}/restaurants/${restaurantId}/fiskalization-plugin`,
    {
      type: "FISKALY_KSV_DE_V2",
      description: restaurantName,
      apiKey: apiKey,
      apiSecret: apiSecret
    }
  );

export const setupTSE = (restaurantId) =>
  axios.post(
    `${fiskalizationService}/restaurants/${restaurantId}/setup-fiskaly`,
  );

export const generateFiskalDailyClosing = (restaurantId, reportId) =>
  axios.post(`${fiskalizationService}/restaurants/${restaurantId}/cash-point-closings`,
    {
      reportId
    },
    {}
  );

export const deleteFiskalDailyClosing = (restaurantId, reportId) =>
  axios.delete(`${fiskalizationService}/restaurants/${restaurantId}/cash-point-closings/reports/${reportId}`,
    {}
  );

export const getCashPointClosings = (restaurantId, limit = 100, offset = 0 ) => axios.get(
  `${fiskalizationService}/restaurants/${restaurantId}/cash-point-closings`,
  {
    params: {
      limit,
      offset,
    }
  }
);
//  sortBy=pageNumber&direction=desc for later - not working rn

export const getCashPointClosingsFiskaly = (restaurantId, limit = 100, offset = 0 ) => axios.get(
  `${fiskalizationService}/fiskaly-data/restaurants/${restaurantId}/cash-point-closings`,
  {
    params: {
      limit,
      offset,
    }
  }
);

export const createDsfinVkExport = (restaurantId, businessStartDate, businessEndDate, isFullReport = false, createNewIfAlreadyExists = false ) => axios.post(
  `${fiskalizationService}/restaurants/${restaurantId}/dsfinvk-exports/by-business-date`,
  {
    businessStartDate,
    businessEndDate,
    isFullReport,
    createNewIfAlreadyExists
  },
  {}
);

export const getDsfinVkExports = (restaurantId, offset = 0, limit = 100) => axios.get(
  `${fiskalizationService}/restaurants/${restaurantId}/dsfinvk-exports`,
  {
    params: {
      offset,
      limit
    }
  }
);

export const createFullDsfinVkExport = (restaurantId, createNewIfAlreadyExists = false ) => axios.post(
  `${fiskalizationService}/restaurants/${restaurantId}/dsfinvk-exports/full-export`,
  {
    createNewIfAlreadyExists
  },
  {}
);

export const getTseExports = (restaurantId) => axios.get(
  `${fiskalizationService}/restaurants/${restaurantId}/tse-exports`,
  {}
);

export const createTseExport = (restaurantId, startDate, endDate, createNewIfAlreadyExists = false) => axios.post(
  `${fiskalizationService}/restaurants/${restaurantId}/tse-exports/by-business-date`,
  {
    startDate,
    endDate,
    createNewIfAlreadyExists
  },
  {}
);

export const createFullTseExport = (restaurantId, createNewIfAlreadyExists = false) => axios.post(
  `${fiskalizationService}/restaurants/${restaurantId}/tse-exports/full-export`,
  {
    createNewIfAlreadyExists
  },
  {}
);

export const downloadTseExport = (restaurantId, exportId) => {
  return axios
    .get( `${fiskalizationService}/restaurants/${restaurantId}/tse-exports/${exportId}/download`, { responseType: 'blob' });
};

export const downloadTseExportFiskaly = (restaurantId, tseExportId) => {
  return axios
    .get( `${fiskalizationService}/restaurants/${restaurantId}/tse-exports/by-fiskaly-export-id/${tseExportId}/download`, { responseType: 'blob' });
};

export const downloadDsfinVkExport = (restaurantId, exportId) => {
  return axios
    .get( `${fiskalizationService}/restaurants/${restaurantId}/dsfinvk-exports/${exportId}/download`, { responseType: 'blob' });
};

export const downloadDsfinVkExportFiskaly = (restaurantId, exportId) => axios.get(
  `${fiskalizationService}/restaurants/${restaurantId}/dsfinvk-exports/by-fiskaly-export-id/${exportId}/download`,
  {}
);

export const deleteAllFiskalyRequests = (restaurantId ) => axios.delete(
  `${fiskalizationService}/restaurants/${restaurantId}/dsfinvk-export-requests/all`,
  {}
);

export const getKasseRegistrationInfo = (restaurantId) => axios.get(
  `${fiskalizationService}/restaurants/${restaurantId}/kasse-registration-info`,
  {}
);

/**
 * printOrder
 */

export const printOrder = (orderId, refundable = false, customerId = null) =>
  axios.get(`${secured}/administration/orders/${orderId}/print?refundable=${refundable}`, { params: { customerId } });

/**
 * Waiter pickup order flow
 */

export const createPickupOrderAsWaiter = (restaurantId, orderDetails) =>
  axios.post(`${secured}/administration/orders/pickup`, { restaurantId, ...orderDetails }, {});

export const updatePickupOrderAsWaiter = (id, orderDetails) =>
  axios.put(`${secured}/administration/orders/pickup/${id}`, { ...orderDetails }, {});

export const deletePickupOrderAsWaiter = (id) =>
  axios.delete(`${secured}/administration/orders/pickup/${id}`, {});

/**
 * Customer management
 */

export const resolveCustomers = (ids) =>
  axios.get(`${orderServiceBasePath}/gluttony-api/customers/?${ids.map(i => `ids=${i}`).join('&')}`, {});

export const getCustomers = (id, limit = 10, offset = 0, query, cancelToken) =>
  axios.get(`${secured}/administration/restaurants/${id}/customers`, { cancelToken, params: { limit, offset, query } });

/**
 *  Floors
 */

export const resolveFloors = (id) =>
  axios.get(`${restaurantService}/business/restaurants/${id}/floors`, {});

export const resolveVersionedFloors = (id, version) =>
  axios.get(`${restaurantService}/restaurants/${id}/versioned-floors`, { params: { version } });

export const createFloor = (id, floor) =>
  axios.post(`${restaurantService}/restaurants/${id}/floors`, { ...floor }, {});

export const updateFloor = (id, floor) =>
  axios.put(`${restaurantService}/floors/${floor.id}`, { ...floor }, {});

export const deleteFloor = (restaurantId, id) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/floors/${id}`, {});

/**
 *  Tables
 */

export const getTableWithQrCode = (id) =>
  axios.get(`${restaurantService}/tables/${id}?includeDynamicQrCode=true`, {});

export const createTable = (id, table) =>
  axios.post(`${restaurantService}/restaurants/${id}/tables`, { ...table }, {});

export const createTablesBulk = (id, table) =>
  axios.post(`${restaurantService}/restaurants/${id}/_tables-bulk`, { ...table }, {});
export const updateTable = (id, table) =>
  axios.post(`${restaurantService}/tables/${table.id}`, { ...table }, {});

export const deleteTable = (id) =>
  axios.delete(`${restaurantService}/tables/${id}`, {});

export const getTableByCode = (id, code) =>
  axios.get(`${restaurantService}/restaurants/${id}/tables/code/${code}`, {})

export const getTableByCodeOrLabel = (id, codeOrLabel) =>
  axios.get(`${restaurantService}/restaurants/${id}/tables/_search`, { params: { codeOrLabel } })

/**
 * Menu Editor and Management
 */

export const resolveMenuGroups = (id) =>
  axios.get(`${restaurantService}/restaurants/${id}/menu-groups`);

export const createMenuGroup = (id, data) =>
  axios.post(`${restaurantService}/restaurants/${id}/menu-groups`, { ...data });

export const editMenuGroup = (id, data) =>
  axios.put(`${restaurantService}/restaurants/${id}/menu-groups/${data.id}`, { ...data }, {});

export const deleteMenuGroup = (restaurantId, groupId) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/menu-groups/${groupId}`)

export const resolveMenus = (id) =>
  axios.get(`${restaurantService}/restaurants/${id}/menus?excludeOneTimeMenu=true`, {});

export const createMenu = (id, menu) =>
  axios.post(`${restaurantService}/restaurants/${id}/menus`, { ...menu }, {});

export const updateMenu = (id, menu) =>
  axios.put(`${restaurantService}/restaurants/${id}/menus/${menu.id}`, { ...menu }, {});

export const deleteMenu = (id,menu) =>
  axios.delete(`${restaurantService}/restaurants/${id}/menus/${menu.id}`)

export const createMenuItem = (id, item) =>
  axios.post(`${restaurantService}/restaurants/${id}/menus/items`, { ...item }, {});

export const updateMenuItem = (id, item) =>
  axios.put(`${restaurantService}/restaurants/${id}/menus/${item.menuId}/items/${item.id}`, { ...item }, {});

export const deleteMenuItem = (id, item) =>
  axios.delete(`${restaurantService}/restaurants/${id}/menu-items/${item.id}`);

export const getMenuItemByCode = (code) =>
  axios.get(`${restaurantService}/restaurants/menus/items/code/${code}`, {});

export const searchMenuItems = (id, filter) =>
  axios.post(`${restaurantService}/restaurants/${id}/menus/items/search`, { ...filter });

export const searchMenuItemByNumeration = (id, numeration) =>
  axios.get(`${restaurantService}/restaurants/${id}/menus/items/numeration/${numeration}`, {});

export const bulkEditMenuItems = (id, payload) =>
  axios.patch(`${restaurantService}/restaurants/${id}/menu-items/_bulk`, payload, {})

export const bulkExtraItems = (id, payload) =>
  axios.patch(`${restaurantService}/restaurants/${id}/extra-items/_bulk`, payload, {})

export const bulkOptionItems = (id, payload) =>
  axios.patch(`${restaurantService}/restaurants/${id}/option-items/_bulk`, payload, {})

export const bulkDeleteMenuItems = (restaurantId, ids = [] ) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/menu-items/_bulk`,
    { data: {
      ids
    }
  }
)

export const uploadMenuImage = (restaurantCode, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/file-upload/${restaurantCode}/menu/gallery`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const exportMenuItems = (restaurantId) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/menus/items/csv`, {}, {});

export const getDATEVExporterConfiguration = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/datev-exporter-configurations`, {});

export const updateDATEVExporterConfiguration = (restaurantId, configuration) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/datev-exporter-configurations`, { ...configuration }, {});

export const getKontoConfigurations = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/konto-configurations`, {});

export const updateKontoConfiguration = (restaurantId, configuration) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/konto-configurations`, { ...configuration }, {});

export const deleteKontoConfiguration = (restaurantId, configurationId) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/konto-configurations/${configurationId}`, {});

export const exportDATEV = (restaurantId, cashRegisterId, from, to) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/cashRegisters/${cashRegisterId}/export/datev/csv`, { from, to }, {});

export const exportDATEVWithAttachmentOption = (restaurantId, cashRegisterId, from, to, emailTo, withAttachments = true) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/cashRegisters/${cashRegisterId}/export/datev/email`, { from, to, emailTo, withAttachments }, {});

export const resolveExtras = (id) =>
  axios.get(`${restaurantService}/business/restaurants/${id}/extras`, {});

export const createExtra = (id, extra) =>
  axios.post(`${restaurantService}/restaurants/${id}/extras`, { ...extra }, {});

export const updateExtra = (id, extra) =>
  axios.put(`${restaurantService}/restaurants/${id}/extras/${extra.id}`, { ...extra }, {});

export const deleteExtra = (id, extra) =>
  axios.delete(`${restaurantService}/restaurants/${id}/extras/${extra.id}`);

export const createExtraItem = (id, item) =>
  axios.post(`${restaurantService}/restaurants/${id}/extras/items`, { ...item }, {});

export const updateExtraItem = (id, item) =>
  axios.put(`${restaurantService}/restaurants/${id}/extras/${item.extraId}/items/${item.id}`, { ...item }, {});

export const deleteExtraItem = (id, item) =>
  axios.delete(`${restaurantService}/restaurants/${id}/extras/${item.extraId}/items/${item.id}`);

export const resolveOptions = (id) =>
  axios.get(`${restaurantService}/business/restaurants/${id}/options`, {});

export const createOption = (id, option) =>
  axios.post(`${restaurantService}/restaurants/${id}/options`, { ...option }, {});

export const updateOption = (id, option) =>
  axios.put(`${restaurantService}/restaurants/${id}/options/${option.id}`, { ...option }, {});

export const deleteOption = (id, option) =>
  axios.delete(`${restaurantService}/restaurants/${id}/options/${option.id}`);

export const createOptionItem = (id, item) =>
  axios.post(`${restaurantService}/restaurants/${id}/options/items`, { ...item }, {});

export const updateOptionItem = (id, item) =>
  axios.put(`${restaurantService}/restaurants/${id}/options/${item.optionId}/items/${item.id}`, { ...item }, {});

export const deleteOptionItem = (id, item) =>
  axios.delete(`${restaurantService}/restaurants/${id}/options/${item.optionId}/items/${item.id}`);

export const resolveRemarks = (id) =>
  axios.get(`${restaurantService}/restaurants/${id}/remarks`, {});

export const createRemark = (id, remark) =>
  axios.post(`${restaurantService}/restaurants/${id}/remarks`, { ...remark }, {});

export const updateRemark = (id, remark) =>
  axios.put(`${restaurantService}/restaurants/${id}/remarks/${remark.id}`, { ...remark }, {});

export const deleteRemark = (restaurantId, remarkId) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/remarks/${remarkId}`);

export const resolveNotes = (id) =>
  axios.get(`${restaurantService}/notes/restaurants/${id}`, {});

export const createNote = (note) =>
  axios.post(`${restaurantService}/notes`, { ...note }, {});

export const updateNote = (note) =>
  axios.put(`${restaurantService}/notes/${note.id}`, { ...note }, {});

export const deleteNote = (restaurantId, noteId) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/notes/${noteId}`);

export const getTags = () =>
  axios.get(`${restaurantService}/tags`, {});

// Order modes for tablet ordering - menu editor
export const resolveOrderModes = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/ordering-modes`, {});

export const updateOderMode = (mode) =>
  axios.put(`${restaurantService}/restaurants/${mode.restaurantId}/ordering-modes/${mode.id}`, { ...mode }, {});

export const createOrderMode = (mode) =>
  axios.post(`${restaurantService}/restaurants/${mode.restaurantId}/ordering-modes`, { ...mode }, {});

export const deleteOrderMode = (restaurantId, modeId) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/ordering-modes/${modeId}`);

// tags for ordering modes
export const getOrderModeTags = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/tags`, {});

export const createRestaurantTag = (restaurantId, tag) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/tags`, { ...tag }, {});

export const deleteRestaurantTag = (restaurantId, tagId) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/tags/${tagId}`);

export const updateRestaurantTag = (restaurantId, tag) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/tags/${tag.id}`, { ...tag }, {});

/**
 * Monthly reports
 */

export const getMonthlyReports = (id, year = '2020') =>
  axios.get(`${path}/restaurants/${id}/monthlyReports/${year}`, {});

export const printMonthlyReport = (id, year, month) =>
  axios.post(`${path}/restaurants/${id}/monthlyReports/${year}/${month}/print`, {}, {});

export const getItemsReport = (id, month = 1, year = '2020') =>
  axios.get(`${secured}/manage/restaurant/${id}/items/report`, { params: { year, month }});

/**
 * Cash Register
 */

export const resolveCashRegister = (id) =>
  axios.get(`${restaurantService}/business/restaurants/${id}/cashRegister`, {});

export const createCashRegister = (id, cashRegister) =>
  axios.post(`${restaurantService}/restaurants/${id}/cashRegisters`, { ...cashRegister }, {});

export const resolveRecords = (id) =>
  axios.get(`${restaurantService}/cashRegisters/${id}/records`, {});

export const searchRecords = (id, startDate, endDate) =>
  axios.get(`${restaurantService}/cashRegisters/${id}/records/search?${startDate ? `startDate=${startDate}${endDate ? `&endDate=${endDate}` : ''}` : ''}` , {});

export const searchRecordsForReport = (id, startDate, endDate) =>
  axios.get(`${restaurantService}/cashRegisters/${id}/records/search/dailyClosing?${startDate ? `startDate=${startDate}${endDate ? `&endDate=${endDate}` : ''}` : ''}` , {});

export const createRecord = (id, record) =>
  axios.post(`${restaurantService}/cashRegisters/${id}/records`, { ...record }, {});

export const updateRecord = (id, record) =>
  axios.post(`${restaurantService}/cashRegisters/${id}/records/${record.id}`, { ...record }, {});

export const uploadRecordDocument = (restaurantCode, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/file-upload/${restaurantCode}/doc/journal`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const updateCashRegisterRecord = (id, documentId, recordId, documentUrl ) => (
   axios.put(`${restaurantService}/cashRegisters/${id}/records/${recordId}`, {
     documentId,
     documentUrl
    }, {})
)

/**
 * Printer Management
 */

export const resolvePrinters = (id, withStatus = false) =>
  axios.get(`${orderService}/restaurants/${id}/printers`, {
    params: {
      withStatus: withStatus
    }
  });

export const createPrinter = (id, printer) =>
  axios.post(`${orderService}/restaurants/${id}/printers`, { ...printer }, {});

export const updatePrinter = (id, printer) =>
  axios.put(`${orderService}/restaurants/${id}/printers/${printer.id}`, { ...printer }, {});

export const deletePrinter = (id, printer) =>
  axios.delete(`${gluttonyService}/restaurants/${id}/printers/${printer.id}`, {});

/**
 * Terminal Management
 */

export const getPaymentTerminals = (restaurantId, online = true, readerId) =>
  axios.get(`${gluttonyService}/restaurants/${restaurantId}/terminals?online=${online}${readerId ? `&readerId=${readerId}` : ''}`, {});

export const createPaymentTerminal = (id, device) =>
  axios.post(`${gluttonyService}/restaurants/${id}/terminals`, { ...device }, {});

export const updatePaymentTerminal = (id, device) =>
  axios.put(`${gluttonyService}/restaurants/${id}/terminals/${device.id}`, { ...device }, {});

export const deletePaymentTerminal = (id, device) =>
  axios.delete(`${gluttonyService}/restaurants/${id}/terminals/${device.id}`,  {});

export const getOpenTerminals = (readerId) => {
  return axios.get(`${gluttonyService}/open-terminals`, { params: { readerId } });
}

/**
 * Terminal Charges (S700)
 */

export const getS700Charges = (restaurantId, date, readerIds, offset, limit) =>
  axios.get(`${gluttonyService}/restaurants/${restaurantId}/terminal-payments?date=${date}&readerIds=${readerIds}&offset=${offset}&limit=${limit}`, {});
// multiple readerIds separated by comma possible
/**
 * Loyalty Card Management
 */

export const resolveCards = (id, limit = 10, offset = 0, codeOrExternal, sorting) => {
  const preparedSorting = sorting && sorting.sortField && sorting.sortDirection ? `sortField=${sorting.sortField}&sortDirection=${sorting.sortDirection}` : '';
  return axios.get(`${marketingService}/cards?${preparedSorting}`, { params: { restaurantId: id, limit, offset, codeOrExternal } });
}

export const resolveCardTransactions = (id, limit = 10, offset = 0, filters) => {
  return axios.get(`${marketingService}/restaurants/${id}/card-transactions?offset=${offset}&limit=${limit}${filters}`);
}

export const getCardTransactions = (id, limit = 10, offset = 0, dateFrom, dateTo, filters ) => {
  return axios.get(`${marketingService}/restaurants/${id}/card-transactions-extensions?offset=${offset}&limit=${limit}&dateFrom=${dateFrom}&dateTo=${dateTo}${filters}`,
    {}
  );
}

export const resolveCard = (restaurantId, code) =>
  axios.get(`${marketingService}/cards/card/_resolve`, { params: { restaurantId: restaurantId, code, externalCode: code } });

export const createCard = (card) =>
  axios.post(`${marketingService}/cards`, { ...card }, {});

export const createCardBulk = (card, count) =>
  axios.post(`${marketingService}/cards/_bulk`,  {
    card: {
      ...card
    },
    count
  }, {});

export const deleteGiftCard = (restaurantId, cardId) =>
  axios.delete(`${marketingService}/restaurants/${restaurantId}/cards/${cardId}`, {});

export const sendGiftCardReceiptEmail = (restaurantId, cardId, email, transactionId) =>
  axios.post(`${marketingService}/restaurants/${restaurantId}/card-transactions-email`,{ email, transactionId, cardId}, {})

export const createOneTimeCard = (restaurantId, card) =>
  axios.post(`${marketingService}/restaurants/${restaurantId}/third-party-cards`, { ...card }, {});

//changed to deleteGiftCard
/*export const deleteOneTimeCard = (restaurantId, cardId) =>
  axios.delete(`${marketingService}/restaurants/${restaurantId}/third-party-cards/${cardId}`, {});*/

export const printCard = (id) =>
  axios.post(`${marketingService}/cards/${id}/_print`, {}, {});

export const updateCard = (card) =>
  axios.put(`${marketingService}/cards/${card.id}`, { ...card }, {});

export const createCardTransaction = (transaction) =>
  axios.post(`${marketingService}/card-transactions`, { ...transaction }, {});

export const getCardsReport = (id) =>
  axios.get(`${marketingService}/reports/cards`, { params: { restaurantId: id } });

export const getCardTransactionsReport = (cardCode) =>
  axios.get(`${marketingService}/card-transactions/_transaction-report?cardCode=${cardCode}&transactionTypes=CASH_IN&transactionTypes=CASH_OUT&transactionTypes=ADJUSTMENT`, {})

export const createCardPayment = (restaurantId, cardId, terminalId, amount, extraPromotionAmount) =>
  axios.post(`${gluttonyService}/restaurants/${restaurantId}/gift-card-payments`, {
    cardId,
    terminalId,
    amount,
    extraPromotionAmount
  }, {})

export const cancelCardPaymentRequest = (restaurantId, cardId) =>
  axios.delete(`${gluttonyService}/restaurants/${restaurantId}/gift-cards/${cardId}/_current-transaction`, {})

/**
 * Promotion Management
 */

export const resolvePromotions = (id) =>
  axios.get(`${orderService}/restaurants/${id}/promotions`, {});

export const createPromotion = (id, promotion) =>
  axios.post(`${orderService}/promotions`, { ...promotion }, {});

export const updatePromotion = (id, promotion) =>
  axios.put(`${orderService}/promotions/${promotion.id}`, { ...promotion }, {});

/**
 * Discount Management
 */

export const resolveDiscounts = (id) =>
  axios.get(`${orderService}/restaurants/${id}/discounts`, {});

/**
 * Discount/Promotion Reporting
 */

export const getPromotionsAndDiscounts = (id, offset = 0, limit = 100, startDate, endDate, filters, sorting ) => {
  const preparedSorting = sorting && sorting.sortField && sorting.sortDirection ? `sortField=${sorting.sortField}&sortDirection=${sorting.sortDirection}` : 'sortField=CREATION_TIME&sortDirection=DESC';
  const preparedFilters = filters ?
    `${filters.type ? '&' : ''}${(filters.type || []).map(n => `type=${encodeURIComponent(n)}`).join('&')}` +
    `${filters.promoCode ? '&' : ''}${(filters.promoCode || []).map(n => `promoCode=${encodeURIComponent(n)}`).join('&')}` +
    `${filters.menuItemCode ? '&' : ''}${(filters.menuItemCode || []).map(n => `menuItemCode=${encodeURIComponent(n)}`).join('&')}`
    : '';
  return axios.get(`${gluttonyService}/restaurants/${id}/discounts?offset=${offset}&limit=${limit}&${preparedSorting}&${preparedFilters}`, { params: { startDate, endDate }} );
}

/**
 * Restaurant Management
 */

export const updateRestaurantById = (id, restaurant) =>
  axios.put(`${restaurantService}/restaurants/${id}`, { ...restaurant });

export const uploadImage = (restaurantId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/restaurants/${restaurantId}/image-uploads`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

/**
 * Account Profile
 */

export const uploadAccountImages = (accountId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${userService}/accounts/${accountId}/image-uploads`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

/**
 * Discounts
 */

export const addDiscount = (orderId, amount, percentage, code, discountPin) =>
  axios.post(`${secured}/administration/orders/${orderId}/discount`, { amount, percentage, code, discountPin }, {});

export const addPaymentDiscount = (restaurantId, orderId, discountRequest, checkoutRequest, discountPin) =>
  axios.post(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/payment-discounts`, { discountRequest, request: checkoutRequest, discountPin }, {});

export const addItemDiscount = (orderId, orderItemId, amount, percentage, code, discountPin) =>
  axios.post(`${secured}/administration/orders/${orderId}/items/${orderItemId}/discount`, { amount, percentage, code, discountPin }, {});

export const deleteDiscount = (orderId) =>
  axios.delete(`${secured}/administration/orders/${orderId}/discount`, {});

export const deleteDiscountById = (restaurantId, discountId) =>
  axios.delete(`${gluttonyService}/restaurants/${restaurantId}/discounts/${discountId}`, {});

export const deleteItemDiscount = (orderId, orderItemId) =>
  axios.delete(`${secured}/administration/orders/${orderId}/items/${orderItemId}/discount`, {});

/**
 * Fast Checkout
 */

export const fastCheckoutOrder = (orderId, paymentChannel = 'CASH', amount = 0) =>
  axios.post(`${secured}/administration/orders/${orderId}/checkout/express`, { paymentChannel, amount }, {});

/**
 *  organizations
 */

export const getOrganization = (id) =>
  axios.get(`${restaurantService}/organizations/${id}`, {});

export const getRestaurants = (ids) =>
  axios.get(`${restaurantService}/restaurants/ids?${ids.map(i => `id=${i}`).join('&')}`, {});

export const getMyRestaurants = (query, limit, cancelToken) =>
  axios.post(`${uiService}/graphql`,
    {
      query: `
      query {
        getMyRestaurants(request: {
          limit: ${limit},
          text: "${query}"
        })
        {
          total
          items {
            id
            name
            mode
            description
            logoUrl
            branchRestaurants {
              id
              name
              slug
              mode
              logoUrl
            }
          }
        }
      }
      `
    },
    { cancelToken }
  );

export const getOrganizationRevenue = (id) =>
  axios.get(`${orderService}/organizations/${id}/report`, {});

/**
 * predefined notes
 */

export const getPredefinedNotes = (id) =>
  axios.get(`${restaurantService}/notes/restaurants/${id}`, {});

/**
 * custom extras
 */

export const getCustomExtras = (id) =>
  axios.get(`${restaurantService}/restaurants/${id}/custom-extra`, {});

export const postCustomExtra = (id, data) =>
  axios.post(`${restaurantService}/restaurants/${id}/custom-extra-items`, {...data}, {});
/**
 * Order refund
 */

export const refundOrder = (id) =>
  axios.post(`${orderService}/orders/${id}/refund`, {}, {})

/**
 * Order rotation
 */

export const moveOrder = (restaurantId, sourceTableId, targetTableId) =>
  axios.put(`${orderService}/orders/move`, {
    restaurantId,
    sourceTableId,
    targetTableId
  }, {})

export const moveOrderItems = (restaurantId, items, sourceTableId, targetTableId) =>
  axios.put(`${gluttonyService}/restaurants/${restaurantId}/_move-items`, {
    items,
    sourceTableId,
    targetTableId
  }, {})

/**
 * Order recovery
 */

export const recoverOrder = (orderId, tableId) =>
  axios.post(`${orderService}/orders/${orderId}/recover`, { targetTableId: tableId }, {})

/**
 * Order removal (by other waiter - without payment)
 */

export const removeOrderFromOtherWaiter = (restaurantId, orderId) =>
  axios.delete(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/unconfirmed-items`, {})

/**
 * Order item full reorder
 */

export const reoderOrderItem = (restaurantId, orderId, orderItemId) =>
  axios.post(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/item-reorders`, { orderItemId }, {})

/**
 * Configuration
 */

export const getConfiguration = (id) =>
  axios.get(`${restaurantService}/restaurants/${id}/configuration`, {});

/**
 * Orders Report
 */

export const getProjectionsReport = (restaurantId) =>
  axios.get(`${orderService}/restaurants/${restaurantId}/reports/orders/open`, {});

/**
 * Reservations
 */

export const resolveReservations = (restaurantId) =>
  axios.get(`${marketingService}/restaurants/${restaurantId}/reservations/_resolve`, {});

export const getReservationsCalendar = (restaurantId, yearMonth) =>
  axios.get(`${marketingService}/restaurants/${restaurantId}/reservation-calendar`, { params: { month: yearMonth } });

export const getReservationsForTable = (restaurantId, tableId, limit = 3, offset = 0) =>
  axios.get(`${marketingService}/restaurants/${restaurantId}/tables/${tableId}/reservations`, { params: { limit, offset } });

//
// export const resolveReservationSuggestions = (restaurantId, people, startTime, query, cancelToken) =>
//   axios.get(`${marketingService}/restaurants/${restaurantId}/reservations/suggestions`, { cancelToken, params: { restaurantId, startTime, people, query: query ? query : null } });
//
// V2 of the above ^
export const resolveReservationSuggestions = (restaurantId, people, startTime, query, cancelToken, tableIds = '', reservationId = null) =>
  axios.get(`${reservationService}/v1/restaurants/${restaurantId}/waiter/table-suggestions`, { cancelToken, params: { restaurantId, startTime, people, query: query ? query : null, tableIds: tableIds, reservationId: reservationId } });

// export const resolveReservationSuggestionsForCustomer = (restaurantId, people, day) =>
//   axios.get(`${marketingService}/restaurants/${restaurantId}/reservations/customer-suggestions-breakdown`, { params: { restaurantId, day, people } });

// V2 of the above ^
export const resolveReservationSuggestionsForCustomer = (restaurantId, people, day) =>
  axios.get(`${reservationService}/v1/restaurants/${restaurantId}/waiter/time-suggestions`, { params: { restaurantId, day, people } });

export const recoverReservation = (restaurantId, reservation, recoveringFromReservationId, customers) =>
  axios.post(`${marketingService}/restaurants/${restaurantId}/reservations`, { reservation, recoveringFromReservationId, customers }, {});

// export const createReservation = (restaurantId, reservation, customers) =>
//  axios.post(`${marketingService}/restaurants/${restaurantId}/reservations`, { reservation, customers }, {});

// V2 of the above ^
export const createReservation = (restaurantId, reservation, customers) =>
 axios.post(`${reservationService}/v1/restaurants/${restaurantId}/waiter/reservations`, { reservation, customers }, {});


// export const updateReservation = (restaurantId, reservation, customers) =>
//   axios.put(`${marketingService}/restaurants/${restaurantId}/reservations/${reservation.id}`, { reservation, customers }, {});
// New endpoint of the above ^
// export const updateReservation = (restaurantId, reservation, customers) =>
//   axios.put(`${marketingService}/restaurants/${restaurantId}/reservations/${reservation.id}`, { reservation, customers }, {});
export const updateReservation = (restaurantId, reservation, customers) =>
  axios.patch(`${reservationService}/v1/waiter/reservations/${reservation.id}`, { reservation, customers }, {});


export const updateReservationStatus = (restaurantId, reservationId, status) =>
  axios.put(`${marketingService}/restaurants/${restaurantId}/_reservations-status`, { id: reservationId, status }, {});

//export const getReservationAvailableTimes = (id, date) => axios.get(`${restaurantService}/restaurants/${id}/reservationTimes`, { params: { date } });
export const getReservationAvailableTimes = (id, date) => axios.get(`${reservationService}/v1/restaurants/${id}/waiter/reservation-times`, { params: { date } });

export const getReservationConfig = (id) => axios.get(`${marketingService}/restaurants/${id}/reservation-config`, {});

export const updateReservationConfig = (id, config, cancelToken) => axios.put(`${marketingService}/restaurants/${id}/reservation-config`, { ...config, cancelToken  }, {});

export const getTokenByUsernameAndPassword = (username, password) => axios.post(
  `${userService}/auth/token`,
  {
    type: grantTypes.USERNAME_PASSWORD.toUpperCase(),
    username,
    password,
    clientId,
    clientSecret
  },
  {}
);

export const getTokenByEmail = (otp, email) => axios.post(
  `${userService}/auth/token`,
  {
    type: grantTypes.EMAIL.toUpperCase(),
    pin: otp,
    email,
    clientId,
    clientSecret
  },
  {}
);

export const getTokenByMobile = (otp, countryCode, mobile) => axios.post(
  `${userService}/auth/token`,
  {
    type: grantTypes.MOBILE.toUpperCase(),
    pin: otp,
    countryCode,
    mobile,
    clientId,
    clientSecret
  },
  {}
);

export const getOneTimePasswordByEmail = (email) => axios.post(
  `${userService}/auth/_signup`,
  {
    type: grantTypes.EMAIL.toUpperCase(),
    email,
    clientId,
    clientSecret
  },
  {}
);

export const getOneTimePasswordByMobile = (countryCode = 49, mobile) => axios.post(
  `${userService}/auth/_signup`,
  {
    type: grantTypes.MOBILE.toUpperCase(),
    countryCode,
    mobile,
    clientId,
    clientSecret
  },
  {}
);

export const refreshToken = (refreshToken, externalClientId, externalClientSecret) => axios.post(
  `${userService}/auth/refresh-token`,
  {
    clientId: externalClientId || clientId,
    clientSecret: externalClientSecret || clientSecret,
    refreshToken
  },
  {}
);

export const logout = () => axios.post(
  `${userService}/auth/logout`,
  {
    clientId,
    clientSecret
  },
  {}
);

export const getAccount = (restaurantId, id) => axios.get(
  `${userService}/restaurants/${restaurantId}/accounts/${id}`,
  {}
);

// use getRestaurantAccounts instead of getAccounts in future
export const getRestaurantsAccounts = (restaurantId, limit = 10, offset = 0, withPermissions = false, query = "", userIds, permission= "", fetchPreferences) => axios.get(
  `${userService}/restaurants/${restaurantId}/accounts`,
  {
    params: {
      limit,
      offset,
      withPermissions,
      query,
      userIds: isEmpty(userIds) ? "" : (userIds ?? []).map(i => `userIds=${i}`).join('&'),
      permission,
      fetchPreferences,
    }
  }
);

export const getEmployee = (restaurantId, id) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/employees/${id}`,
  {}
);

export const putEmployee = (restaurantId, id, hourlyRate) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/employees/${id}`,
  {
    hourlyRate
  },
  {}
);

export const getAccountByUserId = (id = "_me") => axios.get(
  `${userService}/users/${id}/account`,
  {}
);

export const updateAccount = (account = {}) => axios.patch(
  `${userService}/accounts/_me`,
  { ...account },
  {}
);

export const updateAccountByUserId = (account = {}) => axios.patch(
  `${userService}/users/_me/account`,
  { ...account },
  {}
);

export const getAccountPreferences = () => axios.get(
  `${userService}/users/_me/account/preferences`,
  {}
);

export const updateAccountPreferences = (preferences = {}) => axios.patch(
  `${userService}/users/_me/account/preferences`,
  { ...preferences },
  {}
);

export const updateAccountPreferencesPin = (pin) => axios.patch(
  `${userService}/users/_me/account/preferences/_account-pin`,
  { pin },
  {}
);

export const getAccountPayroll = (id = "_me") => axios.get(
  `${userService}/accounts/${id}/payroll`,
  {}
);

export const updateAccountPayroll = (id = "_me", payroll = {}) => axios.put(
  `${userService}/accounts/_me/payroll`,
  { ...payroll },
  {}
);
export const updateAccountPreferencesById = (accountId, smartschankId) => axios.patch(
  `${userService}/accounts/${accountId}/preferences`,
  { smartschankId },
  {}
)

export const getSmartsChankOrderingDevices = (restaurantId) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/smartschank/ordering-devices`,
  {}
)

export const updateSmartsChankOrderingDevice = (restaurantId, orderingDeviceId, smartschankId) => axios.patch(
  `${gluttonyService}/restaurants/${restaurantId}/smartschank/ordering-devices/${orderingDeviceId}`,
  { restaurantId, orderingDeviceId, smartschankId },
  {}
)

export const deleteSmartsChankOrderingDevice = (restaurantId, orderingDeviceId) => axios.delete(
  `${gluttonyService}/restaurants/${restaurantId}/smartschank/ordering-devices/${orderingDeviceId}`,
  {}
)

export const deleteAccountPreferencesById = (accountId = "_me", valuesToDelete= [] ) => axios.delete(
  `${userService}/users/${accountId}/account/preferences/fields`,
  {
    params: {
      fields: valuesToDelete
    }
  }
)

export const getAllAccountPreferences = (accountIds = []) => axios.get(
  `${userService}/accounts-preferences`,
  {
    params: {
      accountIds: accountIds.join(',')
    }
  }
);
export const getAccountAvatars = (id) => axios.get(
  `${userService}/sample-avatars`,
)


export const searchPlaces = (query, cancelToken) => axios.get(
  `${restaurantService}/places/_search`,
  { cancelToken, params: { query } }
);

export const createRestaurant = (restaurant) => axios.post(
  `${restaurantService}/restaurants`,
  restaurant,
  {}
)

export const getAccounts = (restaurantId, limit = 10, offset = 0, query = "", userIds) => axios.get(
  `${userService}/accounts?${isEmpty(userIds) ? "" : (userIds ?? []).map(i => `userIds=${i}`).join('&')}`,
  { params: { restaurantId, limit, offset, query } }
)

export const removeAccountFromRestaurant = (restaurantId, accountId) => axios.delete(
  `${userService}/restaurants/${restaurantId}/accounts/${accountId}`,
  {}
)

export const addAccountToRestaurant = (restaurantId, user) => axios.post(
  `${userService}/restaurants/${restaurantId}/users`,
  user,
  {}
)

export const getPermissionIdentifiers = () => axios.get(
  `${userService}/permissions/identifiers`,
  {}
)

export const getAccountPermissions = (restaurantId, accountId) => axios.get(
  `${userService}/restaurants/${restaurantId}/accounts/${accountId}/permissions`,
  {}
)

export const updateAccountPermissions = (restaurantId, accountId, permissionIdentifiers) => axios.put(
  `${userService}/restaurants/${restaurantId}/accounts/${accountId}/permissions`,
  { permissionIdentifiers },
  {}
)

export const getAccountRoleTemplates = () => axios.get(
  `${userService}/permission-roles`,
  {}
)

export const createOrderParticipant = (restaurantId, orderId, customer) =>
  axios.post(`${orderService}/restaurants/${restaurantId}/orders/${orderId}/participants`,  customer, {})

export const updateCustomer = (restaurantId, customer) =>
  axios.put(`${orderService}/restaurants/${restaurantId}/customers`,  customer, {})

export const updateDriver = (orderId, driverIds = []) => axios.put(
  `${gluttonyService}/orders/${orderId}/drivers`, { driverIds }, {}
)

export const getRestaurantRevenueSummaryReport = (restaurantId, startDate, endDate ,dateType) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/totals`,
  { params: { startDate, endDate, dateType } }
)

export const getRestaurantDailyReport = (restaurantId, id) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/daily-report${id ? `/${id}` : ""}`,
  {}
)

export const getRestaurantRevenueReport = (restaurantId, startDate, endDate, dateType, groupBy, filters = {}) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/revenue`,
  { params: { startDate, endDate, dateType, groupBy, ...filters } }
)

export const getRestaurantProductsReport = (restaurantId, startDate, endDate, dateType) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/products`,
  { params: { startDate, endDate, dateType } }
)

export const getRestaurantTopProductsReport = (restaurantId, startDate, endDate, dateType) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/top-products`,
  { params: { startDate, endDate, dateType } }
)

export const getRestaurantTeamReport = (restaurantId, startDate, endDate, dateType) => axios.get(
  `${userService}/restaurants/${restaurantId}/reports/team-report`,
  { params: { startDate, endDate, dateType } }
)

export const getRestaurantCardsReport = (restaurantId, startDate, endDate, dateType) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/cards`,
  { params: { startDate, endDate, dateType } }
)

export const getRestaurantDiscountsReport = (restaurantId, startDate, endDate, dateType) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/discounts`,
  { params: { startDate, endDate, dateType } }
)

export const getRestaurantReservationsReport = (restaurantId, startDate, endDate, dateType) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/reservation`,
  { params: { startDate, endDate, dateType } }
)

export const getRestaurantCustomerReport = (restaurantId, startDate, endDate, dateType) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/customer`,
  { params: { startDate, endDate, dateType } }
)
export const getDateTimePicker = (restaurantId) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/date-picker`,
  {}
)

export const getReportOrderSource = (restaurantId, startDate, endDate, dateType, menuId, taxCategory, accountId, paymentChannel, orderType) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/source/orders`,
  { params: { startDate, endDate, dateType, menuId, taxCategory, accountId, paymentChannel, orderType } }
)

export const getImprovementAreaOrdersSource = (restaurantId, startDate, endDate, dateType, area) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/source/improvements/orders`,
  { params: { startDate, endDate, dateType, area } }
)

export const getImprovementAreaOrderItemsSource = (restaurantId, startDate, endDate, dateType, area) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/source/improvements/orderItems`,
  { params: { startDate, endDate, dateType, area } }
)

export const searchPayments = (restaurantId, startDate = new Date(), endDate = new Date(), dateType) => axios.post(
  `${uiService}/graphql`,
  {
    query: `
      query {
        searchPayments(request: {
          restaurantId: "${restaurantId}",
          period: {
            start: "${startDate}",
            end: "${endDate}",
            dateType: "${dateType}"
          },
        })
        {
          total
          pages,
          items {
            id
            amount
          }
        }
      }
      `,
  },
  {}
)

export const getRestaurantAppStore = (restaurantId) => axios.post(
  `${uiService}/graphql`,
  {
    query: `
      query {
        getAppsHomePage(request: {
          restaurantId: "${restaurantId}",
        }) {
          recommendedApps {
            id
            name
            category
            recommended
            iconUrl
            highlightedImageUrl
            galleryImageUrls
            numberOfInstallations
            restaurantContext {
              status
            }
            createdAt
            modifiedAt
            createdBy
            modifiedBy
            shortDescription
            description
          }
          categories {
            category
            apps {
              id
              name
              category
              recommended
              iconUrl
              highlightedImageUrl
              galleryImageUrls
              numberOfInstallations
              restaurantContext {
                status
              }
              createdAt
              modifiedAt
              createdBy
              modifiedBy
              shortDescription
              description
            }
          }
        }
      }
      `,
  },
  {}
)

export const getRestaurantApps = (restaurantId, category, installed, identifiers) => axios.get(
  `${marketplaceService}/restaurants/${restaurantId}/apps`,
  { params: { category, installed, identifiers: isEmpty(identifiers) ? null : identifiers.join(",") } }
)

export const getRestaurantApp = (restaurantId, appId) => axios.post(
  `${uiService}/graphql`,
  {
    query: `
      query {
        getRestaurantApp(request: {
          restaurantId: "${restaurantId}",
          appId: "${appId}",
        }) {
          id
          name
          category
          recommended
          iconUrl
          highlightedImageUrl
          galleryImageUrls
          numberOfInstallations
          restaurantContext {
            status
          }
          createdAt
          modifiedAt
          createdBy
          modifiedBy
          shortDescription
          description
        }
      }
      `,
  },
  {}
)

export const installRestaurantApp = (restaurantId, appId) => axios.post(
  `${marketplaceService}/restaurants/${restaurantId}/apps`,
  { appId },
  {}
)

export const getTakeaway = (restaurantId, operationView, startDate, endDate, isMobile, statuses, onlyWithCoordinates = true, includeRestaurant = false) => {
  const queries = {
    [operationViews.BOARD.key]: `
      query {
        getTakeawayView(request: {
          restaurantId: "${restaurantId}",
          isMobile: ${isMobile}
        }) {
          totalOrders
          lanes {
            groups {
              id
              numberOfOrders
              orders {
                id
                status
                number
                type
                numberOfDrinks
                numberOfDishes
                pickupTime
                takeawayDate
                total
                partnerId
                paymentStatus
                estimatedPreparationTime
                remainingDuration
                duration
                isWaiter
                acceptedAt
                isNew
                customer {
                  id
                  fullName
                }
                partnerOrderInfo {
                  orderNumber
                }
                restaurant {
                  id
                  name
                  logoUrl
                }
              }
            }
          }
        }
      }
    `,
    [operationViews.LIST.key]: `
      query {
        getTakeawayOrders(request: {
          restaurantId: "${restaurantId}",
          ${startDate ? `startDate: "${startDate}", endDate: "${endDate}"` : ''}
        }) {
          total
          totalAmount
          items {
            id
            status
            type
            paymentStatus
            partnerId
            isWaiter
            acceptedAt
            isNew
            number
            creationTime
            takeawayDate
            pickupTime
            total
            items {
              id
              status
              category
              total
            }
            restaurant {
              id
              name
              logoUrl
            }
          }
        }
      }
    `,
    [operationViews.MAP.key]: `
      query {
        getTakeawayOrders(request: {
          restaurantId: "${restaurantId}",
          ${statuses?.length ? `statuses: [${statuses}],` : ''}
          onlyWithCoordinates: ${onlyWithCoordinates}
        }) {
          total
          totalAmount
          items {
            id
            status
            type
            paymentStatus
            partnerId
            isWaiter
            acceptedAt
            number
            creationTime
            takeawayDate
            pickupTime
            total
            drivers {
              id
              firstName
              lastName
              pictureUrl
            }
            customer {
              address {
                zipCode
                lat
                lng
              }
            }
            restaurant {
              id
              name
              logoUrl
            }
          }
        }
      }
    `
  }

  return axios.post(
    `${uiService}/graphql`,
    {
      query: queries[operationView]
    },
    {}
  )
}

export const getExpress = (restaurantId, operationView) => {
  const queries = {
    [operationViews.BOARD.key]: `
      query {
        getExpressView(request: {
          restaurantId: "${restaurantId}",
        }) {
          totalOrders
          lanes {
            groups {
              id
              numberOfOrders
              orders {
                id
                status
                number
                type
                numberOfDrinks
                numberOfDishes
                pickupTime
                takeawayDate
                total
                paymentStatus
                accountId
                isWaiter
                toGo
                pagerIdentifier
                customer {
                  id
                  fullName
                }
              }
            }
          }
        }
      }
    `,
    [operationViews.LIST.key]: `
      query {
        getExpressOrders(request: {
          restaurantId: "${restaurantId}",
        }) {
          total
          totalAmount
          items {
            id
            status
            type
            number
            total
            paymentStatus
            creationTime
            pagerIdentifier
          }
        }
      }
    `
  }

  return axios.post(
    `${uiService}/graphql`,
    {
      query: queries[operationView]
    },
    {}
  )
}

export const getReservations = (restaurantId, operationView, filterDate, floorId) => {
  const queries = {
    [operationViews.BOARD.key]: `
      query {
        getReservationView(request: {
          restaurantId: "${restaurantId}",
          date: "${filterDate}",
        }) {
          totalReservations
          lanes {
            totalReservations
            totalGuests
            groups {
              id
              numberOfReservations
              reservations {
                id
                people
                startTime
                endTime
                formattedStartLocalTime
                formattedEndLocalTime
                formattedStartEndLocalTime
                cancelledByAccount
                cancelledByCustomer
                note
                status
                orderId
                partner {
                  id
                }
                customers {
                  id
                  firstName
                  lastName
                  fullName
                  phone
                  email
                  tags
                  address {
                    street
                    number
                    zipCode
                    city
                    country
                  }
                }
                tables {
                  id
                  label
                  code
                }
              }
            }
          }
        }
      }
    `,
    [operationViews.LIST.key]: `
      query {
        getReservations(request: {
          restaurantId: "${restaurantId}",
          date: "${filterDate}",
        }) {
          total
          pages
          items {
            id
            people
            startTime
            endTime
            formattedStartLocalTime
            formattedEndLocalTime
            formattedStartEndLocalTime
            note
            status
            orderId
            customers {
              id
              firstName
              lastName
              fullName
              phone
              email
              address {
                street
                number
                zipCode
                city
                country
              }
            }
            tables {
              id
              code
              label
            }
          }
        }
      }
    `,
    [operationViews.TIMELINE.key]: `
    query {
      getTimelineView(request: {
        restaurantId: "${restaurantId}",
        date: "${filterDate}",
        ${floorId ? `floorId: "${floorId}"` : ''}
      }) {
        tables {
          id
          label
          code
          layout {
            w,
            h,
            x,
            y
          }
          timelineItems {
            type
            startTime
            endTime
            order {
              id
            }
            reservation {
              id
              orderId
              formattedStartLocalTime
              formattedEndLocalTime
              formattedStartEndLocalTime
              customers {
                id
                fullName
              }
              people
            }
          }
        }
        floors {
          id
          name
        }
      }
    }
    `
  }

  return axios.post(
    `${uiService}/graphql`,
    {
      query: queries[operationView]
    },
    {}
  )
}

export const getReservation = (restaurantId, id) => axios.post(
  `${uiService}/graphql`,
  {
    query: `
      query {
        getReservation(request: {
          restaurantId: "${restaurantId}",
          id: "${id}"
        }) {
          id
          people
          startTime
          endTime
          formattedStartLocalTime
          formattedEndLocalTime
          formattedStartEndLocalTime
          note
          status
          cancelledByCustomer
          cancelledByAccount
          restaurantId
          customers {
            id
            firstName
            lastName
            fullName
            phone
            email
            address {
              street
              number
              zipCode
              city
              country
            }
          }
          tableIds
          tables {
            id
            label
            code
          }
        }
      }
    `,
  },
  {}
)

export const getMonitor = (restaurantId) => {
  const query = `
      query {
        getMonitoringView(request: {
          restaurantId: "${restaurantId}",
        }) {
          totalOrders
          lanes {
            groups {
              id
              numberOfOrders
              orders {
                id
                status
                number
                type
                numberOfDrinks
                numberOfDishes
                pickupTime
                takeawayDate
                total
                paymentStatus
                customer {
                  id
                  fullName
                  lastName
                }
              }
            }
          }
        }
      }
    `

  return axios.post(
    `${uiService}/graphql`,
    {
      query
    },
    {}
  )
}

export const cancelOrder = (orderId) => axios.delete(
  `${secured}/administration/orders/${orderId}/cancel`,
  {},
  {}
)

export const createExpressOrder = (restaurantId, isToGo) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/orders`,
  {
    "type": orderTypes.EXPRESS.key,
    "toGo": isToGo
  },
  {}
)

export const updateOrderStatus = (restaurantId, orderId, status) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/_update-order-status`,
  {
    orderId,
    status
  },
  {}
)

export const patchOrder = (restaurantId, orderId, order) => axios.patch(
  `${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}`,
  {
    ...order
  },
  {}
)

export const getOrderModes = (restaurantId) => axios.post(
  `${uiService}/graphql`,
  {
    query: `
      query {
        getOrderModes(request: {
          restaurantId: "${restaurantId}",
        }) {
          tabs {
            id
            ordersCount
          }
        }
      }
      `,
  },
  {}
)

export const getTimeline = (restaurantId, floorId) => axios.post(
  `${uiService}/graphql`,
  {
    query: `
      query {
        getTimelineView(request: {
          restaurantId: "${restaurantId}",
          floorId: "${floorId}"
        }) {
          tables {
            id
            label
            timelineItems {
              type
              startTime
              endTime
              order {
                id
              }
              reservation {
                id
              }
            }
          }
          floors {
            id
            name
          }
        }
      }
      `,
  },
  {}
)

export const checkout = (restaurantId, orderId, checkout) => axios.post(
  `${gluttonyService}/api/restaurants/${restaurantId}/orders/${orderId}/checkout`,
  {
    ...checkout
  },
  {}
)

export const createOneTimeItem = (restaurantId, orderId, otm) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/onetime-items`,
  {
    ...otm
  },
  {}
)

export const getUberEatsStore = (restaurantId) => axios.get(
  `${marketplaceService}/uber-eats/restaurants/${restaurantId}/store`,
  {}
)

export const getUberEatsStoreDetails = (restaurantId) => axios.get(
  `${marketplaceService}/uber-eats/restaurants/${restaurantId}/details`,
  {}
)

export const registerUberEatsStore = (restaurantId, storeId) => axios.post(
  `${marketplaceService}/uber-eats/restaurants/${restaurantId}`,
  {
    restaurantId,
    storeId
  },
  {}
)

export const deleteUberEatsStore = (restaurantId) => axios.delete(
  `${marketplaceService}/uber-eats/restaurants/${restaurantId}`,
  {}
)

export const getUberEatsStoreStatus = (restaurantId) => axios.get(
  `${marketplaceService}/uber-eats/restaurants/${restaurantId}/store/status`,
  {}
)

export const updateUberEatsStoreStatus = (restaurantId, status) => axios.put(
  `${marketplaceService}/uber-eats/restaurants/${restaurantId}/store/_statusUpdate`,
  { status },
  {}
)

export const getUberEatsStoreStatusV2 = (restaurantId) => axios.get(
  `${marketplaceService}/v2/uber-eats/restaurants/${restaurantId}/store/status`,
  {}
)

export const updateUberEatsStoreStatusV2 = (restaurantId, status) => axios.put(
  `${marketplaceService}/v2/uber-eats/restaurants/${restaurantId}/store/_statusUpdate`,
  status,
  {}
)

export const getWoltStore = (restaurantId) => axios.get(
  `${marketplaceService}/wolt/restaurants`,
  {
    params: { restaurantId, offset: 0, limit: 1 }
  }
)

export const getWoltStoreDetails = (restaurantId) => axios.get(
  `${marketplaceService}/wolt/restaurants/${restaurantId}/details`,
  {}
)

export const updateWoltDeliveryProvider = (restaurantId) => axios.patch(
  `${marketplaceService}/wolt/restaurants/${restaurantId}/deliveryProvider`,
  {}
)

export const updateWoltStoreStatus = (restaurantId, status) => axios.patch(
  `${marketplaceService}/wolt/restaurants/${restaurantId}/onlineStatus`,
  status,
  {}
)

export const registerWoltStore = (restaurantId, venueId, apiKey, basicUsername, basicPassword) => axios.post(
  `${marketplaceService}/wolt/restaurants/${restaurantId}`,
  {
    restaurantId,
    venueId,
    apiKey,
    basicUsername,
    basicPassword
  }
)

export const deleteWoltStore = (restaurantId) => axios.delete(
  `${marketplaceService}/wolt/restaurants/${restaurantId}`,
  {}
)

export const getPrintingQueueNext = (restaurantId) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/printing-queue/next`,
  {}
)

// noinspection SpellCheckingInspection
export const printPrintingQueueItemEpson = (ip, deviceId = 'local_printer', content) => axios.post(
  `https://${ip}/cgi-bin/epos/service.cgi?devid=${deviceId}&timeout=10000`,
  content,
  {
    transformRequest: (data, headers) => {
      delete headers.common['Authorization'];
      return data;
    },
    headers: {
      'Content-Type': 'text/xml; charset=utf-8',
      'If-Modified-Since': 'Thu, 01 Jan 1970 00:00:00 GMT',
      'SOAPAction': '""'
    }
  }
)

export const printPrintingQueueItemStar = (ip, content) => axios.post(
  `https://${ip}/StarWebPRNT/SendMessage`,
  content,
  {
    transformRequest: (data, headers) => {
      delete headers.common['Authorization'];
      return data;
    },
    headers: {
      'Content-Type': 'text/xml; charset=utf-8'
    }
  }
)

export const printPrintingQueueItemEpsonViaUsb = (name, content) => axios.post(
  `/print`,
  {
    name,
    content
  },
  {}
)

export const printPrintingQueueItemStarViaUsb = (name, content) => axios.post(
  `/print`,
  {
    name,
    content
  },
  {}
)

export const getSystemPrinters = () => axios.get(
  `http://localhost:8008/allo-local-services/printers`,
  {}
)

export const registerRestaurantForSystemPrinting = (restaurantId) => axios.post(
  `http://localhost:8008/allo-local-services/restaurants/${restaurantId}/_register-restaurant`,
  {},
  {}
)

export const deletePrintingQueueItem = (restaurantId, itemId) => axios.delete(
  `${gluttonyService}/restaurants/${restaurantId}/printing-queue/${itemId}`,
  {}
)

export const sendPrintingQueueItemResponse = (restaurantId, itemId, response) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/printer-response/${itemId}`,
  response,
  {}
)

export const createTimesheet = (restaurantId, accountId, date, hourlyRate, periods) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/timesheet-day`,
  {
    accountId,
    date,
    hourlyRate,
    periods
  }, {}
)

export const getTimesheetEntries = (restaurantId, accountId, date) =>
  axios.post(
    `${uiService}/graphql`,
    {
      query: `
        query {
          searchTimesheetEntries(request: {
            restaurantId: "${restaurantId}",
            includeTotals: false,
            accountId: "${accountId}",
            date: "${date}"
          }) {
            total
            pages
            totalHours
            totalPayableHours
            totalMinutes
            totalPayableMinutes
            items {
              id
              accountId
              date
              start
              fullStartTime
              stop
              fullStopTime
              type
              hourlyRate
              account {
                id
                firstName
                lastName
              }
            }
          }
        }
      `,
    },
    {}
  )

export const getTimesheetAggregatedEntries = (restaurantId, date, accountId) =>
  axios.post(
    `${uiService}/graphql`,
    {
      query: `
        query {
          searchTimesheetAggregatedEntries(request: {
            restaurantId: "${restaurantId}"
            date: "${date}"
            ${accountId ? `accountId: "${accountId}"` : ''}
          }) {
            total
            pages
            items {
              date
              hourlyRate
              workTotal
              payableTotal
              breakTotal
              account {
                id
                firstName
                email
              }
            }
          }
        }
      `,
    },
    {}
  )

export const getEmployeeTimesheetActions = (restaurantId) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/employee-timesheet-actions`,
  {}
)

export const updateEmployeeTimesheet = (restaurantId, action) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/_update_employee_time_shift`,
  {
    action
  },
  {}
)

export const printTest = (restaurantId, printerId) => axios.post(
  `${gluttonyService}/api/restaurants/${restaurantId}/printers/${printerId}/test`,
  {},
  {}
)

export const printTestLong = (restaurantId, printerId) => axios.post(
  `${gluttonyService}/api/restaurants/${restaurantId}/printers/${printerId}/test-long`,
  {},
  {}
)

export const printTestCashDrawer = (restaurantId, printerId) => axios.post(
  `${gluttonyService}/api/restaurants/${restaurantId}/printers/${printerId}/test-cash-drawer`,
  {},
  {}
)

export const getHandpointConfiguration = (restaurantId) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/handpoint-config`,
  {}
)

export const updateHandpointConfiguration = (restaurantId, data) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/handpoint-config`,
  {
    ...data
  },
  {}
)

export const cancelHandpointTransaction = (restaurantId, orderId) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/cancel-terminal-transaction`,
  {},
  {}
)

export const getBillingInfo = (restaurantId) => axios.get(
  `${restaurantService}/billing/restaurants/${restaurantId}/billing-info`,
  {}
)

export const updateBillingInfo = (restaurantId, values) => axios.put(
  `${restaurantService}/billing/restaurants/${restaurantId}/billing-info`,
  values,
  {}
)

export const getBillingHistory = (restaurantId, offset = 0, limit = 50) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/invoice-receipts`,
  {
    params: {
      offset,
      limit
    }
  }
)

export const getRestaurantTemplates = () =>
  axios.post(
    `${uiService}/graphql`,
    {
      query: `
        query {
          getRestaurantTemplates {
            items {
              id
              iconUrl
              name
              description
              templateConfig {
                capabilities
              }
            }
          }
        }
      `,
    },
    {}
  )

export const getBillingPaymentMethods = (restaurantId) => axios.get(
  `${restaurantService}/billing/restaurants/${restaurantId}/payment-methods`,
  {}
)

export const getAcceptedTermsAndConditions = (restaurantId) => axios.get(
  `${restaurantService}/billing/restaurants/${restaurantId}/accepted-terms-and-conditions`,
  {}
)

export const getTermsAndConditions = () => axios.get(
  `${restaurantService}/billing/terms-and-conditions`,
  {}
)

export const acceptTermsAndConditions = (restaurantId, documentHref) => axios.post(
  `${restaurantService}/billing/terms-and-conditions/restaurants/${restaurantId}/_accept`,
  {
    documentHref
  },
  {}
)

export const getCompanyLegalForms = () => axios.get(
  `${restaurantService}/billing/legal-forms`,
  {}
)

export const getRestaurantTutorial = (restaurantId) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/restaurant-tutorial`,
  {}
)

export const getBillingPlans = (restaurantId, paymentPeriod = billingPlanPeriod.MONTHLY.value, voucherCode) =>
  axios.post(
    `${uiService}/graphql`,
    {
      query: `
        query {
          getBillingPlanMatrix (request: {
            restaurantId: "${restaurantId}",
            paymentPeriod: ${paymentPeriod},
            voucherCode: "${voucherCode}"
          }) {
            plans {
              id
              name
              description
              price
              monthlyPrice
              active
              categoryGroups {
                name
                description
                logoUrl
                thumbnailUrl
                available
              }
            }
            groups {
              name
              description
              contents {
                description
              }
              categories {
                name
                features {
                  name
                  contents {
                    description
                  }
                }
              }
            }
          }
        }
      `,
    },
    {}
  )

export const createBillingPaymentMethod = (restaurantId, billingInfoData) => axios.post(
  `${restaurantService}/billing/restaurants/${restaurantId}/payment-methods`,
  {
    ...billingInfoData
  },
  {}
)

export const deleteBillingPaymentMethod = (restaurantId, paymentMethodId) => axios.delete(
  `${restaurantService}/billing/restaurants/${restaurantId}/payment-methods/${paymentMethodId}`,
  {}
)

export const setBillingPaymentMethodAsDefault = (restaurantId, paymentMethodId ) => axios.put(
  `${restaurantService}/billing/restaurants/${restaurantId}/payment-methods/${paymentMethodId}`,
  {
    "defaultMethod": true
  },
  {}
)

export const getAuthenticatedAccounts = () => axios.get(
  `${userService}/auth/account-switcher`,
  {}
)

export const switchAccount = (accountId, pin) => axios.post(
  `${userService}/auth/_switch-account`,
  {
    accountId,
    pin
  },
  {}
)

export const lockCurrentAccount = (restaurantId) => axios.put(
  `${userService}/auth/account-switcher/_lock_current_account`,
  {
    lastAccessedRestaurantId: restaurantId
  },
  {}
)

export const searchPromotionFeatureMappings = (featureIds = []) => axios.get(
  `${restaurantService}/promotion-feature-mapping`,
  { params: { featureIds: isEmpty(featureIds) ? null : featureIds.join(",") }, }
)

export const adminGetRestaurants = (limit = 10, offset = 0, query) => axios.get(
  `${restaurantService}/restaurants`,
  { params: { limit, offset, query }, }
)

export const getLegalInfo = (restaurantId) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/legal-info`,
  {}
)

export const updateLegalInfo = (restaurantId, values) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/legal-info`,
  values,
  {}
)

export const goLive = (restaurantId, migrationData) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/_go-live`,
  {
    ...migrationData
  },
  {}
)

export const uploadBusinessRegistrationDocument = (restaurantId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/file-upload/restaurants/${restaurantId}/_upload-business-registration-doc`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const uploadShareholderIdDocument = (restaurantId, shareholderId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/restaurants/${restaurantId}/shareholders/${shareholderId}/_upload-shareholder-id-doc`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const uploadShareholderResidencePermitDocument = (restaurantId, shareholderId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/restaurants/${restaurantId}/shareholders/${shareholderId}/_upload-shareholder-residence-permit-doc`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const uploadAccountStatementDocument = (restaurantId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/file-upload/restaurants/${restaurantId}/_upload-account-statement-doc`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const uploadSupplierInvoiceDocument = (restaurantId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/file-upload/restaurants/${restaurantId}/_upload-supplier-invoice-doc`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const addShareholder = (restaurantId, values) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/shareholders`,
  values,
  {}
)

export const updateShareholder = (restaurantId, shareholderId, values) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/shareholders/${shareholderId}`,
  values,
  {}
)

export const deleteShareholder = (restaurantId, shareholderId) => axios.delete(
  `${restaurantService}/restaurants/${restaurantId}/shareholders/${shareholderId}`, {}
)

export const sendEmailToCustomerToGenerateReceipt = (restaurantId, orderId, paymentId, email) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/digital-receipts/_generate-receipt-link`,
  {
    orderId,
    paymentId,
    email
  },
  {}
)

export const getRestaurantQrCodes = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/dynamic-codes`, {});

export const deleteDynamicQrCode = (restaurantId, code) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/dynamic-codes/${code}`, {});

export const getQrCodeLinkForDigitalReceipt = (restaurantId, orderId, paymentId) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/digital-receipts`,
  {
    orderId,
    paymentId,
  },
  {}
)

export const linkDynamicQRCode = (restaurantId, code, targetUrl, params) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/dynamic-codes/${code}`,
  {
    targetUrl,
    params
  },
  {}
)

export const getQrCodes = (offset = 0, limit = 100, query = "") => axios.get(
  `${restaurantService}/dynamic-qr-codes?query=${query}`,
  { params: { limit, offset }, }
)

export const getHelpCenterItemsAggregated = () => axios.get(
  `${restaurantService}/help-center-item-aggregation`,
  {}
)

export const abortTerminalPayment = (restaurantId, orderId) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/_abort-payment`,
  {},
  {}
)

export const confirmTerminalPayment = (restaurantId, orderId) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/_confirm-current-payment`,
  {},
  {}
)

export const deletePayment = (restaurantId, orderId, paymentId) => axios.delete(
  `${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/payments/${paymentId}`,
  {}
)

/**
 * Update tool for wolt menu
 */

// used only for comparison purpose
export const takeNextMenuSnapshot = (restaurantId) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/_take-menu-snapshot`,
)

// for taking LIVE snapshot - or fist time integration to generate both DRAFT and LIVE snapshot (with same content)
export const takeLiveSnapshot = (restaurantId) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/_take-live-menu-snapshot
`,
)

// endpoint for getting differences between DRAFT and LIVE snapshot
export const getDiffDraftVsLiveSnapshot = (restaurantId) =>
  axios.get(`${marketplaceService}/wolt/restaurants/${restaurantId}/wolt-diff`,
)

// replaces current menu at wolt with content from LIVE snapshot
export const syncMenuOnWolt = (restaurantId) =>
  axios.put(`${marketplaceService}/wolt/restaurants/${restaurantId}/_sync-menu`,
)

export const getLieferandoRestaurants = (restaurantId, offset = 0, limit = 100) =>
  axios.get(`${marketplaceService}/lieferando/restaurants`,
    {
      params: {
        restaurantId,
        pageRequest: {
          offset,
          limit
        }
      }
    }
  )

export const setLieferandoActivationCode = (restaurantId, partnerRestaurantId) =>
  axios.post(`${marketplaceService}/lieferando/restaurants/${restaurantId}`,
    {
      partnerRestaurantId: partnerRestaurantId,
      integrationType:"POS_PUSH_API"
    },
    {}
  )

export const loginLieferandoRestaurant = (restaurantId) =>
  axios.patch(`${marketplaceService}/lieferando/restaurants/${restaurantId}/_login`,
    {},
    {}
  )

export const logoutLieferandoRestaurant = (restaurantId) =>
  axios.delete(`${marketplaceService}/lieferando/restaurants/${restaurantId}/_logout`,
    {}
  )

export const deleteInventory = (restaurantId) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/inventory-settings`,
    {}
  )

export const getSuppliers = (restaurantId, limit, offset) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/suppliers`,
  { params: { limit, offset } }
)

export const createSupplier = (restaurantId, supplier) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/suppliers`,
  {
    ...supplier
  },
  {}
)

export const updateSupplier = (supplier) => axios.put(
  `${restaurantService}/suppliers/${supplier.id}`,
  {
    ...supplier
  },
  {}
)

export const deleteSupplier = (id) => axios.delete(
  `${restaurantService}/suppliers/${id}`,
  {}
)

export const getSKUCategories = (limit, offset, restaurantId, supplierId) => axios.get(
  `${restaurantService}/sku-categories`,
  { params: { restaurantId, supplierId, limit, offset } }
)

export const getInventoryCategories = (restaurantId, limit, offset, statuses, query) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/inventory-categories`,
  { params: { limit, offset, statuses: (statuses || []).join(','), query } }
)

export const createInventoryCategory = (restaurantId, category) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/inventory-categories`,
  {
    ...category
  },
  {}
)

export const updateInventoryCategory = (restaurantId, category) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/inventory-categories/${category.id}`,
  {
    ...category
  },
  {}
)

export const deleteInventoryCategory = (restaurantId, categoryId) => axios.delete(
  `${restaurantService}/restaurants/${restaurantId}/inventory-categories/${categoryId}`,
  {}
)


export const getSupplierSKUItems = (restaurantId, limit, offset, supplierId, skuCategoryId) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/supplier-sku-items`,
  { params: { skuCategoryId, supplierId, limit, offset } }
)

export const createSupplierSKUItem = (restaurantId, skuItem) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/supplier-sku-items`,
  {
    ...skuItem
  },
  {}
)

export const updateSupplierSKUItem = (restaurantId, skuItem) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/supplier-sku-items/${skuItem.id}`,
  {
    ...skuItem
  },
  {}
)

export const deleteSupplierSKUItem = (restaurantId, skuItemId) => axios.delete(
  `${restaurantService}/restaurants/${restaurantId}/supplier-sku-items/${skuItemId}`,
  {}
)

export const getSupplierInvoices = (restaurantId, limit, offset) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/supplier-invoices`,
  { params: { limit, offset } }
)

export const getSupplierInvoicesViews = (restaurantId, operationView) => {
  const queries = {
    [operationViews.BOARD.key]: `
      query {
        getSupplierInvoiceView(request: {
          restaurantId: "${restaurantId}"
        }) {
          lanes {
            total
            groups {
              id
              total
              invoices {
                id
                status
                number
                poNumber
                total
                supplier {
                  id
                  name
                }
                items {
                  id
                  name
                  total
                }
                total
                note
                creator {
                  id
                  firstName
                  lastName
                  fullName
                }
                formattedCreationDate
              }
            }
          }
        }
      }
    `,
  }

  if (operationView !== operationViews.BOARD.key) {
    return;
  }

  return axios.post(
    `${uiService}/graphql`,
    {
      query: queries[operationView]
    },
    {}
  )
}

export const getSupplierInvoice = (restaurantId, id) => axios.post(
  `${uiService}/graphql`,
  {
    query: `
      query {
        getSupplierInvoice(request: {
          restaurantId: "${restaurantId}",
          id: "${id}"
        }) {
          id
          status
          number
          poNumber
          isSetupOnly
          aiGenerated
          aiUploadedDocumentName
          completionDocumentName
          total
          creator {
            firstName
            preferences {
              avatarUrl
            }
          }
          formattedCreationDate
          supplier {
            id
            name
          }
          items {
            id
            code
            name
            quantity
            unitPrice
            total
            skuItemId
            category {
              id
              name
              unit
            }
          }
          allowedActions
          primaryActions
          secondaryActions
          note
        }
      }
    `,
  },
  {}
)

export const createSupplierInvoice = (restaurantId, invoiceData) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/supplier-invoices`,
  {
    ...invoiceData
  },
  {}
)

export const createSupplierInvoiceItem = (restaurantId, invoiceItemData) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/supplier-invoice-items`,
  {
    ...invoiceItemData
  },
  {}
)

export const updateSupplierInvoiceItem = (restaurantId, invoiceItemId, invoiceItemData) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/supplier-invoice-items/${invoiceItemId}`,
  {
    ...invoiceItemData
  },
  {}
)

export const deleteSupplierInvoiceItem = (restaurantId, invoiceItemId) => axios.delete(
  `${restaurantService}/restaurants/${restaurantId}/supplier-invoice-items/${invoiceItemId}`,
  {}
)

export const updateSupplierInvoice = (restaurantId, invoiceId, invoice) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/supplier-invoices/${invoiceId}`,
  {
    ...invoice
  },
  {}
)

export const updateSupplierInvoiceStatus = (restaurantId, invoiceId, status) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/supplier-invoices/${invoiceId}/_status`,
  {
    status
  },
  {}
)

export const getTableAutoApproval = (restaurantId, tableId) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/auto-approvals/${tableId}`,
  {}
)

export const deleteTableAutoApproval = (restaurantId, tableId) => axios.delete(
  `${gluttonyService}/restaurants/${restaurantId}/auto-approvals/${tableId}`,
  {}
)

export const createTableAutoApproval = (restaurantId, tableId) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/auto-approvals`,
  {
    tableId
  },
  {}
)

export const getQuantityByHourProductsReport = (restaurantId, startDate, endDate, dateType, menuItemCode) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/quantity-by-hour`,
  { params: { startDate, endDate, dateType, menuItemCode } }
)

export const getRevenueByHourProductsReport = (restaurantId, startDate, endDate, dateType, menuItemCode) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/reports/revenue-by-hour`,
  { params: { startDate, endDate, dateType, menuItemCode } }
)

export const getProductUpdateEntries = () => axios.get(
  `${restaurantService}/product-updates`,
  {}
)

export const createProductUpdateEntry = (entry) => axios.post(
  `${restaurantService}/product-updates`,
  {
    ...entry
  }
)

export const publishProductUpdateEntry = (entry) => axios.put(
  `${restaurantService}/product-updates/${entry.id}/_publish`
)

export const deleteProductUpdateEntry = (id) => axios.delete(
  `${restaurantService}/product-updates/${id}`
)

export const updateProductUpdateEntry = (entry) => axios.put(
  `${restaurantService}/product-updates/${entry.id}`,
  {
    ...entry
  },
  {}
)

export const searchAccounts = (restaurantId, limit, offset) => axios.post(
  `${uiService}/graphql`,
  {
    query: `
      query {
        searchAccounts(request: {
          restaurantId: "${restaurantId}",
          pageRequest: {
            limit: "${limit}",
            offset: "${offset}"
          }
        }) {
          total
          pages
          items {
            firstName
            preferences {
              avatarUrl
            }
          }
        }
      }
    `,
  },
  {}
)

/**
 * Stripe related
 */

export const getStripeExpressConfiguration = (restaurantId) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/stripe-express-configuration`,
  {}
)

export const createStripeExpressConfiguration = (restaurantId) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/stripe-express-configuration`,
  {},
  {}
)

export const updateStripePlatformPricingHistory = (restaurantId, platformPricings) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/stripe-express-configuration/platform-pricing-history`,
  {
    platformPricings
  },
  {}
)

export const createStripeExpressDashboardLogin = (restaurantId) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/stripe-express-dashboard-login`,
  {},
  {}
)

export const getStripeExpressCapabilities = (restaurantId) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/stripe-capabilities`,
  {}
)

export const updateStripeExpressCapability = (restaurantId, capabilityId, requested) => axios.put(
  `${restaurantService}/restaurants/${restaurantId}/stripe-capabilities/${capabilityId}`,
  {
    requested
  },
  {}
)

export const getStripeConnectTransactions = (restaurantId, offset = 0, limit = 15, from, to, nextPageStartingObjectId, previousPageEndingObjectId ) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/stripe-charges?offset=${offset}&limit=${limit}${from ? `&from=${from}`: ''}${to ? `&to=${to}`: ''}${nextPageStartingObjectId ? `&startingAfter=${nextPageStartingObjectId}` : ''}${previousPageEndingObjectId ? `&endingBefore=${previousPageEndingObjectId}` : ''}`,
  {}
)

export const getStripeConnectCsvExports = (restaurantId, startDate, endDate) => {
  return axios
    .get(`${restaurantService}/restaurants/${restaurantId}/stripe-charges/export-csv?from=${startDate}&to=${endDate}`,
      { responseType: 'blob' })
}

export const getStripePayoutSettings = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/stripe-payouts-settings`,
  {}
)

export const updateStripePayoutSettings = (restaurantId, settings) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/stripe-payouts-settings/delay-days`,
    settings,
    {}
)

/**
 * PayPal related
 */

export const getPaypalConfiguration = (restaurantId) => axios.get(
  `${restaurantService}/restaurants/${restaurantId}/paypal-configuration`,
  {}
)

export const createPaypalConfiguration = (restaurantId) => axios.post(
  `${restaurantService}/restaurants/${restaurantId}/paypal-configuration`,
  {},
  {}
)

export const disconnectPaypalAccount = (restaurantId) => axios.delete(
  `${restaurantService}/restaurants/${restaurantId}/paypal-configuration`,
  {},
)

export const getCheckoutPaymentRequest = (orderId) => axios.get(
  `${gluttonyService}/orders/${orderId}/payment-link-metadata?checkoutRequestOnly=true`,
  {}
)

export const getCheckoutLastValidPaymentRequest = (restaurantId, orderId) => axios.get(
  `${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/last-valid-payment-request`,
  {}
)

export const getGuestMonitors = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/guest-monitors`, {});

export const createGuestMonitor = (id, device) =>
  axios.post(`${restaurantService}/restaurants/${id}/guest-monitors`, { ...device }, {});

export const updateGuestMonitor = (id, device) =>
  axios.put(`${restaurantService}/restaurants/${id}/guest-monitors/${device.id}`, { ...device }, {});

export const deleteGuestMonitor = (id, device) =>
  axios.put(`${restaurantService}/restaurants/${id}/guest-monitors/${device.id}`, { ...device }, {});


export const getOrderingDevices = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/ordering-devices`, {});

export const createOrderingDevice = (id, device) =>
  axios.post(`${restaurantService}/restaurants/${id}/ordering-devices`, { ...device }, {});

export const updateOrderingDevice = (id, device) =>
  axios.put(`${restaurantService}/restaurants/${id}/ordering-devices/${device.id}`, { ...device }, {});

export const deleteOrderingDevice = (id, device) =>
  axios.delete(`${restaurantService}/restaurants/${id}/ordering-devices/${device.id}`, {});


export const getShopProducts = () =>
  axios.get(`${restaurantService}/allo-products?offset=0&limit=100`);

export const getShopOpenCartByRestaurantId = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/allo-carts/open-cart`)

export const putShopCartItemQuantity = (restaurantId, cartItemId, quantity) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/allo-carts/open-cart/items/${cartItemId}`, {
    quantity: quantity
  });

export const postProductToShopCart = (restaurantId, productId, quantity) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/allo-carts/open-cart/items`, {
    productId,
    quantity
  })

export const putShopCartPayment = (restaurantId) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/allo-carts/open-cart/payment`,{
    "action": "REQUEST"
  })

export const putShoppingCart = (restaurantId, billingAddress, deliveryAddress) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/allo-carts/open-cart`, {
    deliveryAddress,
    billingAddress
  })

export const getShopOrderHistory = (restaurantId, offset = 0, limit = 10) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/allo-carts?offset=${offset}&limit=${limit}`)

export const postRestaurantPayout = (id, amount) =>
  axios.post(`${restaurantService}/restaurants/${id}/payouts`, { amount }, {});

export const getRestaurantPayouts = (id, offset = 0, limit = 10) =>
  axios.get(`${restaurantService}/restaurants/${id}/payouts?offset=${offset}&limit=${limit}`);

export const getRestaurantStripeAccountBalance = (id) =>
  axios.get(`${restaurantService}/restaurants/${id}/stripe-account-balance`);

/*export const sendTestEmail = (emailId, receiverEmail) =>
  axios.post(`${restaurantService}/restaurants/${id}/test-emails`, { emailId, receiverEmail }, {});*/

export const getFPXConfiguration = (restaurantId) =>
  axios.get(`${marketplaceService}/fpx-configurations/restaurants/${restaurantId}`, {});

export const registerFPXConfiguration = (restaurantId, config) =>
  axios.post(`${marketplaceService}/fpx-configurations/restaurants/${restaurantId}`, { ...config }, {});

export const deleteFPXConfiguration = (restaurantId) =>
  axios.delete(`${marketplaceService}/fpx-configurations/restaurants/${restaurantId}`, {});

export const addCourse = (restaurantId, orderId, courseName) => axios.post(
  `${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/courses`,
  {
    courseName
  },
  {}
)

export const printCourseNotification = (restaurantId, orderId, courseNumber) =>
  axios.post(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/_print-course-notification`, {
    courseNumber
  }, {});

export const getOpenInvoices = (restaurantId, cancelToken, dateFrom, dateTo, filters, offset = 0, limit = 100 ) => {
  const preparedFilters = isEmpty(filters.status) ? "OPEN,PAID,FAILED" : filters.status
  return axios.get(`${gluttonyService}/restaurants/${restaurantId}/open-invoices?statuses=${preparedFilters}&offset=${offset}&limit=${limit}&startDate=${dateFrom}&endDate=${dateTo}`, { cancelToken });
}

export const createOpenInvoice = (id, invoice) =>
  axios.post(`${gluttonyService}/restaurants/${id}/open-invoices`, { ...invoice }, {});

export const getOpenInvoiceTaxRates = (id) =>
  axios.get(`${gluttonyService}/open-invoice-tax-rates`, {}, {});

export const deleteInvoice = (restaurantId, id) =>
  axios.delete(`${gluttonyService}/restaurants/${restaurantId}/open-invoices/${id}`, {});

export const updateInvoice = (restaurantId, invoice) =>
  axios.put(`${gluttonyService}/restaurants/${restaurantId}/open-invoices/${invoice.id}`, { ...invoice }, {});

export const patchInvoiceStatus = (restaurantId, id, status) =>
  axios.patch(`${gluttonyService}/restaurants/${restaurantId}/open-invoices/${id}/status`, { status }, {});

// inventory

export const adjustInventoryCategory = (restaurantId, items) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/inventory-category-adjustments`, {items}, {});

export const getInventoryCategoryRecords = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/inventory-category-changes`, {});

export const uploadSupplierInvoiceFile = (restaurantId, file, isSetupOnly) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/restaurants/${restaurantId}/supplier-invoices/files?isSetupOnly=${isSetupOnly}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const resetCashJournalKontos = (restaurantId, allowToOverwriteHistoricalKontoConfigurations = false) =>
  axios.post(`${restaurantService}/migrations/_record-kontos/${restaurantId}?allowToOverwriteHistoricalKontoConfigurations=${allowToOverwriteHistoricalKontoConfigurations}`, {}, {});

export const uploadSupplierInvoiceCompletionFile = (restaurantId, purchaseId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${restaurantService}/restaurants/${restaurantId}/supplier-invoices/${purchaseId}/completion-document`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const downloadSupplierInvoiceCompletionFile = (restaurantId, purchaseId) => {
  return axios
    .get(`${restaurantService}/restaurants/${restaurantId}/supplier-invoices/${purchaseId}/completion-document`, { responseType: 'blob' });
};

// global config - Ai Context
export const getAiConfig = () =>
  axios.get(`${restaurantService}/ai-configs/default`, {});

export const updateInventoryAiConfig = (contextFrom) =>
  axios.put(`${restaurantService}/ai-configs/default`,
    {
      id: contextFrom.id,
      inventoryContext: contextFrom.inventoryContext
    },
    {}
  );


// Global Config - Blocking accounts

export const getBlockedAccounts = () => axios.get(
  `${userService}/blacklists/account_blacklist`,
  {}
)

export const kickOutBlockedAccounts = ( account ) => axios.post(
  `${userService}/blacklists/account_blacklist/_kick-out`,
  { account },
  {}
)

// domain + email
export const blockDomain = ( domain ) => axios.post(
  `${userService}/blacklists/account_blacklist/domains`,
  { domain },
  {}
)

export const unblockDomain = ( domain ) => axios.delete(
  `${userService}/blacklists/account_blacklist/domains/${domain}`,
  {
    data: domain
  }
)

// email
export const blockEmail = ( email ) => axios.post(
  `${userService}/blacklists/account_blacklist/emails`,
  { email },
  {}
)

export const unblockEmail = ( email ) => axios.delete(
  `${userService}/blacklists/account_blacklist/emails/${email}`,
  {
    data: email
  }
)

// phone
export const blockPhoneNumber = ( phone ) => axios.post(
  `${userService}/blacklists/account_blacklist/phones`,
  { phone },
  {}
)

export const unblockPhoneNumber = ( phoneNumber ) => axios.delete(
  `${userService}/blacklists/account_blacklist/phones/${phoneNumber}`,
  {
    data: phoneNumber
  }
)

/**
 * kitchen monitors
 */


export const getKitchenMonitors = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/kitchen-monitors`, {});

export const getKitchenMonitorById = (restaurantId, monitorId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/kitchen-monitors/${monitorId}`, {});

export const createKitchenMonitor = (id, monitor) =>
  axios.post(`${restaurantService}/restaurants/${id}/kitchen-monitors`, { ...monitor }, {});

export const updateKitchenMonitor = (id, monitor) =>
  axios.put(`${restaurantService}/restaurants/${id}/kitchen-monitors/${monitor.id}`, { ...monitor }, {});

export const deleteKitchenMonitor = (id, monitor) =>
  axios.delete(`${restaurantService}/restaurants/${id}/kitchen-monitors/${monitor.id}`, {});

export const getKitchenMonitorOrders = (id, monitorId, type = "OPEN") =>
  axios.get(`${gluttonyService}/restaurants/${id}/kitchen-orders/${monitorId}?type=${type}`, {});

export const putKitchenMonitorOrderItemStatus = (id, orderItemId, status) =>
  axios.put(`${gluttonyService}/restaurants/${id}/kitchen-items/${orderItemId}`, {
    status
  })

export const bulkStatusUpdateKitchenMonitor = (restaurantId, orderId, sequence, courseNumber) => {
  const course = courseNumber ? { number: courseNumber } : null
  return (
    axios.put(`${gluttonyService}/restaurants/${restaurantId}/kitchen-order/${orderId}`, {
      course: course,
      sequence: sequence,
    })
  )
}

export const kitchenMonitorStatusUpdateByItem = (restaurantId, monitorId, orderItemId, sequence, quantity) => (
  axios.put(`${gluttonyService}/restaurants/${restaurantId}/kitchen-monitor/${monitorId}/kitchen-items/${orderItemId}`, {
      sequence,
      quantity
    })
)

export const kitchenMonitorBulkStatusUpdateByCourse = (restaurantId, monitorId, orderId, courseNumber, sequence, view) => (
    axios.put(`${gluttonyService}/restaurants/${restaurantId}/kitchen-monitor/${monitorId}/kitchen-order/${orderId}`, {
      course: {
        number: courseNumber
      },
      sequence,
      kitchenMonitorType: view
    }
  ))

export const kitchenMonitorBulkStatusUpdateByOrder = (restaurantId, monitorId, orderId, sequence, view) => {
  return (
    axios.put(`${gluttonyService}/restaurants/${restaurantId}/kitchen-monitor/${monitorId}/kitchen-order/${orderId}`, {
      sequence,
      kitchenMonitorType: view
      }
    )
  )};

export const kitchenMonitorManualPrinting = (restaurantId, monitorId, orderId, orderItemId, courseNumber) => {
  const payload = orderItemId != null ? { orderItemId } : courseNumber != null ? { course: { number: courseNumber } } : {};
  return (
    axios.post(`${gluttonyService}/restaurants/${restaurantId}/kitchen-monitor/${monitorId}/kitchen-order/${orderId}/_print`, payload )
  )
}

export const getTemplateSetMenus = (restaurantId, withMenus = false) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/template-set-menus?withMenus=${withMenus}`, {});

export const createTemplateSetMenuItemsBulk = (restaurantId, orderId, templateSetMenuId, itemsBulkData ) =>
  axios.post(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/template-set-menu-items`, {
    templateSetMenuId,
    items: itemsBulkData,
  });

export const createOrderItemsBulk = (restaurantId, orderId, itemsBulkData) =>
  axios.post(`${gluttonyService}/restaurants/${restaurantId}/orders/${orderId}/items-bulk`, {
    items: itemsBulkData
  });

export const getContentLanguages = () =>
  axios.get(`${restaurantService}/customer-languages`, {});

export const getSystemLanguages = () =>
  axios.get(`${userService}/system-languages`, {});

export const editTemplateSetMenu = (restaurantId, id, data) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/template-set-menus/${id}`, { ...data });

export const createTemplateSetMenu = (restaurantId, data) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/template-set-menus`, { ...data });

export const deleteTemplateSetMenu = (restaurantId, id) =>
  axios.delete(`${restaurantService}/restaurants/${restaurantId}/template-set-menus/${id}`)

export const searchTakeawayOrders = (restaurantId, query, startDate, endDate) =>
  axios.get(`${path}/restaurants/${restaurantId}/orders/search?query=${query}&startDate=${startDate}&endDate=${endDate}`);

export const testDirmeierSmartSchankIntegration = (ip, timeout = 3000 ) => axios.post(
  `https://${ip}/cash/1.0/credits`,
  { "credittype":"default" },
  {
    headers: {
      'Content-Type': "application/x-www-form-urlencoded"
    },
    timeout
  }
)

export const getStripeMonthlyReport = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/card-fee-reports`)

export const getPricePerWeight = (restaurantId, menuItemId, measurementValue) =>
  axios.get(`${gluttonyService}/restaurants/${restaurantId}/measured-item/price?measurementValue=${measurementValue}&menuItemId=${menuItemId}`, {});

export const searchCRMCompanies = (query) =>
  axios.get(`${restaurantService}/crm/companies`, { params: { name: query, offset: 0, limit: 10 } })

export const updateCRMSettings = (restaurantId, crmCompanyId) =>
  axios.put(`${restaurantService}/restaurants/${restaurantId}/crm-settings`, { companyId: crmCompanyId }, {})

export const inviteRestaurantOwner = (restaurantId, email, firstName, lastName) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/onboarding/owners`, { email, firstName, lastName }, {})

export const getStripeReportMonths = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/allo-pay/report-months`, {})

export const sendStripeReportPerMonthToEmail = (restaurantId, sendReportEmail, yearMonth) =>
  axios.post(`${restaurantService}/restaurants/${restaurantId}/allo-pay/generate-stripe-payout-report`, { sendReportEmail, yearMonth }, {})

export const getReservationConfigV2 = (id) => axios.get(`${reservationService}/v1/restaurants/${id}/configuration/windows`, {});

export const postReservationWindow = (id, data) => axios.post(`${reservationService}/v1/restaurants/${id}/configuration/windows`, {...data},{})
export const putReservationWindow = (restaurantId, reservationId, data) => axios.put(`${reservationService}/v1/restaurants/${restaurantId}/configuration/windows/${reservationId}`, {...data},{})
export const deleteReservationWindow = (restaurantId, reservationId) => axios.delete(`${reservationService}/v1/restaurants/${restaurantId}/configuration/windows/${reservationId}`, {})
export const getReservationConfigV2Enabled = (id) => axios.get(`${reservationService}/v1/restaurants/${id}/configuration/enabled`, {});

export const getFloorsAndTables = (restaurantId) => axios.get(`${restaurantService}/business/restaurants/${restaurantId}/floors`, {});
export const getTableGroups = (restaurantId) => axios.get(`${reservationService}/v1/restaurants/${restaurantId}/configuration/table-groups`, {});
export const postTableGroup = (restaurantId, data) => axios.post(`${reservationService}/v1/restaurants/${restaurantId}/configuration/table-groups`, {...data},{})
export const deleteTableGroup = (restaurantId, tableGroupId) => axios.delete(`${reservationService}/v1/restaurants/${restaurantId}/configuration/table-groups/${tableGroupId}`, {})
export const putTableGroup = (restaurantId, tableGroupId, data) => axios.put(`${reservationService}/v1/restaurants/${restaurantId}/configuration/table-groups/${tableGroupId}`, {...data})

export const getReservationConfiguration = (restaurantId) => axios.get(`${reservationService}/v1/restaurants/${restaurantId}/configuration`);

export const uploadReservationCoverImage = (restaurantId, file) => {
  const formData = new FormData();
  formData.append("file", file, file.name);
  return axios
    .post(`${reservationService}/v1/restaurants/${restaurantId}/configuration/cover/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
};

export const updateReservationCoverImages = (restaurantId, coverImages) => axios.patch(`${reservationService}/v1/restaurants/${restaurantId}/configuration`, { cover: coverImages });
export const updateReservationConfigV2 = (restaurantId, data) => axios.patch(`${reservationService}/v1/restaurants/${restaurantId}/configuration`, {...data},{})

export const getActivePhoneCall = (restaurantId) =>
  axios.get(`${restaurantService}/restaurants/${restaurantId}/local-device-events/active-phone-call`, {})

/* RESERVATION SPECIAL DAYS */

export const getSpecialDays = (restaurantId) =>
  axios.get(`${reservationService}/v1/restaurants/${restaurantId}/configuration/special-days`, {})

export const postSpecialDay = (restaurantId, data) =>
  axios.post(`${reservationService}/v1/restaurants/${restaurantId}/configuration/special-days`, {...data},{})

export const putSpecialDay = (restaurantId, specialDayId, data) =>
  axios.put(`${reservationService}/v1/restaurants/${restaurantId}/configuration/special-days/${specialDayId}`, {...data},{})

export const deleteSpecialDay = (restaurantId, specialDayId) =>
  axios.delete(`${reservationService}/v1/restaurants/${restaurantId}/configuration/special-days/${specialDayId}`, {})
