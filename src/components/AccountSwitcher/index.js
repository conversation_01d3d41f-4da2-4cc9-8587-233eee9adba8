import React, {useState} from 'react';
import {useRouter} from "next/router";
import {useSelector, useDispatch} from "react-redux";
import Avatar from "@material-ui/core/Avatar";
import Typography from "@material-ui/core/Typography";
import MenuItem from "@material-ui/core/MenuItem";
import { accountSelectors, applicationSelectors, restaurantSelectors } from "../../../redux/selectors";
import {ChevronUpDown} from "../../utils/icons";
import { noop } from "../../utils/const";
import useStyles from "./styles";
import Badge from "@material-ui/core/Badge";
import IconButton from "@material-ui/core/IconButton";
import {authActions} from "../../../redux/actions";
import packageJSON from '../../../package.json';
import { i18n, withTranslation } from "../../../i18n";
import { lockCurrentAccount, updateAccountPreferences } from "../../api";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { messages } from "../../../redux/messages";
import CustomMenu from "../_popup/CustomMenu";
import LanguageSelectorModal from "../_popup/LanguageSelectorModal";
import { Confirm } from "../Confirmation";

const data = {
  language: "en"
}

const AccountSwitcher = withTranslation('common')(({ t, connected }) => {
	const classes = useStyles();
	const router = useRouter();
	const dispatch = useDispatch();
	const [anchor, setAnchor] = useState(null);
	const [languageAnchor, setLanguageAnchor] = useState(null);
  const { language } = i18n;
  const { preferences = {} } = useSelector(accountSelectors.getPreferences)
  const [form, setForm] = useState({ ...data, ...preferences, language })
	const { version } = packageJSON || {};
	const isMobile = useMediaQuery('(max-width:600px)');

	const firstName = useSelector(accountSelectors.getAccountFirstName);
	const fullName = useSelector(accountSelectors.getAccountFullname);
	const acronym = useSelector(accountSelectors.getAccountAcronym);
	const embedded = useSelector(applicationSelectors.getIsEmbedded);

	const avatarUrl = useSelector(accountSelectors.getPreferencesAvatarUrl)
	const restaurantId = useSelector(restaurantSelectors.getRestaurantId);

	const openMenu = (e) => setAnchor(e.currentTarget);
	const closeMenu = () => setAnchor(null);

	const openLanguageMenu = (e) => {
		closeMenu()
		setLanguageAnchor(e.currentTarget);
	}
	const closeLanguageMenu = () => setLanguageAnchor(null);

	const onLogout = () => {
		messages.logout();
		dispatch(authActions.logout());
		closeMenu();
	}

	const onUpdate = () => {
		if (embedded) {
			messages.update()
		} else {
			router.reload();
		}
	}

	const updateLanguage = (value) => () => {
    const newForm = {...form, language: value};
    updateAccountPreferences(newForm)
      .then(d => {
        setForm(newForm)
      })
      .catch(console.error)
      .finally(() => {
        i18n.changeLanguage(value).then(noop).catch(noop);
        closeLanguageMenu()
      })
	};

	const onSwitchAccount = () => {
		lockCurrentAccount(restaurantId).then(() => {
			router.replace("/access")
				.then(() => {
					try {
						localStorage.setItem('switch-account', 'true');
					} catch (error) {
						console.log("Failed to set switch-account in local storage", error);
					}
				})
				.catch(() => {});
		}).catch(noop)
	}

	return (
		<div>
			<div className={classes.container}>
				<div className={classes.content} onClick={openMenu}>
					<div className={classes.left}>
						<IconButton color="primary" aria-label="user avatar" component="span" onClick={openMenu} disableRipple className={classes.avatarBtn}>
							<Badge overlap="circle" badgeContent=" " variant="dot" anchorOrigin={{ vertical: 'bottom', horizontal: 'right'}} classes={{ root: connected ? classes.badge : null }}>
								<Avatar
									alt={fullName}
									src={avatarUrl}
									classes={{ root: classes.avatar }}
								>
									{acronym}
								</Avatar>
							</Badge>
						</IconButton>
						{!isMobile && (
							<Typography className={classes.name} style={{ whiteSpace: "nowrap" }}>
								{firstName || t('hi-there')}
							</Typography>
						)}
					</div>
					{!isMobile && (
						<div className={classes.right}>
							<ChevronUpDown />
						</div>
					)}
				</div>
			</div>
			<CustomMenu
				id="account-switcher-menu"
				anchorEl={anchor}
				keepMounted
				open={Boolean(anchor)}
				onClose={closeMenu}
				classes={{
					paper: classes.menu
				}}
			>
				<MenuItem onClick={onUpdate} classes={{ root: classes.menuItem }}>
					<div className={classes.content}>
						<div className={classes.left}>
							<Typography className={classes.name}>
								{`${t('common-update')} (${version})`}
							</Typography>
						</div>
					</div>
				</MenuItem>
				<MenuItem onClick={onSwitchAccount} classes={{ root: classes.menuItem }}>
					<div className={classes.content}>
						<div className={classes.left}>
							<Typography className={classes.name}>
								{t('switch-account')}
							</Typography>
						</div>
					</div>
				</MenuItem>
				<MenuItem onClick={openLanguageMenu} classes={{ root: classes.menuItem }}>
					<div className={classes.content}>
						<div className={classes.left}>
							<Typography className={classes.name}>
								{t('language')}
							</Typography>
						</div>
					</div>
				</MenuItem>
				<Confirm
					closeMenu={closeMenu}
					title={t('waiter-log-out')}
					body={(
						<Typography color="textSecondary" variant="body2">
							<br />
							{t("this-action-will-log-you-out-of-your-account")}
							<br />
							{t("are-you-sure-message")}
						</Typography>
					)}>
					{confirm => (
						<MenuItem onClick={confirm(onLogout)} classes={{ root: classes.menuItem }}>
							<div className={classes.content}>
								<div className={classes.left}>
									<Typography className={classes.name}>
										{t('waiter-log-out')}
									</Typography>
								</div>
							</div>
						</MenuItem>
					)}
				</Confirm>
			</CustomMenu>
			{languageAnchor && <LanguageSelectorModal open={languageAnchor} onClose={closeLanguageMenu} setValue={updateLanguage} t/>}
		</div>
	)
});

export default AccountSwitcher;
