import React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import useStyles from './styles';

const DisplayTitleContainer = ({ withMargin, ...otherProps }) => {
  const classes = useStyles();
  const { children } = otherProps;

  return (
    <div>
      <div className={clsx(
        classes.container,
        { [classes.withMargin]: withMargin }
      )}
      >
        {children}
      </div>
    </div>
  );
};

DisplayTitleContainer.propTypes = {
  withMargin: PropTypes.bool
};

DisplayTitleContainer.defaultProps = {
  withMargin: false
};

export default DisplayTitleContainer;
