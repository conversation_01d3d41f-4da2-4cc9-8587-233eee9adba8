import React from "react";
import useStyles from "./styles";
import typography from "../../../../styles/typography";
import { ButtonBase } from "@material-ui/core";
import palette from "../../../../styles/palette";

function OnlineSwitch(props) {
  const classes = useStyles();
  const { isOnline, disabled, onStatusChange } = props;
  
  return (
    <ButtonBase disableRipple disableTouchRipple className={classes.root} onClick={() => onStatusChange(isOnline)}>
      <div style={{
        backgroundColor: isOnline? palette.secondary.green["400"] : palette.negative["500"],
        borderRadius: 13,
        width: '100%',
        height: '100%',
        transition: 'background-color 0.3s',
        display: 'flex',
        alignItems: 'center',
        justifyContent: isOnline? 'flex-end' : 'flex-start',
        padding: 2,
        boxSizing: 'border-box',
        position: 'relative',
        gap: 4
      }}>
        <div style={{ ...typography.body.regular,
          color: palette.grayscale.white,
          position: 'absolute',
          top: '50%',
          transform: 'translateY(-50%)',
          pointerEvents: 'none',
          userSelect: 'none',
          width: '100%',
          padding: '0 10px',
          textAlign: isOnline ? 'left' : 'right'
        }}>
          {isOnline ? 'Online' : 'Offline'}
        </div>
        <div className={classes.thumb} />
      </div>
    </ButtonBase>
  );
}

export default OnlineSwitch;
