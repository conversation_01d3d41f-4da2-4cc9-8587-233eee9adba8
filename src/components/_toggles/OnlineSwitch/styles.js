import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles(() => ({
  root: {
    position: 'relative',
    width: 80,
    height: 24,
    cursor: 'pointer',
  },
  thumb: {
    width: 20,
    height: 20,
    backgroundColor: '#fff',
    borderRadius: '50%',
    border: 'none',
    outline: 'none',
    boxShadow: 'none',
    transition: 'transform 0.3s',
    zIndex: 2,
  }
}));

export default useStyles;