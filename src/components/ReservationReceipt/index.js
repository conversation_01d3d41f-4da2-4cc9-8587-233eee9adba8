import React, { useEffect, useState } from "react";
import Button from "@material-ui/core/Button";
import { withTranslation } from "../../../i18n";
import { useDispatch, useSelector } from "react-redux";
import {
  accountSelectors,
  reservationsSelectors,
  restaurantSelectors
} from "../../../redux/selectors";
import { noop, permissionIdentifiers } from "../../utils/const";
import {
  recoverReservation,
  updateCustomer, updateReservation, updateReservationStatus
} from "../../api";
import Loading from "../Loading";
import clsx from "clsx";
import Typography from "@material-ui/core/Typography";
import { AppBar, ButtonBase, Drawer } from "@material-ui/core";
import Avatar from "@material-ui/core/Avatar";
import { Panel } from "../Administration/Panel";
import CustomerEditor from "../Administration/Terminal/CustomerEditor";
import isEmpty from "../../utils/isEmpty";
import {
  CloseDialogIcon20,
  CollapseIcon, MoreOptionsIcon, NoPendingItemsIllustration, ReceiptDarkIcon20, UserIcon20, WarningCircle16
} from "../../utils/icons";
import MenuItem from "@material-ui/core/MenuItem";
import getAcronym from "../../utils/getAcronym";
import useStyles, { useExpendableCardStyles, useMenuStyles, useOverviewStyles } from "./styles";
import { appActions, reservationsActions } from "../../../redux/actions";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import ReservationTimeBadge from "../_tags/ReservationTimeBadge";
import { reservationStatuses } from "../../../redux/constants";
import { useRouter } from "next/router";
import { views } from "../../utils/administrationRoutes";
import MoreOptionsButton from "../_buttons/MoreOptionsButton";
import CustomMenu from "../_popup/CustomMenu";
import moment from "moment";
import BadgeWithIcon from "../_tags/BadgeWithIcon";
import { Confirm } from "../Confirmation";

const ReservationReceipt = ({
  t,
  reservationId,
  isOpen,
  close,
  onUpdate
}) => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const overviewClasses = useOverviewStyles();
  const expendableCardClasses = useExpendableCardStyles();
  const menuClasses = useMenuStyles();

  const router = useRouter();
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "");

  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const hasPermissionToEdit = permissionIds.some(permissionId => [permissionIdentifiers.CAN_MANAGE_RESERVATIONS.value].includes(permissionId));

  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);

  const reservation = useSelector(reservationsSelectors.getReservation);
  const {
    tables,
    customers = [],
    creationTime,
    startTime,
    endTime,
    note,
    status,
    people,
    orderId,
    formattedStartLocalTime,
    formattedEndLocalTime,
    formattedStartEndLocalTime,
    cancelledByCustomer,
    cancelledByAccount
  } = (reservation ?? {});

  const getTablesAsLabels = (tables = []) => {
    if (isEmpty(tables)) {
      return "";
    }

    return tables.map(t => t.label || `#${t.code}`).join("+");
  };

  const [anchor, setAnchor] = useState(null);
  const openMenu = (e) => setAnchor(e.currentTarget);
  const closeMenu = () => setAnchor(null);

  const [customersExpanded, setCustomersExpanded] = useState(true);
  const [detailsExpanded, setDetailsExpanded] = useState(true);

  const toggleCustomers = () => setCustomersExpanded(!customersExpanded);
  const toggleDetails = () => setDetailsExpanded(!detailsExpanded);

  const [isCreatingCustomer, setCreatingCustomer] = useState(false);
  const openCustomerEditor = () => {
    setCreatingCustomer(true);
    setSelectedCustomer({
      anchor: null,
      customer: selectedCustomer.customer
    });
    setAnchor(null);
  };
  const closeCustomerEditor = () => setCreatingCustomer(false);

  const [selectedCustomer, setSelectedCustomer] = useState({
    anchor: null,
    customer: null
  });
  const openCustomerMenu = (e, customer) => setSelectedCustomer({
    anchor: e.currentTarget,
    customer
  });
  const closeCustomerMenu = () => setSelectedCustomer({
    anchor: null,
    customer: null
  });

  const fetch = () => {
    dispatch(reservationsActions.getReservation(reservationId));
  };

  useEffect(() => {
    fetch();
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "unset";
      dispatch(reservationsActions.resetReservation());
    };
  }, [reservationId]);

  const createCustomer = (customer) => {
    if (customer && customer.id) {
      updateCustomer(restaurantId, customer).then(() => {
        fetch();
        closeCustomerEditor();
      }).catch(noop);
    } else {
      updateReservation(restaurantId, reservation, [...reservation.customers, customer])
        .then(() => {
          fetch();
          closeCustomerEditor();
        }).catch(noop);
    }
  };

  if (isEmpty(reservation)) {
    return <Loading />;
  }

  const updateStatus = (st) => updateReservationStatus(restaurantId, reservationId, st).then(close).catch(noop);

  const accept = () => updateStatus(reservationStatuses.CONFIRMED.key);
  const reject = () => updateStatus(reservationStatuses.CANCELLED.key);
  const markAsNoShow = () => updateStatus(reservationStatuses.NO_SHOW.key);
  const start = () => updateStatus(reservationStatuses.IN_PROGRESS.key);
  const completed = () => updateStatus(reservationStatuses.COMPLETED.key);

  const recover = () => {
    const currentTime = moment();
    const recoveringFromReservationId = reservationId;
    const reservationStartTimeIsInThePast = moment(startTime).isBefore(currentTime);
    let updatedReservation = {
      ...reservation,
      id: null,
      status: null
    };
    if (reservationStartTimeIsInThePast) {
      updatedReservation = {
        ...reservation,
        id: null,
        status: null,
        startTime: currentTime
      };
    }
    recoverReservation(restaurantId, updatedReservation, recoveringFromReservationId, customers).then(() => {
      close();
      dispatch(appActions.setNotification("reservation-successfully-recovered", "success"));
    }).catch(() => {
      dispatch(appActions.setNotification("reservation-could-not-be-recovered", "error"));
    });
  };

  const goToTable = () => {
    if (isEmpty(tables)) { return; }
    const tableId = (tables[0] ?? {}).id;
    router
      .push(`${resolvedAsPath}?v=${views.TERMINAL}&&tableId=${tableId}`, undefined, { shallow: true });
  };

  const cancelReservation = () => reject().then(noop).catch(noop);

  const getBottomActions = () => {
    if (reservationStatuses.UNCONFIRMED.key === status) {
      return (
        <div style={{
          paddingLeft: 16,
          paddingRight: 16,
          paddingBottom: 12,
          paddingTop: 12,
          background: palette.grayscale["100"],
          borderTop: `1px solid ${palette.grayscale.divider}`
        }}>
          <div style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between"
          }}>
            <ButtonBase
              disableRipple
              disableTouchRipple
              onClick={reject}
              style={{
                paddingTop: 11,
                paddingBottom: 11,
                width: "100%",
                display: "flex",
                alignItems: "center",
                background: palette.grayscale["100"],
                border: `1px solid ${palette.grayscale["350"]}`,
                borderRadius: 12,
                marginRight: 6
              }}
            >
              <Typography style={{ ...typography.body.medium }}>
                {t("reject")}
              </Typography>
            </ButtonBase>
            <ButtonBase
              key={"update-status"}
              disableRipple
              disableTouchRipple
              onClick={accept}
              style={{
                paddingTop: 12,
                paddingBottom: 12,
                width: "100%",
                display: "flex",
                alignItems: "center",
                background: palette.primary["500"],
                borderRadius: 12,
                marginLeft: 6
              }}
            >
              <Typography style={{
                ...typography.body.medium,
                color: palette.grayscale["100"]
              }}>
                {t("accept")}
              </Typography>
            </ButtonBase>
          </div>
        </div>
      );
    }
    if (reservationStatuses.CONFIRMED.key === status) {
      return (
        <div style={{
          paddingLeft: 16,
          paddingRight: 16,
          paddingBottom: 12,
          paddingTop: 12,
          background: palette.grayscale["100"],
          borderTop: `1px solid ${palette.grayscale.divider}`
        }}>
          <div style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between"
          }}>
            <ButtonBase
              disableRipple
              disableTouchRipple
              onClick={markAsNoShow}
              style={{
                paddingTop: 11,
                paddingBottom: 11,
                width: "100%",
                display: "flex",
                alignItems: "center",
                background: palette.grayscale["100"],
                border: `1px solid ${palette.grayscale["350"]}`,
                borderRadius: 12,
                marginRight: 6
              }}
            >
              <Typography style={{ ...typography.body.medium }}>
                {/*{t(moment(startTime).isAfter(moment.now()) ? 'common-cancel' : 'mark-as-no-show')}*/}
                {t("mark-as-no-show")}
              </Typography>
            </ButtonBase>
            <ButtonBase
              key={"update-status"}
              disableRipple
              disableTouchRipple
              onClick={start}
              style={{
                paddingTop: 12,
                paddingBottom: 12,
                width: "100%",
                display: "flex",
                alignItems: "center",
                background: palette.primary["500"],
                borderRadius: 12,
                marginLeft: 6
              }}
            >
              <Typography style={{
                ...typography.body.medium,
                color: palette.grayscale["100"]
              }}>
                {t("start-reservation")}
              </Typography>
            </ButtonBase>
          </div>
        </div>
      );
    }
    if (reservationStatuses.NO_SHOW.key === status || reservationStatuses.CANCELLED.key === status) {
      return (
        <div style={{
          paddingLeft: 16,
          paddingRight: 16,
          paddingBottom: 12,
          paddingTop: 12,
          background: palette.grayscale["100"],
          borderTop: `1px solid ${palette.grayscale.divider}`
        }}>
          <div style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between"
          }}>
            <ButtonBase
              disableRipple
              disableTouchRipple
              disabled={orderId}
              onClick={recover}
              style={{
                paddingTop: 11,
                paddingBottom: 11,
                width: "100%",
                display: "flex",
                alignItems: "center",
                background: palette.grayscale["100"],
                border: `1px solid ${palette.grayscale["350"]}`,
                borderRadius: 12,
                marginRight: 6,
                opacity: orderId ? 0.4 : 1
              }}
            >
              <Typography style={{ ...typography.body.medium }}>
                {t("recover-this-reservation")}
              </Typography>
            </ButtonBase>
          </div>
        </div>
      );
    }
    if (reservationStatuses.IN_PROGRESS.key === status) {
      return (
        <div style={{
          paddingLeft: 16,
          paddingRight: 16,
          paddingBottom: 12,
          paddingTop: 12,
          background: palette.grayscale["100"],
          borderTop: `1px solid ${palette.grayscale.divider}`
        }}>
          <div style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between"
          }}>
            <ButtonBase
              disableRipple
              disableTouchRipple
              disabled={orderId}
              onClick={accept}
              style={{
                paddingTop: 11,
                paddingBottom: 11,
                width: "100%",
                display: "flex",
                alignItems: "center",
                background: palette.grayscale["100"],
                border: `1px solid ${palette.grayscale["350"]}`,
                borderRadius: 12,
                marginRight: 6,
                opacity: orderId ? 0.4 : 1
              }}
            >
              <Typography style={{ ...typography.body.medium }}>
                {t("send-back-to-confirmed")}
              </Typography>
            </ButtonBase>
            {/* <ButtonBase
              disableRipple
              disableTouchRipple
              onClick={goToTable}
              disabled={isEmpty(tables)}
              style={{
                paddingTop: 12,
                paddingBottom: 12,
                width: "100%",
                display: "flex",
                alignItems: "center",
                background: isEmpty(tables) ? palette.grayscale["400"] : palette.primary["500"],
                borderRadius: 12,
                marginLeft: 6
              }}
            >
              <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                {t('go-to-table')}
              </Typography>
            </ButtonBase>*/}
            <ButtonBase
              disableRipple
              disableTouchRipple
              onClick={completed}
              style={{
                paddingTop: 12,
                paddingBottom: 12,
                width: "100%",
                display: "flex",
                alignItems: "center",
                background: isEmpty(tables) ? palette.grayscale["400"] : palette.primary["500"],
                borderRadius: 12,
                marginLeft: 6
              }}
            >
              <Typography style={{
                ...typography.body.medium,
                color: palette.grayscale["100"]
              }}>
                {t("completed")}
              </Typography>
            </ButtonBase>
          </div>
        </div>
      );
    }
  };

  const getTopActions = () => {
    if ([reservationStatuses.CONFIRMED.key, reservationStatuses.UNCONFIRMED.key].indexOf(status) > -1 && !!onUpdate) {
      return (
        <Button variant="outlined" className={classes.iconButton} onClick={onUpdate} disableRipple>
          <Typography className={classes.outlinedButtonTxt}>
            {t("common-update")}
          </Typography>
        </Button>
      );
    }
    return null;
  };

  return (
    <Drawer
      variant="temporary"
      anchor="right"
      open={isOpen}
      onClose={close}
      className={clsx(classes.drawer)}
      classes={{
        paper: clsx({
          [classes.drawerOpen]: open,
          [classes.drawerClose]: !open
        })
      }}
    >
      <div className={classes.drawerContent}>
        <AppBar
          position="static"
          elevation={0}
          className={clsx(classes.appBar)}
          color="inherit"
          data-transform="translate"
        >
          <div className={classes.headerContainer}>
            <div className={classes.headerLeft}>
              {cancelledByCustomer && (
                <BadgeWithIcon icon={<WarningCircle16 />} title={t("cancelled-by-customer")} />
              )}
              {cancelledByAccount && (
                <BadgeWithIcon icon={<WarningCircle16 />}
                               title={t("cancelled-by-account", { name: cancelledByAccount })} />
              )}
              {!cancelledByAccount && !cancelledByCustomer && (
                <ReservationTimeBadge formattedStartEndLocalTime={formattedStartEndLocalTime} />
              )}
            </div>
            <div className={classes.headerRight}>
              {hasPermissionToEdit && getTopActions()}
              <MoreOptionsButton onClick={openMenu} style={{ marginLeft: 8 }} />
              <Button variant="outlined" className={clsx(classes.iconButton, classes.closeIconButton)}
                      style={{ marginLeft: 8 }} onClick={close} disableRipple>
                <CloseDialogIcon20 />
              </Button>
            </div>
          </div>
        </AppBar>
        <div className={classes.receiptContent}>
          <div className={overviewClasses.wrapper}>
            <div className={overviewClasses.container}>
              <div className={overviewClasses.row}>
                <div className={overviewClasses.column}>
                  <Typography
                    className={overviewClasses.cardSubtitle}>{t("order-history-table-header-table-number")}</Typography>
                  <Typography className={overviewClasses.cardTitle}>{getTablesAsLabels(tables)}</Typography>
                </div>
                <div className={overviewClasses.column}>
                  <Typography
                    className={overviewClasses.cardSubtitle}>{t("order-history-table-header-order-started")}</Typography>
                  <Typography className={overviewClasses.cardTitle}>{formattedStartLocalTime}</Typography>
                </div>
                <div className={overviewClasses.column}>
                  <Typography
                    className={overviewClasses.cardSubtitle}>{t("order-history-table-header-order-ended")}</Typography>
                  <Typography className={overviewClasses.cardTitle}>{formattedEndLocalTime}</Typography>
                </div>
              </div>
              <div className={overviewClasses.row}>
                <div className={overviewClasses.column}>
                  <Typography className={overviewClasses.cardSubtitle}>{t("guests")}</Typography>
                  <Typography className={overviewClasses.cardTitle}>{people}</Typography>
                </div>
                <div className={overviewClasses.column}>
                  <Typography
                    className={overviewClasses.cardSubtitle}>{t("order-history-table-header-order-notes")}</Typography>
                  <Typography className={overviewClasses.cardTitle}>{note || "-"}</Typography>
                </div>
                {!isEmpty(customers) && (customers[0].phone) && (
                  <div className={overviewClasses.column}>
                    <Typography
                      className={overviewClasses.cardSubtitle}>{t("pickup-order-creation-form-phone-field-label")}</Typography>
                    <Typography className={overviewClasses.cardTitle}>{customers[0].phone}</Typography>
                  </div>
                )}
                {!isEmpty(customers) && (customers[0].email) && (
                  <div className={overviewClasses.column}>
                    <Typography className={overviewClasses.cardSubtitle}>{t("reservation-email-label")}</Typography>
                    <Typography className={overviewClasses.cardTitle}>{customers[0].email}</Typography>
                  </div>
                )}
              </div>
            </div>
          </div>
          {!isEmpty(customers) && (
            <div className={overviewClasses.wrapper}>
              <div className={expendableCardClasses.container}>
                <div className={expendableCardClasses.header} onClick={toggleCustomers}>
                  <div className={expendableCardClasses.left}>
                    <UserIcon20 />
                    <Typography className={expendableCardClasses.headerText}>
                      {t("administration-layout-customers-nav-label")}
                    </Typography>
                    <span
                      className={expendableCardClasses.counterBadge}>{isEmpty(customers) ? 0 : customers.length}</span>
                  </div>
                  <div
                    className={clsx(expendableCardClasses.expandBtn, { [expendableCardClasses.rotate]: !customersExpanded })}>
                    <CollapseIcon />
                  </div>
                </div>
                {customersExpanded && customers.map((customer = {}) => (
                  <div className={expendableCardClasses.itemCard}>
                    <div className={expendableCardClasses.customerHeader}>
                      <div className={expendableCardClasses.left}>
                        <Avatar
                          alt={customer.fullName}
                          src={customer.avatar}
                          classes={{ root: expendableCardClasses.customerAvatar }}
                          defaultValue="allO"
                        >
                          {getAcronym(customer.fullName)}
                        </Avatar>
                        <Typography className={expendableCardClasses.customerFullName}>
                          {customer.fullName}
                        </Typography>
                      </div>
                      <div className={expendableCardClasses.right}>
                        <Button variant="outlined"
                                className={clsx(classes.iconButton, expendableCardClasses.paymentsMenuButton)}
                                onClick={(e) => openCustomerMenu(e, customer)} disableRipple>
                          <MoreOptionsIcon />
                        </Button>
                      </div>
                    </div>
                    {customer.id === customers[0].id && (
                      <div className={expendableCardClasses.tags}>
                        <Typography className={expendableCardClasses.tag}>
                          {t("reservation-reference-label")}
                        </Typography>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
          <div className={overviewClasses.wrapper}>
            <div className={expendableCardClasses.container}>
              <div className={expendableCardClasses.header} onClick={toggleDetails}>
                <div className={expendableCardClasses.left}>
                  <ReceiptDarkIcon20 />
                  <Typography className={expendableCardClasses.headerText}>
                    {t("activity-title-order-details")}
                  </Typography>
                </div>
                <div className={expendableCardClasses.right}>
                  <div
                    className={clsx(expendableCardClasses.expandBtn, { [expendableCardClasses.rotate]: !detailsExpanded })}>
                    <CollapseIcon />
                  </div>
                </div>
              </div>
              {detailsExpanded && (
                <div className={expendableCardClasses.detailsBody}>
                  <div style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: 48,
                    marginBottom: 32
                  }}>
                    <NoPendingItemsIllustration />
                    <Typography style={{
                      ...typography.body.medium,
                      marginTop: 15
                    }}>
                      {t("no-items-title")}
                    </Typography>
                    <Typography style={{
                      ...typography.body.medium,
                      color: palette.grayscale["600"],
                      marginTop: 4
                    }}>
                      {t("this-reservation-has-no-orders-yet")}
                    </Typography>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        {hasPermissionToEdit && getBottomActions()}
      </div>
      {isCreatingCustomer && (
        <Panel open={isCreatingCustomer} close={closeCustomerEditor} variant="temporary">
          <CustomerEditor
            close={closeCustomerEditor}
            submit={createCustomer}
            customer={selectedCustomer.customer}
          />
        </Panel>
      )}
      <CustomMenu
        id="receipt-menu"
        anchorEl={anchor}
        keepMounted
        open={Boolean(anchor)}
        onClose={closeMenu}
        classes={{
          paper: menuClasses.menu
        }}
      >
        <MenuItem onClick={openCustomerEditor} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t("assign-customer")}
              </Typography>
            </div>
          </div>
        </MenuItem>
        <MenuItem onClick={goToTable} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t("go-to-table")}
              </Typography>
            </div>
          </div>
        </MenuItem>
        {
          reservationStatuses.CANCELLED.key !== status && (
            <Confirm
            closeMenu={closeMenu}
            title={t("cancel-reservation-title")}
            body={(
              <Typography color="textSecondary" variant="body2">
                <br />
                {t("cancel-reservation-description")}
                <br />
                {t("are-you-sure-cancel-reservation")}
              </Typography>
            )}
          >
            {confirm => (
              <MenuItem onClick={confirm(cancelReservation)} classes={{ root: menuClasses.menuItem }}>
                <div className={menuClasses.content}>
                  <div className={menuClasses.left}>
                    <Typography className={menuClasses.name}>
                      {t("cancel-reservation")}
                    </Typography>
                  </div>
                </div>
              </MenuItem>
            )}
          </Confirm>
        )
      }
      </CustomMenu>
      <CustomMenu
        id="customer-menu"
        anchorEl={selectedCustomer.anchor}
        keepMounted
        open={Boolean(selectedCustomer.anchor)}
        onClose={closeCustomerMenu}
        classes={{
          paper: menuClasses.menu
        }}
      >
        <MenuItem onClick={openCustomerEditor} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t("order-item-edit-btn")}
              </Typography>
            </div>
          </div>
        </MenuItem>
      </CustomMenu>
    </Drawer>
  );
};

export default withTranslation("common")(ReservationReceipt);
