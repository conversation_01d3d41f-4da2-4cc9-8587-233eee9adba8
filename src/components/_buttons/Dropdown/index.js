import React from 'react';
import { ButtonBase, Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import { ChevronDown20 } from "../../../utils/icons";
import palette from "../../../../styles/palette";

const Dropdown = ({ icon, label, onClick, style }) => {
  return (
    <ButtonBase
      data-testid={'operation-view-dropdown-toggle'}
      disableRipple
      disableTouchRipple
      style={{
        display: "flex",
        alignItems: "center",
        paddingRight: 5,
        paddingTop: 5,
        paddingBottom: 5,
        paddingLeft: !!icon ? 7 : 11,
        border: `1px solid ${palette.grayscale["350"]}`,
        borderRadius: 10,
        ...style
      }}
      onClick={onClick}
    >
      <span style={{ marginRight: 4, display: "inline-flex" }}>
        {icon}
      </span>
      <Typography style={{ ...typography.body.medium }}>
        {label}
      </Typography>
      <span style={{ marginLeft: 6, display: "inline-flex" }}>
        <ChevronDown20 />
      </span>
    </ButtonBase>
  )
}

export default Dropdown;
