import React from "react";
import Backdrop from "@material-ui/core/Backdrop";
import CircularProgress from "@material-ui/core/CircularProgress";
import FadeIn from "react-fade-in";
import { makeStyles } from "@material-ui/core/styles";
import clsx from 'clsx';

const useStyles = makeStyles(theme => ({
  backdropRoot: {
    zIndex: theme.zIndex.drawer + 1,
    color: "#333",
    background: "white",
    display: 'flex',
    flexDirection: 'column'
  },
  swipeableViewStyle: {
    minHeight: '60vh',
    position: 'absolute'
  },
  image: {
    maxWidth: 150,
    marginBottom: 30
  },
  imageContainer: {
    textAlign: "center"
  }
}));

const overrideProgressInlineStyle = {
  width: 20,
  height: 20
};

const overrideBackdropInlineStyles = {
  opacity: .4
};

const Loading = ({ inSwipeableView }) => {
  const classes = useStyles();
  return (
    <FadeIn delay={400}>
      <Backdrop classes={{ root: clsx(classes.backdropRoot, { [classes.swipeableViewStyle]: inSwipeableView }) }} style={overrideBackdropInlineStyles} open>
        <CircularProgress color="inherit" style={overrideProgressInlineStyle} />
      </Backdrop>
    </FadeIn>
  );
};

export default Loading;
