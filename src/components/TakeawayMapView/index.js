import { withTranslation } from "../../../i18n";
import { GoogleMap, OverlayView, useLoadScript } from "@react-google-maps/api";
import { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { restaurantSelectors, takeawaySelectors } from "../../../redux/selectors";
import { defaultLogoUrl, mapRestaurantDefaultLogoUrl, smallLogoUrl } from "../../utils/const";
import isEmpty from "../../utils/isEmpty";
import { usePostHog } from "posthog-js/react";
import { useBoardStyles } from "../Takeaway/styles";
import shadows from "../../../styles/shadows";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import { Typography } from "@allo/ui-lib";
import { takeawayPartners } from "../../../redux/constants";
import DishesDrinksBadge from "../_tags/DishesDrinksBadge";
import RevenueBadge from "../_tags/RevenueBadge";
import PaymentStatusBadge from "../_tags/PaymentStatusBadge";
import PickupTimeBadge from "../_tags/PickupTimeBadge";
import { Avatar, ButtonBase, CircularProgress } from "@material-ui/core";
import { PlusIconFilled20 } from "../../utils/icons";
import { getRestaurantsAccounts, updateDriver } from "../../api";

const PARTNERS = {
  UBER_EATS: {
    img: '/apps/uber-eats/img-small.png',
    color: '#000'
  },
  WOLT: {
    img: '/apps/wolt/img-small.png',
    color: '#02bbe3'
  },
  LIEFERANDO: {
    img: '/apps/lieferando/img-small.png',
    color: '#ff8415'
  },
  ALLO: {
    img: smallLogoUrl,
    color: palette.primary["500"]
  }
}

const libraries = ["places"];

const mapContainerStyle = {
  minWidth: "70%",
  height: "100%",
  borderRadius: 16,
  padding: 16,
};

const options = {
  zoom: 12,
  mapId: '2743df2b155d7cc7',
  mapTypeControl: false,
  streetViewControl: false,
  mapTypeControlOptions: { mapTypeIds: [] },
  linksControl: false,
  panControl: false,
  addressControl: false,
  enableCloseButton: false,
  fullScreenControl: false,
  disableDefaultUI: true,
  zoomControlsEnabled: false,
  mapToolbarEnabled: false,
  gestureHandling: "greedy",        // Allows full interaction
  clickableIcons: false
};

const apiKey = process.env.NODE_ENV === "development" ? "AIzaSyDeu6W99or4XsnQ4tz-M_T4lR5xqFXDhcQ" : "AIzaSyDIB1FjsuZP2Jx_8FPimtgj-8XOQ-uQXpU"

const formatDriverName = (driver, small = false) => {
  if (!small) {
    return driver.firstName || driver.lastName || driver?.email?.split?.('@')?.[0] || '';
  }

  let driverName = '';
  if (driver.firstName) {
    driverName = driver.firstName[0];
  }

  if (driver.lastName) {
    driverName += driver.lastName[0];
  }

  if (!driverName && driver.email) {
    const emailParts = driver.email.split('@');
    driverName = `${emailParts[0]} ${emailParts[1]}`;
  }

  return driverName || '';
}

const DriverAvatar = ({ orderDriver, selectedDriver, width = 55, height = 55, loading = false, small = false }) => {
  if (loading) {
    return (
      <span 
        style={{ 
          width, 
          height, 
          color: '#333', 
          display: "flex", 
          alignItems: "center", 
          justifyContent: "center", 
          background: 'white', 
          borderRadius: '50%' 
        }}
      >
        <CircularProgress color="#333" size={width - 10} />
      </span>
    );
  }

  if (orderDriver) {
   return <Avatar
      alt={orderDriver?.firstName}
      src={orderDriver?.pictureUrl ?? orderDriver?.avatar}
      defaultValue="allO"
      style={{ 
        width, 
        height, 
        borderRadius: '50%', 
        backgroundColor: "#929191",
        color: palette.grayscale["100"],
        fontSize: 11,
        lineHeight: '16px',
        fontWeight: 500,
        textTransform: 'uppercase',
        letterSpacing: '0.02em',
        textAlign: "center",
        border: selectedDriver?.id === orderDriver?.id ? `2px solid ${palette.primary["500"]}` : `2px solid #f9f9f9`
      }}
    >
      {formatDriverName(orderDriver, small)}
    </Avatar> 
  }

  if (selectedDriver) {
    return (
      <div style={{ display: "flex", alignItems: "center", justifyContent: "center", background: 'white', borderRadius: '50%', width, height, cursor: 'pointer' }}>
        <span 
          style={{ display: "flex", alignItems: "center", justifyContent: "center" }}
        >
          <PlusIconFilled20
            width={width + 5}
            height={height + 5}
          />
        </span>
      </div>
    )
  }

  return null;
}

const CustomOrderMarker = ({ position, orderId, onClick, item, selectedDriver, onAssignDriver, loading }) => {
  const partner = PARTNERS[item.partnerId || takeawayPartners.ALLO.key];
  
  return (
    <OverlayView
      position={position}
      mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
    >
     <>
        <div
          style={{
            width: '50px',
            height: '50px',
            backgroundColor: partner.color,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            fontSize: '12px',
            cursor: 'pointer',
            transform: 'translate(-50%, -50%)',
            boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
            border: '2px solid white',
            backgroundImage: `url(${item.icon || partner.img})`,
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            backgroundSize: 'cover'
          }}
          onClick={onClick}
        >
          <div style={{ backgroundColor: partner.color, color: 'white', marginTop: -75, borderRadius: 5, padding: '3px' }}>
            #{orderId}
          </div>
        </div>
        <div style={{ position: 'absolute', top: 5, right: -26, borderRadius: '50%' }}>
          <DriverAvatar 
            orderDriver={item?.drivers?.[0]} 
            selectedDriver={selectedDriver} 
            onAssignDriver={onAssignDriver} 
            id={item.id} 
            width={25} 
            height={25} 
            loading={loading}
            small
            onClick={(e) => {
              e.stopPropagation();
              onAssignDriver(id, selectedDriver);
            }}
          />
        </div>
      </>
    </OverlayView>
  );
};


const TakeawayMapView = ({ onClickOrder, t }) => {
  const posthog = usePostHog();
  const mapRef = useRef();
  const boardClasses = useBoardStyles()
  const { items = [] } = useSelector(takeawaySelectors.getMap);
  const [assigningDrivers, setAssigningDrivers] = useState(false);
  const [drivers, setDrivers] = useState([]);
  const [selectedDriver, setSelectedDriver] = useState(null);
  const [currentLoadingOrderId, setCurrentLoadingOrderId] = useState(null);

  const restaurant = useSelector(restaurantSelectors.getRestaurantMemo);
  const restaurantBranchIds = useSelector(restaurantSelectors.getRestaurantsBranchRestaurantIds)
  const isParent = !isEmpty(restaurantBranchIds);

  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: apiKey,
    libraries,
  });

  const handleMapLoaded = (map) => {
    mapRef.current = map;
    const restaurantPosition = { lat: restaurant?.address?.lat, lng: restaurant?.address?.lng }

    if (mapRef.current) {
      posthog.capture("google_maps.loaded");

      new google.maps.Marker({
        position: restaurantPosition,
        map: mapRef.current,
        icon: mapRestaurantDefaultLogoUrl
      });

      mapRef.current.setCenter(restaurantPosition);
      mapRef.current.setZoom(14);
    }
  }

  const handleClickOrder = (order) => {
    if (isEmpty(order)) {
      return;
    }
    const { id, type, status, paymentStatus, isWaiter } = (order || {})
    onClickOrder(id, type, status, paymentStatus, isWaiter)
  }

  const handleToggleAssignDrivers = () => {
    setAssigningDrivers((prev) => !prev);
  }

  const handleSelectDriver = (driver) => {
    return () => {
      setSelectedDriver(prev => prev?.id === driver.id ? null : driver);
    }
  }

  const getDrivers = () => {
    return getRestaurantsAccounts(restaurant.id, 50, 0, false, "", [], "CAN_WORK_AS_DRIVER", true)
      .then(({ data = {} }) => {
        setDrivers(data?.items)
      })
      .catch((e) => {
        console.log(e)
      })
  }

  const onAssignDriver = (orderId, selectedDriver) => {
    setCurrentLoadingOrderId(orderId);
    const driverIds = !isEmpty(selectedDriver)? [selectedDriver.userIds[0]] : [];

    updateDriver(orderId, driverIds)
      .then(getDrivers)
      .finally(() => {
        setTimeout(() => {
          setCurrentLoadingOrderId(null);
        }, 1000);
      })
  };

  useEffect(() => {
    if (!assigningDrivers) {
      setSelectedDriver(null);
      return;
    }

    getDrivers();
  }, [assigningDrivers]);

  if (loadError) return "Error loading maps";
  if (!isLoaded) return "Loading Maps";

  return (
    <div style={{
      height: "100%",
      width: "100%",
      overflow: "hidden",
      borderRadius: 16,
      padding: 16,
      display: "flex",
      flexDirection: "row",
      gap: 16
    }}>
      <GoogleMap
        id="takeaway-map"
        mapContainerStyle={mapContainerStyle}
        zoom={12}
        options={options}
        onLoad={handleMapLoaded}
      >
        {items.map((item) => (
          <CustomOrderMarker
            key={item.id}
            position={{ lat: item?.customer?.address?.lat, lng: item?.customer?.address?.lng }}
            selectedDriver={selectedDriver}
            onAssignDriver={onAssignDriver}
            orderId={item.number}
            item={item}
            onClick={() => {
              if (selectedDriver) {
                return onAssignDriver(item.id, selectedDriver);
              }

              handleClickOrder(item);
            }}
            loading={currentLoadingOrderId === item.id}
          />
        ))}
      </GoogleMap>
      <div className={boardClasses.lane} style={{ maxWidth: "29%" }}>
        <div className={boardClasses.group} style={{ flex: 1, position: 'relative' }}>
          {assigningDrivers ? (
            <>
              <div className={boardClasses.groupHeader} style={{ textAlign: 'center' }}>
                <div>
                  <ButtonBase
                    onClick={handleToggleAssignDrivers}
                    style={{
                      paddingTop: 12,
                      paddingBottom: 12,
                      paddingLeft: 16,
                      paddingRight: 16,
                      display: "flex",
                      alignItems: "center",
                      background: palette.primary["500"],
                      borderRadius: 12
                    }}
                  >
                    <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                      {t('back')}
                    </Typography>
                  </ButtonBase>
                </div>
                <div style={{ display: "flex", alignItems: "center", width: "100%", justifyContent: "center", marginLeft: '-10%' }}>
                  <Typography style={{ ...typography.body.medium }}>{t('assign-drivers')}</Typography>
                </div>
              </div>
              <div style={{ paddingLeft: 16, paddingRight: 12, paddingBottom: 12 }}>
                <div>
                  <Typography style={{ ...typography.body.medium }}>{t('select-driver')}</Typography>
                </div>
                <div style={{ display: "flex", flexWrap: "nowrap", gap: 10, marginLeft: 0, marginTop: 8, paddingRight: 20, height: 67, overflowX: "scroll", overflowY: "hidden", maxWidth: "100%" }}>
                  {drivers.map((currentDriver) => {
                    return (
                      <ButtonBase
                        onClick={handleSelectDriver(currentDriver)}
                        style={{ minWidth: 55, height: 55, borderRadius: "50%", background: 'white'}}
                      >
                        <Avatar
                          alt={currentDriver.firstName}
                          src={currentDriver.preferences?.avatarUrl ?? currentDriver.avatar}
                          defaultValue="allO"
                          style={{ 
                            minWidth: "55px", 
                            minHeight: "55px", 
                            borderRadius: '50%', 
                            backgroundColor: !currentDriver.preferences?.avatarUrl && "#929191",
                            color: palette.grayscale["100"],
                            fontSize: 11,
                            lineHeight: '16px',
                            fontWeight: 500,
                            textTransform: 'uppercase',
                            letterSpacing: '0.02em',
                            textAlign: "center",
                            border: selectedDriver?.id === currentDriver.id ? `2px solid ${palette.primary["500"]}` : `2px solid #E8E7E6`,
                            overflowWrap: "anywhere",
                          }}
                        >
                          {currentDriver.firstName || currentDriver.lastName || currentDriver?.email?.split?.('@')?.[0] || ''}
                        </Avatar>
                      </ButtonBase>
                    )
                  })} 
                </div>
              </div>
            </>
          ): (
            <div className={boardClasses.groupHeader}>
              <div style={{ display: "flex", alignItems: "center" }}>
                <Typography style={{ ...typography.body.medium }}>{t('order-title')} {items?.length}</Typography>
              </div>
              <div>
                <ButtonBase
                  onClick={handleToggleAssignDrivers}
                  style={{
                    paddingTop: 12,
                    paddingBottom: 12,
                    paddingLeft: 16,
                    paddingRight: 16,
                    display: "flex",
                    alignItems: "center",
                    background: palette.primary["500"],
                    marginLeft: 12,
                    borderRadius: 12
                  }}
                >
                  <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                    {t('assign-drivers')}
                  </Typography>
                </ButtonBase>
              </div>
            </div>
          )}
          {!isEmpty(items) && (
            <div className={boardClasses.cards} style={{ paddingBottom: 200, position: "relative" }}>
              {items.map(
                (order) => {
                  const {
                    id,
                    number,
                    pickupTime,
                    takeawayDate,
                    numberOfDishes,
                    isNew,
                    numberOfDrinks,
                    customer = {},
                    total,
                    status,
                    type,
                    paymentStatus,
                    partnerId = takeawayPartners.ALLO.key,
                    partnerOrderInfo = {},
                    estimatedPreparationTime,
                    duration = 0,
                    remainingDuration = 0,
                    isWaiter,
                    restaurant = {},
                    drivers: orderDrivers = [],
                    ...rest
                  } = order;

                  const { name, logoUrl } = restaurant;

                  const partnerOrderNumber = !isEmpty(partnerOrderInfo) && partnerOrderInfo.orderNumber;
                  const partnerName = partnerId ? takeawayPartners[partnerId || takeawayPartners.ALLO.key].i18nKey : "";

                  return (
                    <div 
                      key={id} 
                      className={boardClasses.updatedCard} 
                      style={{  background: palette.grayscale["100"], ...shadows.base, borderRadius: 12 }}
                      onClick={() => {
                        if (selectedDriver) {
                          return onAssignDriver(id, selectedDriver);
                        }
    
                        handleClickOrder(order);
                      }}
                    >
                      <div style={{ display: "flex", alignItems: "center", flexDirection: "row", justifyContent: "space-between", padding: 12 }}>
                        <div style={{ display: "flex", alignItems: "center", flexDirection: "row", justifyContent: "space-between" }}>
                          <div>
                            <div style={{ display: "flex", alignItems: "flex-start" }}>
                              <div style={{ display: "inline-flex", marginRight: 4 }}>
                                {takeawayPartners[partnerId || takeawayPartners.ALLO.key].icon}
                              </div>
                              <Typography style={{ ...typography.body.medium }}>
                                #{number}
                                {partnerOrderNumber && partnerName ? ` (${t(partnerName)} #${partnerOrderNumber})` : ""}{" "}
                                {customer && customer.fullName}
                              </Typography>
                              {!isWaiter && isNew && (
                                <div style={{ marginLeft: 4 }}>
                                  <Badge label={t("newly-added")} color="ACTION" />
                                </div>
                              )}
                            </div>
                            <div style={{ display: "flex", alignItems: "center", marginTop: 6, marginLeft: -1 }}>
                              <DishesDrinksBadge dishes={numberOfDishes} drinks={numberOfDrinks} />
                              <div style={{ marginLeft: 4 }}>
                                <RevenueBadge revenue={total} />
                              </div>
                              <div style={{ marginLeft: 4 }}>
                                <PaymentStatusBadge status={paymentStatus} />
                              </div>
                            </div>
                            <div style={{ display: "flex", alignItems: "center", flexWrap: "wrap" }}>
                              <div style={{ marginTop: 6 }}>
                                <PickupTimeBadge orderType={type} date={takeawayDate} time={pickupTime} partnerId={partnerId} status={status} />
                              </div>
                            </div>
                          </div>
                        </div>
                        {/* driver */}
                        <DriverAvatar 
                          orderDriver={orderDrivers?.[0]} 
                          selectedDriver={selectedDriver} 
                          onAssignDriver={onAssignDriver} 
                          id={id} 
                          loading={currentLoadingOrderId === id} 
                          onClick={(e) => {
                            e.stopPropagation();
                            onAssignDriver(id, selectedDriver);
                          }}
                        />
                      </div>
                      {isParent && (
                        <div style={{ display: "flex", alignItems: "center", borderTop: `1px solid ${palette.grayscale.border}`, padding: 12, marginTop: 12  }}>
                          <div style={{ display: "flex", alignItems: "center", gap: 4 }}>
                            <Avatar
                              alt={name}
                              src={logoUrl || defaultLogoUrl}
                              defaultValue="allO"
                              style={{ width: "24px", height: "24px", borderRadius: 8 }}
                            >
                            {name}
                            </Avatar>

                            <Typography style={{ ...typography.body.medium }}>{name}</Typography>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                }
              )}
            </div>
          )}
          {selectedDriver && (
            <div style={{ height: 60, position: 'absolute', bottom: 0, left: 0, right: 0, background: 'white', textAlign: 'center', paddingTop: 20 }}>
              <Typography style={{ ...typography.body.medium }}>{t('select-items-to-connect')} {formatDriverName(selectedDriver)}</Typography>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default withTranslation("common")(TakeawayMapView);
