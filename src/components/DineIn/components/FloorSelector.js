import React from 'react'
import isEmpty from '../../../utils/isEmpty';
import { operationViews } from '../../../../redux/constants';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import { useSelector } from 'react-redux';
import { dineinSelectors } from '../../../../redux/selectors';

const FloorSelector = ({
  floors,
  isMobile,
  classes,
  selectedFloor,
  setSelectorFloorAndUpdateLayout
}) => {
  const view = useSelector(dineinSelectors.getView);
  return (
    <React.Fragment>
      {
        !(isEmpty(floors)) &&
        !isMobile &&
        (floors.length > 1) &&
        (view === operationViews.PLAN.key) && (
          <div className={classes.bottomTabs}>
            <div className={classes.tabsWrapper}>
              <Tabs
                data-transform="translate"
                value={selectedFloor}
                onChange={(_, newValue) => setSelectorFloorAndUpdateLayout(newValue)}
                aria-label="Vertical tabs example"
                variant="scrollable"
                scrollButtons="off"
                TabIndicatorProps={{ children: <div /> }}
                classes={{
                  root: classes.tabsRoot
                }}
              >
                {(floors.length > 1) && floors.map(({ id, name }, index) => (
                  <Tab
                    disableRipple
                    data-transform="translate"
                    value={index}
                    key={id || 'other'}
                    label={name}
                  />
                ))}
              </Tabs>
            </div>
          </div>
        )}
    </React.Fragment>
  )
}

export default FloorSelector;
