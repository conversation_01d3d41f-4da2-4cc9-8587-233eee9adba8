import React, { useEffect, useState } from "react";
import clsx from "clsx";
import { ButtonBase, Radio } from "@material-ui/core";
import Typography from "@material-ui/core/Typography";
import MenuItem from "@material-ui/core/MenuItem";
import {withTranslation } from '../../../../../i18n';
import { CaretDownIcon, CaretUpIcon} from "../../../../utils/icons";
import {useMenuStyles, useRadioButtonStyles} from "./styles";
import Button from "@material-ui/core/Button";
import {
  dataManipulationTypes,
  dataManipulationTypesOptions
} from "../../../../utils/const";
import isEmpty from "../../../../utils/isEmpty";
import preventEventPropagation from "../../../../utils/preventEventPropagation";
import typography from "../../../../../styles/typography";
import CustomMenu from "../../../_popup/CustomMenu";
import Badge from "../../../_tags/Badge";
import palette from "../../../../../styles/palette";

function RadioButton(props) {
  const classes = useRadioButtonStyles();
  
  return (
    <Radio
      className={classes.root}
      disableRipple
      color="default"
      checkedIcon={<span className={clsx(classes.icon, classes.checkedIcon)} />}
      icon={<span className={classes.icon} />}
      {...props}
    />
  );
}

const DineInPropertiesMenu = ({ t, filters, sortBys, properties, initialProperties, callback }) => {
  const menuClasses = useMenuStyles();
  
  const [anchor, setAnchor] = useState(null);
  const openMenu = (e) => setAnchor(e.currentTarget);
  const closeMenu = () => setAnchor(null);
  
  const [optionType, setOptionType] = useState(null);
  const resetOptionType = () => setOptionType(null);
  
  useEffect(() => {
    if (!isEmpty(filters) && isEmpty(sortBys) && isEmpty(properties)) {
      setOptionType(dataManipulationTypes.FILTER.value)
    }
    
    if (isEmpty(filters) && !isEmpty(sortBys) && isEmpty(properties)) {
      setOptionType(dataManipulationTypes.SORT.value)
    }
    
    if (isEmpty(filters) && isEmpty(sortBys) && !isEmpty(properties)) {
      setOptionType(dataManipulationTypes.PROPERTIES.value)
    }
    
  }, [!!filters, !!sortBys, !!properties])
  
  const [extendedFilter, setExtendedFilter] = useState(null);
  const extendFilter = (val) => () => setExtendedFilter(val);
  
  const [selectedFilters, setSelectedFilters] = useState({});
  const selectFilter = (key, value) => () => {
    const updatedFilters = {...selectedFilters, [key]: value};
    setSelectedFilters(updatedFilters)
    callback(updatedFilters);
  }
  const resetFilters = (e) => {
    preventEventPropagation(e);
    setSelectedFilters({});
    callback({});
  };
  
  const [selectedSorting, setSelectedSorting] = useState(null);
  
  const [selectedProperties, setSelectedProperties] = useState(initialProperties ?? []);
  const selectProperty = (value) => () => {
    let updatedProperties = selectedProperties;
    const max = 2;
    
    if (!isEmpty(updatedProperties) && updatedProperties.indexOf(value) > -1) {
      updatedProperties = updatedProperties.filter(v => v !== value)
    } else if (isEmpty(updatedProperties) || updatedProperties.length < max) {
      updatedProperties = updatedProperties.concat([value])
    } else if (!isEmpty(updatedProperties) && updatedProperties.indexOf(value) === -1 && updatedProperties.length === max) {
      updatedProperties.shift();
      updatedProperties = updatedProperties.concat([value])
    }
    
    setSelectedProperties(updatedProperties)
  }
  
  const renderOptions = (key, options) => options.map(({ value, label }) => (
    <MenuItem key={value} onClick={selectFilter(key, value)} classes={{ root: menuClasses.menuItemRoot }} disableRipple>
      <div className={menuClasses.content}>
        <div>
          <Typography className={clsx(menuClasses.option, { [menuClasses.selected]: (selectedFilters[key] === value) })}>
            {label}
          </Typography>
        </div>
        <div className={menuClasses.right}>
          <RadioButton checked={selectedFilters[key] === value} />
        </div>
      </div>
    </MenuItem>
  ))
  
  const renderFilters = () => {
    if (isEmpty(filters)) {
      return null;
    }
    return filters.map(({ value, i18nKey, options = [] }) => (
      [
        <MenuItem
          key={value}
          onClick={extendFilter(value)}
          classes={{ root: menuClasses.menuItemRoot }}
          className={clsx({ [menuClasses.borderBottom]: extendedFilter === value })}
          disableRipple
        >
          <div className={menuClasses.content}>
            <div>
              <Typography className={menuClasses.option}>
                {t(i18nKey)}
              </Typography>
            </div>
            <div className={menuClasses.right}>
              {/*<Typography className={menuClasses.selection}>{selectedFilters[value]}</Typography>*/}
              {(extendedFilter === value) ? <CaretUpIcon /> : <CaretDownIcon/>}
            </div>
          </div>
        </MenuItem>,
        (extendedFilter === value) && renderOptions(value, options),
      ]
    ));
  }
  
  const renderProperties = () => {
    if (isEmpty(properties)) {
      return null;
    }
    
    return (
      <MenuItem
        key={"property-option-types"}
        classes={{ root: menuClasses.styleFreeMenuItem }}
        disableRipple
      >
        <div style={{ paddingLeft: 4, paddingRight: 4, paddingTop: 12, paddingBottom: 4 }}>
          <div style={{ marginBottom: 12, paddingLeft: 8, paddingRight: 8 }}>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
              {t('select-up-to-count', { count: 2 })}
            </Typography>
          </div>
          <div style={{ display: "flex", flexWrap: "wrap", maxWidth: 340 }}>
            {properties.map(({ icon, activeIcon, value, i18nKey }) => {
              const selected = selectedProperties.indexOf(value) > -1
              return (
                <ButtonBase key={value} className={clsx(menuClasses.property, { [menuClasses.selectedProperty]: selected })} disableRipple disableTouchRipple onClick={selectProperty(value)}>
                  {selected ? activeIcon : icon}
                  <Typography style={{ ...typography.body.regular, color: selected ? palette.grayscale["100"] : palette.grayscale["600"], marginLeft: 2 }}>
                    {t(i18nKey)}
                  </Typography>
                </ButtonBase>
              )
            })}
          </div>
        </div>
      </MenuItem>
    )
  }
  
  const OptionType = () => {
    const selectedOptionType = dataManipulationTypes[optionType];
    const { value, icon, i18nKey } = (selectedOptionType ?? {});
    return (
      [
        <MenuItem
          key={`option-type-menu-item-${value}`}
          classes={{ root: menuClasses.menuItemRoot }}
          className={clsx(menuClasses.borderBottom)}
          disableRipple
          onClick={resetOptionType}
        >
          <div className={menuClasses.content}>
            <div className={menuClasses.contentWithIcon}>
              {icon}
              <Typography className={clsx(menuClasses.option, menuClasses.optionWithIcon)}>
                {t(i18nKey)}
              </Typography>
            </div>
            {value === dataManipulationTypes.FILTER.value && (
              <Button onClick={resetFilters} variant="text" className={menuClasses.clearBtn} disableRipple disableFocusRipple disableTouchRipple>
                {t('clear')}
              </Button>
            )}
          </div>
        </MenuItem>,
        (value === dataManipulationTypes.FILTER.value) && renderFilters(),
        (value === dataManipulationTypes.PROPERTIES.value) && renderProperties()
      ]
    )
  }
  
  const renderOptionTypes = () => dataManipulationTypesOptions.map(({ value, icon, i18nKey }) => (
    <MenuItem
      key={"date-type-menu-item"}
      classes={{ root: menuClasses.menuItemRoot }}
      disableRipple
      disabled={
        (value === dataManipulationTypes.FILTER.value && isEmpty(filters)) ||
        (value === dataManipulationTypes.PROPERTIES.value && isEmpty(properties)) ||
        (value === dataManipulationTypes.SORT.value && isEmpty(sortBys))}
      onClick={() => setOptionType(value)}
    >
      <div className={menuClasses.content}>
        <div className={menuClasses.contentWithIcon}>
          {icon}
          <Typography className={clsx(menuClasses.option, menuClasses.optionWithIcon)}>
            {t(i18nKey)}
          </Typography>
        </div>
      </div>
    </MenuItem>
  ))
  
  const MoreOptions = () => {
    if (optionType) {
      return <OptionType />;
    }
    
    
    return (
      [
        <MenuItem
          key={"date-type-menu-item"}
          classes={{ root: menuClasses.menuItemRoot }}
          className={clsx(menuClasses.borderBottom)}
          disableRipple
        >
          <div className={menuClasses.content}>
            <div>
              <Typography className={clsx(menuClasses.option, menuClasses.optionTitle)}>
                {t('more-options')}
              </Typography>
            </div>
          </div>
        </MenuItem>,
        renderOptionTypes(),
      ]
    )
  }
  
  return (
    <div>
        {!isEmpty(initialProperties) && (
          <ButtonBase style={{ width: "100%", display: "flex", alignItems: "center", justifyContent: "space-between", border: `1px solid ${palette.grayscale.border}`, borderRadius: 10, paddingTop: 5, paddingBottom: 5, paddingRight: 0, paddingLeft: 7, marginRight: 12 }}
                      onClick={openMenu}>
            <div className={menuClasses.content}>
              <div style={{ display: "flex", alignItems: "center" }}>
                {dataManipulationTypes.PROPERTIES.icon}
                <Typography className={clsx(menuClasses.option, menuClasses.optionWithIcon)}>
                  {t(dataManipulationTypes.PROPERTIES.i18nKey)}
                </Typography>
                <div style={{ display: "inline-flex", marginLeft: 4 }}>
                  <Badge quantity={initialProperties.length} />
                </div>
              </div>
            </div>
          </ButtonBase>
        )}
      <CustomMenu
        id="group-by-picker-menu"
        anchorEl={anchor}
        keepMounted
        open={Boolean(anchor)}
        onClose={() => {
          closeMenu();
          if (callback && properties) {
            callback(selectedFilters, selectedSorting, selectedProperties)
          }
        }}
        classes={{
          list: menuClasses.list,
          paper: menuClasses.paper
        }}
      >
        <div>
          {/* enclosing div fixes "Function components cannot be given refs" */}
          <MoreOptions />
        </div>
      </CustomMenu>
    </div>
  )
}

export default withTranslation("common")(DineInPropertiesMenu);
