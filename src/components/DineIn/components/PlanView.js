import React from 'react';
import isEmpty from '../../../utils/isEmpty';
import { dineInViewProperties, operationViews } from '../../../../redux/constants';
import { useSelector } from 'react-redux';
import { dineinSelectors, ordersSelectors } from '../../../../redux/selectors';
import GridLayoutWrapper from '../GridLayoutWrapper';
import clsx from 'clsx';
import DinningTable from '../../floorPlan/DinningTable';
import useStyles from '../styles';
import { views } from '../../../utils/administrationRoutes';
import { ButtonBase, Typography } from '@material-ui/core';
import RevenueBadgeTaken from '../../_tags/RevenueBadgeTaken';
import RevenueBadgeActive from '../../_tags/RevenueBadgeActive';
import DishesDrinksBadgeTaken from '../../_tags/DishesDrinksBadgeTaken';
import DishesDrinksBadgeActive from '../../_tags/DishesDrinksBadgeActive';
import DurationBadgeTaken from '../../_tags/DurationBadgeTaken';
import DurationBadgeActive from '../../_tags/DurationBadgeActive';
import ProgressBadgeTaken from '../../_tags/ProgressBadgeTaken';
import ProgressBadgeActive from '../../_tags/ProgressBadgeActive';
import CustomersBadgeTaken from '../../_tags/CustomersBadgeTaken';
import CustomersBadgeActive from '../../_tags/CustomersBadgeActive';
import { PaymentPartialBadgeIcon20 } from '../../../utils/icons';
import { useRouter } from 'next/router';
import withTableStatus from '../HoC/withTableStatus';

const PlanView = ({
  layout,
  tables,
  setTables,
  getTableStatus,
  conditionalBind
}) => {
  const router = useRouter();
  const view = useSelector(dineinSelectors.getView);
  const orders = useSelector(ordersSelectors.getOrdersByTableId);
  const classes = useStyles();
  const properties = useSelector(dineinSelectors.getProperties);
  const showNoProperties = isEmpty(properties);
  const showAmount = !showNoProperties && properties.indexOf(dineInViewProperties.AMOUNT.value) > -1;
  const showItems = !showNoProperties && properties.indexOf(dineInViewProperties.ITEMS.value) > -1;
  const showDuration = !showNoProperties && properties.indexOf(dineInViewProperties.DINNING_TIME.value) > -1;
  const showProgress = !showNoProperties && properties.indexOf(dineInViewProperties.REMAINING_TIME.value) > -1;
  const showCustomers = !showNoProperties && properties.indexOf(dineInViewProperties.CUSTOMERS.value) > -1;
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "")

  return (
    <React.Fragment>
      {!isEmpty(layout) && (view === operationViews.PLAN.key) && (
        <GridLayoutWrapper layout={layout}>
          {layout.map(item => {
            const table = tables.find(t => t.id === item.i) || {};
            const order = table.status === 'TAKEN' ? (isEmpty(orders[table.id]) ? null : orders[table.id][0]) : null;
            const hasOrder = !isEmpty(order);
            const tableStylingStatus = getTableStatus(table);
            return (
              <DinningTable
                key={item.i}
                status={tableStylingStatus.status}
                height={item.h}
                width={item.w}
                circular={table.layout && table.layout.shape === 'CIRCLE'}
                className={clsx(
                  classes.cursor)
                }
              >
                <ButtonBase
                  id={table.id}
                  // disableTouchRipple
                  className={clsx(classes.buttonBox, classes.gridButtonBox)}
                  onClick={() =>
                    router.push(`${resolvedAsPath}?v=${views.TERMINAL}&tableId=${item.i}`, undefined, { shallow: true })
                  }
                  {...conditionalBind(table)}
                >
                  <Typography className={classes.tableLabel}>
                    {table.label || `#${table.code}`}
                  </Typography>
                  {showAmount && !isEmpty(order) && (
                    <div className={classes.propertyContent}>
                      {tableStylingStatus.status === "TAKEN" ? (
                        <RevenueBadgeTaken revenue={order.total} />
                      ) : (
                        <RevenueBadgeActive revenue={order.total} />
                      )}
                    </div>
                  )}
                  {showItems && hasOrder && (!!order.dishCount || !!order.beverageCount) && (
                    <div className={classes.propertyContent}>
                      {tableStylingStatus.status === "TAKEN" ? (
                        <DishesDrinksBadgeTaken dishes={order.dishCount} drinks={order.beverageCount} />
                      ) : (
                        <DishesDrinksBadgeActive dishes={order.dishCount} drinks={order.beverageCount} />
                      )}
                    </div>
                  )}
                  {showDuration && hasOrder && (!!order.duration) && (
                    <div className={classes.propertyContent}>
                      {tableStylingStatus.status === "TAKEN" ? (
                        <DurationBadgeTaken minutes={order.duration} />
                      ) : (
                        <DurationBadgeActive minutes={order.duration} />
                      )}
                    </div>
                  )}
                  {showProgress && hasOrder && (
                    <div className={classes.propertyContent}>
                      {tableStylingStatus.status === "TAKEN" ? (
                        <ProgressBadgeTaken remaining={order.remainingDuration} />
                      ) : (
                        <ProgressBadgeActive remaining={order.remainingDuration} />
                      )}
                    </div>
                  )}

                  {showCustomers && hasOrder && order.numberOfParticipants > 0 && (
                    <div className={classes.propertyContent}>
                      {tableStylingStatus.status === "TAKEN" ? (
                        <CustomersBadgeTaken customers={order.numberOfParticipants} />
                      ) : (
                        <CustomersBadgeActive customers={order.numberOfParticipants} />
                      )}
                    </div>
                  )}

                  {!isEmpty(order) && !!order.paymentOption && (
                    <div className={classes.paymentsWrapper}>
                      <PaymentPartialBadgeIcon20 />
                    </div>
                  )}
                </ButtonBase>
              </DinningTable>
            )
          })}
        </GridLayoutWrapper>
      )}
    </React.Fragment>
  )
}

export default withTableStatus(PlanView);
