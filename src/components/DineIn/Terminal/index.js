import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import moment from "moment";
import clsx from "clsx";
import { withTranslation } from "../../../../i18n";
import Typography from "@material-ui/core/Typography";
import AppBar from "@material-ui/core/AppBar";
import Button from "@material-ui/core/Button";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import Menus from "../../Administration/Terminal/Menus";
import Orders from "../../Administration/Terminal/Orders";
import { views } from "../../../utils/administrationRoutes";
import OrdersDrawer from "../../Administration/Terminal/OrdersDrawer";
import { ButtonContainer } from "../../Containers";
import { MainButton } from "../../Buttons";
import BottomPanel from "../../BottomPanel";
import {
  addCourse,
  addDiscount,
  addItemDiscount,
  approveOrderParticipant,
  cancelOrder,
  cancelOrderItem,
  clearOrder,
  clearOrderItem,
  confirmOfflineOrderItems,
  createOneTimeItem,
  createTableAutoApproval,
  createTemplateSetMenuItemsBulk,
  deleteDiscount,
  deleteItemDiscount,
  deleteTableAutoApproval,
  orderItemOffline,
  orderOngoingItemOffline,
  promoteParticipant, reoderOrderItem,
  reserveAsWaiter,
  updateReservationStatus
} from "../../../api";
import { appActions, menusActions, tablesActions, terminalActions } from "../../../../redux/actions";
import {
  accountSelectors,
  applicationSelectors,
  configurationSelectors,
  floorsSelectors,
  menusSelectors,
  restaurantSelectors,
  tablesSelectors,
  terminalSelectors
} from "../../../../redux/selectors";
import { status as orderItemStatus } from "../../../utils/categorizeOrderItems";
import isEmpty from "../../../utils/isEmpty";
import {
  ArrowLeftDark20,
  CaretRightIcon,
  ChevronUpDown16,
  DoubleCheckmarkIcons20,
  FileIcon20,
  MagnifierIconWhite20,
  ScanQRCodeIcon,
  TerminalMenuIcon,
  WarningCircle16
} from "../../../utils/icons";
import { noop, terminalMobileNavigationTabs, websocketStates } from "../../../utils/const";
import { zeroPadNumber } from "../../../utils/zeroPadNumber";
import ReceiptInformation from "../../Administration/ReceiptInformation";
import { orderTypes, paymentStatuses, reservationStatuses } from "../../../../redux/constants";
import Checkout from "../Checkout";
import TerminalPaymentModal from "../../_popup/TerminalPaymentModal";
import PaymentMissingBar from "../../_navigation/PaymentMissingBar";
import NetworkIssueBar from "../../_navigation/NetworkIssueBar";
import ModeBar from "../../_navigation/ModeBar";
import TableQRModal from "../../_popup/TableQRModal";
import { ButtonBase, MenuItem } from "@material-ui/core";
import MoreOptionsButton from "../../_buttons/MoreOptionsButton";
import Badge from "../../_tags/Badge";
import OneTimeModal from "../../_popup/OneTimeItemModal";
import CustomMenu from "../../_popup/CustomMenu";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import useStyles, { useAppBarStyles, useMenuItemStyles, useMenuStyles } from "./styles";
import MobileOrderingFlowConfigurationModal from "../../_popup/MobileOrderingFlowConfigurationModal";
import OrderEventEmitterForSUNMI from "../../OrderEventEmitterForSUNMI";
import ConfirmationDialog from "../../_popup/ConfirmationDialog";

let ordersTimer, tableTimer;

const Terminal = ({ t, tableId, ws }) => {
  const classes = useStyles();
  const appBarClasses = useAppBarStyles();
  const menuClasses = useMenuStyles();
  const menuItemClasses = useMenuItemStyles();

  const dispatch = useDispatch();
  const router = useRouter();

  const is700 = useSelector(applicationSelectors.getIsStripeDevice)
  const orderType = orderTypes.DINE_IN.key
  const isMobileMediaQuery = useMediaQuery('(max-width:750px)');
  const isMobile = isMobileMediaQuery || is700

  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "")

  const [isCheckoutOpen, setIsCheckoutOpen] = useState(null);
  const [paymentChannel, setPaymentChannel] = useState('CASH')
  const [addingItem, setAddingItem] = useState(false)
  const [startingReservation, setStartingReservation] = useState(false)
  const [addingDiscount, setAddingDiscount] = useState(false);

  const account = useSelector(accountSelectors.getAccountMemo);
  const { id: accountId, preferences } = (account || {});
  const { terminalMenuOrientation } = (preferences || {})

  const restaurantId = useSelector(restaurantSelectors.getRestaurantId)
  const restaurantHasScanToOrderEnabled = useSelector(restaurantSelectors.getRestaurantHasScanToOrder)
  const configuration = useSelector(configurationSelectors.getConfigurationMemo);
  const useUpdatedTerminal = useSelector(configurationSelectors.getConfigurationUseUpdatedTerminal)

  const table = useSelector(state => tablesSelectors.getTableMemo(state, tableId));
  const { orderId, reservation = {}, hasScanToOrderAutoApproval, status: tableStatus } = table;
  const order = useSelector(terminalSelectors.getOrderMemo);
  const { paymentStatus } = (order || {});
  const tablesById = useSelector(floorsSelectors.getTablesByIdMemo);
  const embeddedInApp = useSelector(applicationSelectors.getIsEmbedded);
  const groupBy = useSelector(terminalSelectors.getGroupByMemo);

  const menus = useSelector(state => menusSelectors.getMenusForTerminalMemo(state, orderType));

  const selectedCourse = useSelector(terminalSelectors.getSelectedCourseMemo)
  const setSelectedCourse = (val) => dispatch(terminalActions.setSelectedCourse(val))
  const { courseConfig = {}, enableTakeawayInDineIn, displayOrderedItemsTotalsInMenu } = (configuration || {})
  const { enabled: coursesEnabled, courses = [] } = (courseConfig || {})
  const isDineIn = orderType === orderTypes.DINE_IN.key;
  const showCourses = coursesEnabled && !isEmpty(courses) && isDineIn
  const [selectingCourse, setSelectingCourse] = useState(false)

  const [isOrdersDrawerOpen, setOrdersDrawerOpen] = useState(false);
  const showOrdersDrawer = () => setOrdersDrawerOpen(true);
  const hideOrdersDrawer = () => setOrdersDrawerOpen(false);
  const [isFindMenuItemDrawerOpen, setFindMenuItemDrawerOpen] = useState(false);
  const showFindMenuItemDrawer = () => setFindMenuItemDrawerOpen(true);

  const [isTableDetailsOpen, setTableDetails] = useState(null);
  const showTableDetailsDrawer = (id) => () => setTableDetails(id);
  const hideTableDetailsDrawer = () => setTableDetails(null);

  const [isTableQROpen, setTableQR] = useState(null);
  const showTableQRModal = () => setTableQR(true);
  const hideTableQRModal = () => setTableQR(false);

  const [anchor, setAnchor] = useState(null);
  const openTableActionsMenu = (e) => setAnchor(e.currentTarget);
  const closeTableActionsMenu = () => setAnchor(null);

  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);

  const handleCancelOrder = () => {
    closeTableActionsMenu();
    setShowCancelConfirmation(true);
  };

  const confirmCancelOrder = () => {
    cancelOrder(orderId)
      .then(close)
      .catch(noop);

    setShowCancelConfirmation(false);
  };

  const cancelCancelOrder = () => {
    setShowCancelConfirmation(false);
  };
  

  const [activeMobileNavigationTab, setActiveMobileNavigationTab] = useState(terminalMobileNavigationTabs.MENU);
  const isMenuActive = activeMobileNavigationTab === terminalMobileNavigationTabs.MENU;
  const isPendingActive = activeMobileNavigationTab === terminalMobileNavigationTabs.PENDING;
  const isToPayActive = activeMobileNavigationTab === terminalMobileNavigationTabs.TO_PAY;

  const [addingOneTimeItem, setAddingOneTimeItem] = useState(false);

  // ordering flow type specifies if the item in this dine in order should be sent with
  // flag toGo to backend
  const [takeawayOrderingFlowType, setTakeawayOrderingFlowType] = useState(false);
  const showOrderingFlowConfiguration = showCourses || enableTakeawayInDineIn

  const fetchTable = () => {
    if (tableId) {
      dispatch(tablesActions.getTable(restaurantId, tableId))
    }
  };

  const fetchOrder = () => {
    if (tableId) {
      dispatch(terminalActions.getOrderByTableId(tableId, !!isTableDetailsOpen));
    }
  }

  const resolveTerminal = () => {
    if (ws === websocketStates.DISCONNECTED) {
      fetchTable();
    }
    fetchOrder();
  }

  {/* get table and reset table */}
  useEffect(() => {
    dispatch(tablesActions.getTable(restaurantId, tableId))

    return () => {
      dispatch(tablesActions.resetTable(tableId))
    }
  }, [tableId, restaurantId])

  {/* get order and reset order */}
  useEffect(() => {
    if (tableStatus === "TAKEN" && orderId) {
      dispatch(terminalActions.getOrder(orderId, null, !!isTableDetailsOpen))
    } else {
      dispatch(terminalActions.reset())
    }

    return () => {
      dispatch(terminalActions.reset())
    }
  }, [orderId, tableStatus, isTableDetailsOpen])

  {/* get menus */}
  useEffect(() => {
    dispatch(menusActions.getMenusForTerminal(restaurantId, orderTypes.DINE_IN.key, null))
  }, [])

  {/* poll order and table */}
  useEffect(() => {
    if (tableId && (ws === websocketStates.DISCONNECTED)) {
      tableTimer = setInterval(() => fetchTable(), 3000);
    }

    if (tableTimer && ws === websocketStates.CONNECTED) {
      clearInterval(tableTimer);
    }

    ordersTimer = setInterval(() => fetchOrder(), 3000);

    return () => {
      if (tableTimer) {
        clearInterval(tableTimer);
      }
      if (ordersTimer) {
        clearInterval(ordersTimer);
      }
    }
  }, [ws, tableId, isTableDetailsOpen]);

  {/* get order for group by */}
  useEffect(() => {
    fetchOrder();
  }, [groupBy])

  {/* reset orders drawer */}
  useEffect(() => {
    if (!order || isEmpty(order) && isMobile && isOrdersDrawerOpen) {
      hideOrdersDrawer()
    }
  }, [order, isMobile])

  {/* reset checkout */}
  useEffect(() => {
    if (isCheckoutOpen && (paymentStatus === "PAID" || isEmpty(order))) {
      onPaymentEnd()
    }
  }, [isCheckoutOpen, paymentStatus])

  useEffect(() => {
    /**
     * if table is free and checkout is open, close checkout
     */
    if (isCheckoutOpen && (tableStatus === "FREE")) {
      onPaymentEnd()
    }
  }, [tableStatus])

  const handleStartReservation = (reservationId) => {
    setStartingReservation(true)

    updateReservationStatus(restaurantId, reservationId, reservationStatuses.IN_PROGRESS.key)
      .then(fetchTable).catch(noop).finally(() => {
      setStartingReservation(false)
    });
  }

  const handleAdd = (itemRequestData) => {
    const itemRequest = itemRequestData
    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }

    if (addingItem) {
      return;
    }

    // we check for undefined since other components might send true or false
    if (itemRequest && takeawayOrderingFlowType && typeof itemRequest.toGo === 'undefined') {
      itemRequest.toGo = true
    }

    if (table.orderId) {
      orderItemOffline(table.orderId, itemRequest).then(() => {
        if(isMobile && isFindMenuItemDrawerOpen){
          // TODO: Add a check in the config to enable/disable the notifications.
          // TODO: Leaving this commented out for future reference.
          // dispatch(appActions.setNotification('item-added-notification', 'success'))
        }
        resolveTerminal()
      }).catch(() => {})
    } else {
      setAddingItem(true)
      reserveAsWaiter(tableId).then(({ data }) => {
        dispatch(tablesActions.getTableSuccess(data))
        setAddingItem(false);
        orderItemOffline(data.orderId, itemRequest).then(resolveTerminal).catch(() => {})
      }).catch(() => {})
    }
  };

  const handleAddOneTime = (itemRequestData) => {
    const itemRequest = itemRequestData

    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }

    if (addingItem) {
      return;
    }

    // we check for undefined since other components might send true or false
    if (itemRequest && takeawayOrderingFlowType && typeof itemRequest.toGo === 'undefined') {
      itemRequest.toGo = true
    }

    if (table.orderId) {
      createOneTimeItem(restaurantId, table.orderId, itemRequest).then(resolveTerminal).catch(() => {})
    } else {
      setAddingItem(true)
      reserveAsWaiter(tableId).then(({ data }) => {
        dispatch(tablesActions.getTableSuccess(data))
        setAddingItem(false);
        createOneTimeItem(restaurantId, data.orderId, itemRequest).then(resolveTerminal).catch(() => {})
      }).catch(() => {})
    }
  };

  const handleAddOngoing = (itemRequest) => {
    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }
    if (addingItem) {
      return;
    }

    if (table.orderId) {
      orderOngoingItemOffline(table.orderId, itemRequest).then(resolveTerminal).catch(() => {})
    } else {
      setAddingItem(true)
      reserveAsWaiter(tableId).then(({ data }) => {
        dispatch(tablesActions.getTableSuccess(data))
        setAddingItem(false);
        orderOngoingItemOffline(data.orderId, itemRequest).then(resolveTerminal).catch(() => {})
      }).catch(() => {})
    }
  };

  const handleAddTemplateSetMenu = (templateSetMenuId, itemRequest) => {
    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }
    if (addingItem) {
      return;
    }
    if (itemRequest && takeawayOrderingFlowType && typeof itemRequest.toGo === 'undefined') {
      itemRequest = itemRequest.map(item => ({
        ...item,
        toGo: true
      }))
    }
    
    if (table.orderId) {
      createTemplateSetMenuItemsBulk(restaurantId, table.orderId, templateSetMenuId, itemRequest).then(resolveTerminal).catch(() => {})
    } else {
      setAddingItem(true)
      reserveAsWaiter(tableId).then(({ data }) => {
        dispatch(tablesActions.getTableSuccess(data))
        setAddingItem(false);
        createTemplateSetMenuItemsBulk(restaurantId, data.orderId, templateSetMenuId, itemRequest).then(resolveTerminal).catch(() => {})
      }).catch(() => {})
    }
  }

  const handleAddReorder = (orderItemId) => {
    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }

    if (addingItem) {
      return;
    }

    if (table.orderId) {
      reoderOrderItem(restaurantId, table.orderId, orderItemId).then(() => {
        if(isMobile && isFindMenuItemDrawerOpen){
          // TODO: Add a check in the config to enable/disable the notifications.
          // TODO: Leaving this commented out for future reference.
          // dispatch(appActions.setNotification('item-added-notification', 'success'))
        }
        resolveTerminal()
      }).catch(() => {})
    }
  };

  const onAddDiscount = (orderId, amount, percentage, code, discountPin) => {
    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }

    setAddingDiscount(true)

    addDiscount(orderId, amount, percentage, code, discountPin)
      .then(({ data }) => {
        dispatch(terminalActions.setOrder(data))
        resolveTerminal()
      })
      .catch(({ response = {} }) => {
        const { status } = (response || {});
        if (status === 422) {
          dispatch(appActions.setNotification('wrong-pin-when-adding-discount', "error"))
        }
      })
      .finally(() => {
        setAddingDiscount(false);
      })
  };

  const onAddItemDiscount = (orderId, orderItemId, amount, percentage, code, discountPin) => {
    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }

    addItemDiscount(orderId, orderItemId, amount, percentage, code, discountPin)
      .then(({ data }) => {
        dispatch(terminalActions.setOrder(data))
        resolveTerminal()
      })
      .catch(({ response = {} }) => {
        const { status } = (response || {});
        if (status === 422) {
          dispatch(appActions.setNotification('wrong-pin-when-adding-discount', "error"))
        }
      });
  };

  const onDeleteDiscount = (orderId) => {
    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }

    setAddingDiscount(true);

    deleteDiscount(orderId)
      .then(({ data }) => {
        dispatch(terminalActions.setOrder(data))
        resolveTerminal()
      })
      .catch(() => {})
      .finally(() => {
        setAddingDiscount(false);
      })
  };

  const onDeleteItemDiscount = (orderId, itemId) => {
    // do not allow items to be added while payment is ongoing
    if (isCheckoutOpen) {
      return
    }

    deleteItemDiscount(orderId, itemId)
      .then(({ data }) => {
        dispatch(terminalActions.setOrder(data))
        resolveTerminal()
      })
      .catch(() => {})
  };

  const onApproveOrder = (orderId) => {
    if (order) {
      const { participants = [] } = order;
      approveOrderParticipant(orderId, participants[0].id)
        .then(({}) => resolveTerminal())
        .catch(() => {})
    }
  };

  const onApproveParticipant = (orderId, participantId) => {
    approveOrderParticipant(orderId, participantId)
      .then(({}) => resolveTerminal())
      .catch(() => {})
  };

  const onPromoteParticipant = (orderId, participantId) => {
    promoteParticipant(orderId, participantId)
      .then(({}) => resolveTerminal())
      .catch(() => {})
  };

  const onConfirmOrderItems = (orderId) => {
    const promise = confirmOfflineOrderItems(orderId);
    promise
      .then(({}) => {
        resolveTerminal()
      })
      .catch(() => {})
  };

  const onClearOrderItem = (orderId, orderItemId) => {
    clearOrderItem(orderId, orderItemId)
      .then(({ data }) => resolveTerminal())
      .catch(() => {})
  };

  const onCancelOrderItem = (orderId, orderItemId, qtd, notes, selectedNotes) => {
    cancelOrderItem(orderId, orderItemId, qtd, notes, selectedNotes)
      .then(({ data }) => {
        resolveTerminal()
      })
      .catch(() => {})
  };

  const onClearTable = (orderId) => {
    clearOrder(orderId)
      .then(({ data = {} }) => resolveTerminal())
      .catch(() => {})
  };

  const onStartPayment = (orderId) => {
    if (!addingDiscount) {
      setIsCheckoutOpen(orderId)
    }
  };

  const onPaymentEnd = () => {
    setIsCheckoutOpen(null);
    setPaymentChannel('CASH');
    resolveTerminal();
    fetchTable();
  };

  const toggleAutoApprove = () => {
    if (hasScanToOrderAutoApproval) {
      deleteTableAutoApproval(restaurantId, tableId).then(() => fetchTable()).catch(noop);
    } else {
      createTableAutoApproval(restaurantId, tableId).then(() => fetchTable()).catch(noop);
    }
  }

  const setGroupBy = (value) => {
    dispatch(terminalActions.setGroupBy(value))
  }

  const onAddCourse = (orderId, courseName) => {
    addCourse(restaurantId, orderId, courseName)
      .then(({ data = {} }) => {
        resolveTerminal();
        dispatch(appActions.setNotification('course-added', "success"))
      })
      .catch(() => {
        dispatch(appActions.setNotification('could-not-add-course', "error"))
      })
  }

  const showMenus = !isMobile || !useUpdatedTerminal || (isMobile && useUpdatedTerminal && isMenuActive)
  const showPending = isMobile && useUpdatedTerminal && isPendingActive
  const showToPay = isMobile && useUpdatedTerminal && isToPayActive

  const onBack = () => {
    if (isCheckoutOpen) {
      setIsCheckoutOpen(null)
    } else {
      router
        .push(`${resolvedAsPath}?v=${views.DINE_IN}`, undefined, { shallow: true })
        .catch(() => {})
    }
  }

  const goToReceipts = () => {
    router.push({
      pathname: `${resolvedAsPath}`,
      query: { v: views.RECEIPTS, data: JSON.stringify({ tableId, accountId, orderType, sortField: 'ORDER_NUMBER', sortDirection: 'DESC' })}
    }, undefined, { shallow: true }).then(() => {});
  }

  const getBackBtn = () => {
    return (
      <ButtonBase data-testid={'table-view-back-btn'} disableRipple disableTouchRipple style={{ padding: 5, borderRadius: 10, border: `1px solid ${palette.grayscale["350"]}`, marginRight: 10 }} onClick={onBack}>
        <ArrowLeftDark20 />
      </ButtonBase>
    )
  }

  const getBiggerBackBtn = () => {
    return (
      <ButtonBase disableRipple disableTouchRipple style={{ padding: 11, borderRadius: 12, border: `1px solid ${palette.grayscale["350"]}`, background: palette.grayscale["100"], marginRight: 8 }} onClick={onBack}>
        <ArrowLeftDark20 />
      </ButtonBase>
    )
  }

  const renderMobileNavigation = () => {
    if (!isMobile) {
      return null;
    }

    const isMobileAndIsShowingPendingOrToPay = isMobile && (showPending || showToPay)

    let pendingItemsCount = null;
    let toPayItemsCount = null;

    let menuName = t("menu")
    const onMenuTab = activeMobileNavigationTab === terminalMobileNavigationTabs.MENU
    if (showOrderingFlowConfiguration && onMenuTab) {
      if (showCourses && selectedCourse) {
        const { courseName, courseNumber } = (selectedCourse || {})
        menuName = courseName || `${t('course')} ${courseNumber}`
      }

      if (takeawayOrderingFlowType) {
        menuName = t('takeaway')
      }
    }

    if (!isEmpty(order)) {
      const { items = [] } = (order ?? {})
      pendingItemsCount = (items || [])
        .filter(i => i.status === orderItemStatus.UNCONFIRMED && i.offline)
        .length;
      toPayItemsCount = (items || [])
        .filter(i => i.status === orderItemStatus.CONFIRMED)
        .length;
    }

    return (
      <div style={{ paddingTop: 12, paddingLeft: 12, paddingRight: 12, paddingBottom: isMobileAndIsShowingPendingOrToPay ? (embeddedInApp ? 24 + 12 : 12) : (embeddedInApp ? 24 : 0), borderTop: `1px solid ${palette.grayscale["300"]}`, background: palette.transparency.light["90"] }}>
        <div style={{ width: "100%", display: "flex", alignItems: "center", overflowX: "auto" }}>
          {getBiggerBackBtn()}
          <ButtonBase
            style={{
              height: 44,
              flex: 1,
              paddingLeft: 8,
              paddingRight: 8,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              borderRadius: 10,
              background: isMenuActive ? palette.grayscale["300"] : "transparent"
            }}
            disableRipple disableTouchRipple
            onClick={() => {
              setActiveMobileNavigationTab(terminalMobileNavigationTabs.MENU);
              if (showOrderingFlowConfiguration && onMenuTab) {
                setSelectingCourse(true)
              }
            }}
          >
            <div style={{ display: "flex" }}>
              <TerminalMenuIcon color={"#929191"} />
              <Typography style={{ ...typography.body.medium, whiteSpace: "nowrap" }}>
                {menuName}
              </Typography>
              {showOrderingFlowConfiguration && onMenuTab && (
                <div style={{ display: "flex", marginLeft: 4, alignItems: "center" }}>
                  <ChevronUpDown16 color={"#929191"} />
                </div>
              )}
            </div>
            {/*<div style={{ display: "flex", flexDirection: "column" }}>*/}
            {/*  */}
            {/*  {showCourses && (*/}
            {/*    <div>*/}
            {/*      <Typography style={{ ...typography.body.regular, fontSize: 8, lineHeight: '10px' }}>*/}
            {/*        Select Course*/}
            {/*      </Typography>*/}
            {/*    </div>*/}
            {/*  )}*/}
            {/*</div>*/}
          </ButtonBase>
          <ButtonBase style={{
            flex: 1,
            padding: 12,
            borderRadius: 10,
            background: isPendingActive ? palette.grayscale["300"] : "transparent"
          }} disableRipple disableTouchRipple
                      onClick={() => setActiveMobileNavigationTab(terminalMobileNavigationTabs.PENDING)}>
            <Typography style={{ ...typography.body.medium, whiteSpace: "nowrap" }}>
              {t('pending')}
            </Typography>
            <div style={{ marginLeft: 6 }}>
              <Badge quantity={pendingItemsCount || 0} color={!!pendingItemsCount ? "ACTION" : isPendingActive ? "GREY_300_BG" : "GREY_LIGHT_BG"} />
            </div>
          </ButtonBase>
          <ButtonBase style={{ flex: 1, padding: 12, borderRadius: 10, background: isToPayActive ? palette.grayscale["300"] : "transparent" }} disableRipple disableTouchRipple onClick={() => setActiveMobileNavigationTab(terminalMobileNavigationTabs.TO_PAY)}>
            <Typography style={{ ...typography.body.medium, whiteSpace: "nowrap" }}>
              {t('to-pay')}
            </Typography>
            <div style={{ marginLeft: 6 }}>
              <Badge quantity={toPayItemsCount || 0} color={!!toPayItemsCount ? "ACTION" : isToPayActive ? "GREY_300_BG" : "GREY_LIGHT_BG"} />
            </div>
          </ButtonBase>
        </div>
      </div>
    )
  }

  const renderMainAction = () => {
    if (!isMobile) {
      return null
    }

    if (!isEmpty(order)) {
      const { items = [] } = (order ?? {})
      const unconfirmedItems = (items || []).filter(i => i.status === orderItemStatus.UNCONFIRMED);
      const unconfirmedItemsTotal = unconfirmedItems.reduce((a = 0, b) => a + b.total, 0)
      const unconfirmedItemsSize = unconfirmedItems.reduce((a = 0, b) => a + b.qtd, 0)
      return (
        <BottomPanel size='s' isSticky useDiv={useUpdatedTerminal}>
          <div className={classes.bottomActions}>
            <ButtonContainer>
              <div className={classes.compositeBtnLayout}>
                <MainButton
                  className={classes.compositeBtnContainer}
                  disableElevation
                  onClick={showOrdersDrawer}
                >
                  <div className={classes.compositeBtnLayout}>
                    <div className={clsx({ [classes.compositeBtnExtend]: !unconfirmedItemsSize && !unconfirmedItemsTotal })}>
                      {t('view-order-drawer-btn')}
                      {!!unconfirmedItemsSize && (
                        <span className={classes.compositeBtnCountLabel}>
                        {t('view-order-drawer-items-quantity-btn-label', { count: unconfirmedItemsSize })}
                      </span>
                      )}
                    </div>
                    {!!unconfirmedItemsTotal && (
                      <div className={classes.compositeBtnSidebar}>
                        <div className={classes.compositeBtnDivider} />
                        <div className={classes.compositeBtnPriceLabel}>
                          {`${unconfirmedItemsTotal.toFixed(2)} €`}
                        </div>
                      </div>
                    )}
                  </div>
                </MainButton>
                {!useUpdatedTerminal && (
                  <div className={classes.compositeSecondaryBtn}>
                    <MainButton onClick={showFindMenuItemDrawer}>
                      <MagnifierIconWhite20 />
                    </MainButton>
                  </div>
                )}
              </div>
            </ButtonContainer>
          </div>
        </BottomPanel>
      )
    } else {
      if (!useUpdatedTerminal) {
        return (
          <BottomPanel size='s' isSticky>
            <div className={classes.bottomActions}>
              <ButtonContainer>
                <MainButton onClick={showFindMenuItemDrawer} startIcon={<MagnifierIconWhite20 />}>
                  {t('search-menu-btn')}
                </MainButton>
              </ButtonContainer>
            </div>
          </BottomPanel>
        )
      }
    }
  }

  const getOrderInfo = () => {
    const { label: tableLabel = "", code: tableCode = "" } = (table || {});
    let info = t('order');
    let orderStartTime = null;
    let orderNumber = "";

    info = `${isMobile ? '' : t('table')} ${tableLabel || tableCode}`

    if (order && order.creationTime) {
      const isToday = moment(order.creationTime).isSame(new Date(), 'day');
      orderStartTime = moment(order.creationTime).format(isToday ? 'HH:mm' : 'DD MMM HH:mm')
    }
    if (order && order.number) {
      orderNumber = zeroPadNumber(order.number, 3)
    }

    if (isMobile) {
      return (
        <div className={appBarClasses.orderInfo}>
          {isCheckoutOpen ? (
            <Typography style={{ ...typography.medium.regular, whiteSpace: "nowrap", color: palette.grayscale["600"] }}>{info}</Typography>
          ) : <Typography style={{ ...typography.medium.medium, whiteSpace: "nowrap" }}>{info}</Typography>}
          {orderNumber && !isCheckoutOpen && (
            <div style={{ display: "flex" }}>
              <div style={{ marginLeft: 6, marginRight: 6 }}>
                <Typography style={{ ...typography.medium.medium, color: palette.grayscale["500"] }}>
                  ·
                </Typography>
              </div>
              <Typography style={{ ...typography.medium.medium, whiteSpace: "nowrap" }}>#{orderNumber}</Typography>
            </div>
          )}
          {isCheckoutOpen && (
            <div style={{ display: "flex", alignItems: "center" }}>
              <CaretRightIcon />
              <Typography style={{ ...typography.medium.medium, whiteSpace: "nowrap" }}>
                {t('payment')}
              </Typography>
            </div>
          )}
          {orderStartTime && !isCheckoutOpen && (
            <div className={appBarClasses.badge}>
              <Typography className={appBarClasses.badgeContent}>{orderStartTime}</Typography>
            </div>
          )}
        </div>
      )
    }

    return (
      <div className={appBarClasses.orderInfo}>
        <Typography style={{ ...typography.body.medium, whiteSpace: "nowrap" }}>{info}</Typography>
        {orderStartTime && !isCheckoutOpen && (
          <div className={appBarClasses.badge}>
            <Typography className={appBarClasses.badgeContent}>{orderStartTime}</Typography>
          </div>
        )}
        {orderNumber && (
          <div className={appBarClasses.badge}>
            <Typography className={appBarClasses.badgeContent}>{orderNumber}</Typography>
          </div>
        )}
      </div>
    )
  }

  const getTerminalPayment = () => {
    if (paymentStatus === paymentStatuses.PAYING.key) {
      const oId = isEmpty(table) ? orderId : table.orderId
      return <TerminalPaymentModal titleI18n={"allo-pay-terminal"} open orderId={oId } />
    }

    return null
  }

  const getOrdersView = (staticOrderingStatusGroup = null) => (
    <Orders
      orderType={orderType}
      account={account}
      handleAdd={handleAdd}
      handleAddOngoing={handleAddOngoing}
      handleAddReorder={handleAddReorder}
      table={table}
      order={order}
      reservation={reservation}
      onApproveOrder={onApproveOrder}
      onApproveParticipant={onApproveParticipant}
      onPromoteParticipant={onPromoteParticipant}
      onConfirmOrderItems={onConfirmOrderItems}
      onClearOrderItem={onClearOrderItem}
      onCancelOrderItem={onCancelOrderItem}
      onAddDiscount={onAddDiscount}
      onAddItemDiscount={onAddItemDiscount}
      onDeleteDiscount={onDeleteDiscount}
      onDeleteItemDiscount={onDeleteItemDiscount}
      onClearTable={onClearTable}
      onStartPayment={onStartPayment}
      paymentChannel={paymentChannel}
      setPaymentChannel={(v) => setPaymentChannel(v)}
      configuration={configuration}
      isMobile={isMobile}
      handleStartReservation={handleStartReservation}
      startingReservation={startingReservation}
      staticOrderingStatusGroup={staticOrderingStatusGroup}
      setGroupBy={setGroupBy}
      addCourse={onAddCourse}
      hasTakeawayOrderingFlow={takeawayOrderingFlowType}
      setTakeawayOrderingFlow={setTakeawayOrderingFlowType}
    />
  )

  const getTableHeader = () => {
    return (
      <div style={{ borderBottom: `1px solid ${palette.grayscale.divider}` }}>
        <AppBar
          position="fixed"
          elevation={0}
          color="inherit"
          data-transform="translate"
        >
          <NetworkIssueBar />
          <ModeBar />
          <div className={appBarClasses.content}>
            <div className={appBarClasses.left}>
              {(!isMobile || (isMobile && !useUpdatedTerminal)) && getBackBtn()}
              {getOrderInfo()}
            </div>
            <div className={appBarClasses.center}>
            </div>
            {!isCheckoutOpen && (
              <div className={appBarClasses.right}>
                {restaurantHasScanToOrderEnabled && (
                  <Button className={clsx(appBarClasses.btn, { [appBarClasses.btnWithLabel]: !isMobile })}
                          onClick={toggleAutoApprove}
                          style={hasScanToOrderAutoApproval ? { background: palette.secondary.green["100"], borderColor: palette.secondary.green["100"] } : null}
                          disableRipple
                  >

                    <DoubleCheckmarkIcons20 color={hasScanToOrderAutoApproval ? palette.secondary.green["600"] : (isMobile ? palette.grayscale.black : palette.grayscale["400"])}/>
                    {!isMobile && (
                      <Typography className={clsx(appBarClasses.btnLabel, appBarClasses.btnLabelWithIcon)}>
                        {t('pre-approve')}
                      </Typography>
                    )}
                  </Button>
                )}
                {!isMobile && [
                  restaurantHasScanToOrderEnabled ? (
                    <Button variant="outlined" className={clsx(appBarClasses.btn, appBarClasses.btnWithLabel)}
                            onClick={showTableQRModal}
                            disableRipple
                    >
                      <ScanQRCodeIcon />
                      <Typography className={clsx(appBarClasses.btnLabel, appBarClasses.btnLabelWithIcon)}>
                        {t('qr-code')}
                      </Typography>
                    </Button>
                  ) : null,
                  isEmpty(order) ? null : (
                    <div className={clsx({ [appBarClasses.btnDivider]: restaurantHasScanToOrderEnabled })}>
                      <Button variant="outlined" className={clsx(appBarClasses.btn, appBarClasses.btnWithLabel)} onClick={showTableDetailsDrawer(order.id)} disableRipple>
                        <FileIcon20 />
                        <Typography className={clsx(appBarClasses.btnLabel, appBarClasses.btnLabelWithIcon)}>
                          {t('table-details')}
                        </Typography>
                      </Button>
                    </div>
                  )
                ]}
                {[
                  <MoreOptionsButton onClick={openTableActionsMenu} style={{ marginLeft: 8 }} />,
                  <CustomMenu id="table-actions" anchorEl={anchor} keepMounted open={Boolean(anchor)} onClose={closeTableActionsMenu} classes={{ list: menuClasses.list, paper: menuClasses.paper }}>
                    <div>
                      {!isEmpty(order) && (
                        <MenuItem classes={{ root: menuItemClasses.menuItemRoot }} disableRipple onClick={() => {
                          closeTableActionsMenu();
                          setTableDetails(order.id)
                        }}>
                          <div className={menuItemClasses.content}>
                            <div style={{ display: "flex", alignItems: "center" }}>
                              <FileIcon20 />
                              <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                                {t('table-details')}
                              </Typography>
                            </div>
                          </div>
                        </MenuItem>
                      )}
                      <MenuItem classes={{ root: menuItemClasses.menuItemRoot }} disableRipple onClick={() => {
                        closeTableActionsMenu();
                        setAddingOneTimeItem(true)
                      }}>
                        <div className={menuItemClasses.content}>
                          <div style={{ display: "flex", alignItems: "center" }}>
                            <FileIcon20 />
                            <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                              {t('add-one-time-item')}
                            </Typography>
                          </div>
                        </div>
                      </MenuItem>
                      {!isEmpty(tableId) && isMobile && (
                        <MenuItem classes={{ root: menuItemClasses.menuItemRoot }} disableRipple onClick={() => {
                          closeTableActionsMenu();
                          goToReceipts()
                        }}>
                          <div className={menuItemClasses.content}>
                            <div style={{ display: "flex", alignItems: "center" }}>
                              <FileIcon20 />
                              <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                                {t('see-latest-table-receipts')}
                              </Typography>
                            </div>
                          </div>
                        </MenuItem>
                      )}
                      {isMobile && (
                        <MenuItem classes={{ root: menuItemClasses.menuItemRoot }} disableRipple onClick={() => {
                          closeTableActionsMenu();
                          showTableQRModal();
                        }}>
                          <div className={menuItemClasses.content}>
                            <div style={{ display: "flex", alignItems: "center" }}>
                              <ScanQRCodeIcon />
                              <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                                {t('qr-code')}
                              </Typography>
                            </div>
                          </div>
                        </MenuItem>
                      )}
                      {/*{!!orderId && <MenuItem classes={{ root: menuItemClasses.menuItemRoot }} disableRipple onClick={handleCancelOrder} disabled={!order?.actions?.includes?.('CANCEL')}>*/}
                      {/*  <div className={menuItemClasses.content}>*/}
                      {/*    <div style={{ display: "flex", alignItems: "center", marginLeft: 2 }}>*/}
                      {/*      <WarningCircle16 color={palette.grayscale["400"]} />*/}
                      {/*      <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>*/}
                      {/*        {t('cancel-order')}*/}
                      {/*      </Typography>*/}
                      {/*    </div>*/}
                      {/*  </div>*/}
                      {/*</MenuItem>}*/}
                    </div>
                  </CustomMenu>
                ]}
              </div>
            )}
          </div>
        </AppBar>
      </div>
    )
  }

  const getContentPadding = () => {
    if (!isMobile || !useUpdatedTerminal) {
      return 12
    }

    if (isMobile) {
      if (useUpdatedTerminal && (showPending || showToPay)) {
        return "0px 0px 12px 0px"
      }
      if (useUpdatedTerminal && showMenus) {
        return "0px 0px 12px 0px"
      }
    }

    return null
  }

  const getMenus = () => {
    return (
      <Menus
        hide={!showMenus}
        menus={menus}
        orderType={orderType}
        orderId={table.orderId || orderId}
        handleAdd={handleAdd}
        handleAddOneTime={handleAddOneTime}
        handleAddOngoing={handleAddOngoing}
        handleAddTemplateSetMenu={handleAddTemplateSetMenu}
        preferences={preferences}
        isMobile={isMobile}
        isFindMenuItemDrawerOpen={isFindMenuItemDrawerOpen}
        setFindMenuItemDrawerOpen={setFindMenuItemDrawerOpen}
        handleSendItemsToKitchen={onConfirmOrderItems}
        displayOrderedItemsTotalsInMenu={displayOrderedItemsTotalsInMenu}
      />
    )
  }

  if (isCheckoutOpen) {
    return (
      <div className={classes.wrapper}>
        <div className={classes.container}>
          {getTableHeader()}
          <div className={clsx(classes.content)}>
            <Checkout groupBy={groupBy} setGroupBy={setGroupBy} endPayment={onPaymentEnd} onBack={onBack} />
          </div>
        </div>
        {getTerminalPayment()}
      </div>
    );
  }

  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        {getTableHeader()}
        <PaymentMissingBar />
        <div className={clsx(classes.content)}>
          <div style={{
            height: '100%',
            overflow: 'hidden',
            position: 'relative',
            display: 'flex',
            flexDirection: terminalMenuOrientation === "RIGHT" ? "row-reverse" : "row",
            padding: getContentPadding(),
            gap: 12
          }}>
            <div style={{ flexGrow: 1, overflow: "hidden" }}>
              {getMenus()}
              {showPending && getOrdersView("PENDING")}
              {showToPay && getOrdersView("ORDERED")}
              {useUpdatedTerminal && renderMobileNavigation()}
            </div>
            {!isMobile && (
              <div style={{
                maxWidth: "100%",
                minWidth: 450,
                overflow: "hidden"
              }}>
                <div className={classes.relative}>
                  {getOrdersView()}
                </div>
              </div>
            )}
          </div>
          {isMobile && isOrdersDrawerOpen && !useUpdatedTerminal && (
            <OrdersDrawer isOpen={isOrdersDrawerOpen} close={hideOrdersDrawer} isMobile={isMobile}>
              {getOrdersView()}
            </OrdersDrawer>
          )}
          {!useUpdatedTerminal && renderMainAction()}
        </div>
        {isTableDetailsOpen && (
          <ReceiptInformation
            orderId={isTableDetailsOpen}
            isOpen={!!isTableDetailsOpen}
            close={hideTableDetailsDrawer}
            callback={noop}
            goBackToFloorplan={onBack}
            tablesById={tablesById}
          />
        )}
        {isTableQROpen && (
          <TableQRModal table={table} open={isTableQROpen} onClose={hideTableQRModal} />
        )}
        {useUpdatedTerminal && addingOneTimeItem && (
          <OneTimeModal
            onClose={() => setAddingOneTimeItem(false)}
            open={addingOneTimeItem}
            submit={handleAddOneTime}
          />
        )}
        {selectingCourse && (
          <MobileOrderingFlowConfigurationModal
            open={selectingCourse}
            onClose={() => setSelectingCourse(false)}
            hasCourses={showCourses}
            takeawayOrderingFlowType={takeawayOrderingFlowType}
            setTakeawayOrderingFlowType={setTakeawayOrderingFlowType}
          />
        )}
      </div>
      {getTerminalPayment()}
      <OrderEventEmitterForSUNMI />
      <ConfirmationDialog
        open={showCancelConfirmation}
        title={t('cancel-this-order')}
        messages={[t('cancel-order-dine-in-confirmation-message'), t('cancel-order-dine-in-confirmation-message-line-2')]}
        confirmText={t('cancel-order')}
        cancelText={t('go-back')}
        onConfirm={confirmCancelOrder}
        onCancel={cancelCancelOrder}
      />
    </div>
  );
};

export default withTranslation('common')(Terminal)
