import { makeStyles } from "@material-ui/core/styles";
import {colors, drawer} from "../../../styles/theme";
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";
import shadows from "../../../styles/shadows";

const useStyles = makeStyles(theme => ({
	wrapper: {
		background: palette.grayscale["100"],
		borderRadius: 12,
		...shadows.base
	},
	container: {
		padding: 12,
	},
	dropdown: {
		borderTop: `1px solid ${palette.grayscale["250"]}`,
	},
	content: {
		display: "flex",
		alignItems: "center",
		justifyContent: "space-between",
		cursor: "pointer"
	},
	avatar: {
		width: 32,
		height: 32,
		color: theme.palette.common.white,
		fontSize: 12,
		lineHeight: '12px',
		fontWeight: 500,
		textTransform: 'uppercase',
		borderRadius: 12
	},
	restaurantName: {
		marginLeft: 8,
		...typography.body.regular
	},
	left: {
		display: "flex",
		alignItems: "center",
	},
	right: {
		display: "flex",
		justifyContent: "flex-end"
	},
	menu: {
		backgroundColor: theme.palette.common.white,
		borderRadius: 12,
		width: `${drawer - 32}px`,
		maxWidth: "100%",
		boxShadow: "0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04)",
		maxHeight: 425 // shows half of the next menu item
	},
	menuItem: {
		padding: 16
	},
	searchBar: {
		margin: "12px 12px 6px 12px"
	}
}));

export default useStyles;
