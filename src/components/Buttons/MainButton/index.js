import React from 'react';
import Button from '../Button';
import useStyles from './styles';

const MainButton = (props) => {
  const classes = useStyles();

  return (
    <Button
      variant="contained"
      {...props}
      classes={{
        root: classes.btn,
        // eslint-disable-next-line react/destructuring-assignment
        ...props.classes
      }}
    />
  );
};

export default MainButton;
