import React from 'react';
import MuiIconButton from '@material-ui/core/IconButton';
import { Typography, withStyles } from "@material-ui/core";
import useStyles from './styles';
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";
import SvgIcon from "@material-ui/core/SvgIcon";

const StyledMuiIconButton = withStyles({
  root: {
    padding: 14,
    // border: `1px solid #D9D9D8`,
    // borderRadius: 12,
  },
})(MuiIconButton);

const PlusIconLight16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <path d="M8 3V13" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13 8L3 8" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

const MinusIconLight16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <path d="M13 8L3 8" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

const NumericalInput = (props) => {
  const classes = useStyles();
  const { counter = 0, disabled, onMore, onLess, ...otherProps } = props;

  return (
    <div>
      <div className={classes.layout} style={{
        border: "1px solid #D8D7D6",
        borderRadius: 12,
        backgroundColor: palette.grayscale["100"],
      }}>
        <StyledMuiIconButton
          {...otherProps}
          disabled={disabled || counter === 0}
          onClick={onLess}
          data-testid={"numerical-input-minus"}
        >
          <MinusIconLight16 />
        </StyledMuiIconButton>
        <div className={classes.counter}>
          <Typography style={{ ...typography.body.regular }}>{counter}</Typography>
        </div>
        <StyledMuiIconButton
          {...otherProps}
          onClick={onMore}
          data-testid={"numerical-input-plus"}
        >
          <PlusIconLight16 />
        </StyledMuiIconButton>
      </div>
    </div>
  );
};

export default NumericalInput;
