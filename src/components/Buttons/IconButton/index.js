import React from 'react';
import MuiIconButton from '@material-ui/core/IconButton';
import { withStyles } from '@material-ui/core';
import { Paragraph } from '../../Text';
import useStyles from './styles';

const StyledMuiIconButton = withStyles({
  root: {
    marginTop: -12,
    marginBottom: -12,
    '&:hover': {
      background: 'transparent'
    },
  },
})(MuiIconButton);

const IconButton = (props) => {
  const classes = useStyles();
  const { children, label } = props;

  return (
    <StyledMuiIconButton {...props} style={label ? { borderRadius: 0 } : null}>
      {children}
      {label && (
        <div className={classes.label}>
          <Paragraph>{label}</Paragraph>
        </div>
      )}
    </StyledMuiIconButton>
  );
};

export default IconButton;
