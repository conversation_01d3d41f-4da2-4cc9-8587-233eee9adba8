import React, { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../i18n";
import Typography from "@material-ui/core/Typography";
import { ButtonBase, ClickAwayListener } from "@material-ui/core";
import Keyboard from "../Keyboard";
import Radio from "../_toggles/Radio";
import Switch from "../_toggles/Switch";
import TipModal from "../_popup/TipModal";
import ScanGiftCardModal from "../_popup/ScanGiftCardModal";
import {
  accountSelectors, applicationSelectors,
  configurationSelectors,
  restaurantSelectors,
  terminalSelectors
} from "../../../redux/selectors";
import {
  defaultPaymentMethods,
  orderTypes,
  partnerIdToPaymentMethodMap,
  paymentMethods,
  paymentStatuses,
  paymentSuggestions,
  receiptModes, splitPaymentModes,
  splitTakeawayPaymentModes,
  splitTakeawayPaymentOptions
} from "../../../redux/constants";
import {
  addDiscount,
  checkout,
  deleteDiscount, deleteGiftCard,
  deletePayment,
  getPaymentTerminals,
  printPayment,
  printTerminalPaymentReceipt,
  resolveCard,
} from "../../api";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import isEmpty from "../../utils/isEmpty";
import { noop, orderItemsGroups, permissionIdentifiers } from "../../utils/const";
import { formatNumber } from "../../utils/formatNumber";
import { removeDuplicates } from "../../utils/ArrayUtils";
import {
  CollapseIcon20Grayscale400,
  EditIcon20,
  PlusIconFilled20,
  ScanQRCodeIcon,
  TerminalMenuIcon, WarningIcon20Red
} from "../../utils/icons";
import { appActions, printersActions, terminalActions } from "../../../redux/actions";
import DiscountModal from "../_popup/DiscountModal";
import Field from "../form/Field";
import shadows from "../../../styles/shadows";
import useStyles, { useBoardStyles } from "./styles";
import TerminalPaymentModal from "../_popup/TerminalPaymentModal";
import TextUpdateModal from "../_popup/TextUpdateModal";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import BottomPanel from "../BottomPanel";
import CheckoutTableSelector from "../CheckoutTableSelector";
import SegmentedControlBaseTabs from "../_tabs/SegmentedControlBaseTabs";
import { status as orderItemStatus } from "../../utils/categorizeOrderItems";
import Checkbox from "../_toggles/Checkbox";
import clsx from "clsx";
import byId from "../../utils/byId";
import CheckoutOrderItem from "../CheckoutOrderItem";
import QuantityInput from "../_input/QuantityInput";
import { Confirm } from "../Confirmation";
import PrintPaymentModal from "../_popup/PrintPaymentModal";
import CreateInvoiceModal from "../_popup/CreateInvoiceModal";
import isValidEmail from "../../utils/isValidEmail";

const Checkout = ({ t, groupBy, setGroupBy }) => {
  const dispatch = useDispatch();
  
  const classes = useStyles();
  const boardClasses = useBoardStyles();
  const isMobile = useMediaQuery('(max-width:750px)');
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant
  
  const is700 = useSelector(applicationSelectors.getIsStripeDevice)
  
  const { configuration = {} } = useSelector(configurationSelectors.getConfiguration);
  const { checkoutAutoClose, receiptOption = {}, allowPagerInputInExpress, partialCheckout } = configuration;
  
  const { order = {} } = useSelector(terminalSelectors.getOrder);
  const { id: orderId, items = [], total = 0, type, discount = {}, deliveryFee, itemsTotal = 0, partnerId, paymentStatus, toGo, payments, totalDue } = (order || {});
  const { amount: discountAmount, promotion } = (discount || {});
  
  const paidAmount = (payments || []).reduce((acc, next) => {
    acc = acc + (next.amount || 0) + (next.tipAmount || 0)
    return acc
  }, 0)
  const [expandedPayment, setExpandedPayment] = useState(null);
  const [selectedPaymentForPrintingId, setSelectedPaymentForPrintingId] = useState(null);
  const togglePaymentExpand = (id) => {
    if (!expandedPayment || (expandedPayment !== id)) {
      setExpandedPayment(id)
    } else {
      setExpandedPayment(null);
    }
  }
  
  const { orderByOrderingStatus = [] } = useSelector(terminalSelectors.getOrderByOrderingStatus);
  const byOrderingStatusById = byId(orderByOrderingStatus);
  const orderedOrderGroup = byOrderingStatusById["ORDERED"] || {}
  const { groups: orderedOrderItemGroups = [] } = (orderedOrderGroup || {})
  
  const { account } = useSelector(accountSelectors.getAccount)
  
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const canDiscountOrder = permissionIds.includes(permissionIdentifiers.CAN_DISCOUNT_ORDER.value)
  
  const restaurantPaymentMethods = restaurant.paymentChannels || defaultPaymentMethods;
  const getPaymentMethod = () => {
    const resolvedPaymentMethodFromPartnerId = partnerIdToPaymentMethodMap[partnerId]
    if (partnerId && resolvedPaymentMethodFromPartnerId && restaurantPaymentMethods.indexOf(resolvedPaymentMethodFromPartnerId) > -1) {
      return resolvedPaymentMethodFromPartnerId
    }
    return is700 ? paymentMethods.ALLO_PAY.key : restaurantPaymentMethods[0]
  }
  const [paymentMethod, setPaymentMethod] = useState(getPaymentMethod());
  const [terminal, setTerminal] = useState(null);
  
  const isPayByInvoiceMethodSelected = paymentMethod === paymentMethods.KAUF_AUF_RECHNUNG.key;
  
  const receiptOptions = ["STANDARD", "BUSINESS", "PDF", "NONE"]
  const selectedReceiptOptionTypeByDefault = isEmpty(receiptOption) ? "STANDARD" : receiptOption.type
  const [selectedReceiptOption, setSelectedReceiptOption] = useState({ type: selectedReceiptOptionTypeByDefault, email: "" })
  
  const [card, setCard] = useState({});
  const { id: cardId, code: cardCode, cashAmount: cardAmount } = card;
  
  const [scanningCard, setScanningCard] = useState(false);
  const startScanningCard = () => setScanningCard(true);
  const stopScanningCard = () => setScanningCard(false);
  
  const onResolveCard = (code) => {
    resolveCard(restaurant.id, code)
      .then(({ data = {} }) => {
        const { status, disabled } = data
        if (status === 'SOLD' && !disabled) {
          setCard(data)
        }
      })
      .catch(() => {})
  }
  
  const removeCard = () => {
    let storedCard = card
    if (storedCard && storedCard.id && storedCard.thirdParty) {
      deleteGiftCard(restaurant.id, storedCard.id).then(() => {}).catch(() => {})
    }
    setCard({});
  }

  const [calculatorAmount, setCalculatorAmount] = useState('');
  
  const calculatorOnClick = (v) => {
    if (calculatorAmount.length < 7) {
      setCalculatorAmount(calculatorAmount + v)
    }
  }
  const calculatorOnDelete = () => {
    if (calculatorAmount) {
      setCalculatorAmount(calculatorAmount.slice(0, -1))
    }
  }
  const calculatorOnSuggestion = (v) => {
    setCalculatorAmount(v + "");
  }
  const getCalculatorAmount = () => {
    return parseInt(calculatorAmount || 0) / 100
  }
  
  const [tipAmount, setTipAmount] = useState(0);
  const [addingTip, setAddingTip] = useState(false);
  const startAddingTip = () => setAddingTip(true);
  const stopAddingTip = () => setAddingTip(false);
  const [changeAmount, setChangeAmount] = useState(0);
  const [asTip, setAsTip] = useState(false);
  const toggleAsTip = () => setAsTip(!asTip);
  const removeTip = () => {
    setTipAmount(0);
    setAsTip(false);
  }
  const [cardDeductedAmount, setCardDeductedAmount] = useState(0);
  
  const [addingDiscount, setAddingDiscount] = useState(false);
  const startAddingDiscount = () => setAddingDiscount(true);
  const stopAddingDiscount = () => setAddingDiscount(false);
  const removeDiscount = () => {
    deleteDiscount(orderId).then(noop).catch(noop).finally(() => setCalculatorAmount(''));
  };
  const onResolveDiscount = (amount, percentage, code, discountPin) => {
    addDiscount(orderId, amount, percentage, code, discountPin).then(() => {
      setCalculatorAmount('');
    }).catch(({ response = {} }) => {
      const { status } = (response || {});
      if (status === 422) {
        dispatch(appActions.setNotification('wrong-pin-when-adding-discount', "error"))
      }
    });
  }
  
  // pay by invoice
  const [addingPayByInvoiceData, setAddingPayByInvoiceData] = useState(false);
  const startAddingPayByInvoiceData = () => setAddingPayByInvoiceData(true);
  const stopAddingPayByInvoiceData = () => setAddingPayByInvoiceData(false);
  const [payByInvoiceData, setPayByInvoiceData] = useState(null)
  const updatePayByInvoiceData = (d) => {
    setPayByInvoiceData(d)
    stopAddingPayByInvoiceData()
  }
  const isPayByInvoiceDataValid = payByInvoiceData
    && payByInvoiceData.customer
    && payByInvoiceData.customer.name
    && payByInvoiceData.customer.email
    && isValidEmail(payByInvoiceData.customer.email)
    && payByInvoiceData.customer.address
    && payByInvoiceData.customer.address.street
    && payByInvoiceData.customer.address.number
    && payByInvoiceData.customer.address.zipCode
    && payByInvoiceData.customer.address.city
    && payByInvoiceData.customer.address.country
  
  useEffect(() => {
    if (isPayByInvoiceMethodSelected && isEmpty(payments)) {
      setAddingPayByInvoiceData(true)
    }
  }, [isPayByInvoiceMethodSelected])
  
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [paymentDueAmount, setPaymentDueAmount] = useState(total);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [autoClose, setAutoClose] = useState(checkoutAutoClose);
  const toggleAutoClose = () => setAutoClose(!autoClose)
  
  const [splitPaymentMode, setSplitPaymentMode] = useState(splitTakeawayPaymentModes.ALL.key)
  const [paymentGroup, setPaymentGroup] = useState("CHECKOUT")
  const showingCheckoutGroup = paymentGroup === "CHECKOUT"
  const showingPaymentsGroup = paymentGroup === "PAYMENTS"
  const openCheckoutGroup = () => setPaymentGroup("CHECKOUT")
  const openPaymentsGroup = () => {
    if (!isEmpty(payments)) {
      setPaymentGroup("PAYMENTS")
    }
  }
  
  useEffect(() => {
    if (isPayByInvoiceMethodSelected) {
      setSplitPaymentMode(splitTakeawayPaymentModes.ALL.key)
    }
  }, [isPayByInvoiceMethodSelected])
  
  const [keyboard, setKeyboard] = useState(false);
  const pager = useSelector(terminalSelectors.getPager);
  const setPager = (pager) => dispatch(terminalActions.setPager(pager))
  const toggleKeyboard = () => setKeyboard(!keyboard)
  const showKeyboard = () => setKeyboard(true);
  const hideKeyboard = () => setKeyboard(false);
  
  const [selectedTableId, setSelectedTableId] = useState(null)
  
  const [terminals, setTerminals] = useState([]);
  const { readerId } = useSelector(applicationSelectors.getReaderId)
  
  useEffect(() => {
    getPaymentTerminals(restaurant.id, true, readerId).then(({ data }) => setTerminals(data)).catch(noop);
  }, [])
  
  useEffect(() => {
    if (!isEmpty(terminals) && paymentMethod === paymentMethods.ALLO_PAY.key) {
      setTerminal(terminals[0].id)
    } else {
      setTerminal(null)
    }
  }, [JSON.stringify(terminals), paymentMethod])
  
  useEffect(() => {
    setPaymentDueAmount(total);
  }, [total])
  
  useEffect(() => {
    if (splitPaymentMode === splitTakeawayPaymentModes.ALL.key) {
      const amount = getCalculatorAmount();
      setPaymentAmount(amount);
  
      const change = amount - paymentDueAmount;
      setChangeAmount(Math.max(change, 0))
    }
  }, [calculatorAmount, splitPaymentMode])
  
  useEffect(() => {
    if (asTip && changeAmount) {
      setTipAmount(changeAmount);
    } else {
      setTipAmount(0);
    }
  }, [asTip, changeAmount])
  
  useEffect(() => {
    if (paymentMethod !== paymentMethods.CASH.key) {
      setAsTip(true);
    } else {
      setAsTip(false);
    }
  }, [paymentMethod])
  
  useEffect(() => {
    if (splitPaymentMode === splitTakeawayPaymentModes.ALL.key) {
      if (cardAmount > 0) {
        setCardDeductedAmount(Math.min(paymentDueAmount, cardAmount));
    
        const nextPaymentDueAmount = Math.max(paymentDueAmount - cardAmount, 0);
        setPaymentDueAmount(nextPaymentDueAmount)
        setCalculatorAmount(nextPaymentDueAmount * 100 + "")
      } else {
        setCardDeductedAmount(0)
        setPaymentDueAmount(total)
        setCalculatorAmount('')
      }
    }
  }, [cardAmount, splitPaymentMode])
  
  useEffect(() => {
    if (splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key) {
      if (cardAmount > 0) {
        const selectedItems = Object.values(partialCheckoutItems != null ? partialCheckoutItems : {});
        let selectedItemsTotal = (selectedItems || []).filter(i => !!i).reduce((acc, next) => {
          if (next.total) {
            if (next.totalQtd === 1) {
              acc += next.total
            } else {
              acc += (next.total / next.totalQtd) * next.qtd
            }
          }
          return acc
        }, 0)
        
        setCardDeductedAmount(Math.min(selectedItemsTotal, cardAmount));
      
        const nextPaymentDueAmount = Math.max(paymentDueAmount - cardAmount, 0);
        // setPaymentDueAmount(nextPaymentDueAmount)
        // setCalculatorAmount(nextPaymentDueAmount * 100 + "")
      } else {
        setCardDeductedAmount(0)
        // setPaymentDueAmount(total)
        // setCalculatorAmount("")
      }
    }
  }, [cardAmount, splitPaymentMode, partialCheckoutItems])
  
  const [suggestions, setSuggestions] = useState(paymentSuggestions(paymentDueAmount))
  
  useEffect(() => {
    const sg = paymentSuggestions(total)
    if (!isEmpty(sg) && paymentMethod !== paymentMethods.CASH.key) {
      calculatorOnSuggestion(sg[0].value)
    }
  }, [total, paymentMethod])
  
  const setFirstSuggestion = () => {
    if (suggestions) {
      calculatorOnSuggestion(suggestions[0].value)
    }
  }
  
  useEffect(() => {
    const sg = paymentSuggestions(paymentDueAmount)
    setSuggestions(sg)
  }, [paymentDueAmount])
  
  useEffect(() => {
    if (!isEmpty(payments) && payments.some(i => !isEmpty(i.orderItemIds))) {
      setSplitPaymentMode(splitTakeawayPaymentModes.BY_ITEMS.key)
    }
  }, [payments])
  
  // const suggestions = paymentSuggestions(paymentDueAmount);
  
  const isTerminalPayment = paymentMethod === paymentMethods.ALLO_PAY.key;
  
  const disablePaymentProcessingDueToMissingPayByInvoiceData = isPayByInvoiceMethodSelected && !isPayByInvoiceDataValid
  
  const disablePaymentProcessingDueToSplitAmountWhenPayingByInvoice = isPayByInvoiceMethodSelected
    && (
      !isEmpty(payments)
      || (getCalculatorAmount() < totalDue)
    )
  
  const disablePaymentProcessing =
    processingPayment
    || ((paymentAmount + cardDeductedAmount) < total && (splitPaymentMode === splitTakeawayPaymentModes.ALL.key))
    || (totalDue > 0 && ((paymentAmount + cardDeductedAmount) === 0))
    || (isTerminalPayment && !terminal)
    || disablePaymentProcessingDueToMissingPayByInvoiceData
    || (!isEmpty(selectedReceiptOption) && selectedReceiptOption.type === "PDF" && isEmpty(selectedReceiptOption.email))
    || disablePaymentProcessingDueToSplitAmountWhenPayingByInvoice
  
  const isExpress = (type === orderTypes.EXPRESS.key)
  const isTakeaway = (type === orderTypes.DELIVERY.key) || (type === orderTypes.PICKUP.key)
  const isPickup = type === orderTypes.PICKUP.key
  // this is wrong below, needs .key what was the idea here ;(
  const disableAddingTip = (type === orderTypes.EXPRESS);
  const isCashPayment = paymentMethod === paymentMethods.CASH.key;
  const cardExpendableAmount = (splitPaymentMode === splitTakeawayPaymentModes.ALL.key) ? (Math.min(total, cardAmount)) : cardDeductedAmount;
  const payableAmount = paymentAmount;
  const payableAmountWithTip = payableAmount + (splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key ? tipAmount : 0)
  
  const renderSelectedPaymentMethod = () => {
    if (!paymentMethod) {
      return ""
    }
    const method =  paymentMethods[paymentMethod]
    return method.i18nKey
  }
  
  const processPayment = () => {
    setProcessingPayment(true);
    dispatch(terminalActions.setProcessingPayment(true));
    if (paymentMethod === "CASH") {
      dispatch(printersActions.openCashDrawer());
    } else {
      dispatch(printersActions.pingPrinters());
    }
    
    const selectedOrderItems = Object.values(partialCheckoutItems || {})
  
    // when user selects allo pay but has nothing to pay, we select CASH instead since allo pay can not request 0
    // amount to terminal and can not close the order directly
    let selectedPaymentChannel = paymentMethod
    try {
      if (selectedPaymentChannel === "ALLO_PAY" && card && card.cashAmount && card.thirdParty) {
        const dueAmount = calculateDueAmount()
        if (dueAmount <= 0) {
          selectedPaymentChannel = "CASH"
        }
      }
    } catch (e) {}
    
    const openInvoiceData = isPayByInvoiceMethodSelected ? payByInvoiceData : null
    
    console.log(new Date().toISOString() + " - PContent checkout for order " + order.id + " called");
    checkout(restaurant.id, order.id, {
      paymentChannel: selectedPaymentChannel,
      paymentOption: splitTakeawayPaymentModes.ALL.key,
      amount: paymentAmount + cardDeductedAmount,
      tipAmount: tipAmount,
      cardId,
      checkoutAutoClose: autoClose,
      pagerIdentifier: pager,
      tableId: selectedTableId,
      toGo,
      terminalId: terminal,
      receiptOption: selectedReceiptOption,
      selectedOrderItems: splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key ? selectedOrderItems : null,
      openInvoice: openInvoiceData
    })
      .then(({ data: orderData }) => {
        dispatch(terminalActions.getOrder(orderData.id))
        setProcessingPayment(false);
        dispatch(terminalActions.setProcessingPayment(false));
        // closing of order stops processing payment
        console.log(new Date().toISOString() + " - PContent checkout for order " + order.id + " sync responded");
        // dispatch(terminalActions.reset());
        if (splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key) {
          setPartialCheckoutItems({})
          setTipAmount(0)
          setCard({})
        }
      })
      .catch(() => {
        setProcessingPayment(false);
        dispatch(terminalActions.setProcessingPayment(false));
      })
      .finally(() => {
        if (paymentMethod === paymentMethods.ALLO_PAY.key || paymentMethod === paymentMethods.ALLO_PAY_LINK.key) {
          setTimeout(() => {
            setProcessingPayment(false);
            dispatch(terminalActions.setProcessingPayment(false));
          }, 2000);
        }
      });
  };
  
  const [updatingEmailField, setUpdatingEmailField] = useState(false);
  const updateEmailField = (value) => {
    if (!value) {
      return;
    }
    setSelectedReceiptOption({...selectedReceiptOption, email: value})
    setUpdatingEmailField(false);
  };
  
  const TableUpdateBtn = () => (
    <div style={{ display: "flex", alignItems: "center", paddingLeft: 10, paddingRight: 10, minWidth: 100 }}>
      <EditIcon20 />
      <Typography style={{
        ...typography.body.medium,
        color: palette.grayscale["600"],
        marginLeft: 2
      }}>{t("common-update")}</Typography>
    </div>
  );
  
  const getEmailField = () => {
    return (
      <Fragment>
        <Field
          value={selectedReceiptOption.email}
          name="email"
          readOnly
          style={{marginBottom: 16 }}
        />
        <TextUpdateModal
          titleI18n={"email"}
          type='email'
          open={updatingEmailField}
          onClose={() => setUpdatingEmailField(false)}
          value={selectedReceiptOption.email}
          setValue={updateEmailField}
        />
      </Fragment>
    )
  }
  
  const showSplitPayment = isPickup && partialCheckout && (isEmpty(payments)) && !isPayByInvoiceMethodSelected
  
  const [calculatorTotal, setCalculatorTotal] = useState(total);
  const calculateDueAmount = () => {
    const { cashAmount = 0 } = (card ?? {})
    return Math.max(0, ((calculatorTotal || 0) + (isCustomCheckout ? 0 : tipAmount)) - cashAmount)
  }
  
  const isCustomCheckout = false
  
  const checkoutPayingAmount = calculateDueAmount()
  const orderDueAmount = totalDue
  
  const userHasConfirmedItems = (items) => (items || []).some(it => (it.status !== orderItemStatus.UNCONFIRMED) && !it.includedInPrice);
  const getConfirmedItems = (items, accountId, isPickup) => (items || []).filter(it => (it.status !== orderItemStatus.UNCONFIRMED) && !it.includedInPrice);
  const getCustomerConfirmedItems = (items, customerId) => items.filter(it => (it.status !== orderItemStatus.UNCONFIRMED) && (it.customerId === customerId) && !it.includedInPrice);
  
  const [partialCheckoutItems, setPartialCheckoutItems] = useState({});
  
  const getSelectedItemsTotal = (selectedCheckoutItems) => {
    const selectedItems = Object.values(selectedCheckoutItems != null ? selectedCheckoutItems : {});
    return (selectedItems || []).filter(i => !!i).reduce((acc, next) => {
      if (next.total) {
        if (next.totalQtd === 1) {
          acc += next.total
        } else {
          acc += (next.total / next.totalQtd) * next.qtd
        }
      }
      return acc
    }, 0)
  }
  
  const [allPartialCheckoutItemsSelected, setAllPartialCheckoutItemsSelected] = useState(true);
  
  const selectAllPartialCheckoutItems = () => {
    const confirmedItems = getConfirmedItems(items);
    const reducedItemsObject = confirmedItems.filter(i => i.status === 'CONFIRMED').reduce((acc, next) => {
      acc[next.id] = Object.assign({}, { id: next.id, totalQtd: next.qtd, total: next.total, qtd: next.qtd })
      return acc;
    }, {})
    setPartialCheckoutItems(reducedItemsObject)
  }
  
  const unselectAllPartialCheckoutItems = () => {
    const confirmedItems = getConfirmedItems(items);
    const reducedItemsObject = confirmedItems.filter(i => i.status === 'CONFIRMED').reduce((acc, next) => {
      acc[next.id] = null
      return acc;
    }, {})
    setPartialCheckoutItems(reducedItemsObject)
  }
  
  const toggleSelectAllPartialCheckoutItems = () => {
    if (allPartialCheckoutItemsSelected) {
      unselectAllPartialCheckoutItems()
    } else {
      selectAllPartialCheckoutItems()
    }
  }
  
  const toggleSelectPartialCheckoutItem = (item) => {
    if (item.status !== "CONFIRMED") {
      return
    }
    const discountOrderItem = items.find(it => it.category === "DISCOUNT" && it.discountedItemId === item.id)
    if ((partialCheckoutItems && partialCheckoutItems[item.id])) {
      const payload = { ...partialCheckoutItems, [item.id]: null }
      if (discountOrderItem) {
        payload[discountOrderItem.id] = null
      }
      
      setPartialCheckoutItems(payload)
    } else {
      const payload = { ...partialCheckoutItems, [item.id]: { id: item.id, qtd: item.qtd, total: item.total, totalQtd: item.qtd } }
      if (discountOrderItem) {
        payload[discountOrderItem.id] = { id: discountOrderItem.id, qtd: discountOrderItem.qtd, total: discountOrderItem.total, totalQtd: discountOrderItem.qtd }
      }
      setPartialCheckoutItems(payload)
    }
  }
  
  const incrementSelectPartialCheckoutItemQuantity = (item) => {
    const qtd = partialCheckoutItems && partialCheckoutItems[item.id] ? partialCheckoutItems[item.id].qtd : 0
    if (qtd !== item.qtd) {
      setPartialCheckoutItems({ ...partialCheckoutItems, [item.id]: { id: item.id, qtd: qtd + 1, total: item.total, totalQtd: item.qtd } })
    }
  }
  
  const decrementSelectPartialCheckoutItemQuantity = (item) => {
    const qtd = partialCheckoutItems && partialCheckoutItems[item.id] ? partialCheckoutItems[item.id].qtd : 0
    if (qtd !== 0) {
      const updatedQtd = qtd - 1
      if (updatedQtd === 0) {
        setPartialCheckoutItems({ ...partialCheckoutItems, [item.id]: null })
      } else {
        setPartialCheckoutItems({ ...partialCheckoutItems, [item.id]: { id: item.id, qtd: qtd - 1, total: item.total, totalQtd: item.qtd } })
      }
    }
  }
  
  const handlePrintPayment = (refundable) => printPayment(order.id, selectedPaymentForPrintingId, refundable).then(() => {}).catch(() => {}).finally(() => {
    setSelectedPaymentForPrintingId(null);
  })
  
  const handlePrintingPaymentModal = (paymentId) => () => setSelectedPaymentForPrintingId(paymentId);
  
  const handlePrintTerminalPaymentReceipt = (paymentId) => () => printTerminalPaymentReceipt(order.id, paymentId).then(() => {}).catch(() => {})
  
  const handleRemovePayment = (paymentId) => () => {
    setProcessingPayment(true);
    deletePayment(restaurantId, order.id, paymentId).then(() => {}).catch(() => {}).finally(() => {
      setProcessingPayment(false);
    })
  }
  
  useEffect(() => {
    // effect to select all items on first render
    if (partialCheckout) {
      selectAllPartialCheckoutItems()
      setAllPartialCheckoutItemsSelected(true)
    }
  }, [])
  
  useEffect(() => {
    if (isEmpty(partialCheckoutItems)) {
      setAllPartialCheckoutItemsSelected(false)
      return
    }
    const values = Object.values(partialCheckoutItems ?? {});
    if (values.some(v => v === null || v.qtd < v.totalQtd)) {
      setAllPartialCheckoutItemsSelected(false)
    } else {
      setAllPartialCheckoutItemsSelected(true)
    }
    
  }, [JSON.stringify(partialCheckoutItems)])
  
  useEffect(() => {
    // effect to update payment amount on update of items selected for checkout
    if (partialCheckout && !isCustomCheckout) {
      const selectedItemsTotal = getSelectedItemsTotal(partialCheckoutItems)
      
      // if any items are selected, add delivery fee to it if its the first payment
      // if (deliveryFee && isEmpty(payments) && !isEmpty(selectedItems)) {
      //   const hasSelectedQuantity = (selectedItems || []).some(i => !!i && i.qtd > 0)
      //   if (hasSelectedQuantity) {
      //     selectedItemsTotal += deliveryFee
      //   }
      // }
      
      const roundedSelectedItemsTotal = Math.round(((selectedItemsTotal || 0) + Number.EPSILON) * 100) / 100
      setCalculatorTotal(roundedSelectedItemsTotal)
    }
    if (partialCheckout && splitPaymentMode === splitTakeawayPaymentModes.ALL.key) {
      setCalculatorTotal(total)
    }
  }, [JSON.stringify(partialCheckoutItems), splitPaymentMode, payments])
  
  useEffect(() => {
    if (splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key) {
      setPaymentAmount(calculateDueAmount());
    } else {
      const sg = paymentSuggestions(total)
      if (!isEmpty(sg) && paymentMethod !== paymentMethods.CASH.key) {
        calculatorOnSuggestion(sg[0].value)
      } else {
        setPaymentAmount(0)
        setCalculatorAmount('')
      }
    }
  }, [calculatorTotal, splitPaymentMode])
  
  useEffect(() => {
    if (discount && (splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key)) {
      removeDiscount()
    }
  }, [splitPaymentMode])
  
  const [expandedOrderItemGroupsByOrderItemCode, setExpandedOrderItemGroupsByOrderItemCode] = useState([])
  
  const handleToggleOrderItemGroupExpanded = (code) => {
    let codes = [...expandedOrderItemGroupsByOrderItemCode]
    
    if (!isEmpty(codes)) {
      const index = codes.indexOf(code);
      if (index > -1) {
        codes.splice(index, 1)
      } else {
        codes.push(code)
      }
    } else {
      codes = [code]
    }
    
    setExpandedOrderItemGroupsByOrderItemCode(codes)
  }
  
  const getGroupByHeaderName = (header) => {
    if (isEmpty(header)) {
      return
    }
    
    const { orderItemNumeration, orderItemName, customer } = header
    
    if (groupBy === orderItemsGroups.ITEM.value) {
      return `${orderItemNumeration ? `${orderItemNumeration} ` : ''}${orderItemName}`;
    }
    
    if (groupBy === orderItemsGroups.CUSTOMER.value) {
      return isEmpty(customer) ? t('unassigned') : `${customer.firstName || ""} ${customer.lastName || ""}`
    }
    
    return t('unassigned')
  }
  
  const getToggleSelectAllItems = () => {
    return (
      <ButtonBase
        style={{ display: "flex", alignItems: "center", justifyContent: "space-between", padding: 12, width: "100%", borderRadius: 12, ...shadows.base, background: palette.grayscale["100"], marginBottom: 12 }}
        onClick={toggleSelectAllPartialCheckoutItems}
        disableRipple
        disableTouchRipple
      >
        <div style={{ display: "flex", alignItems: "center" }}>
          <div style={{ marginRight: 8, display: "flex" }}>
            <Checkbox checked={allPartialCheckoutItemsSelected} onClick={toggleSelectAllPartialCheckoutItems} />
          </div>
          <Typography style={{ ...typography.body.medium }}>
            {t('select-all')}
          </Typography>
        </div>
        <div>
          <Typography style={{ ...typography.body.regular }}>
            {formatNumber(orderDueAmount)}€
          </Typography>
        </div>
      </ButtonBase>
    )
  }
  
  const getGroupedItems = () => {
    // if (groupBy === orderItemsGroups.NONE.value) {
      return (
        <>
          {userHasConfirmedItems(order.items) && (
            <div className={classes.orderPartial}>
              {getConfirmedItems(order.items).filter(it => (it.category !== "DISCOUNT") && !it.includedInPrice).map(item => (
                <div style={{
                  display: "flex",
                  alignItems: "baseline",
                  marginBottom: 12,
                  flexDirection: "column",
                  width: "100%",
                  background: "#F9F9F9",
                  borderRadius: 12,
                  padding: 12,
                  boxShadow: "0px 4px 8px rgb(0 0 0 / 2%), 0px 0px 2px rgb(0 0 0 / 4%), 0px 0px 1px rgb(0 0 0 / 2%)"
                }} key={item.id}
                >
                  <ButtonBase className={clsx(classes.selectableOrderItem, { [classes.noMargin]: item.status === 'PAID' || !partialCheckout })}
                              onClick={item.status === 'CONFIRMED' ? () => toggleSelectPartialCheckoutItem(item) : null}
                              disableRipple
                              disableTouchRipple
                  >
                    <CheckoutOrderItem
                      isPayment
                      selected={partialCheckoutItems && partialCheckoutItems[item.id]}
                      onSelect={item.status === 'CONFIRMED' ? () => toggleSelectPartialCheckoutItem(item) : null}
                      key={item.id}
                      code={item.code}
                      id={item.id}
                      orderId={order.id}
                      item={item}
                      nestedOrderItems={item.nestedOrderItems ?? []}
                      {...item}
                      description={`No: ${item.qtd} - ${item.unitPrice} €`}
                      status={item.status}
                    />
                  </ButtonBase>
                  {item.discount && (
                    <div style={{     display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginTop: 6,
                      width: "100%",
                      minHeight: 44
                    }}>
                      <div style={{
                        display: 'flex',
                        flex: 1,
                        alignItems: 'center'
                      }}>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          width: '100%'
                        }}>
                          <Typography style={{ ...typography.body.regular }}>
                            {`${t('terminal-orders-discount-label')} ${item.discount.percentage ? `(${item.discount.percentage}%)` : ''} ${item.discount.code ? `(${item.discount.code})` : ''}`}
                          </Typography>
                          {item.status === 'CONFIRMED' && (
                            <Typography style={{ ...typography.body.regular }}>
                              {item.discount.amount ? `-${(item.discount.amount || 0).toFixed(2)}€` : ''}
                            </Typography>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  {!(item.status === 'PAID') && !(item.status === 'CANCELLED') && partialCheckout && (item.qtd > 1) && partialCheckoutItems && partialCheckoutItems[item.id] && (partialCheckoutItems[item.id] !== 0) && (
                    <div className={classes.quantitySelectionForOrderItem}>
                      <Typography style={{ ...typography.body.regular }}>{t("selected")}</Typography>
                      <QuantityInput
                        quantity={partialCheckoutItems && partialCheckoutItems[item.id] ? partialCheckoutItems[item.id].qtd : 0}
                        min={1}
                        max={item.qtd}
                        increment={() => incrementSelectPartialCheckoutItemQuantity(item)}
                        decrement={() => decrementSelectPartialCheckoutItemQuantity(item)}
                      />
                    </div>
                  )}
                </div>
              ))}
              {/*{order.deliveryFee && order.deliveryFee > 0 && (*/}
              {/*  <div style={{ background: palette.grayscale["100"], borderRadius: 12, marginTop: 12, padding: 12, ...shadows.base }}>*/}
              {/*    <div style={{*/}
              {/*      display: 'flex',*/}
              {/*      flex: 1,*/}
              {/*      alignItems: 'center'*/}
              {/*    }}>*/}
              {/*      <div style={{*/}
              {/*        display: 'flex',*/}
              {/*        justifyContent: 'space-between',*/}
              {/*        width: '100%'*/}
              {/*      }}>*/}
              {/*        <Typography style={{ ...typography.body.regular }}>*/}
              {/*          {t('terminal-orders-delivery-fee-label')}*/}
              {/*        </Typography>*/}
              {/*        <Typography style={{ ...typography.body.regular }}>*/}
              {/*          {`${(order.deliveryFee).toFixed(2)}€`}*/}
              {/*        </Typography>*/}
              {/*      </div>*/}
              {/*    </div>*/}
              {/*  </div>*/}
              {/*)}*/}
            </div>
          )}
        </>
      )
    // }
    
    // const isGroupByNone = groupBy === orderItemsGroups.NONE.value
    // return orderedOrderItemGroups.map((itemGroup, index) => {
    //   const { header, items: allItems, orderItemCode } = (itemGroup || {})
    //   const { itemsQuantity, itemsTotalPrice } = (header || {})

    //   const items = (allItems || []).filter(i => !i.includedInPrice)
      
    //   const itemsToSingleQuantity = (items || []).reduce((acc, next, index) => {
    //     if (next.qtd === 1) {
    //       next.key = next.id
    //       acc.push(next)
    //     } else {
    //       for (let i = 1; i <= next.qtd; i++) {
    //         let singleItemNext = { ...next }
    //         singleItemNext.key = `${next.id}-${i}`
    //         singleItemNext.qtd = 1
    //         acc.push(singleItemNext)
    //       }
    //     }
    //     return acc
    //   }, [])
      
    //   const groupExpanded = expandedOrderItemGroupsByOrderItemCode && expandedOrderItemGroupsByOrderItemCode.indexOf(orderItemCode) > -1
      
    //   const headerName = getGroupByHeaderName(header)
    //   // const itemIds = (items || []).map(i => i.id)
    //   // const itemIdsCheckedQtd = partialCheckoutItems
    //   // const itemGroupChecked = partialCheckoutItems && partialCheckoutItems[item.id]
      
    //   if (isGroupByNone) {
    //     return (items || []).map(item => {
    //       return (
    //         <CheckoutOrderItem
    //           isPayment
    //           grouped
    //           selected={false}
    //           onSelect={() => {}}
    //           key={item.id}
    //           orderId={order.id}
    //           orderType={order.type}
    //           item={item}
    //           {...item}
    //           total={item.totalDue}
    //           currentAccountId={account.id}
    //         />
    //       )
    //     })
    //   }
      
    //   return (
    //     <div>
    //       <div style={{
    //         width: '100%',
    //         background: palette.grayscale["100"],
    //         overflow: 'auto',
    //         borderRadius: 12,
    //         boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.02)",
    //         marginTop: index > 0 ? 12 : 0
    //       }}>
    //         <div>
    //           <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'top', justifyContent: 'space-between', verticalAlign: 'middle' }}>
    //             <ButtonBase disableRipple disableTouchRipple style={{ display: 'flex', flex: 1, padding: 12, textAlign: "left" }} onClick={() => handleToggleOrderItemGroupExpanded(orderItemCode)}>
    //               <div style={{ marginRight: 8, display: "flex" }}>
    //                 <Checkbox />
    //               </div>
    //               <div style={{ minWidth: 20, marginRight: 8, paddingTop: 2, }}>
    //                 <Typography style={{
    //                   ...typography.extraSmall.medium,
    //                   background: palette.grayscale["250"],
    //                   height: 16,
    //                   paddingRight: 6,
    //                   paddingLeft: 6,
    //                   display: "flex",
    //                   justifyContent: "center",
    //                   borderRadius: 12
    //                 }}
    //                 >
    //                   {itemsQuantity}X
    //                 </Typography>
    //               </div>
    //               <div style={{ flex: 1 }}>
    //                 <div>
    //                   <div style={{ display: 'flex', alignItems: 'baseline', flexWrap: "wrap" }}>
    //                     <div style={{ flex: 1 }}>
    //                       <div style={{ flex: 1 }}>
    //                         <Typography style={{ ...typography.body.medium }}>
    //                           {headerName}
    //                         </Typography>
    //                       </div>
    //                     </div>
    //                     <div style={{ display: "flex" }}>
    //                       <Typography style={{ ...typography.body.regular, marginRight: 2 }}>{`${formatNumber(itemsTotalPrice)}€`}</Typography>
    //                       <div style={{ display: "flex", transform: groupExpanded ? null : 'rotate(180deg)' }}>
    //                         <CollapseIcon20Grayscale400 />
    //                       </div>
    //                     </div>
    //                   </div>
    //                 </div>
    //               </div>
    //             </ButtonBase>
    //           </div>
    //           {groupExpanded && <div style={{ borderBottom: `1px solid ${palette.grayscale["250"]}`, marginLeft: 12, marginRight: 12, height: 1 }} />}
    //           {groupExpanded && items && itemsToSingleQuantity.map(item => {
    //             return (
    //               <CheckoutOrderItem
    //                 isPayment
    //                 grouped
    //                 selected={false}
    //                 onSelect={() => {}}
    //                 key={item.id}
    //                 orderId={order.id}
    //                 orderType={order.type}
    //                 item={item}
    //                 {...item}
    //                 currentAccountId={account.id}
    //               />
    //             )
    //           })}
    //         </div>
    //       </div>
    //     </div>
    //   )
    // })
  }
  
  const getCheckoutPayingAmountBottomPanel = () => {
    const selectedItemsTotal = getSelectedItemsTotal(partialCheckoutItems)
    const checkoutPaymentProgressPercentage = Math.min(selectedItemsTotal / orderDueAmount * 100, 100)
    
    return (
      <div>
        <div style={{
          background: palette.grayscale["100"],
          ...shadows.base,
          borderRadius: 12,
          padding: 12
        }}>
          <div style={{ marginBottom: 10, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
            <Typography style={{ ...typography.body.medium }}>{t("paying")}</Typography>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <Typography style={{ ...typography.body.medium }}>{formatNumber(selectedItemsTotal)}€</Typography>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginLeft: 4 }}>{t("of-amount", { amount: formatNumber(orderDueAmount) })}€</Typography>
            </div>
          </div>
          <div style={{ marginBottom: 4 }}>
            <div style={{ width: "100%", height: 8, borderRadius: 100, background: palette.grayscale["300"], position: "relative" }}>
              <div style={{ position: "absolute", left: 0, top: 0, width: `${checkoutPaymentProgressPercentage}%`, height: 8, borderRadius: 100, background: palette.primary["500"] }} />
            </div>
          </div>
        </div>
      </div>
    )
  }
  
  const getSplitPaymentByItems = () => {
    return (
      <div style={{ margin: 12, display: "flex", flexDirection: "column", height: "100%", overflow: "hidden" }}>
        {/*<div style={{ marginBottom: 6 }}>*/}
        {/*  <GroupBySwitcher*/}
        {/*    options={orderItemGroupOptions}*/}
        {/*    selectOption={setGroupBy}*/}
        {/*    selectedOption={orderItemsGroups[groupBy]}*/}
        {/*  />*/}
        {/*</div>*/}
        <div style={{ flex: 1, overflow: "hidden" }}>
          <div style={{ height: "100%", overflow: "auto" }}>
            <div>
              {getToggleSelectAllItems()}
            </div>
            {getGroupedItems()}
          </div>
        </div>
        {getCheckoutPayingAmountBottomPanel()}
      </div>
    )
  }
  
  const getSplitPaymentByAmount = () => {
    return (
      <div style={{ height: "100%", display: "flex", flexDirection: "column", marginTop: 12 }}>
        <div style={{ flex: 1 }}>
          <div style={{ marginTop: 32, marginLeft: 12, marginRight: 12 }}>
            <Typography style={{ ...typography.body.medium, marginBottom: 16, textAlign: "center" }}>
              {t('define-amount-to-pay')}
            </Typography>
            <Typography style={{ ...typography.x.paymentAmount, textAlign: "center" }}>
              <span style={{ color: !calculatorAmount ? palette.grayscale["400"] : "inherit" }}>{formatNumber(getCalculatorAmount())}</span>€
            </Typography>
          </div>
        </div>
        <div style={{ padding: 12 }}>
          {!!paymentDueAmount && !isEmpty(suggestions) && (
            <div className={classes.suggestions}>
              {removeDuplicates(suggestions).map(({ value, label }, index) => (
                <ButtonBase data-testid={`suggestion-${index}`} key={value} className={classes.suggestion} onClick={() => calculatorOnSuggestion(value)}>
                  <Typography style={{ ...typography.body.regular }}>{label}€</Typography>
                </ButtonBase>
              ))}
            </div>
          )}
          <Keyboard onClick={calculatorOnClick} onDelete={calculatorOnDelete} />
          <div className={classes.changeCalculator}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <Typography style={{ ...typography.body.medium }}>{t(asTip ? 'tip' : 'change')}</Typography>
              <Typography style={{ ...typography.body.medium }}>
                {formatNumber(changeAmount)}€
                <span style={{ marginLeft: 4, color: palette.grayscale["600"] }}>{t('of-amount', { amount: `${formatNumber(paymentDueAmount)}€` })}</span>
              </Typography>
            </div>
            {!!changeAmount && isCashPayment && (
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginTop: 8 }}>
                <Typography style={{ ...typography.body.medium, marginRight: 4 }}>{t('as-tip')}</Typography>
                <div>
                  <Switch onClick={toggleAsTip} checked={!!asTip}/>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }
  
  const getSplitPayment = () => {
    if (splitPaymentMode === splitTakeawayPaymentModes.ALL.key) {
      return getSplitPaymentByAmount()
    }
  
    if (splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key) {
      return getSplitPaymentByItems()
    }
    
    return null
  }
  
  const getToPayTotalFormatted = () => {
    if (splitPaymentMode === splitTakeawayPaymentModes.ALL.key) {
      return formatNumber(itemsTotal)
    } else {
      const selectedItemsTotal = getSelectedItemsTotal(partialCheckoutItems)
      const roundedSelectedItemsTotal = Math.round(((selectedItemsTotal || 0) + Number.EPSILON) * 100) / 100
      return formatNumber(roundedSelectedItemsTotal)
    }
  }
  
  const getPayments = () => {
    return (payments || []).map((payment = {}, index) => {
        const expanded = expandedPayment === payment.id;
        const paymentWithTipAmount = (payment.amount || 0) + (payment.tipAmount || 0)
        const formattedAmountWithTip = `${(paymentWithTipAmount || 0).toFixed(2)}€`;
        const formattedPaymentAmount = `${(payment.amount || 0).toFixed(2)}€`;
        const formattedTipAmount = `${(payment.tipAmount || 0).toFixed(2)}€`;
        const formattedPaymentChannelI18nKey = (payment.paymentChannel && !isEmpty(paymentMethods[payment.paymentChannel]))
          ? paymentMethods[payment.paymentChannel].i18nKey
          : payment.paymentChannel;
    
        const { paymentProvider, isTerminalPayment, isManualPaymentConfirmation, accountDetails } = (payment || {})
        const isStripeTerminalPayment = paymentProvider === "STRIPE" && isTerminalPayment && !isManualPaymentConfirmation
        return (
          <div style={{
            marginLeft: 12,
            marginRight: 12,
            marginTop: 12,
            minWidth: 147,
            background: palette.grayscale["100"],
            ...shadows.base,
            borderRadius: 12
          }} key={payment.id}>
            <ButtonBase style={{ display: "flex", justifyContent: "space-between", alignItems: "center", padding: 12, width: "100%" }} onClick={() => togglePaymentExpand(payment.id)}>
              <Typography style={{ ...typography.body.medium }}>{t(formattedPaymentChannelI18nKey)}</Typography>
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <Typography style={{ ...typography.body.regular, marginRight: 4 }}>{formattedAmountWithTip}</Typography>
                <div style={{ display: "flex", transform: expanded ?  "none" : 'rotate(180deg)' }}>
                  <CollapseIcon20Grayscale400 />
                </div>
              </div>
            </ButtonBase>
            {expanded && (
              <div style={{ paddingTop: 16, borderTop: `1px solid ${palette.grayscale["250"]}`, padding: 12 }}>
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                  <Typography style={{ ...typography.body.regular }}>{t('payment-order-value')}</Typography>
                  <Typography style={{ ...typography.body.regular }}>{formattedPaymentAmount}</Typography>
                </div>
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginTop: 12 }}>
                  <Typography style={{ ...typography.body.regular }}>{t('tip')}</Typography>
                  <Typography style={{ ...typography.body.regular }}>{formattedTipAmount}</Typography>
                </div>
                {isManualPaymentConfirmation && (
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginTop: 12 }}>
                    <Typography style={{ ...typography.body.regular }}>
                      {t('confirmed-by-name', { name: accountDetails })}
                    </Typography>
                  </div>
                )}
              </div>
            )}
            {expanded && (
              <div style={{ padding: '8px 12px 12px 12px'}}>
                <div style={{ display: "flex", justifyContent: "flex-start", alignItems: "center" }}>
                  <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "6px 11px", marginRight: 8 }} onClick={handlePrintingPaymentModal(payment.id)}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t('report-history-print-report-btn')}
                    </Typography>
                  </ButtonBase>
                  {isStripeTerminalPayment && (
                    <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "6px 11px", marginRight: 8 }} onClick={handlePrintTerminalPaymentReceipt(payment.id)}>
                      <Typography style={{ ...typography.body.medium }}>
                        {t('print-terminal-payment-receipt')}
                      </Typography>
                    </ButtonBase>
                  )}
                  {((payment.paymentChannel !== "ALLO_PAY") || isManualPaymentConfirmation) && (
                    <Confirm
                      title={t('remove-payment-of-amount', { amount: formattedPaymentAmount })}
                      description={t("are-you-sure-you-want-to-remove-payment")}
                    >
                      {confirm => (
                        <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "6px 11px" }} onClick={confirm(handleRemovePayment(payment.id))}>
                          <Typography style={{ ...typography.body.medium, color: palette.negative["600"] }}>
                            {t('remove')}
                          </Typography>
                        </ButtonBase>
                      )}
                    </Confirm>
                  )}
                </div>
              </div>
            )}
          </div>
        )
      })
  }
  
  const getPaymentsGroup = () => {
    if (!isEmpty(payments)) {
      return (
        <div className={boardClasses.group} style={{ flex: showingPaymentsGroup ? 1 : null, overflow: "hidden", marginTop: 12 }}>
          <div className={boardClasses.groupHeader} style={showingCheckoutGroup ? { borderBottom: "none" } : null} onClick={openPaymentsGroup}>
            <Typography style={{ ...typography.body.medium }}>{t('paid')}</Typography>
            <Typography style={{ ...typography.medium.semiBold }}>{formatNumber(paidAmount)}€</Typography>
          </div>
          {showingPaymentsGroup && (
            <div style={{ height: "100%" }}>
              <div style={{ display: "flex", flexDirection: "column", height: "100%" }}>
                {getPayments()}
              </div>
            </div>
          )}
        </div>
      )
    }
    
    return null
  }
  
  return (
    <div className={classes.background} style={ isMobile ? { overflow: "auto" } : null}>
      {processingPayment && (
        <TerminalPaymentModal titleI18n={"allo-pay-terminal"} open orderId={orderId} paymentMethod={paymentMethod}/>
      )}
      <div className={classes.layout} style={isMobile ? { flexDirection: "column" } : null}>
        <div className={classes.paymentPanel}>
          <div className={boardClasses.content}>
            <div className={boardClasses.lane} style={{ overflow: "auto" }} key={`lane-payments`}>
              <div className={boardClasses.group} style={{ flex: showingCheckoutGroup ? 1 : null, overflow: "hidden" }}>
                <div className={boardClasses.groupHeader} style={showingPaymentsGroup ? { borderBottom: "none" } : null} onClick={openCheckoutGroup}>
                  <Typography style={{ ...typography.body.medium }}>{t('amount-due')}</Typography>
                  <Typography style={{ ...typography.medium.semiBold }}>{formatNumber((isExpress || !isPickup) ? paymentDueAmount : totalDue)}€</Typography>
                </div>
                {showingCheckoutGroup && (
                  <div style={{ height: "100%" }}>
                    <div style={{ display: "flex", flexDirection: "column", height: "100%", overflow: "hidden" }}>
                      {showSplitPayment && (
                        <div style={{ marginLeft: 12, marginRight: 12, marginTop: 12 }}>
                          <SegmentedControlBaseTabs
                            tabs={splitTakeawayPaymentOptions}
                            value={splitPaymentMode}
                            setValue={setSplitPaymentMode}
                          />
                        </div>
                      )}
                      {getSplitPayment()}
                    </div>
                  </div>
                )}
              </div>
              {getPaymentsGroup()}
            </div>
          </div>
        </div>
        <div className={classes.checkout} style={isMobile ? { overflow: "initial", marginLeft: 0 } : null}>
          <div className={classes.checkoutForm}>
            <div className={classes.breadcrumbs}>
              <TerminalMenuIcon />
              <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                {t('checkout')}
              </Typography>
            </div>
            <div className={classes.paymentMethod}>
              <Typography style={{ ...typography.body.medium }}>
                {t('select-payment-method')}
              </Typography>
              <div className={classes.paymentMethods}>
                {restaurantPaymentMethods.map(method => {
                  const pMethod = paymentMethods[method];
                  
                  if (!pMethod) {
                    return null
                  }
                  
                  return (
                    <ButtonBase disableRipple disableTouchRipple className={classes.paymentMethodOption} key={pMethod.key} onClick={() => setPaymentMethod(pMethod.key)}>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        {pMethod.icon}
                        <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                          {t(pMethod.i18nKey)}
                        </Typography>
                      </div>
                      <Radio checked={pMethod.key === paymentMethod} />
                    </ButtonBase>
                  )
                })}
              </div>
            </div>
            {(paymentMethod === paymentMethods.ALLO_PAY.key) && (
              <div className={classes.paymentMethod}>
                <Typography style={{ ...typography.body.medium }}>
                  {t('select-payment-terminal')}
                </Typography>
                <div className={classes.paymentMethods}>
                  {!isEmpty(terminals) && terminals.map(_terminal => {
                    return (
                      <ButtonBase disableRipple disableTouchRipple className={classes.paymentMethodOption} key={_terminal.id} onClick={() => setTerminal(_terminal.id)}>
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                            {t(_terminal.label)}
                          </Typography>
                        </div>
                        <Radio checked={_terminal.id === terminal} />
                      </ButtonBase>
                    )
                  })}
                  {isEmpty(terminals) && (
                    <div style={{ width: "100%", paddingTop: 12, paddingBottom: 12, display: "flex", justifyContent: "center" }}>
                      <Typography style={{ ...typography.body.regular }}>
                        {t('there-are-no-terminals-available')}
                      </Typography>
                    </div>
                  )}
                </div>
              </div>
            )}
            <div className={classes.giftCard}>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", minHeight: 24 }}>
                <ButtonBase disableRipple disableTouchRipple style={{ padding: 0, display: "flex", alignItems: "center" }} onClick={startScanningCard}>
                  {!cardCode && <ScanQRCodeIcon />}
                  {!cardCode && (
                    <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                      {t('scan-gift-card')}
                    </Typography>
                  )}
                  {!!cardCode && (
                    <Typography style={{ ...typography.body.medium}}>
                      {t('card')}
                    </Typography>
                  )}
                </ButtonBase>
                {!!cardCode && (
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <Typography style={{ ...typography.body.regular, marginRight: 4 }}>
                      {`${cardCode} (${formatNumber(cardAmount)}€)`}
                    </Typography>
                    <ButtonBase
                      disableRipple
                      disableTouchRipple
                      onClick={removeCard}
                      style={{
                        padding: 0,
                        display: "flex",
                        alignItems: "center"
                      }}
                    >
                      <Typography style={{ ...typography.body.medium, color: palette.negative["500"] }}>
                        {t('common-remove')}
                      </Typography>
                    </ButtonBase>
                  </div>
                )}
              </div>
            </div>
            {isPayByInvoiceMethodSelected && (
              <div className={classes.promotion}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", minHeight: 24 }}>
                  <ButtonBase disableRipple disableTouchRipple
                              style={{ padding: 0, display: "flex", flexDirection: "column", alignItems: "flex-start", opacity: processingPayment ? 0.5 : 1 }}
                              onClick={startAddingPayByInvoiceData}
                              disabled={processingPayment}
                  >
                    {!isPayByInvoiceDataValid && (
                      <React.Fragment>
                        <div style={{ display: "flex", alignItems: "center", justifyContent: "flex-start" }}>
                          <WarningIcon20Red />
                          <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                            {t("data-missing-for-paying-by-receipt")}
                          </Typography>
                        </div>
                        <div>
                          <Typography style={{
                            ...typography.body.medium,
                            marginTop: 8,
                            padding: "5px 11px",
                            border: `1px solid ${palette.grayscale.border}`,
                            borderRadius: 10
                          }}>{t("fill-invoice-data")}</Typography>
                        </div>
                      </React.Fragment>
                    )}
                    {isPayByInvoiceDataValid && (
                      <div style={{ display: "flex", flexDirection: "column", textAlign: "left" }}>
                        <div style={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
                          <EditIcon20 />
                          <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                            {t("edit-invoice-data")}
                          </Typography>
                        </div>
                        <Typography style={{ ...typography.body.regular, marginTop: 4 }}>{payByInvoiceData?.customer?.name}</Typography>
                        <Typography style={{ ...typography.body.regular, marginTop: 2 }}>{payByInvoiceData?.customer?.email}</Typography>
                      </div>
                    )}
                  </ButtonBase>
                </div>
                {disablePaymentProcessingDueToSplitAmountWhenPayingByInvoice && (
                  <ButtonBase disableRipple disableTouchRipple
                              style={{ padding: 0, marginTop: 16, display: "flex", flexDirection: "column", alignItems: "flex-start", opacity: processingPayment ? 0.5 : 1 }}
                              onClick={setFirstSuggestion}
                              disabled={processingPayment}
                  >
                    <div style={{ display: "flex", alignItems: "center", justifyContent: "flex-start" }}>
                      <WarningIcon20Red />
                      <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                        {t('can-not-split-payment-when-paying-by-receipt')}
                      </Typography>
                    </div>
                    {isEmpty(payments) && (
                      <div>
                        <Typography style={{
                          ...typography.body.medium,
                          marginTop: 8,
                          padding: "5px 11px",
                          border: `1px solid ${palette.grayscale.border}`,
                          borderRadius: 10
                        }}>{t("select-all")}</Typography>
                      </div>
                    )}
                  </ButtonBase>
                )}
              </div>
            )}
            {!disableAddingTip && !!tipAmount && ( //{!disableAddingTip && (!!tipAmount || splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key) && (
              <div className={classes.tip}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <ButtonBase disableRipple disableTouchRipple style={{ padding: 0, display: "flex", alignItems: "center", opacity: !!cardCode ? 0.6 : 1 }} onClick={startAddingTip} disabled={!!cardCode}>
                    {!tipAmount && [
                      <PlusIconFilled20 />,
                      <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                        {t('add-tip')}
                      </Typography>
                    ]}
                    {!!tipAmount && (
                      <Typography style={{ ...typography.body.medium}}>
                        {t('tip')} {!!cardCode && `⚠️`}
                      </Typography>
                    )}
                  </ButtonBase>
                  {!!tipAmount && (
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <Typography style={{ ...typography.body.regular, marginRight: 4 }}>
                        {formatNumber(tipAmount)}€
                      </Typography>
                      {isCashPayment && (
                        <ButtonBase
                          disableRipple
                          disableTouchRipple
                          onClick={removeTip}
                          style={{
                            padding: 0,
                            display: "flex",
                            alignItems: "center"
                          }}
                        >
                          <Typography style={{ ...typography.body.medium, color: palette.negative["500"] }}>
                            {t('common-remove')}
                          </Typography>
                        </ButtonBase>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
            {canDiscountOrder && isEmpty(payments) && (splitPaymentMode === splitTakeawayPaymentModes.ALL.key) && (
              <div className={classes.promotion}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", minHeight: 24 }}>
                  <ButtonBase disableRipple disableTouchRipple style={{ padding: 0, display: "flex", alignItems: "center", opacity: (!!cardCode || !!tipAmount) ? 0.6 : 1 }} onClick={startAddingDiscount} disabled={(!!cardCode || !!tipAmount)}>
                    {!discountAmount && <PlusIconFilled20 />}
                    {!discountAmount && (
                      <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                        {t('add-promotion-or-discount')} {(!!cardCode || !!tipAmount) && `⚠️`}
                      </Typography>
                    )}
                    {!!discountAmount && (
                      <Typography style={{ ...typography.body.medium}}>
                        {t('discount')}
                      </Typography>
                    )}
                  </ButtonBase>
                  {!!discountAmount && (
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <Typography style={{ ...typography.body.regular, marginRight: 4 }}>
                        {`${promotion ?? ""} ${formatNumber(discountAmount)}€`}
                      </Typography>
                      <ButtonBase
                        disableRipple
                        disableTouchRipple
                        onClick={removeDiscount}
                        style={{
                          padding: 0,
                          display: "flex",
                          alignItems: "center"
                        }}
                      >
                        <Typography style={{ ...typography.body.medium, color: palette.negative["500"] }}>
                          {t('common-remove')}
                        </Typography>
                      </ButtonBase>
                    </div>
                  )}
                </div>
              </div>
            )}
            {/*<div className={classes.printReceipt}>*/}
            {/*  <Typography style={{ ...typography.body.medium }}>*/}
            {/*    {t('should-print-receipt')}*/}
            {/*  </Typography>*/}
            {/*</div>*/}
            {allowPagerInputInExpress && (
              <div className={classes.meta} >
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography style={{ ...typography.body.medium }}>
                    {t('assigned-pager')}
                  </Typography>
                  <div style={{ maxWidth: 100, position: "relative" }}>
                    <ClickAwayListener onClickAway={hideKeyboard}>
                      <div style={{ position: "relative" }}>
                        <ButtonBase style={{ padding: 0, margin: 0, zIndex: 12, position: "absolute", width: 100, height: 44 }} disableRipple disableTouchRipple onClick={toggleKeyboard} />
                        <Field value={pager || order.pagerIdentifier} noFocus style={{ zIndex: 11 }} />
                        {!!keyboard && (
                          <div style={{ position: "absolute", right: 0, top: 56, zIndex: 99999 }}>
                            <Keyboard extended style={{ ...shadows.large }}
                                      specialChar={"-"}
                                      onClick={(char) => setPager(pager + char)}
                                      onDelete={() => pager ? setPager(pager.slice(0, -1)) : noop} />
                          </div>
                        )}
                      </div>
                    </ClickAwayListener>
                  </div>
                </div>
              </div>
            )}
            <CheckoutTableSelector tableId={selectedTableId} setTableId={setSelectedTableId}/>
            <div className={classes.paymentMethod}>
              <Typography style={{ ...typography.body.medium }}>{t("chose-receipt-option-payment-process")} </Typography>
              <div className={classes.paymentMethods}>
                {receiptOptions.map(option => {
                  const rOption = receiptModes[option];
                  return (
                    <ButtonBase disableRipple disableTouchRipple className={classes.paymentMethodOption} onClick={() => setSelectedReceiptOption({...selectedReceiptOption, type: rOption.key})} key={rOption.key}>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                          {t(rOption.i18nKey)}
                        </Typography>
                      </div>
                      <Radio checked={rOption.key === selectedReceiptOption.type} />
                    </ButtonBase>
                  );
                })}
              </div>
            </div>
            {selectedReceiptOption.type === "PDF" && (
              <div style={{ borderBottom: `1px solid ${palette.grayscale.divider}`, marginTop: 16 }}>
                <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-end", width: "400", maxWidth: "100%" }}>
                  <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%", marginBottom: 16 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("provide-email-for-pdf-receipt-checkout")}</Typography>
                    <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                                style={{ padding: 0 }} onClick={() => setUpdatingEmailField(true)}>
                      <TableUpdateBtn />
                    </ButtonBase>
                  </div>
                  {getEmailField()}
                </div>
              </div>
            )}
            <div className={classes.printReceipt}>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t('close-order-immediately')}
                </Typography>
                <Switch checked={autoClose} onClick={toggleAutoClose} />
              </div>
            </div>
          </div>
          <div style={ isMobile ? { paddingBottom: 32 } : null}>
            <div className={classes.expendableSummary}>
              <div className={classes.summaryLine}>
                <Typography style={{ ...typography.body.medium }}>{t('order-total')}</Typography>
                <Typography style={{ ...typography.body.medium }}>{getToPayTotalFormatted()}€</Typography>
              </div>
              {!!discountAmount && (
                <div className={classes.summaryLine}>
                  <Typography style={{ ...typography.body.medium }}>{t('discount')}</Typography>
                  <Typography style={{ ...typography.body.medium }}>-{formatNumber(discountAmount)}€</Typography>
                </div>
              )}
              {!!deliveryFee && (isEmpty(payments)) && (
                <div className={classes.summaryLine}>
                  <Typography style={{ ...typography.body.medium }}>{t('terminal-orders-delivery-fee-label')}</Typography>
                  <Typography style={{ ...typography.body.medium }}>{formatNumber(deliveryFee)}€</Typography>
                </div>
              )}
              {!!tipAmount && (
                <div className={classes.summaryLine}>
                  <Typography style={{ ...typography.body.medium }}>{t('tip')}</Typography>
                  <Typography style={{ ...typography.body.medium }}>{formatNumber(tipAmount)}€</Typography>
                </div>
              )}
              {!!cardExpendableAmount && (
                <div className={classes.summaryLine}>
                  <Typography style={{ ...typography.body.medium }}>{t('card')}</Typography>
                  <Typography style={{ ...typography.body.medium }}>-{formatNumber(cardExpendableAmount)}€</Typography>
                </div>
              )}
              {!!changeAmount && !asTip && (
                <div className={classes.summaryLine}>
                  <Typography style={{ ...typography.body.medium }}>{t('change')}</Typography>
                  <Typography style={{ ...typography.body.medium }}>{formatNumber(changeAmount)}€</Typography>
                </div>
              )}
            </div>
          </div>
          <BottomPanel size='s' isSticky transparent position="sticky">
            <div style={{ width: "100%", display: "flex", alignItems: "center" }}>
              {splitPaymentMode === splitTakeawayPaymentModes.BY_ITEMS.key && (
                <ButtonBase
                  disableRipple
                  disableTouchRipple
                  disabled={disablePaymentProcessing}
                  onClick={startAddingTip}
                  style={{
                    background: disablePaymentProcessing ? palette.grayscale["400"] : palette.grayscale["100"],
                    border: `1px solid ${palette.grayscale["350"]}`,
                    borderRadius: 12,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    width: "100%",
                    textAlign: "center",
                    flex: 1,
                    maxWidth: 200,
                    marginRight: 8
                  }}
                >
                  <div style={{ padding: 11, flex: 1, justifyContent: "center" }}>
                    <Typography style={{ ...typography.body.medium, color: disablePaymentProcessing ? '#FFFFFF' : null }}>
                      {t('add-amount')}
                    </Typography>
                  </div>
                </ButtonBase>
              )}
              <ButtonBase
                disableRipple
                disableTouchRipple
                disabled={disablePaymentProcessing}
                onClick={processPayment}
                style={{
                  background: disablePaymentProcessing ? palette.grayscale["400"] : palette.primary["500"],
                  borderRadius: 12,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100%",
                  textAlign: "left",
                  flex: 1
                }}
              >
                <div style={{ padding: 12, flex: 1 }}>
                  <Typography style={{ ...typography.body.medium, color: disablePaymentProcessing ? '#FFFFFF' : palette.grayscale["100"] }}>
                    {t('process-payment')} {`(${t(renderSelectedPaymentMethod())})`}
                  </Typography>
                </div>
                <div style={{ display: "flex", alignItems: "center", borderLeft: `1px solid rgba(242, 242, 242, 0.2)`, padding: 12 }}>
                  <Typography style={{ ...typography.body.medium, color: disablePaymentProcessing ? '#FFFFFF' : palette.grayscale["100"] }}>
                    {formatNumber(payableAmountWithTip)}€
                  </Typography>
                </div>
              </ButtonBase>
            </div>
          </BottomPanel>
        </div>
      </div>
      {addingTip && (
        <TipModal open={addingTip} onClose={stopAddingTip} setValue={setTipAmount} selectedTotal={payableAmount} />
      )}
      {scanningCard && (
        <ScanGiftCardModal open={scanningCard} onClose={stopScanningCard} setValue={onResolveCard} orderId={orderId} />
      )}
      {addingDiscount && (
        <DiscountModal open={addingDiscount} onClose={stopAddingDiscount} setValue={onResolveDiscount} />
      )}
      {selectedPaymentForPrintingId && (
        <PrintPaymentModal open={Boolean(selectedPaymentForPrintingId)} onClose={() => setSelectedPaymentForPrintingId(null)} onDone={handlePrintPayment} />
      )}
      
      {/* pay by invoice data */}
      {addingPayByInvoiceData && (
        <CreateInvoiceModal open={Boolean(addingPayByInvoiceData)}
                            onClose={stopAddingPayByInvoiceData} submit={updatePayByInvoiceData} data={payByInvoiceData} />
      )}
    </div>
  )
}

export default withTranslation('common')(Checkout);
