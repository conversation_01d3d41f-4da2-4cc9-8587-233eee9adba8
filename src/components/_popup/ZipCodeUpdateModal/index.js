import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import { ButtonBase } from "@material-ui/core";
import { CloseIcon20Gray } from "../../../utils/icons";
import isEmpty from "../../../utils/isEmpty";
import { uniq } from "lodash/array";

const ZipCodeUpdateModal = ({ t, open, onClose, _done, value, setValue, fieldProps, titleI18n }) => {
  
  const [zipCodes, setZipCodes] = useState([]);
  const [inputString, setInputString]=useState("")
  
  const ZipCodeChip = ({ t, zipCode }) => {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent:"flex-end",
        borderRadius: 12,
        backgroundColor: palette.grayscale["350"],
        padding: '2px 2px 2px 6px',
        height: "fit-content"
      }}>
        <Typography style={{ ...typography.small.medium }}>{zipCode}</Typography>
        <ButtonBase style={{ marginLeft: 2 }} onClick={() => onZipCodeDelete(zipCode)}>
          <CloseIcon20Gray/>
        </ButtonBase>
      </div>
    )
  }
  
  const onDone = () => {
    setValue(zipCodes);
    setZipCodes([])
    onClose();
  }
  
  const onChange = (e) => {
    setInputString(e.target.value);
    if (e.target.value.length === 5) {
      setZipCodes(uniq([...zipCodes, e.target.value]));
      setInputString("");
    }
  };
  
  const onZipCodeDelete = (toBeDeletedZip) => {
    const filteredZipCodes = zipCodes.filter((zip) => zip !== toBeDeletedZip);
    setZipCodes(filteredZipCodes);
  };
  
  useEffect(() => {
    setInputString(value);
  }, [value, open]);
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent", ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} onDone={onDone} />
      <div style={{
        height: "100%",
        overflow: "auto",
        background: palette.grayscale["200"],
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20
      }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
          <div style={{ display: "flex", gap: 4, alignItems: "center", marginBottom: 8, flexWrap: "wrap" }}>
            {(zipCodes || []).map((zip) => {
              if (isEmpty(zip)) {
              } else
                return (
                  <ZipCodeChip zipCode={zip} />
                );
            })}
          </div>
          <Field
            value={inputString}
            onChange={onChange}
            {...fieldProps}
          />
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(ZipCodeUpdateModal);
