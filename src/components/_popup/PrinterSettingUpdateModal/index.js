import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import FormControl from "@material-ui/core/FormControl";
import { Checkbox, FormControlLabel, FormGroup, FormLabel } from "@material-ui/core";
import Grid from "@material-ui/core/Grid";

const PrinterSettingUpdateModal = ({ t, open, onClose, value = [{}], setValue, titleI18n, printers = [], selectedPrinterIds }) => {


  const [printerSettings, setPrinterSettings] = useState({printerIds: selectedPrinterIds});


  const onDone = () => {
    setValue(printerSettings);
    onClose();
  }

  const togglePrinterSelection = (e) => {
    if (e.target.checked) {
      setPrinterSettings({ ...printerSettings, printerIds: (printerSettings.printerIds || []).concat([e.target.name]) });
    } else {
      setPrinterSettings({ ...printerSettings, printerIds: (printerSettings.printerIds || []).filter(i => i !== e.target.name) });
    }
  };

  /*const onChange = (e) => {
    setPrinterSettings(e.target.value);
  }*/



  const printersByCategory = printers.reduce((acc, next = {}) => {
    acc[next.printerCategory] = (acc[next.printerCategory] || []).concat([next]);
    return acc;
  }, {});

  const cashierPrinters = printersByCategory["MAIN"] || [];
  const barPrinters = printersByCategory["BAR"] || [];
  const kitchenPrinters = printersByCategory["KITCHEN"] || [];


  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} onDone={onDone} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40, display:"flex", justifyContent:"space-evenly"}}>
          <Grid>
          <FormControl component="fieldset">
            <FormLabel component="legend">{t("floor-management-select-main-printer-label")}</FormLabel>
            <FormGroup>
              {cashierPrinters.map(p => {
                const checked = (printerSettings.printerIds || []).indexOf(p.id) > -1;
                return (
                  <FormControlLabel
                    key={p.id}
                    control={<Checkbox  checked={checked} onChange={togglePrinterSelection} name={p.id} />}
                    label={p.label}
                  />
                );
              })}
            </FormGroup>
          </FormControl>
          </Grid>
          <Grid>
          <FormControl component="fieldset">
            <FormLabel component="legend">{t("floor-management-select-bar-printer-label")}</FormLabel>
            <FormGroup>
              {barPrinters.map(p => {
                const checked = (printerSettings.printerIds || []).indexOf(p.id) > -1;
                return (
                  <FormControlLabel
                    key={p.id}
                    control={<Checkbox checked={checked} onChange={togglePrinterSelection} name={p.id} />}
                    label={p.label}
                  />
                );
              })}
            </FormGroup>
          </FormControl>
          </Grid>
          <Grid>
            <FormControl component="fieldset">
              <FormLabel component="legend">{t("floor-management-select-kitchen-printer-label")}</FormLabel>
              <FormGroup>
                {kitchenPrinters.map(p => {
                  const checked = (printerSettings.printerIds || []).indexOf(p.id) > -1;
                  return (
                    <FormControlLabel
                      key={p.id}
                      control={<Checkbox checked={checked} onChange={togglePrinterSelection} name={p.id} />}
                      label={p.label}
                    />
                  );
                })}
              </FormGroup>
            </FormControl>
          </Grid>
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(PrinterSettingUpdateModal);
