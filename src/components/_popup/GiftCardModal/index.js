import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import { ButtonBase, useMediaQuery } from "@material-ui/core";
import Field from "../../form/Field";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import Radio from "../../_toggles/Radio";
import Keyboard from "../../Keyboard";
import { noop, permissionIdentifiers } from "../../../utils/const";
import { formatNumber } from "../../../utils/formatNumber";
import isEmpty from "../../../utils/isEmpty";
import {
  createCard, createCardPayment,
  createCardTransaction,
  getCardTransactionsReport,
  getPaymentTerminals,
  printCard, resolveCard, sendGiftCardReceiptEmail, updateCard
} from "../../../api";
import { useDispatch, useSelector } from "react-redux";
import { accountSelectors, applicationSelectors, restaurantSelectors } from "../../../../redux/selectors";
import { cardPaymentStatuses, paymentMethods } from "../../../../redux/constants";
import moment from "moment";
import useStyles from "./styles";
import useCheckoutStyles from "../../Checkout/styles";
import sliceText from "../../../utils/sliceText";
import Switch from "../../_toggles/Switch";
import BottomBar from "../../_navigation/BottomBar";
import CustomerSuggestions from "../../_input/CustomerSuggestions";
import {
  CollapseIcon20, EditIcon20,
  ExpandIcon20,
  QrCode20,
  TerminalMenuIcon,
  TrashIcon20White,
  WarningIcon20Red
} from "../../../utils/icons";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import Table from "@material-ui/core/Table";
import TableContainer from "@material-ui/core/TableContainer";
import { appActions } from "../../../../redux/actions";
import GiftCardPaymentModal from "../GiftCardPaymentModal";
import { Confirm } from "../../Confirmation";
import TextUpdateModal from "../TextUpdateModal";
import GiftCardQrModal from "../GiftCardQrModal";
import {
  giftCardTransactionsTypeOptions
} from "../../Administration/Marketing/CardEditor/CardTransactions/CardsSortAndFilterBar/filterTypes";
import CreateInvoiceModal from "../CreateInvoiceModal";
import isValidEmail from "../../../utils/isValidEmail";


const data = {
  code: "",
  externalCode: "",
  disabled: false,
  initialCashAmount: "",
  customer: null,
  status: 'SOLD',
  paymentChannel: null,
  extraPromotionAmount: null,
};

let timer;

const GiftCardModal = ({ t, open, onClose, values, submit, submitting, _delete, accounts = [], ...otherProps }) => {
  const dispatch = useDispatch();
  
  const isBiggerScreen = useMediaQuery('(min-width:600px)');
  
  const classes = useStyles();
  const checkoutClasses = useCheckoutStyles();
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  
  let consolidatedData = isEmpty(values) ? data : { ...data, ...values, extraPromotionAmount: null };
  
  const [form, setForm] = useState(consolidatedData);
  
  const inputRef = React.useRef();
  
  const [openEmailModal, setOpenEmailModal] = useState(false)
  const [receiptEmail, setReceiptEmail] = useState(form?.customer?.email || "")
  const [selectedTransactionId, setSelectedTransactionId] = useState( "")
  
  const [isOpenQrCodeModal, setIsOpenQrCodeModal] = useState(false)
  
  const [giftCardCount, setGiftCardCount] = useState(1)
  const [isBulkCreation, setIsBulkCreation] = useState(false);
  const [touched, setTouched] = useState(false);
  
  const onKeyboardClickInitialCashAmount = (char) => {
    // block multiple dots or starting with dot
    if (char === ".") {
      if (!form.initialCashAmount) {
        return;
      }
      if (form.initialCashAmount.indexOf(".") > -1) {
        return;
      }
    }
    
    if (!form.initialCashAmount && (char === "0" || char === "00")) {
      return;
    }
    
    let settingFormVal = "";
    
    if (!form.initialCashAmount) {
      settingFormVal = `0.0${char}`
    } else {
      try {
        const nr = parseFloat(form.initialCashAmount);
        let total = 0;
        if (char === "00") {
          total = nr * 100
        } else {
          total = (nr * 10) + (parseInt(char) / 100)
        }
        settingFormVal = formatNumber(total)
      } catch (ignore) {
        settingFormVal = form.initialCashAmount
      }
    }
    
    //remove more than 2 decimals
    settingFormVal = settingFormVal.match(/^-?\d+(?:\.\d{0,2})?/)[0]
    
    setForm({ ...form, initialCashAmount: settingFormVal })
    
    if (inputRef) {
      inputRef.current.value = inputRef.current.value + char;
    }
  }
  
  const onKeyboardDeleteInitialCashAmount = () => {
    if (form.initialCashAmount) {
      try {
        let settingFormVal = "";
        let val = parseFloat(form.initialCashAmount);
        // means values are 0.0X
        if (val >= 0.1) {
          val = val / 10
          settingFormVal = val + "";
          settingFormVal = settingFormVal.match(/^-?\d+(?:\.\d{0,2})?/)[0]
        }
  
        if (inputRef) {
          inputRef.current.value = settingFormVal;
        }
        setForm({ ...form, initialCashAmount: settingFormVal })
      } catch (ignore) {}
    }
  }
  
  const handleClose = () => {
    onClose();
  }
  
  const [loading, setLoading] = useState(false);
  
  const [calculatorCashAmount, setCalculatorCashAmount] = useState("");
  const [cashAmount, setCashAmount] = useState(0);
  
  const getCalculatorToInt = () => {
    return parseFloat(calculatorCashAmount || 0)
  }
  
  const onDeleteCashAmount = () => {
    if (calculatorCashAmount) {
      try {
        let settingFormVal = "";
        let val = parseFloat(calculatorCashAmount);
        // means values are 0.0X
        if (val >= 0.1) {
          val = val / 10
          settingFormVal = val + "";
          settingFormVal = settingFormVal.match(/^-?\d+(?:\.\d{0,2})?/)[0]
        }
        
        setCalculatorCashAmount(settingFormVal)
      } catch (ignore) {}
    }
  }
  
  const onKeyboardClickCashAmount = (char) => {
    // block multiple dots or starting with dot
    if (char === ".") {
      if (!calculatorCashAmount) {
        return;
      }
      if (calculatorCashAmount.indexOf(".") > -1) {
        return;
      }
    }
    
    if (!calculatorCashAmount && (char === "0" || char === "00")) {
      return;
    }
    
    let settingFormVal = "";
    
    if (!calculatorCashAmount) {
      settingFormVal = `0.0${char}`
    } else {
      try {
        const nr = parseFloat(calculatorCashAmount);
        let total = 0;
        if (char === "00") {
          total = nr * 100
        } else {
          total = (nr * 10) + (parseInt(char) / 100)
        }
        settingFormVal = formatNumber(total)
      } catch (ignore) {
        settingFormVal = calculatorCashAmount
      }
    }
    
    //remove more than 2 decimals
    settingFormVal = settingFormVal.match(/^-?\d+(?:\.\d{0,2})?/)[0]
    setCalculatorCashAmount(settingFormVal)
  }
  
  const isSold = !isEmpty(values) && (values.status === 'SOLD');
  
  const onChange = (e) => {
    let { name, value } = e.target;
    if (name === 'initialCashAmount' && value) {
      value = Math.max(0, value)
    }
    setForm({ ...form, [name]: value })
  };
  
  const onCustomerChange = (e) => {
    let { name, value } = e.target;
    setForm({ ...form, customer: { ...form.customer, [name]: value } })
  }
  
  const setPaymentMethod = (paymentChannel) => {
    setForm({ ...form, paymentChannel})
    if (paymentChannel === paymentMethods.KAUF_AUF_RECHNUNG.key) {
      setAddingPayByInvoiceData(true)
    }
  }
  
  const setExtraPromotionAmount = (extraPromotionAmount) => {
    setForm({ ...form, extraPromotionAmount })
  }
  
  const setCustomer = (customer) => {
    setForm({ ...form, customer })
  }
  
  const setCustomerFirstname = (val) => {
    setForm({ ...form, customer: { ...form.customer, firstName: val } })
  }
  
  const removeCustomer = () => {
    setForm({ ...form, customer: null })
  }
  
  const [terminal, setTerminal] = useState(null);
  const [terminals, setTerminals] = useState([]);
  const { readerId } = useSelector(applicationSelectors.getReaderId)
  
  useEffect(() => {
    getPaymentTerminals(restaurantId, true, readerId).then(({ data }) => setTerminals(data)).catch(noop);
  }, [])
  
  useEffect(() => {
    if (!isEmpty(terminals) && form.paymentChannel === paymentMethods.ALLO_PAY.key) {
      setTerminal(terminals[0].id)
    } else {
      setTerminal(null)
    }
  }, [JSON.stringify(terminals), form.paymentChannel])
  
  const onCheckboxChange = (e) => setForm({ ...form, [e.target.name]: e.target.checked });
  
  const onSubmit = (notify) => {
    form.restaurantId = restaurantId;
    let giftCardQtd = parseFloat(giftCardCount);
    if (form.status === 'STOCK') {
      form.paymentChannel = null
      if (isEmpty(values)) {
        form.initialCashAmount = 0;
        form.disabled = false;
      }
    }
    if(giftCardQtd < 1 || isEmpty(giftCardQtd)) {
      giftCardQtd = 1
    }
    if (isBulkCreation) {
      submit(form, giftCardQtd);
    } else {
      submit(form);
    }
    if (notify) {
      if (isEmpty(values)) {
        if (form.status === "STOCK") {
          dispatch(appActions.setNotification("stock-gift-card-created", "success"))
        } else {
          dispatch(appActions.setNotification("sold-gift-card-created", "success", { amount: form.initialCashAmount }))
        }
      }
    }
  };
  
  const onUpdate = (notify) =>
    updateCard(form).then(() => {}).catch(() => {}).finally(() => {
      handleClose();
      setLoading(false)
      if (notify) {
        dispatch(appActions.setNotification("gift-card-updated", "success", { code: values.code }))
      }
    })
  
  const onSell = () => {
    const sellingTransaction = {
      type: 'CARD_SELLING',
      cardCode: values.code,
      restaurantId: restaurantId,
      paymentChannel: form.paymentChannel,
      extraPromotionAmount: form.extraPromotionAmount
    }
    const cashInTransaction = {
      type: 'CASH_IN',
      cardCode: values.code,
      restaurantId: restaurantId,
      amount: cashAmount,
      paymentChannel: form.paymentChannel,
      extraPromotionAmount: form.extraPromotionAmount
    }
    setLoading(true)
  
    createCardTransaction(cashInTransaction)
      .then(() => {
        onUpdate();
      })
      .catch(() => {})
      .finally(() => {
        setLoading(false)
      })
  }
  
  const onCloseEmailModal = () => {
    setOpenEmailModal(false)
    setReceiptEmail("")
  };
  const sendEmailReceipt = (email) => {
    if(!email || !selectedTransactionId){
      return
    }
    setReceiptEmail(email)
    sendGiftCardReceiptEmail(restaurantId, form.id, email, selectedTransactionId )
      .then(() => {
        setOpenEmailModal(false)
        dispatch(appActions.setNotification("gift-card-receipt-success", "success"))
      })
      .catch(() => {
        dispatch(appActions.setNotification("gift-card-receipt-error", "error"))
      })
  };
  
  const onCreate = () => {
    createCard(form).then(() => {}).catch(() => {});
  }

  const permissions = useSelector(accountSelectors.getPermissionIdentifiers);
  const canDelete = permissions && permissions.includes(permissionIdentifiers.CAN_DELETE_GIFT_CARD.value)
  
  const handleDelete = () => {
    if (!canDelete) {
      return;
    }
    _delete(form.id)
  }
  
  const onLoadMoney = () => {
    const cashInTransaction = {
      type: 'CASH_IN',
      cardCode: values.code,
      restaurantId: restaurantId,
      amount: cashAmount,
      paymentChannel: form.paymentChannel,
      extraPromotionAmount: form.extraPromotionAmount
    }
    setLoading(true)
    createCardTransaction(cashInTransaction)
      .then(() => {
        onUpdate();
      })
      .catch(() => {})
      .finally(() => {
        dispatch(appActions.setNotification("gift-card-topped-up", "success", { amount: cashAmount, code: values.code }))
      })
  }
  
  const onPrint = () => {
    printCard(values.id).then(() => {}).catch(() => {})
  }
  
  const isValid = ((form.status === 'SOLD') && (values.status !== 'SOLD')) ? (!!form.paymentChannel && form.initialCashAmount) : true;
  
  const isSelling = !isNew && values.status === "STOCK"
  
  const paymentChannels = useSelector(restaurantSelectors.getRestaurantPaymentChannels);
  
  const toggleStock = () => {
    if (form.status === "STOCK") {
      setForm({ ...form, status: "SOLD" })
      setIsBulkCreation(false)
    } else {
      setForm({ ...form, status: "STOCK" })
    }
  }
  
  const toggleDisabled = () => {
    setForm({ ...form, disabled: !form.disabled })
  }
  
  const isNew = isEmpty(values);
  const isCreating = isNew && form.status === "STOCK";
  const isCreatingAndSelling = isNew && form.status === "SOLD";
  const isToppingUp = !isNew && form.status === "SOLD";
  
  const canSubmit = ((form.status === 'SOLD') && (values.status !== 'SOLD'))
    ? (!!form.paymentChannel && !!form.initialCashAmount)
    : true
  
  const canGoToCheckout = ((form.status === 'SOLD') && (values.status !== 'SOLD')) ?
    !!form.initialCashAmount : true
  
  const [customerExpanded, setCustomerExpanded] = useState(false);
  const toggleCustomerExpanded = () => setCustomerExpanded(!customerExpanded);
  
  const isTerminalPayment = form.paymentChannel === paymentMethods.ALLO_PAY.key;
  const isOpenInvoicePayment = form.paymentChannel === paymentMethods.KAUF_AUF_RECHNUNG.key;
  
  // get card when allO Pay and keep polling
  const [card, setCard] = useState(null);
  const [processingCardPayment, setProcessingCardPayment] = useState(false);
  
  const [step, setStep] = useState(1);
  const [isMakingAdjustment, setIsMakingAdjustment] = useState(false);
  const startCheckout = () => {
    setStep(2);
  }

  const isPayByInvoiceMethodSelected = form?.paymentChannel === paymentMethods.KAUF_AUF_RECHNUNG.key;
  debugger
  const [addingPayByInvoiceData, setAddingPayByInvoiceData] = useState(false);
  const startAddingPayByInvoiceData = () => setAddingPayByInvoiceData(true);
  const stopAddingPayByInvoiceData = () => setAddingPayByInvoiceData(false);
  const [payByInvoiceData, setPayByInvoiceData] = useState(null)
  const updatePayByInvoiceData = (d) => {
    setPayByInvoiceData(d)
    stopAddingPayByInvoiceData()
  }
  const isPayByInvoiceDataValid = payByInvoiceData
    && payByInvoiceData.customer
    && payByInvoiceData.customer.name
    && payByInvoiceData.customer.email
    && isValidEmail(payByInvoiceData.customer.email)
    && payByInvoiceData.customer.address
    && payByInvoiceData.customer.address.street
    && payByInvoiceData.customer.address.number
    && payByInvoiceData.customer.address.zipCode
    && payByInvoiceData.customer.address.city
    && payByInvoiceData.customer.address.country
  
  const pollCard = (code) => {
    return resolveCard(restaurantId, code)
      .then(({ data }) => {
        setCard(data);
        if (data.paymentStatus === cardPaymentStatuses.COMPLETED.key) {
          if (!isEmpty(values)) {
            dispatch(appActions.setNotification("gift-card-topped-up", "success", { amount: cashAmount, code: values.code }))
            onUpdate();
          } else {
            onUpdate(true);
          }
        }
  
        if (data.paymentStatus === cardPaymentStatuses.FAILED.key) {
          dispatch(appActions.setNotification("gift-card-payment-failed", "error"));
          setLoading(false);
          setProcessingCardPayment(false);
          clearInterval(timer);
        }
      })
      .catch(() => {})
  }
  
  const finishCheckout = () => {
    if (isTerminalPayment && !isEmpty(terminal)) {
      if (!isEmpty(values) && values.id) {
        setProcessingCardPayment(true);
        setCard(values);
        createCardPayment(restaurantId, values.id, terminal, cashAmount, form.extraPromotionAmount)
          .then(() => {
            timer = setInterval(() => pollCard(values.code), 1000)
          })
          .catch(() => {
            setProcessingCardPayment(false);
            dispatch(appActions.setNotification("gift-card-payment-failed", "error"));
          })
      } else {
        setProcessingCardPayment(true);
        createCard({
          ...form,
          status: "STOCK",
          initialCashAmount: 0,
          cashAmount: 0,
          disabled: false,
          restaurantId: restaurantId,
          paymentChannel: null,
          extraPromotionAmount: null
        }).then(({ data }) => {
          createCardPayment(restaurantId, data.id, terminal, cashAmount, form.extraPromotionAmount)
            .then(() => {
              timer = setInterval(() => pollCard(data.code), 1000)
            })
            .catch(() => {
              setProcessingCardPayment(false);
            })
        })
      }
    } else if (isOpenInvoicePayment){
      if (!isEmpty(values) && values.id) {
        setLoading(true)
        const cashInTransaction = {
          type: 'CASH_IN',
          cardCode: values.code,
          restaurantId: restaurantId,
          amount: cashAmount,
          paymentChannel: form.paymentChannel,
          extraPromotionAmount: form.extraPromotionAmount,
          customer: payByInvoiceData?.customer,
          notes: payByInvoiceData?.notes
        }

        createCardTransaction(cashInTransaction)
          .then(() => {
            dispatch(appActions.setNotification("gift-card-topped-up", "success", { amount: cashAmount, code: values.code }))
            handleClose();
            setLoading(false)
          })
          .catch(() => {
            setLoading(false)
            dispatch(appActions.setNotification("gift-card-payment-failed", "error"));
          })
          .finally(() => {
            setLoading(false)
            stopAddingPayByInvoiceData();
          })
      } else {
        setLoading(true)
        const cashInTransaction = {
          type: 'CASH_IN',
          cardCode: values.code,
          restaurantId: restaurantId,
          amount: cashAmount,
          paymentChannel: form.paymentChannel,
          extraPromotionAmount: form.extraPromotionAmount,
          customer: payByInvoiceData?.customer,
          notes: payByInvoiceData?.notes
        }

        createCard({
          ...form,
          status: "STOCK",
          initialCashAmount: 0,
          cashAmount: 0,
          disabled: false,
          restaurantId: restaurantId,
          paymentChannel: null,
          extraPromotionAmount: null
        }).then(({ data }) => {
          createCardTransaction({ ...cashInTransaction, cardCode: data?.code })
            .then(() => {
              dispatch(appActions.setNotification("gift-card-topped-up", "success", { amount: cashAmount, code: values.code }))
              handleClose();
              setLoading(false)
            })
            .catch(() => {
              setLoading(false)
              dispatch(appActions.setNotification("gift-card-payment-failed", "error"));
            })
            .finally(() => {
              setLoading(false)
              stopAddingPayByInvoiceData();
            })
        })
      }



    } else {
      if (isSelling) {
        onSell();
      }
      if (isCreatingAndSelling) {
        onSubmit();
      }
  
      if (isToppingUp) {
        onLoadMoney();
      }
    }
  }
  
  useEffect(() => {
    if (!isEmpty(values) && (values.paymentStatus === cardPaymentStatuses.IN_PROGRESS)) {
      timer = setInterval(() => pollCard(values.code), 1000)
    }
    return function cleanup() {
      clearInterval(timer)
    }
  }, [])
  
  useEffect(() => {
    if (step === 1) {
      setPaymentMethod(null);
    }
    if (step === 2) {
      setPaymentMethod(paymentChannels[0])
    }
    
    if (step === 2 && (isCreatingAndSelling || isSelling) && !!form.initialCashAmount) {
      setCashAmount(parseFloat(form.initialCashAmount || ""))
    } else {
      const amount = getCalculatorToInt();
      setCashAmount(amount);
    }
    
  }, [calculatorCashAmount, step])
  
  const [report, setReport] = useState({});
  
  useEffect(() => {
    if (isSold) {
      getCardTransactionsReport(values.code).then(({ data = {} }) => {
        setReport(data)
        if (!isEmpty(data)) {
          const { transactions = [] } = (data || {});
          if (!isEmpty(transactions)) {
            setForm({ ...form, paymentChannel: transactions[0].paymentChannel })
          }
        }
      }).catch(() => {})
    }
  }, [isSold])

  const showPaymentModal = isEmpty(card) ? processingCardPayment : (card.paymentStatus === cardPaymentStatuses.IN_PROGRESS.key)

  const disablePaymentProcessing = submitting || (isTerminalPayment && !terminal) || (isOpenInvoicePayment && !isPayByInvoiceDataValid);
  
  const getHistory = () => {
    const { transactions = [] } = (report || {});
    if (isEmpty(transactions)) {
      return null;
    }
    
    return (
      <div>
        <TableContainer>
          <Table stickyHeader className={classes.table} aria-label="gift card transaction table">
            <TableHead>
              <TableRow>
                <TableCell>{t('date')}</TableCell>
                <TableCell align="right">{t("discount-amount-label")}</TableCell>
                <TableCell align="right">{t('promotion')}</TableCell>
                <TableCell align="right">{t('payment-channel')}</TableCell>
                <TableCell align="left">{t('common-type')}</TableCell>
                <TableCell>{t('account')}</TableCell>
                <TableCell />
              </TableRow>
            </TableHead>
            <TableBody>
              {transactions.map((transaction = {}) => {
                const { id, createdByAccount = {}, paymentChannel, type } = transaction;
                const { firstName, lastName } = (createdByAccount || {})
                let formattedUser = `${firstName ?? ""} ${lastName ?? ""}`
                const transactionType = giftCardTransactionsTypeOptions.filter(x => x.value === type);
                const typeText = transactionType.length > 0 ? transactionType[0].i18nKey : type;
                /*if (!isEmpty(accounts)) {
                  const account = (accounts.find(a => (a['userIds'] || {}).indexOf(createdBy) > -1))
                  formattedUser = account ? `${account.firstName ?? ""}${!!account.lastName ? " " : ""}${account.lastName ?? ""}` : '';
                }*/
                return (
                  <TableRow key={id}>
                    <TableCell align="left">{moment.utc(transaction.createdAt).format('DD-MM-YYYY')}</TableCell>
                    <TableCell align="right">{`${(type === 'CASH_OUT' || type === 'ADJUSTMENT') ? '-' : ''}${(transaction.amount || 0).toFixed(2)}€`}</TableCell>
                    <TableCell align="right">{`+${(transaction.extraPromotionAmount || 0).toFixed(2)}€`}</TableCell>
                    <TableCell align="left">{paymentChannel && paymentMethods[paymentChannel] ? t(paymentMethods[paymentChannel].i18nKey) : null}</TableCell>
                    <TableCell align="left">{t(typeText)}</TableCell>
                    <TableCell align="left">{formattedUser}</TableCell>
                    <TableCell onClick={() => {
                      setSelectedTransactionId(id)
                      setOpenEmailModal(true)
                    }}>
                      <ButtonBase style={{ border: `1px solid ${palette.grayscale.border}`, padding: "6px 8px", borderRadius: 10 }}>
                        <Typography style={{ ...typography.body.regular }}>{t("send-receipt")}</Typography>
                      </ButtonBase>
                    </TableCell>
                  </TableRow>
                )
              })}
              <TableRow>
                <TableCell align="left">
                  <Typography style={{ ...typography.body.medium }}>{t('cards-balance-label')}</Typography>
                </TableCell>
                <TableCell align="right">{`${(values.cashAmount || 0).toFixed(2)}€`}</TableCell>
                <TableCell/>
                <TableCell/>
                <TableCell/>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <ButtonBase style={{ borderRadius: 10, backgroundColor: palette.primary["500"], padding: "8px 6px"}} onClick={() => setIsMakingAdjustment(true)}>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale.white }}>{t("adjust-gift-card-total")}</Typography>
          </ButtonBase>
        </div>
      </div>
    )
  }
  
  const renderSelectedPaymentMethod = () => {
    if (!form.paymentChannel) {
      return ""
    }
    const method =  paymentMethods[form.paymentChannel]
    return method.i18nKey
  };
  
  const processAdjustment = () => {
    const AdjustmentTransaction = {
      type: "ADJUSTMENT",
      cardCode: values.code,
      restaurantId: restaurantId,
      amount: cashAmount,
    }
    setLoading(true)
    createCardTransaction(AdjustmentTransaction)
      .then(() => {
        onUpdate();
      })
      .catch(() => {})
      .finally(() => {
        setLoading(false)
      })
  }
  
  const calculatorOnClick = (v) => {
    if ((v === '0' || v === '00') && (!touched)) {
      return
    }
    
    if (!touched) {
      setGiftCardCount(v + '')
    }
    
    if (touched && (giftCardCount.length < 5)) {
      setGiftCardCount(giftCardCount + v)
    }
    
    setTouched(true)
  }
  
  const calculatorOnDelete = () => {
    if (!touched) {
      setGiftCardCount('')
    }
    
    if (touched && giftCardCount) {
      setGiftCardCount(giftCardCount.slice(0, -1))
    }
    
    setTouched(true)
  }
  
  const ExtraPromotionAmount = () => {
    if (!cashAmount) {
      return null
    }
    
    try {
      const percent10 = (10 * cashAmount / 100).toFixed(2);
      const percent15 = (15 * cashAmount / 100).toFixed(2);
      const percent20 = (20 * cashAmount / 100).toFixed(2);
      const percent30 = (30 * cashAmount / 100).toFixed(2);
  
      const list = [
        { amount: null, label: t('none') },
        { amount: percent10, label: '10%' },
        { amount: percent15, label: '15%' },
        { amount: percent20, label: '20%' },
        { amount: percent30, label: '30%' },
      ]
      
      return (
        <div className={checkoutClasses.paymentMethod}>
          <Typography style={{ ...typography.body.medium }}>
            {t('select-sale-discount')}
          </Typography>
          <div className={checkoutClasses.paymentMethods}>
            {list.map(item => {
              return (
                <ButtonBase disableRipple disableTouchRipple className={checkoutClasses.paymentMethodOption} key={item.label} onClick={() => setExtraPromotionAmount(item.amount)}>
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                      {`${item.label} (${item.amount || '0.00'}€)`}
                    </Typography>
                  </div>
                  <Radio checked={item.amount === form.extraPromotionAmount} />
                </ButtonBase>
              )
            })}
          </div>
        </div>
      )
    } catch (e) {
      return null
    }
  }
  
  const getAdjustments = () => {
    const adjustmentBiggerThanBalance = form.cashAmount - cashAmount < 0
    return (
      <div style={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        width: "100%",
        overflow: "auto"
      }}>
        <div style={{
          background: palette.grayscale["300"],
          borderRadius: 16,
        }}>
          <div style={{ marginTop: 32, marginLeft: 12, marginRight: 12, marginBottom: 24 }}>
            <Typography style={{ ...typography.body.medium, marginBottom: 16, textAlign: "center", textTransform: "capitalize" }}>
              {t('define-amount-to-reduce')}
            </Typography>
            <Typography style={{ ...typography.x.paymentAmount, textAlign: "center" }}>
              <span style={{ color: !cashAmount ? palette.grayscale["400"] : "inherit" }}>{formatNumber(cashAmount)}</span>€
            </Typography>
          </div>
          {!isNew && (
            <div style={{ marginTop: 24, padding: 12 }}>
              <Keyboard
                style={{ ...shadows.base }}
                specialChar="00"
                onDelete={onDeleteCashAmount}
                onClick={onKeyboardClickCashAmount}
              />
            </div>
          )}
        </div>
        <div className={checkoutClasses.checkoutAction}>
          <div className={checkoutClasses.expendableSummary}>
            <div className={checkoutClasses.summaryLine} style={{ marginTop: 12 }}>
              <Typography style={{ ...typography.body.medium }}>{t('adjusted-balance')}</Typography>
              <Typography style={{ ...typography.body.medium, color: adjustmentBiggerThanBalance ? palette.primary["500"] : null }}>{formatNumber(form.cashAmount - cashAmount)}€</Typography>
            </div>
          </div>
          <div className={checkoutClasses.paymentAction}>
            <ButtonBase
              disableRipple
              disableTouchRipple
              disabled={disablePaymentProcessing || loading || !cashAmount || processingCardPayment || adjustmentBiggerThanBalance}
              onClick={processAdjustment}
              style={{
                background: (disablePaymentProcessing|| loading || !cashAmount || processingCardPayment || adjustmentBiggerThanBalance) ? palette.grayscale["400"] : palette.primary["500"],
                borderRadius: 12,
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
                textAlign: "left"
              }}
            >
              <div style={{ padding: 12, flex: 1 }}>
                <Typography style={{ ...typography.body.medium, color: disablePaymentProcessing ? '#FFFFFF' : palette.grayscale["100"] }}>
                  {t('reduce-balance-by')}
                </Typography>
              </div>
              <div style={{ display: "flex", alignItems: "center", borderLeft: `1px solid rgba(242, 242, 242, 0.2)`, padding: 12 }}>
                <Typography style={{ ...typography.body.medium, color: disablePaymentProcessing ? '#FFFFFF' : palette.grayscale["100"] }}>
                  {formatNumber(cashAmount)}€
                </Typography>
              </div>
            </ButtonBase>
          </div>
        </div>
      </div>
    )
  }
  
  const getCheckout = () => {
    return (
      <div style={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        width: "100%",
        overflow: "auto"
      }}>
        <div style={{
          background: palette.grayscale["300"],
          borderRadius: 16,
        }}>
          <div style={{ marginTop: 32, marginLeft: 12, marginRight: 12, marginBottom: 24 }}>
            <Typography style={{ ...typography.body.medium, marginBottom: 16, textAlign: "center", textTransform: "capitalize" }}>
              {!isNew ? t('amount-to-pay') : t('define-amount-to-pay')}
            </Typography>
            <Typography style={{ ...typography.x.paymentAmount, textAlign: "center" }}>
              <span style={{ color: !cashAmount ? palette.grayscale["400"] : "inherit" }}>{formatNumber(cashAmount)}</span>€
            </Typography>
          </div>
          {!isNew && (
            <div style={{ marginTop: 24, padding: 12 }}>
              <Keyboard
                style={{ ...shadows.base }}
                specialChar="00"
                onDelete={onDeleteCashAmount}
                onClick={onKeyboardClickCashAmount}
              />
            </div>
          )}
        </div>
        <div className={checkoutClasses.checkoutForm} style={{ marginTop: 24 }}>
          <div className={checkoutClasses.breadcrumbs}>
            <TerminalMenuIcon />
            <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
              {t('checkout')}
            </Typography>
          </div>
          <div className={checkoutClasses.paymentMethod}>
            <Typography style={{ ...typography.body.medium }}>
              {t('select-payment-method')}
            </Typography>
            <div className={checkoutClasses.paymentMethods}>
              {paymentChannels.map(method => {
                const pMethod = paymentMethods[method];
                return (
                  <ButtonBase disableRipple disableTouchRipple className={checkoutClasses.paymentMethodOption} key={pMethod.key} onClick={() => setPaymentMethod(pMethod.key)}>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      {pMethod.icon}
                      <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                        {t(pMethod.i18nKey)}
                      </Typography>
                    </div>
                    <Radio checked={pMethod.key === form.paymentChannel} />
                  </ButtonBase>
                )
              })}
            </div>
          </div>
          {(form.paymentChannel === paymentMethods.ALLO_PAY.key) && !isEmpty(terminals) && (
            <div className={checkoutClasses.paymentMethod}>
              <Typography style={{ ...typography.body.medium }}>
                {t('select-payment-terminal')}
              </Typography>
              <div className={checkoutClasses.paymentMethods}>
                {terminals.map(_terminal => {
                  return (
                    <ButtonBase disableRipple disableTouchRipple className={checkoutClasses.paymentMethodOption} key={_terminal.id} onClick={() => setTerminal(_terminal.id)}>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                          {t(_terminal.label)}
                        </Typography>
                      </div>
                      <Radio checked={_terminal.id === terminal} />
                    </ButtonBase>
                  )
                })}
              </div>
            </div>
          )}
          <ExtraPromotionAmount />
          {isPayByInvoiceMethodSelected && (
            <div className={checkoutClasses.paymentMethod}>
              <div
                style={{ display: "flex", alignItems: "center", justifyContent: "space-between", minHeight: 24 }}>
                <ButtonBase disableRipple disableTouchRipple
                            style={{ padding: 0, display: "flex", flexDirection: "column", alignItems: "flex-start", opacity: showPaymentModal ? 0.5 : 1 }}
                            onClick={startAddingPayByInvoiceData}
                            disabled={showPaymentModal}
                >
                  {!isPayByInvoiceDataValid && (
                    <React.Fragment>
                      <div style={{ display: "flex", alignItems: "center", justifyContent: "flex-start" }}>
                        <WarningIcon20Red />
                        <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                          {t("data-missing-for-paying-by-receipt")}
                        </Typography>
                      </div>
                      <div>
                        <Typography style={{
                          ...typography.body.medium,
                          marginTop: 8,
                          padding: "5px 11px",
                          border: `1px solid ${palette.grayscale.border}`,
                          borderRadius: 10
                        }}>{t("fill-invoice-data")}</Typography>
                      </div>
                    </React.Fragment>
                  )}
                  {isPayByInvoiceDataValid && (
                    <div style={{ display: "flex", flexDirection: "column", textAlign: "left" }}>
                      <div style={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
                        <EditIcon20 />
                        <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
                          {t("edit-invoice-data")}
                        </Typography>
                      </div>
                      <Typography style={{ ...typography.body.regular, marginTop: 4 }}>{payByInvoiceData?.customer?.name}</Typography>
                      <Typography style={{ ...typography.body.regular, marginTop: 2 }}>{payByInvoiceData?.customer?.email}</Typography>
                    </div>
                  )}
                </ButtonBase>
              </div>
            </div>
          )}
        </div>
        <div className={checkoutClasses.checkoutAction}>
          <div className={checkoutClasses.expendableSummary}>
            <div className={checkoutClasses.summaryLine}>
              <Typography style={{ ...typography.body.medium }}>{t('order-total')}</Typography>
              <Typography style={{ ...typography.body.medium }}>{formatNumber(cashAmount)}€</Typography>
            </div>
          </div>
          <div className={checkoutClasses.paymentAction}>
            <ButtonBase
              disableRipple
              disableTouchRipple
              disabled={disablePaymentProcessing || loading || !cashAmount || processingCardPayment}
              onClick={finishCheckout}
              style={{
                background: (disablePaymentProcessing|| loading || !cashAmount || processingCardPayment) ? palette.grayscale["400"] : palette.primary["500"],
                borderRadius: 12,
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
                textAlign: "left"
              }}
            >
              <div style={{ padding: 12, flex: 1 }}>
                <Typography style={{ ...typography.body.medium, color: disablePaymentProcessing ? '#FFFFFF' : palette.grayscale["100"] }}>
                  {t('process-payment')} {`(${t(renderSelectedPaymentMethod())})`}
                </Typography>
              </div>
              <div style={{ display: "flex", alignItems: "center", borderLeft: `1px solid rgba(242, 242, 242, 0.2)`, padding: 12 }}>
                <Typography style={{ ...typography.body.medium, color: disablePaymentProcessing ? '#FFFFFF' : palette.grayscale["100"] }}>
                  {formatNumber(cashAmount)}€
                </Typography>
              </div>
            </ButtonBase>
          </div>
        </div>
      </div>
    )
  }
  
  const getDone = () => {
    if (canSubmit && step !== 2) {
      return () => onSubmit(true);
    }
    return null;
  }
  
  const getBack = () => {
    if (step === 2) {
      return () => setStep(1)
    }
    return null
  };
  
  const getRight = () => {
    if (step === 2) {
      return null;
    }
    if (isMakingAdjustment) {
      return null;
    }
    
    if (step === 1) {
      let rightButton = null;
      if (isCreating) {
        rightButton = (
          <ButtonBase
            key={"done"}
            disableRipple
            disableTouchRipple
            disabled={!canSubmit || submitting}
            onClick={onSubmit}
            style={{ paddingTop: 12, paddingBottom: 12, paddingLeft: 16, paddingRight: 16, display: "flex", alignItems: "center", background: (!canSubmit || submitting) ? palette.grayscale["500"] : palette.primary["400"], marginLeft: 12, borderRadius: 12 }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {t('common-create')}
            </Typography>
          </ButtonBase>
        )
      }
      if (isCreatingAndSelling || isSelling) {
        rightButton = (
          <ButtonBase
            key={"checkout"}
            disableRipple
            disableTouchRipple
            disabled={!canGoToCheckout}
            onClick={startCheckout}
            style={{
              paddingTop: 12, paddingBottom: 12, paddingLeft: 16, paddingRight: 16,
              display: "flex", alignItems: "center", background: (!canGoToCheckout || submitting) ? palette.grayscale["500"] : palette.primary["400"] , marginLeft: 12, borderRadius: 12
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {t('checkout')}
            </Typography>
          </ButtonBase>
        )
      }
      if (isToppingUp) {
        if (values.disabled) {
          if (form.disabled) {
            rightButton = (
              <ButtonBase
                key={"disabled"}
                disableRipple
                disableTouchRipple
                disabled
                style={{
                  paddingTop: 12, paddingBottom: 12, paddingLeft: 16, paddingRight: 16,
                  display: "flex", alignItems: "center", background: palette.grayscale["400"], marginLeft: 12, borderRadius: 12
                }}
              >
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                  ⚠️ {t('card-disabled-label')}
                </Typography>
              </ButtonBase>
            )
          }
          if (!form.disabled) {
            rightButton = (
              <ButtonBase
                key={"disabled"}
                disableRipple
                disableTouchRipple
                onClick={canSubmit ? onSubmit : null}
                style={{
                  paddingTop: 12, paddingBottom: 12, paddingLeft: 16, paddingRight: 16,
                  display: "flex", alignItems: "center", background: (!canSubmit || submitting) ? palette.grayscale["500"] : palette.primary["400"], marginLeft: 12, borderRadius: 12
                }}
              >
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                  {t('common-save')}
                </Typography>
              </ButtonBase>
            )
          }
          
        } else {
          rightButton = (
            <ButtonBase
              key={"checkout"}
              disableRipple
              disableTouchRipple
              disabled={!canSubmit}
              onClick={startCheckout}
              style={{
                paddingTop: 12, paddingBottom: 12, paddingLeft: 16, paddingRight: 16,
                display: "flex", alignItems: "center", background: (!canSubmit || submitting) ? palette.grayscale["500"] : palette.primary["400"], marginLeft: 12, borderRadius: 12
              }}
            >
              <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                {t('card-load-money-label')}
              </Typography>
            </ButtonBase>
          )
        }
      }
      
      return <div style={{ display: "flex", alignItems: "center" }}>
        {!isEmpty(values) && (
          <ButtonBase
            key={"print"}
            disableRipple
            disableTouchRipple
            onClick={onPrint}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 16,
              paddingRight: 16,
              marginRight: !!rightButton ? 4 : 0,
              display: "flex",
              alignItems: "center",
              background: "transparent",
              borderRadius: 12,
              whiteSpace: "nowrap"
            }}
          >
            <Typography style={{ ...typography.body.medium }}>
              {t('waiter-common-print')}
            </Typography>
          </ButtonBase>
        )}
        {rightButton}
      </div>
    }
  }
  
  const getCardRender = () => {
    return (
      <div style={{ ...shadows.large, background: palette.grayscale["100"], borderRadius: 12, overflow: "hidden" }}>
        <div style={{ height: 120, background: palette.primary["400"] }} />
        <div style={{ padding: 12, display: "flex", alignItems: "top", justifyContent: "space-between" }}>
          <div>
            <Typography style={{ ...typography.body.medium }}>
              {t('receipt-card-label')}
            </Typography>
          </div>
          <div>
            <Typography style={{ ...typography.large.semiBold }}>
              {(isEmpty(values) || values.status === "STOCK") ? form.initialCashAmount : values.cashAmount}€
            </Typography>
          </div>
        </div>
      </div>
    )
  }
  
  useEffect(() => {
    setForm({ ...form, initialCashAmount: "", disabled: false })
  }, [form.status])
  
  return (
    <>
      <Modal
        open={!!open}
        onClose={handleClose}
        {...otherProps}
        fullScreen
        style={{ marginTop: 16 }}
        PaperProps={{ style: { background: "transparent",  ...shadows.large, zIndex: 9999 } }}
      >
        <ModalBar
          title={form.id ? sliceText(form.code, 30) : t('card-editor-actions-create-card')}
          onClose={handleClose}
          onDone={getDone()}
        />
        <div style={{ height: "100%", overflow: "auto" }}>
          <div style={{ minHeight: "100%", background: palette.grayscale["200"], paddingTop: 24, paddingBottom: 24, paddingLeft: 12, paddingRight: 12 }}>
              <div style={{ maxWidth: "485px", margin: "0 auto", display: "flex", flexDirection: "column", height: "100%" }}>
                {showPaymentModal && (
                  <GiftCardPaymentModal titleI18n={"allo-pay-terminal"} open cardId={card ? card.id : null} />
                )}
                {step === 1 && !isMakingAdjustment && (
                  <div>
                    {/* InStock Warning */}
                    {!isNew && (values.status === "STOCK") && (
                      <div className={classes.switchSetting} style={{ marginBottom: 24 }}>
                        <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                          <div style={{ marginRight: 8 }}>
                            <Typography style={{ ...typography.body.medium, whiteSpace: "break-spaces" }}>
                              ⚠️ {t('card-status-stock')}
                            </Typography>
                            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], whiteSpace: "break-spaces", marginTop: 4 }}>
                              {t('card-status-stock-description')}
                            </Typography>
                          </div>
                        </div>
                      </div>
                    )}
                    {/* Stock or Sold status selector */}
                    {isEmpty(values) && (
                      <div>
                        <div className={classes.switchSetting} style={{ marginTop: 0 }}>
                          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                            <div style={{ marginRight: 8 }}>
                              <Typography style={{ ...typography.body.medium, whiteSpace: "break-spaces" }}>
                                {t('card-status-stock')}
                              </Typography>
                              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], whiteSpace: "break-spaces", marginTop: 4 }}>
                                {t('card-status-set-to-stock-description')}
                              </Typography>
                            </div>
                            <Switch checked={form.status === "STOCK"} onClick={toggleStock} />
                          </div>
                        </div>
                        <div>
                          {form.status === "STOCK" && (
                            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
                              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                                <div style={{ marginRight: 8 }}>
                                  <Typography style={{ ...typography.body.medium, whiteSpace: "break-spaces" }}>
                                    {t('multiple-card-creation')}
                                  </Typography>
                                  <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], whiteSpace: "break-spaces", marginTop: 4 }}>
                                    {t('multiple-card-creation-description')}
                                  </Typography>
                                </div>
                                <Switch checked={isBulkCreation} onClick={() => setIsBulkCreation(!isBulkCreation)} />
                              </div>
                            </div>
                          )}
                          {form.status === "STOCK" && isBulkCreation && (
                            <div style={{ marginTop: 24 }}>
                              <Field
                                label={`${t('number-of-cards')}`}
                                name="count"
                                value={giftCardCount}
                                type="text"
                                readOnly
                                variant="filled"
                                autoComplete="off"
                              />
                              <div style={{ marginTop: 24 }}>
                                <Keyboard onClick={calculatorOnClick} onDelete={calculatorOnDelete}
                                          style={{ ...shadows.base }} specialChar={false} />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                    )}
                    {/* Initial Cash Amount field */}
                    {(isEmpty(values) && form.status !== "STOCK") && (
                      <div style={{ marginTop: 24 }}>
                        <Field
                          inputRef={inputRef}
                          label={`${t('card-initial-cash-amount-label')}`}
                          name="initialCashAmount"
                          value={form.initialCashAmount}
                          type="text"
                          placeholder="0"
                          onChange={noop}
                          variant="filled"
                          autoComplete="off"
                          required={true}
                        />
                      </div>
                    )}
                    {/* Initial Cash Amount keyboard */}
                    {(isEmpty(values) && form.status !== "STOCK") && (
                      <div style={{ marginTop: 24 }}>
                        <Keyboard
                          style={{ ...shadows.base }}
                          specialChar="00"
                          onDelete={onKeyboardDeleteInitialCashAmount}
                          onClick={onKeyboardClickInitialCashAmount}
                        />
                      </div>
                    )}
                    {/* ReadOnly Balance field */}
                    {!isEmpty(values) && (values.status !== "STOCK") && (
                      <div style={{ marginTop: 0 }}>
                        <Field
                          label={`${t('cards-balance-label')}`}
                          name="cashAmount"
                          value={values.cashAmount}
                          type="text"
                          onChange={noop}
                          variant="filled"
                          autoComplete="off"
                          disabled
                        />
                      </div>
                    )}
                    {/* Transaction history */}
                    {!isEmpty(values) && (
                      <div style={{ marginTop: 24, marginLeft: isBiggerScreen ? -60 : 0, marginRight: isBiggerScreen ? -60 : 0, display: "flex", flexDirection: "column" }}>
                        {getHistory()}
                      </div>
                    )}
                    {/* Readonly Code and ExternalCode input */}
                    <div style={{ marginTop: 24, paddingTop: 24, paddingBottom: 24, borderTop: `1px dashed ${palette.grayscale.divider}`, borderBottom: `1px dashed ${palette.grayscale.divider}`, }}>
                      {/* ReadOnly Code field */}
                      {!isEmpty(values) && !isBulkCreation &&(
                        <div style={{ marginBottom: 24, display: "flex", gap: 8, alignItems: "flex-end" }}>
                          <Field
                            label={`${t('card-code-label')}`}
                            name="code"
                            value={values.code}
                            variant="filled"
                            autoComplete="off"
                            disabled
                          />
                          <ButtonBase style={{ display: "flex", gap: 4, height: 43, minWidth: "fit-content", alignItems: "center", borderRadius: 10, border: `1px solid ${palette.grayscale.border}`, padding: "6px 8px" }}
                                      onClick={() => setIsOpenQrCodeModal(true)}>
                            <QrCode20 />
                            <Typography style={{ ...typography.body.regular }}>{t("qr-code")}</Typography>
                          </ButtonBase>
                        </div>
                      )}
                      {!isBulkCreation && (
                        <>
                          <Field
                            label={`${t('card-external-code-label')}`}
                            name="externalCode"
                            value={form.externalCode}
                            onChange={onChange}
                            variant="filled"
                            autoComplete="off"
                            required={false}
                            placeholder="XXX-XX"
                          />
                          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], textAlign: "left", width: "100%", marginTop: 10 }}>
                        {t('card-external-code-information-label')}
                          </Typography>
                        </>
                      )}
                    </div>
                    {/* Customer input with autocomplete */}
                    {!isBulkCreation && (
                      <div className={classes.customer}>
                        <div className={classes.fields}>
                          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                            <div style={{ flex: 1 }}>
                              <CustomerSuggestions value={isEmpty(form.customer) ? "" : form.customer.firstName}
                                                   setValue={setCustomerFirstname} setCustomer={setCustomer} />
                            </div>
                          </div>
                          <div style={{ display: "flex", alignItems: "center", gap: 12,  marginTop: 12 }}>
                            <Field
                              label={`${t("card-firstName-label")}`}
                              name="firstName"
                              onChange={onCustomerChange}
                              value={isEmpty(form.customer) ? "" : form.customer.firstName}
                              placeholder={t("card-firstName-label")}
                            />
                            <Field
                              label={`${t("card-lastName-label")}`}
                              name="lastName"
                              onChange={onCustomerChange}
                              value={isEmpty(form.customer) ? "" : form.customer.lastName}
                              placeholder={t("card-lastName-label")}
                            />
                          </div>
                          <div style={{ marginTop: 12 }}>
                            <ButtonBase disableRipple disableTouchRipple
                                        style={{ padding: 0, display: "flex", alignItems: "center" }}
                                        onClick={toggleCustomerExpanded}>
                              {customerExpanded ? <CollapseIcon20 /> : <ExpandIcon20 />}
                              <Typography
                                style={{ ...typography.body.medium, marginLeft: 2, color: palette.grayscale["600"] }}>
                                {t(customerExpanded ? "see-less" : "see-more")}
                              </Typography>
                            </ButtonBase>
                          </div>
                          {customerExpanded && (
                            <div style={{ borderTop: `1px dashed ${palette.grayscale["400"]}`, marginTop: 12 }}>
                              <div style={{ marginTop: 12 }}>
                                <Field
                                  label={`${t("card-email-label")}`}
                                  name="email"
                                  type="text"
                                  onChange={onCustomerChange}
                                  value={isEmpty(form.customer) ? "" : form.customer.email}
                                  placeholder="<EMAIL>"
                                />
                              </div>
                              <div style={{ marginTop: 12 }}>
                                <Field
                                  label={`${t("pickup-order-creation-form-phone-field-label")}`}
                                  name="phone"
                                  type="number"
                                  pattern="\d*"
                                  inputmode="decimal"
                                  onChange={onCustomerChange}
                                  value={isEmpty(form.customer) ? "" : form.customer.phone}
                                  placeholder="+49 12345"
                                />
                              </div>
                              <div style={{ display: "flex", justifyContent: "flex-end", marginTop: 12 }}>
                                <ButtonBase style={{
                                  borderRadius: 10,
                                  padding: "6px 8px",
                                  border: `1px solid ${palette.grayscale.border}`
                                }} onClick={removeCustomer}>
                                  <Typography
                                    style={{ ...typography.body.regular }}>{t("order-item-remove-btn")}</Typography>
                                </ButtonBase>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* Deactivate Card field */}
                    {!(isEmpty(values) && form.status !== "STOCK") && !isBulkCreation && (
                      <div className={classes.switchSetting} style={{ marginTop: 24 }}>
                        <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                          <div style={{ marginRight: 8 }}>
                            <Typography style={{ ...typography.body.medium, whiteSpace: "break-spaces" }}>
                              {t('card-enabled-label')}
                            </Typography>
                            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], whiteSpace: "break-spaces", marginTop: 4 }}>
                              {t('card-enabled-description')}
                            </Typography>
                          </div>
                          <Switch checked={!form.disabled} onClick={toggleDisabled} />
                        </div>
                      </div>
                    )}
                    {/* Delete Card Btn */}
                    {!isEmpty(values) && canDelete && (
                      <div style={{ marginBottom: 20, background: palette.grayscale["300"], borderRadius: 12, padding: "16px", marginTop: 24 }}>
                        <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("delete-section-header-gift-card")}</Typography>
                        <div style={{ marginBottom: 12 }} >
                          <Typography style={{ ...typography.body.regular, marginRight: 2 }}>
                            {t("this-action-deletes-gift-card")}
                          </Typography>
                        </div>
                        <div>
                          <Confirm
                            title={t("delete-section-header-gift-card")}
                            body={(
                              <Typography color="textSecondary" variant="body2">
                                <br />
                                {t("delete-table-description")}
                                <br />
                                {t("are-you-sure-message")}
                              </Typography>
                            )}
                          >
                            {Confirm => (
                              <ButtonBase style={{ borderRadius: 12, padding: '6px 12px', background: palette.negative["500"] }} disableRipple disableTouchRipple onClick={Confirm(handleDelete)}>
                                <TrashIcon20White/>
                                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"], marginLeft: 4 }}>
                                  {t('common-delete')}
                                </Typography>
                              </ButtonBase>
                            )}
                          </Confirm>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                {step === 2 && getCheckout()}
                {isMakingAdjustment && getAdjustments()}
              </div>
          </div>
        </div>
        {!isMakingAdjustment && (
          <BottomBar
            onBack={getBack()}
            title={isCreating ? null : t("step-count-of-total", { count: step, total: 2 })}
            right={getRight()}
          />
        )}
      </Modal>
    {openEmailModal && <TextUpdateModal titleI18n={"pdf-receipt"} open={openEmailModal} onClose={onCloseEmailModal} value={receiptEmail} setValue={sendEmailReceipt} type={'email'}/>}
    {isOpenQrCodeModal && <GiftCardQrModal open={isOpenQrCodeModal} onClose={() => setIsOpenQrCodeModal(false)} qrCode={form.code} />}
    {/* pay by invoice data */}
    {addingPayByInvoiceData && (
      <CreateInvoiceModal open={Boolean(addingPayByInvoiceData)}
                          onClose={stopAddingPayByInvoiceData} submit={updatePayByInvoiceData} data={payByInvoiceData} hasServiceFee={false}/>
    )}
  </>
  )
}

export default withTranslation("common")(GiftCardModal);
