import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import isEmpty from "../../../utils/isEmpty";
import moment from "moment";
import DatePicker from "../../DatePicker";
import Checkbox from "../../_toggles/Checkbox";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";

const ExcludedDatesReservationModal = ({ t, open, onClose, values = [], setValue, titleI18n, }) => {
  
  const [dates, setDates] = useState([{ date: moment().format("yyyy-MM-DD"), isStaffBlocked: false }]);
  const [error, setError] = useState("")
  
  const onDone = () => {
    
    const hasDuplicates = (dates || []).some((item, index, arr) =>
      arr.findIndex(otherItem => otherItem.date === item.date) < index
    );
    
    if(hasDuplicates){
      setError("please-remove-duplicate-date-to-proceed")
    } else {
      setValue(dates);
      setError("")
      onClose();
    }
  }
  
  const onChange = (stateKey, value, index) => {
    setError("")
    setDates(dates => {
      const updatedDates = [...dates];
      updatedDates[index][stateKey] = value;
      return updatedDates
    })
  }
  
  const deleteDate = (indexToBeDeleted) => {
    setError("")
    const filteredDates = dates.filter((d, index ) => index !== indexToBeDeleted)
    setDates(filteredDates)
  };
  
  const addMoreDates = () => {
    const newDate = { date: moment().format("yyyy-MM-DD"), isStaffBlocked: false }
    const updatedDates = [...dates, newDate]
    setDates(updatedDates)
  };
  
  useEffect(() => {
    if(!isEmpty(values)){
      setDates(values);
    }
  }, [values, open])
  
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, minWidth: 400, maxWidth: "90%" } }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} onDone={onDone} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20, minWidth: 360 }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
          {isEmpty(dates) && (
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["400"] }}>
                {t("no-dates-set-up-yet")}
              </Typography>
            </div>
          )}
          {!isEmpty(dates) && (dates || []).map((d, index) => {
            const { isStaffBlocked, date } = d
            
            return (
              <div style={{ display: "flex", alignItems: "center", width: "100%", justifyContent: "space-evenly", marginBottom: 12, gap: 12 }}>
                <DatePicker DatePicker value={date} setValue={(dt) => {
                  const formattedDate = moment(dt).format('YYYY-MM-DD')
                  onChange("date", formattedDate, index)
                  }}
                />
                <ButtonBase
                  style={{ display: "flex", alignItems: "center", justifyContent: "space-around", width: "fit-content", backgroundColor: palette.grayscale.white, borderRadius: 10, padding: " 8px 10px" }}
                  onClick={() => onChange("isStaffBlocked", !isStaffBlocked, index)} disableRipple disableTouchRipple>
                  <Typography style={{ ...typography.body.medium, marginRight: 4 }}>
                    {t("also-blocked-staff-checkbox")}
                  </Typography>
                  <Checkbox checked={isStaffBlocked} />
                </ButtonBase>
                <ButtonBase onClick={() => deleteDate(index) } style={{ border: `1px solid ${palette.grayscale.border}`, borderRadius: 10, padding: "6px 8px" }}>
                  <Typography style={{ ...typography.body.regular }}>
                    {t("remove")}
                  </Typography>
                </ButtonBase>
              </div>
            )
          })}
          <div style={{ borderTop: `1px dashed ${palette.grayscale.border}`, marginTop: 24, paddingTop: 12, display: "flex", justifyContent: "flex-end" }}>
            <ButtonBase style={{ border: `1px solid ${palette.grayscale.border}`, borderRadius: 10, padding: "6px 8px" }}
                        onClick={addMoreDates}>
              <Typography style={{ ...typography.body.medium }}>{t('add-more')}</Typography>
            </ButtonBase>
          </div>
          {error && (
            <div style={{ marginTop: 12 }}>
              <Typography style={{ ...typography.body.medium, color: palette.primary["500"] }}>{t(error)}</Typography>
            </div>
          )}
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(ExcludedDatesReservationModal);
