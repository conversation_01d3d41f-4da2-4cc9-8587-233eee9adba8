import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import isEmpty from "../../../utils/isEmpty";
import Field from "../../form/Field";
import { ButtonBase } from "@material-ui/core";
import { PlusIconFilled20 } from "../../../utils/icons";
import Button from "@material-ui/core/Button";
import EmptyScreen from "../../_placeholder/EmptyScreen";
import { timesheetPeriodTypeOptions, timesheetPeriodTypes } from "../../../../redux/constants";
import NativeSelect from "@material-ui/core/NativeSelect";
import FormControl from "@material-ui/core/FormControl";
import { getAccounts, getEmployee } from "../../../api";
import { noop } from "../../../utils/const";
import { useSelector } from "react-redux";
import { restaurantSelectors } from "../../../../redux/selectors";
import moment from "moment";

const data = {
  date: moment().format("yyyy-MM-DD"),
  accountId: null,
  hourlyRate: "",
  periods: [{
    start: "00:00",
    stop: "00:00",
    type: "ACTIVE"
  }],
}

const TimesheetModal = ({ t, open, onClose, values, submit }) => {
  const consolidatedData = isEmpty(values) ? { ...data } : { ...data, ...values };
  const [form, setForm] = useState(consolidatedData);
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const [accounts, setAccounts] = useState([]);
  const [employee, setEmployee] = useState({});
  
  const getHourlyRateVal = (val = "") => {
    if (val) {
      const matches = val.match(/\d+(?:\.\d{0,2})?/)
      if (isEmpty(matches)) {
        val = ""
      } else {
        val = matches[0]
      }
    }
    return val
  }
  
  useEffect(() => {
    getAccounts(restaurantId, 100, 0, null)
      .then(({ data: userData }) => setAccounts(userData.items))
      .catch(noop);
  }, [])
  
  const fetchEmployee = (accountId) => {
    getEmployee(restaurantId, accountId)
      .then(({ data }) => {
        setEmployee(data);
        const { hourlyRate: fetchedHourlyRate } = data;
        setForm({ ...form, hourlyRate: fetchedHourlyRate });
      })
      .catch(noop)
  }
  
  useEffect(() => {
    if (form.accountId && isEmpty(values)) {
      fetchEmployee(form.accountId);
    }
  }, [form.accountId])
  
  const onChange = (e) => {
    let val = e.target.value;
    const name = e.target.name;
    
    if (name === "hourlyRate") {
      val = getHourlyRateVal(val);
    }
    setForm({ ...form, [name]: val })
  }
  
  const onDone = () => {
    // const clearedPeriods = isEmpty(periods) ?
    //   null :
    //   periods
    //     .filter(p =>
    //       p.opens &&
    //       p.closes &&
    //       !(p.opens === "00:00:00" && p.closes === "00:00:00")
    //     )
    // setValue(clearedPeriods);
    submit(form);
    setForm({
      date: moment().format("yyyy-MM-DD"),
      accountId: null,
      hourlyRate: "",
      periods: [{
        start: "00:00",
        stop: "00:00",
        type: "ACTIVE"
      }]
    })
    onClose();
  }
  
  const updatePeriodKey = (periods, index, value, key) => {
    if (!isEmpty(periods) && periods[index] && periods[index][key]) {
      periods[index][key] = value;
    }
    return periods
  }
  
  const updatePeriod = (index, value, key) => {
    if (!isEmpty(form.periods)) {
      const foundPeriod = form.periods[index];
      if (foundPeriod) {
        setForm({
          ...form,
          periods: updatePeriodKey(form.periods, index, value, key)
        })
      }
    }
  }
  
  const removePeriod = (index) => {
    if (!isEmpty(form.periods)) {
      setForm({ ...form, periods: form.periods.filter((_, idx) => idx !== index) })
    }
  }
  
  const addPeriod = () => {
    setForm({ ...form, periods: (form.periods ?? []).concat({
        start: "00:00",
        stop: "00:00",
        type: timesheetPeriodTypes.ACTIVE.key
      }) })
  }
  
  const getFullName = (acc) => {
    return `${acc.firstName ?? ""} ${acc.lastName ?? ""}`
  }
  
  const isValid = form && form.date && form.accountId && form.periods && !form.periods.some((p) =>  !p.type || !p.start || !p.stop || (p.start === p.stop))
  
  const isUpdate = !isEmpty(values);
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 480, maxWidth: "90%" } }}
    >
      <ModalBar title={t(form.id ? "update-timesheet" : "create-timesheet")} onClose={onClose} onDone={isValid ? onDone : null} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
          <div style={{ maxWidth: 200 }}>
            <Field
              type="date"
              name="date"
              label={t('date')}
              onChange={onChange}
              value={form.date}
              disabled={isUpdate}
            />
          </div>
          <div style={{ marginTop: 12 }}>
            <FormControl variant="filled">
              <NativeSelect
                value={form.accountId}
                onChange={onChange}
                disabled={isUpdate}
                inputProps={{
                  name: 'accountId',
                  id: 'account-selector',
                }}
                input={<Field label={t('select-account')} style={{ minWidth: 200 }} />}
              >
                <option value={null} key={null}>-</option>
                {accounts.map((acc) => <option value={acc.id} key={acc.id}>{getFullName(acc)}</option>)}
              </NativeSelect>
            </FormControl>
          </div>
          <div style={{ marginTop: 12, maxWidth: 100 }}>
            <Field label={t('hourly-rate')} name="hourlyRate" type="text" value={form.hourlyRate} adornEnd={"€"} onChange={onChange}/>
          </div>
          {isEmpty(form.periods) && (
            <div style={{ display: "flex", alignItems: "center", justifyContent: "center", paddingTop: 20, paddingBottom: 20, marginTop: 12 }}>
              <EmptyScreen titleI18nKey={'no-time-periods-added'} descriptionI18nKey={'add-a-period-to-configure-start-and-end-times'} action={{
                i18nKey: 'common-add',
                onClick: addPeriod
              }}/>
            </div>
          )}
          {!isEmpty(form.periods) && form.periods.map(({ start, stop, type, hourlyRate }, index) => {
            return (
              <div style={{ display: "flex", alignItems: "center", marginTop: 16 }} key={`period_${index}`}>
                <div style={{ marginRight: 6 }}>
                  <Field label={t('start-time')} type="time" value={start} onChange={(e) => updatePeriod(index, e.target.value, 'start')}/>
                </div>
                <div style={{ marginRight: 6 }}>
                  <Field label={t('end-time')} type="time" value={stop} onChange={(e) => updatePeriod(index, e.target.value, 'stop')}/>
                </div>
                <div style={{ marginRight: 6 }}>
                  <FormControl variant="filled">
                    <NativeSelect
                      value={index === 0 ? "ACTIVE" : type}
                      onChange={(e) => updatePeriod(index, e.target.value, 'type')}
                      disabled={index === 0}
                      inputProps={{
                        name: 'type',
                        id: 'period-type-selector',
                      }}
                      input={<Field label={t('select-type')} />}
                    >
                      {timesheetPeriodTypeOptions.map((type) => <option value={type.key} key={type.key}>{t(type.i18nKey)}</option>)}
                    </NativeSelect>
                  </FormControl>
                </div>
                {index > 0 && (
                  <div style={{ marginLeft: 6, marginTop: 24 }}>
                    <Button disableElevation disableRipple disableFocusRipple disableTouchRipple onClick={() => removePeriod(index)}
                            style={{ backgroundColor: "transparent", paddingTop: 6, paddingBottom: 6, paddingLeft: 16, paddingRight: 16, borderRadius: 10 }}>
                      <Typography style={{...typography.body.medium, color: palette.negative["500"] }}>
                        {t("common-remove")}
                      </Typography>
                    </Button>
                  </div>
                )}
              </div>
            )
          })}
          <ButtonBase disableRipple disableTouchRipple onClick={addPeriod}
                      style={{ marginTop: 24, paddingTop: 12, paddingBottom: 12, paddingLeft: 16, paddingRight: 16, borderRadius: 10, border: `1px solid ${palette.grayscale["350"]}` }}>
            <div style={{ display: "flex", alignItems: "center" }}>
              <PlusIconFilled20 />
              <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], marginLeft: 2}}>{t('common-add')}</Typography>
            </div>
          </ButtonBase>
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(TimesheetModal);
