import React, { useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import isEmpty from "../../../utils/isEmpty";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";

const data = {
  label:"",
  //maxQtdPerPerson: null,
  //maxQtdPerPersonPerOrdering: null,
  //minQtdPerPersonPerOrderingThatTriggersBlocking: null,
  //blockingTimeInMinIfMinQtdPerPersonPerOrderingReached: null
}

const TagModal = ({ t, open, onClose, submit, values }) => {
  
  const consolidatedData = isEmpty(values) ? data : { ...data, ...values };
  const [tag, setTag] = useState(consolidatedData)
  
  const onSubmit = () => {
    submit(tag)
  }
  
  const onChange = (e) => {
    if(e.target.name === "label"){
      setTag({ ...tag, [e.target.name]: e.target.value })
    } else {
      let value = parseInt(e.target.value, 10);
      if (value < 0) {
        value = 0
      }
      setTag({ ...tag, [e.target.name]: value })
    }
  }
  
  const isValid = !!tag.label
  
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 420, maxWidth: "90%" } }}
    >
      <ModalBar title={t("add-new-tag")} onClose={onClose} onDone={isValid ? onSubmit : null}/>
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20, padding: 20 }}>
        <div>
          <Field
            variant={"outlined"}
            style={{ borderRadius: 12 }}
            label={t("common-tags-label")}
            value={tag.label}
            name="label"
            onChange={onChange}
            type="text"
            required
          />
        </div>
        {/*<div style={{ marginTop: 24 }}>
          <Field
            variant={"outlined"}
            style={{ borderRadius: 12 }}
            label={t("max-quantity-per-person")}
            value={tag.maxQtdPerPerson}
            name="maxQtdPerPerson"
            onChange={onChange}
            type="number"
          />
        </div>
        <div style={{ marginTop: 24 }}>
          <Field
            variant={"outlined"}
            style={{ borderRadius: 12 }}
            label={t("max-quantity-per-person-per-ordering")}
            value={tag.maxQtdPerPersonPerOrdering}
            name="maxQtdPerPersonPerOrdering"
            onChange={onChange}
            type="number"
          />
        </div>
        <div style={{ marginTop: 24 }}>
          <Field
            variant={"outlined"}
            style={{ borderRadius: 12 }}
            label={t("min-quantity-per-person-that-triggers-blocking")}
            value={tag.minQtdPerPersonPerOrderingThatTriggersBlocking}
            name="minQtdPerPersonPerOrderingThatTriggersBlocking"
            onChange={onChange}
            type="number"
          />
        </div>
        <div style={{ marginTop: 24 }}>
          <Field
            variant={"outlined"}
            style={{ borderRadius: 12 }}
            label={t("blocking-time-when-blocking-triggers")}
            value={tag.blockingTimeInMinIfMinQtdPerPersonPerOrderingReached}
            name="blockingTimeInMinIfMinQtdPerPersonPerOrderingReached"
            onChange={onChange}
            type="number"
          />
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["400"], marginTop: 8 }}>{t("blocking-time-when-blocking-triggers-explanation")}</Typography>
        </div>*/}
      </div>
    </Modal>
  )
}

export default withTranslation("common")(TagModal);
