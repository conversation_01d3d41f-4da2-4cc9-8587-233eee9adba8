import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import isValidEmail from "../../../utils/isValidEmail";
import Select from "@material-ui/core/Select";
import { ChevronDown20new } from "../../../utils/icons";
import { MenuProps } from "../../../utils/const";
import MenuItem from "@material-ui/core/MenuItem";
import { getAccounts } from "../../../api";
import useStyles from "./styles";
import typography from "../../../../styles/typography";
import Typography from "@material-ui/core/Typography";

const DailyReportModal = ({ t, open, onClose, dailyReportEmail, restaurantId, hasFullReportingPermission, titleI18n, isPrinting, setValue, fieldProps  }) => {
  const classes = useStyles();
  
  const [email, setEmail] = useState(dailyReportEmail ?? "");
  const [account, setAccount] = useState(null);
  const [accounts, setAccounts] = useState([]);
  
  const onDone = () => {
    setValue(email, hasFullReportingPermission ? account: null);
    onClose();
  }
  
  const onChangeEmail = (e) => {
    setEmail(e.target.value);
  };
  
  const updateAccount = (e) => {
    setAccount(e.target.value);
  };
  
  useEffect(() => {
    getAccounts(restaurantId, 100, 0)
      .then(({ data }) => {
        setAccounts(data.items)
      }).catch(() => {})
  }, [])
  
  const valid = isPrinting? true : isValidEmail(email);
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} onDone={valid ? onDone : null} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
          <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
            {!isPrinting &&
              <Field
              label={t("email")}
              name={"email"}
              value={email}
              onChange={onChangeEmail}
              error={!valid}
              {...fieldProps}
             />
            }
            {accounts && hasFullReportingPermission && (
              <div className={classes.selectWrapper}>
                <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("account")}</Typography>
                <Select
                  variant={"outlined"}
                  style={{ borderRadius: 12, backgroundColor: palette.grayscale["100"], height: 44 }}
                  value={account}
                  name="account"
                  IconComponent={ChevronDown20new}
                  onChange={updateAccount}
                  MenuProps={MenuProps}
                >
                  {accounts?.map(acc => (
                    <MenuItem key={acc.id} value={acc} >
                      {t(acc.email)}
                    </MenuItem>
                  ))}
                </Select>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 8 }}>{t("daily-report-for-certain-account-id-hint")}</Typography>
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(DailyReportModal);
