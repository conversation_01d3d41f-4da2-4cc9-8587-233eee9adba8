import React from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";

const ReportBlockingErrorModal = ({ t, open, onClose }) => {
  
  const blockedPeriods = [
    { timeRange: "00:00 - 10:30" },
    { timeRange: "14:00 - 16:30" },
    { timeRange: "22:30 - 23:59" },
  ]

  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t("error-bar-message")} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40, display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography style={{ ...typography.body.medium }}>{t("report-availability-notice-heading")}</Typography>
          <Typography style={{ ...typography.body.regular }}>{t("reports-of-this-length-are-blocked-during-these-periods-of-time")}:</Typography>
          {blockedPeriods.map((tr) => {
            return (
              <Typography style={{ ...typography.body.medium, marginLeft: 12}}>- {tr.timeRange}</Typography>
            )
          })}
          <Typography style={{ ...typography.body.regular }}>{t("accessing-reports-outside-of-these-times-not-available")}</Typography>
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(ReportBlockingErrorModal);
