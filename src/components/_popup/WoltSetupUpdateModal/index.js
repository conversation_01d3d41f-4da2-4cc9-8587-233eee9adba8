import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";

const data = {
  venueId: null,
  apiKey: null,
  basicUsername: null,
  basicPassword: null
}

const WoltSetupUpdateModal = ({ t, open, onClose, value = {}, setValue }) => {
  const [form, setForm] = useState({ ...data, ...(value ?? {}) });
  
  const onDone = () => {
    setValue(form);
    onClose();
  }
  
  useEffect(() => {
    setForm({ ...form, ...(value ?? {}) })
  }, [JSON.stringify(value)])
  
  const onChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  }
  
  const valid = form && form.venueId && form.apiKey && form.basicUsername && form.basicPassword
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t('wolt-setup')} onClose={onClose} onDone={valid ? onDone : null} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
          <Field
            value={form.venueId}
            onChange={onChange}
            label={t('venue-id')}
            name={"venueId"}
          />
          <div style={{ marginTop: 12 }}>
            <Field
              value={form.apiKey}
              onChange={onChange}
              label={t('api-key')}
              name={"apiKey"}
            />
          </div>
          <div style={{ marginTop: 12 }}>
            <Field
              value={form.basicUsername}
              onChange={onChange}
              label={t('username')}
              name={"basicUsername"}
            />
          </div>
          <div style={{ marginTop: 12 }}>
            <Field
              value={form.basicPassword}
              onChange={onChange}
              label={t('password')}
              name={"basicPassword"}
            />
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(WoltSetupUpdateModal);
