import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../../i18n";
import Typography from "@material-ui/core/Typography";
import { ButtonBase} from "@material-ui/core";
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import Field from "../../form/Field";
import { restaurantSelectors } from "../../../../redux/selectors";
import palette from "../../../../styles/palette";
import shadows from "../../../../styles/shadows";
import typography from "../../../../styles/typography";
import { DECIMAL_REGEX } from "../../../utils/const";
import { postRestaurantPayout } from "../../../api";
import Loader from "../../_progress/Loader";
import { appActions } from "../../../../redux/actions";

const StripePayoutCreationModal = ({ t, open, onClose, triggerRefetch }) => {
  const dispatch = useDispatch();
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const [amount, setAmount] = useState('')

  const [formIsLoading, setFormIsLoading] = useState(false)
  const formIsValid = !!amount;

  const onAmountChange = (e) => {
    let value = e.target.value
    value = value.replace(DECIMAL_REGEX, '')
    if (!value || value < 0) {
      setAmount('')
    }
    setAmount(value)
  }

  const handleClose = () => {
    setAmount('')
    onClose()
  }

  const onDone = () => {
    if (formIsLoading) {
      return
    }
    setFormIsLoading(true)
    postRestaurantPayout(restaurantId, amount)
      .then(() => {
        dispatch(appActions.setNotification('amount-payout-created', "success", { amount }))
        triggerRefetch();
        handleClose();
      })
      .catch((err) => {
        const { response } = (err || {})
        const { data } = (response || {})
        const { title = "" } = (data || {})
        try {
          const sanitizedTitle = (title || "").split(";")[0]
          dispatch(appActions.setNotification(sanitizedTitle || 'amount-payout-not-created', "error", { amount }))
        } catch (e) {
          dispatch(appActions.setNotification('amount-payout-not-created', "error", { amount }))
        }
      })
      .finally(() => setFormIsLoading(false))
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent", ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t("create-payout")} onClose={handleClose} onDone={formIsValid ? onDone : null} />
      <div style={{
        height: "100%",
        overflow: "auto",
        background: palette.grayscale["200"],
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20
      }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <div style={{ flex: 1 }}>
            <div style={{ display: "flex", maxWidth: 520, width: "100%" }}>
              <div style={{ flex: 1 }}>
                <Field
                  label={t("payout-amount")}
                  placeholder={t("enter-amount")}
                  adornEnd={"€"}
                  value={amount}
                  onChange={onAmountChange}
                  disabled={formIsLoading}
                />
              </div>
            </div>
            <div style={{ marginTop: 20, background: palette.primary["100"], borderRadius: 12, padding: "8px 12px" }}>
              <Typography style={{ ...typography.body.regular }}>
                {t('instant-payments-charge-percentage')}
              </Typography>
            </div>
            <div style={{ display: "flex", flexDirection: "column", alignItems: "top", marginTop: 20 }}>
              <ButtonBase style={{ padding: '12px 24px', width: "100%", background: !formIsValid ? palette.grayscale["400"] : palette.primary["500"], borderRadius: 12 }} disableRipple disableTouchRipple onClick={onDone} disabled={!formIsValid || formIsLoading}>
                {formIsLoading ? <Loader /> : (
                  <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                    {t('create-payout')}
                  </Typography>
                )}
              </ButtonBase>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default withTranslation("common")(StripePayoutCreationModal);
