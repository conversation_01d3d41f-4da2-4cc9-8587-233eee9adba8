import React, { useEffect, useState } from "react";
import {withTranslation} from '../../../../i18n';
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import Modal from "../Modal";
import Field from "../../form/Field";
import isEmpty from "../../../utils/isEmpty";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import { ButtonBase } from "@material-ui/core";

const defaultData = {
  name: "",
  smartschankId: ""
}
const SmartschankSelectionUpdateModal = ({ t, open, close, submit, values, remove, title }) => {
  const [form, setForm] = useState({});
  
  const onChange = (e) => {
    const updatedFrom = { ...form, [e.target.name] : e.target.value }
    setForm(updatedFrom)
  }
  
  const onSubmit = () => {
    submit(form)
    close();
  };
  
  const onRemove = () => {
    remove(form)
  }
  
  useEffect(() => {
    if(!isEmpty(values)){
      setForm(values)
    } else {
      setForm(defaultData)
    }
  },[open])
  
  return (
    <Modal 
      open={open}
      close={close}
      maxWidth={false}
      PaperProps={{ style: { ...shadows.large, width: 500, maxWidth: "90%", borderRadius: 20 } }}
    >
      <ModalBar title={title} onClose={close} onDone={onSubmit} />
      <div style={{ padding: 16 }}>
        <div style={{ marginBottom: 12 }}>
          <Field
            label={t("name")}
            variant={"outlined"}
            value={form.name}
            style={{borderRadius: 12 }}
            readOnly
            type="text"
            disabled
          />
        </div>
        <div style={{ marginBottom: 12 }}>
          <Field
            label={t("beer-tap-id")}
            variant={"outlined"}
            defaultValue={form?.smartschankDevice?.smartschankId ?? ""}
            value={(form?.smartschankId || form?.smartschankDevice?.smartschankId) ?? ""}
            name={"smartschankId"}
            style={{borderRadius: 12 }}
            onChange={onChange}
            type="number"
          />
        </div>
        {form.smartschankDevice?.smartschankId && (
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", padding: 12, backgroundColor: palette.grayscale["300"], borderRadius: 10 }}>
            <Typography style={{ ...typography.body.medium }}>{t("remove-smart-schank-integration-for-this-item-or-account")}</Typography>
            <ButtonBase 
              style={{ padding: "6px 8px", borderRadius: 10, border: `1px solid ${palette.grayscale.border}`}}
              onClick={onRemove}
            >
              <Typography style={{ ...typography.body.medium }}>{t("remove")}</Typography>
            </ButtonBase>
          </div>
        )}
      </div>
    </Modal>
  )
};

export default withTranslation('common')(SmartschankSelectionUpdateModal);
