import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import { ButtonBase, Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import isEmpty from "../../../utils/isEmpty";
import EmptyScreen from "../../_placeholder/EmptyScreen";
import { getInventoryCategories } from "../../../api";
import { measurementUnits, MenuProps, noop } from "../../../utils/const";
import { useSelector } from "react-redux";
import { restaurantSelectors } from "../../../../redux/selectors";
import { ChevronDown20new } from "../../../utils/icons";
import Select from "@material-ui/core/Select";
import MenuItem from "@material-ui/core/MenuItem";
import useStyles from "./styles";
import Field from "../../form/Field";
import byId from "../../../utils/byId";

const SetupMenuItemIngredientsModal = ({ t, open, onClose, ingredients = [], setValue }) => {
  const classes = useStyles();
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const [assignedIngredients, setAssignedIngredients] = useState([])
  const [inventoryCategories, setInventoryCategories] = useState({ total: 0, pages: 0, items: null });
  const inventoryCategoriesById = byId(inventoryCategories?.items || [])
  const [pageOffset, setPageOffset] = useState(0);
  const pageSize = 200;
  const [fetching, setFetching] = useState(true);

  useEffect(() => {
    setAssignedIngredients([].concat(ingredients || []))
  }, [JSON.stringify(ingredients)])

  const fetchInventoryCategories = () => {
    setFetching(true);
    getInventoryCategories(restaurantId, pageSize, pageOffset)
      .then(({ data: fetchedInventoryCategories = {} }) => {
        setInventoryCategories(fetchedInventoryCategories || {})
      })
      .catch(noop)
      .finally(() => setFetching(false));
  }

  useEffect(() => {
    fetchInventoryCategories()
  }, []);
  
  const onChange = (e, index) => {
    if (assignedIngredients[index]) {
      const updatedIngredientsList = JSON.parse(JSON.stringify(assignedIngredients))
      updatedIngredientsList[index] = { ...updatedIngredientsList[index], [e.target.name]: e.target.value }
      if (e.target.name === "categoryId") {
        const selectedItem = (inventoryCategoriesById || {})[e.target.value]
        if (selectedItem) {
          updatedIngredientsList[index] = { ...updatedIngredientsList[index], name: selectedItem.name, unit: selectedItem.unit }
        }
      }
      setAssignedIngredients(updatedIngredientsList)
    }
  }
  
  const addIngredient = () => {
    let existingIngredients = assignedIngredients
    existingIngredients = existingIngredients.concat([{ categoryId: "", name: "", amount: 0 }])
    setAssignedIngredients(existingIngredients)
  }
  
  const removeIngredient = (index) => {
    let existingIngredients = [].concat((assignedIngredients || []))
    existingIngredients.splice(index, 1)
    setAssignedIngredients(existingIngredients)
  }

  const onDone = () => {
    setValue(assignedIngredients);
    onClose();
  }
  
  const isValid = isEmpty(assignedIngredients)
    || !(assignedIngredients.some(it => isEmpty(it) || (!it.categoryId) || (!it.name) || (!it.amount)))
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t('setup-ingredients')} onClose={onClose} onDone={isValid ? onDone : null} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
          {!isEmpty(assignedIngredients) && assignedIngredients.map((ingredient, index) => {
            const { categoryId, amount } = (ingredient || {})
            return (
              <div key={`ingredients_${index}_modal`} style={{ paddingBottom: 24, marginBottom: 24, borderBottom: `1px solid ${palette.grayscale.border}` }}>
                <div className={classes.selectWrapper}>
                  <Typography style={{ marginBottom: 8, ...typography.body.medium }}>
                    {t('ingredient')}
                  </Typography>
                  <Select
                    name="categoryId"
                    disableUnderline
                    variant="outlined"
                    style={{ borderRadius: 12, paddingLeft: 1.5, backgroundColor: palette.grayscale["100"], height: 44 }}
                    value={categoryId}
                    MenuProps={MenuProps}
                    onChange={(e,child) => onChange(e, index, child)}
                    IconComponent={ChevronDown20new}
                    multiple={false}
                  >
                    {(inventoryCategories?.items || []).map(category => {
                      const { unit } = (category || {})
                      const resolvedMeasurementUnit = measurementUnits[unit] ? t(measurementUnits[unit].i18nKey) : '-'
                      return (
                        <MenuItem key={category.id} value={category.id}>
                          <Typography style={{ ...typography.medium.regular }}>
                            {t(category.name)} {resolvedMeasurementUnit}
                          </Typography>
                        </MenuItem>
                      )
                    })}
                  </Select>
                </div>
                <div style={{ marginTop: 16 }}>
                  <Field
                    label={t('amount')}
                    value={amount}
                    type={"number"}
                    name={"amount"}
                    onChange={(e) => onChange(e, index)}
                  />
                </div>
                <div style={{ display: "flex", marginTop: 12 }}>
                  <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: 6, display: "flex", alignItems: "center" }} onClick={() => removeIngredient(index)}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t('common-remove')}
                    </Typography>
                  </ButtonBase>
                </div>
              </div>
            )
          })}
          {!isEmpty(assignedIngredients) && (
            <div style={{
              padding: 12,
              borderRadius: 12,
              background: palette.grayscale["100"],
              ...shadows.base,
              marginTop: 24
            }}>
              <div style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between"
              }}>
                <div>
                  <Typography style={{ ...typography.body.medium }}>
                    {t('add-ingredient')}
                  </Typography>
                  <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 4 }}>
                    {t('setup-ingredients')}
                  </Typography>
                </div>
                <div>
                  <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: '5px 11px', display: "flex", alignItems: "center" }} onClick={addIngredient}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t('common-add')}
                    </Typography>
                  </ButtonBase>
                </div>
              </div>
            </div>
            
          )}
          {isEmpty(assignedIngredients) && (
            <EmptyScreen
              titleI18nKey={'no-ingredients'}
              descriptionI18nKey={'create-ingredients-for-this-category-item'}
              action={{
                i18nKey: 'add-ingredient',
                onClick: addIngredient
              }} />
          )}
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(SetupMenuItemIngredientsModal);
