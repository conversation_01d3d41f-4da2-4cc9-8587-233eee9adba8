import { makeStyles, fade } from '@material-ui/core/styles';
import palette from "../../../../styles/palette";
import shadows from "../../../../styles/shadows";


const useStyles = makeStyles(theme => ({
  content: {
    padding: theme.spacing(2),
    '&+&': {
      borderTop: `1px solid ${fade("#04172F", 0.06)}`,
      width: "50%"
    }
  },
  actions: {
    textAlign: 'right',
    '& > button': {
      marginLeft: 8
    }
  },
  page: {
    padding: theme.spacing(2),
    minHeight: `calc(100vh - 49px)`
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    justifyContent: 'space-between'
  },
  container:{
    display:"flex",
    flexWrap: "wrap",
    boxSizing: "border-box",
  },
  smallContainer: {
    display:"flex",
    flexWrap: "nowrap",
    boxSizing: "border-box",
  },
  toolbox: {
    marginLeft: theme.spacing(2)
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  spacing: {
    flex: 1
  },
  box: {
    padding: theme.spacing(1)
  },
  info: {
    marginTop: theme.spacing(2),
    overflow: 'hidden'
  },
  cursor: {
    cursor: 'pointer'
  },
  taken: {
    '&&': {
      background: '#FFBCBC'
    }
  },
  selected: {
    '&&': {
      border: '2px solid #dbdede'
    }
  },
  requestingPayment: {
    '&&': {
      background: '#FEF7EB',
      border: '2px solid #fde7a1'
    }
  },
  infoHeader: {
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
  },
  data: {
    padding: theme.spacing(2),
    display: 'flex',
    flexDirection: 'column'
  },
  field: {
    width: '100%'
  },
  capitalize: {
    textTransform: 'capitalize'
  },
  fieldGroup: {
    paddingBottom: theme.spacing(4),
    width: "520px"
  },
  smallFieldGroup: {
    paddingBottom: theme.spacing(4),
    width: "90%"
  },
  uploaderContainer: {
    cursor: 'pointer',
    flexDirection: "column",
    textAlign: "center",
    height: 180,
    width: '100%',
    maxHeight: 120,
    borderRadius: 12,
    background: palette.grayscale["250"],
    backgroundPosition: 'center',
    backgroundSize: 'cover',
    display: 'flex',
    alignItems: 'center',
    '& > p': {
      textAlign: 'center',
      width: '100%'
    }
  },
  clearImage: {
    marginTop: theme.spacing(1),
    fontSize: 14,
    padding: '4px 6px',
    borderRadius: theme.spacing(1)
  },
  formControl: {
    minWidth: 200
  },
  fullWidth: {
    width: '100%'
  },
  selectActionBtn: {
    paddingBottom: theme.spacing(2),
  },
  subItem: {
    paddingLeft: theme.spacing(2)
  },
  tag: {
    display: 'flex',
    padding: theme.spacing(1),
    '&:first-child': {
      paddingLeft: 0
    }
  },
  tagImg: {
    marginRight: theme.spacing(1) / 2,
    width: 18,
    height: 18
  },
  chips: {
    display: 'flex',
    flexWrap: 'wrap',
  },
  chip: {
    margin: 2,
  },
  sourceBtnText: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: palette.grayscale["600"],
    marginRight: 2
  },
  sourceBtnIcon: {
    transform: "rotate(180deg)",
    display: "flex"
  },
  selectWrapper: {
    ["& .MuiSelect-select.MuiSelect-select"]: { padding: '10px 19px 10px 14px', borderColor: palette.grayscale["350"] },
    ["& .MuiSelect-select.MuiSelect-select:focus"]: {borderRadius:12},
    ["& svg"]: {width:'12px', height: '7px', color: palette.grayscale["400"], position: 'absolute',
      right: 19,
      top: 'calc(50% - 2px)',
      pointerEvents: 'none',
    },
    ["& fieldset"]: { borderColor: palette.grayscale["350"] },
    iconOpen: {
      transform: 'rotate(180deg)',
    },
  },
  chipSelectWrapper: {
    ["& .MuiSelect-select.MuiSelect-select"]: { paddingRight: '19px', borderColor: palette.grayscale["350"] },
    ["& .MuiSelect-select.MuiSelect-select:focus"]: {borderRadius:12},
    ["& .MuiInputBase-root"]: { minHeight: 44 },
    ["& svg"]: {width:'12px', height: '7px', color: palette.grayscale["400"], position: 'absolute',
      right: 19,
      top: 'calc(50% - 2px)',
      pointerEvents: 'none',
    },
    ["& fieldset"]: { borderColor: palette.grayscale["350"] },
    iconOpen: {
      transform: 'rotate(180deg)',
    },
  },
  TaxSelect: {
    background: palette.grayscale["100"],
    borderRadius: 12,
    padding: "12.5px 12px",
    textAlign: "center"
  },
  TaxSelectSmall:{
    background: palette.grayscale["100"],
    borderRadius: 12,
    padding: "12.5px 0px",
    textAlign: "center"
  },
  selectAdornment: {
    ["& .MuiTypography-colorTextSecondary"]: { color: palette.grayscale["800"], paddingLeft: 12, fontSize: 14, fontWeight: 400},
   
  },
  multipleTextField: {
    ["& .MuiInputBase-root"] : {height: 44},
    width: '100%'
  },
  checkboxContainer: {
    backgroundColor: palette.grayscale["100"],
    borderRadius: 12,
    display: "flex",
    alignItems: "center",
    padding: 12,
    ...shadows.base
  },
  menuItem: {
    padding: 16,
    "&:focus": {
      background: "transparent"
    }
  }
}));


export default useStyles;
