import React, { useEffect, useState } from "react";
import { withTranslation } from "../../../../i18n";
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import { ClockIcon20, EuroSymbolIcon20Light } from "../../../utils/icons";


const PrepTimeFieldUpdateModal = ({ t, open, onClose, values = {}, setValue, titleI18n, deliveryPartners }) => {
  
  const [prepTime, setPrepTime] = useState(values);
  
  const onDone = () => {
    setValue(prepTime);
    onClose();
  };
  
  const onChange = (stateKey, value) => {
    setPrepTime({ ...prepTime, [stateKey]: value });
  };
  
  useEffect(() => {
    setPrepTime(values);
  }, [values, open]);
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent", ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <>
        <ModalBar title={t(titleI18n)} onClose={onClose} onDone={onDone} />
        <div style={{
          height: "100%",
          overflow: "auto",
          background: palette.grayscale["200"],
          borderBottomLeftRadius: 20,
          borderBottomRightRadius: 20
        }}>
          <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
            <div style={{ flex: 1 }}>
              <div style={{ display: "flex", maxWidth: 520, width: "100%", gap: 12 }}>
                <Field
                  label={t("pickup-order-creation-form-type-field-pickup-option-label")}
                  value={prepTime.PICKUP}
                  onChange={(e) => onChange( "PICKUP", e.target.value)}
                  iconAdornEnd={< ClockIcon20 />}
                  type={"number"}
                  fieldProps={{
                    type: "number"
                  }}
                />
                <Field
                  label={t("pickup-order-creation-form-type-field-delivery-option-label")}
                  value={prepTime.DELIVERY}
                  onChange={(e) => onChange("DELIVERY", e.target.value)}
                  iconAdornEnd={< ClockIcon20 />}
                  type={"number"}
                  fieldProps={{
                    type: "number"
                  }}
                />
              </div>
              <div style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
                gap: 12,
                marginTop: 8
              }}>
                {deliveryPartners.map((partner) => {
                  return (
                    <Field
                      key={partner.i18nKey}
                      label={partner.i18nKey}
                      value={prepTime[partner.value]}
                      onChange={(e) => onChange( [partner.value], e.target.value)}
                      iconAdornEnd={< ClockIcon20 />}
                      type={"number"}
                      fieldProps={{
                        type: "number"
                      }}
                    />
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </>
    </Modal>
  );
};

export default withTranslation("common")(PrepTimeFieldUpdateModal);
