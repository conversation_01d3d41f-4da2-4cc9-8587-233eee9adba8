import React, {useEffect, useState} from 'react';
import { withTranslation } from '../../../../i18n';
import MenuItemAddition, { MenuItemAdditionElement } from "./MenuItemAddition";
import BottomPanel from '../../BottomPanel';
import byId from '../../../utils/byId';
import {useSelector} from "react-redux";
import {
  accountSelectors,
  configurationSelectors,
  menusSelectors, restaurantSelectors,
  terminalSelectors
} from "../../../../redux/selectors";
import shadows from "../../../../styles/shadows";
import ModalBar from "../../_navigation/ModalBar";
import Modal from "../../_popup/Modal";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import ButtonBase from "@material-ui/core/ButtonBase";
import { Typography } from "@material-ui/core";
import { CollapseIcon20Grayscale400, KeyboardIcon20 } from "../../../utils/icons";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import typography from "../../../../styles/typography";
import useStyles from './styles';
import isEmpty from "../../../utils/isEmpty";
import Checkbox from "../../_toggles/Checkbox";
import QuantityInput from "../../_input/QuantityInput";
import { getPricePerWeight } from "../../../api";
import useDebounce from "../../../hooks/useDebounce";
import ScaleModal from "../ScaleModal";
import CustomItemCreationModal from "./CustomItemCreationModal";

const factorOngoingAdditions = (additions, factor) => {
  if (!isEmpty(additions) && factor > 1 ) {
    additions.map(addition => {
      if (addition.qtd) {
        addition.qtd *= factor
      }
      if (addition.max) {
        addition.max *= factor
      }

      if (!isEmpty(addition.items)) {
        addition.items = addition.items.map( i => Object.assign(i, { max: i.max * factor }))
      }
      return addition
    })
  }
  return additions
}

const MenuItemDetailsModal = ({
                                t, open, onClose, internalName, name, id, code, description, remarks, defaultCourseNumber, unitPrice, unitOfMeasure, salePrice, volume, addOrderItem, readOnly,
                                options = [], extras = [], orderItem = {}, ongoing = false, ongoingExtrasQuantityMax: additionsCrossCategoryMax, toGo, removeItem
                              }) => {
  const classes = useStyles();
  const fullWidthMax = useMediaQuery('(max-width: 700px)'); // from this width down, details become full screen
  const orderPanelWidth = fullWidthMax ? '0px' : '450px';

  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;

  const menuItemIsSoldByWeight = !isEmpty(unitOfMeasure)
  const orderItemIsSoldByWeight = !isEmpty(orderItem.measurementValue)

  const { preferences = {} } = useSelector(accountSelectors.getPreferences)
  const { terminalMenuOrientation } = (preferences || {})

  const menuRightOriented = terminalMenuOrientation === "RIGHT"

  const { orderCoursesConfig } = useSelector(terminalSelectors.getOrderCoursesConfig);
  const { enabled: orderCoursesConfigurationEnabled, courses: orderCourses } = (orderCoursesConfig || {})
  const coursesConfiguration = useSelector(configurationSelectors.getCoursesConfiguration);
  const { enabled: coursesConfigurationEnabled, courses: configurationCourses } = (coursesConfiguration || {})

  const coursesEnabled = orderCoursesConfigurationEnabled || coursesConfigurationEnabled
  const courses = orderCourses || configurationCourses
  const hasCourses = !isEmpty(courses)

  const [calculatedPrice, setCalculatedPrice] = useState(0)
  const [weightValue, setWeightValue] = useState(orderItem.measurementValue ?? 0);
  const [isShowingKeyboard, setIsShowingKeyboard] = useState(false);

  const [customAdditionCreateModalOpen, setCustomAdditionCreateModalOpen] = useState(false);
  const openCustomAdditionCreateModal = () => setCustomAdditionCreateModalOpen(true);
  const closeCustomAdditionCreateModal = () => setCustomAdditionCreateModalOpen(false);

  const customExtrasEnabled = useSelector(configurationSelectors.getCustomExtrasEnabledConfiguration);
  const customExtras = useSelector(menusSelectors.getCustomExtras);
  const [customAdditionsCollapse, setCustomAdditionsCollapse] = useState(false);
  const toggleCustomAdditionsCollapse = () => setCustomAdditionsCollapse(!customAdditionsCollapse);

  const [notesCollapsed, setNotesCollapsed] = useState(false)
  const toggleNotesCollapsed = () => setNotesCollapsed(!notesCollapsed)

  const [remarksCollapsed, setRemarksCollapsed] = useState(false)
  const toggleRemarksCollapsed = () => setRemarksCollapsed(!remarksCollapsed)

  const [coursesCollapsed, setCoursesCollapsed] = useState(false)
  const toggleCoursesCollapsed = () => setCoursesCollapsed(!coursesCollapsed)

  const { predefinedNotes = [] } = useSelector((state => menusSelectors.getPredefinedNotes(state, 'ORDER')))

  const [excludedAdditionalItemIds, setExcludedAdditionalItemIds] = useState([]);
  const { qtd: orderItemQtd = 1, course, id: orderItemId } = orderItem;

  const orderSelectedCourse = useSelector(terminalSelectors.getSelectedCourseMemo)
  const formattedOrderSelectedCourse = orderSelectedCourse
    ? { number: orderSelectedCourse.courseNumber, name: orderSelectedCourse.courseName }
    : (defaultCourseNumber && !isEmpty(courses))
      ? { number: defaultCourseNumber, name: courses[defaultCourseNumber - 1]?.nameI18n?.de ?? "" }
      : null
  const orderCourse = course || formattedOrderSelectedCourse

  const [selectedCourse, setSelectedCourse] = useState(orderCourse)
  const { number: selectedCourseNumber } = (selectedCourse || {})

  const updateSelectedCourse = (val) => setSelectedCourse(val)

  let resolvedOptions = (!ongoing || orderItemQtd === 1) ? options : factorOngoingAdditions(JSON.parse(JSON.stringify(options)), orderItemQtd);
  let resolvedExtras = (!ongoing || orderItemQtd === 1) ? extras : factorOngoingAdditions(JSON.parse(JSON.stringify(extras)), orderItemQtd);

  useEffect(() => {
    const additionIds = []
    if (ongoing && (!isEmpty(resolvedOptions) || !isEmpty(resolvedExtras))) {
      const { nestedOrderItems = [] } = orderItem;
      if (!isEmpty(nestedOrderItems)) {
        nestedOrderItems.forEach(({ options = [], extras = [] }) => {
          if (!isEmpty(options)) {
            options.forEach(({ id, rounds }) => {
              if (rounds === 1) {
                additionIds.push(id)
              }
            })
          }
          if (!isEmpty(extras)) {
            extras.forEach(({ id, rounds }) => {
              if (rounds === 1) {
                additionIds.push(id)
              }
            })
          }
        })
        setExcludedAdditionalItemIds(additionIds)
      }
    }
  }, [])

  resolvedOptions = ongoing ? resolvedOptions.filter(itm => excludedAdditionalItemIds.indexOf(itm.id) === -1 && !isEmpty(itm.items)) : resolvedOptions

  resolvedExtras = ongoing ? resolvedExtras.filter(itm => excludedAdditionalItemIds.indexOf(itm.id) === -1 && !isEmpty(itm.items)) : resolvedExtras

  unitPrice = (unitPrice || 0).toFixed(Number.isInteger(unitPrice) ? 0 : 2);
  salePrice = salePrice && salePrice.toFixed(Number.isInteger(salePrice) ? 0 : 2);

  const [customAddition, setCustomAddition] = useState('')
  const [notes, setNotes] = useState('');
  const [qtd, setQtd] = useState(1);

  const additionsCrossCategoryMaxForQtd = additionsCrossCategoryMax * qtd;
  const [canAddAddition, setCanAddAddition] = useState(true);

  const onMore = () => setQtd(qtd + 1);
  const onLess = () => setQtd(qtd > 0 ? qtd - 1 : 0);

  const [selectedNotes, setSelectedNotes] = useState([]);
  const selectNote = (note) => setSelectedNotes((selectedNotes || []).concat([note]))
  const unselectNote = (note) => setSelectedNotes((selectedNotes || []).filter(n => n.id !== note.id))
  const updateNotes = (e) => setNotes(e.target.value);
  const updateCustomAddition = (e) => setCustomAddition(e.target.value);
  const [selectedOptions, setOptions] = useState({});
  const [selectedExtras, setExtras] = useState({});
  const [validAdditionsForQtd, setValidAdditionsForQtd] = useState(true);

  const calculateQtd = (additionItems = {}) => {
    if (isEmpty(additionItems)) {
      return 0;
    }

    return Object.values(additionItems).reduce((acc, next = {}) => {
      const nextTotal = (Object.values(next) || []).reduce((acm, nxt) => {
        acm += nxt.qtd;
        return acm;
      }, 0);
      acc += nextTotal;
      return acc;
    }, 0)
  };

  const handleGetMeasurement = (event) => {
    try {
      const parsedData = JSON.parse(event?.detail)
      setWeightValue(parsedData.measurementValue)
    } catch (e) {}
  }

  useEffect(() => {
    window.addEventListener('getMeasurement', handleGetMeasurement);

    // Cleanup the event listener when the component unmounts
    return () => {
      window.removeEventListener('getMeasurement', handleGetMeasurement);
    };
  }, []);

  const getCalculatedPrice = () => {
    const amount = weightValue
    setCalculatedPrice(0)
    if(amount < 1){
      return
    }
    getPricePerWeight( restaurantId, id, amount )
      .then(({ data }) => {
        setCalculatedPrice(data.baseTotal)
      })
      .catch((e) => {
        console.log(e)
      })
  };

  const getDebouncedCalculatedPrice = useDebounce(getCalculatedPrice, 400);

  useEffect(() => {
    setCalculatedPrice(0)
    if(!weightValue){
      return
    }
    getDebouncedCalculatedPrice()
  },[weightValue])

  useEffect(() => {
    if (!additionsCrossCategoryMaxForQtd || (isEmpty(selectedExtras) && isEmpty(selectedOptions))) {
      setCanAddAddition(true);
    } else {
      const selectedExtrasAmount = calculateQtd(selectedExtras);
      const selectedOptionsAmount = calculateQtd(selectedOptions);
      const selectedAdditionsAmount = selectedExtrasAmount + selectedOptionsAmount

      if (selectedAdditionsAmount > additionsCrossCategoryMaxForQtd) {
        setValidAdditionsForQtd(false)
      } else {
        setValidAdditionsForQtd(true)
      }

      if (additionsCrossCategoryMaxForQtd && ((selectedAdditionsAmount + 1) > additionsCrossCategoryMaxForQtd)) {
        setCanAddAddition(false);
      } else {
        setCanAddAddition(true);
      }
    }
  }, [JSON.stringify(selectedExtras), JSON.stringify(selectedOptions), qtd])

  // order is valid if there are no options
  // only option qtd amount could invalidate ordering
  const [optionsState, setOptionsState] = useState(isEmpty(resolvedOptions) ? null : {});
  const [isValid, setValid] = useState(false);

  const updateOptions = (optionId, items, isOptionValid) => {
    setOptions((prevOptions) => ({ ...prevOptions, ...{ [optionId]: items } }));
    setOptionsState((prevOptionsState) => ({ ...prevOptionsState, ...{ [optionId]: isOptionValid } }));
  };

  const updateExtras = (extraId, items) => {
    setExtras((prevExtras) => ({ ...prevExtras, ...{ [extraId]: items } }));
  };

  const [optionsPrice, setOptionsPrice] = useState(0);
  const [extrasPrice, setExtrasPrice] = useState(0);

  const calculatePrice = (additionItems = {}) => (isEmpty(additionItems) ? 0 : (
    Object.values(additionItems).reduce((acc, next = {}) => {
      const nextTotal = (Object.values(next) || []).reduce((acm, nxt) => {
        acm += nxt.qtd * nxt.unitPrice;
        return acm;
      }, 0);
      acc += nextTotal;
      return acc;
    }, 0)
  ));

  const calculateValid = (additionItems = {}) => (isEmpty(additionItems) ? true : (
    Object.values(additionItems).filter(itm => !isEmpty(itm.items)).reduce((acc, b) => {
      acc = acc && b;
      return acc;
    }, true)
  ));

  useEffect(() => {
    setOptionsPrice(calculatePrice(selectedOptions));
  }, [JSON.stringify(selectedOptions)]);

  useEffect(() => {
    setExtrasPrice(calculatePrice(selectedExtras));
  }, [JSON.stringify(selectedExtras)]);

  useEffect(() => {
    setValid(calculateValid(optionsState));
  }, [JSON.stringify(optionsState)]);

  useEffect(() => {
    if (!isEmpty(orderItem)) {
      console.log('Resetting order item');
      const { notes: prevNotes = '', predefinedNotes: prevPredefinedNotes = [], qtd: prevQtd = 1 } = orderItem
      if (prevNotes) {
        setNotes(prevNotes)
      }
      if (prevPredefinedNotes) {
        setSelectedNotes(prevPredefinedNotes)
      }
      if (prevQtd) {
        setQtd(prevQtd)
      }
    }
  }, [])

  const prevSelectedOptionsById = isEmpty(orderItem) || isEmpty(orderItem.options) ? {} : byId(orderItem.options)
  const prevSelectedExtrasById = isEmpty(orderItem) || isEmpty(orderItem.extras) ? {} : byId(orderItem.extras)

  const onAddOrderItem = () => {
    if(menuItemIsSoldByWeight){
      addOrderItem({ notes, code, qtd, measurementValue: weightValue, selectedOptions, selectedExtras, selectedNotes, id: orderItem.id, courseNumber: selectedCourseNumber, toGo });
    } else {
      addOrderItem({ notes, code, qtd, selectedOptions, selectedExtras, selectedNotes, id: orderItem.id, courseNumber: selectedCourseNumber, toGo });
    }
  };

  const disableConfirmButton = !qtd || !isValid || (ongoing && isEmpty(selectedExtras) && isEmpty(selectedOptions)) || !validAdditionsForQtd || (menuItemIsSoldByWeight && calculatedPrice < 1 );
  const toBePaidAmount = ongoing ? 0 : menuItemIsSoldByWeight ? calculatedPrice + (optionsPrice * qtd) + (extrasPrice * qtd) : (unitPrice * qtd) + (optionsPrice * qtd) + (extrasPrice * qtd)

  return (
    <>
      <Modal
        open={open}
        onClose={onClose}
        maxWidth={false}
        PaperProps={{ style: {
            background: "transparent",
            ...shadows.base,
            position: 'absolute',
            left: menuRightOriented ? null : 0,
            right: menuRightOriented ? 0 : null,
            top: 0,
            bottom: 0,
            width: "100%",
            // margin: 0,
            maxWidth: `calc(100% - ${orderPanelWidth} - 12px - 12px - ${fullWidthMax ? '0px' : '12px'})`, // 100% width on absolute item excludes the shifted/margin part as part of width and can shift off screen on 100% width, counter by removing margins as well
            // maxHeight: "100%",
            marginTop: 56,
            marginLeft: 12,
            marginRight: 12,
            marginBottom: 12,
            borderRadius: 20
          } }}
        BackdropProps={{ invisible: true }}
      >
        <ModalBar title={internalName || name} onClose={onClose} />
        <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["100"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}>
          <div className={classes.content}>
            <div>
              {/*
              <div className={classes.layout}>
                <div className={classes.text}>
                  {description && (
                    <div className={classes.description}>
                      <Paragraph>{description}</Paragraph>
                      {additionsCrossCategoryMax && (additionsCrossCategoryMax < 99) && (
                        <Paragraph>{t('item-has-additions-limit', { limit: additionsCrossCategoryMax })}</Paragraph>
                      )}
                    </div>
                  )}
                  {volume && (
                    <div className={classes.description}>
                      <Paragraph>{volume}</Paragraph>
                    </div>
                  )}
                  <div className={classes.pricing}>
                    <div className={classes.price}>
                      <Paragraph>{`${!isEmpty(resolvedOptions) ? `${t('menu-item-price-from')} ` : ''}${salePrice || unitPrice}€`}</Paragraph>
                    </div>
                    {salePrice && (
                      <div className={classes.regularPrice}>
                        <Paragraph>{`${unitPrice}€`}</Paragraph>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              */}
              {menuItemIsSoldByWeight && (
                <div style={{ marginBottom: 16, paddingBottom: 16, borderBottom: `1px solid ${palette.grayscale["250"]}` }}>
                  <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%", marginBottom: 16 }}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t('price-by-weight')}
                    </Typography>
                  </div>
                  <div style={{ display: "grid", gridTemplateColumns: '2fr 0.5fr 2fr 0.5fr 2fr', gridGap: 12 }}>

                      <div style={{ display: "flex", border: `1px solid ${palette.grayscale.border}`, borderRadius: 12  }}>
                        <div style={{ display: "flex", justifyContent: "space-between", width: "100%" }}>
                          <div style={{ display: "flex", flexDirection: "column", padding: fullWidthMax ? "12px 12px 12px 12px" : "12px 0px 12px 12px" }}>
                            <Typography style={{ ...typography.large.regular }}>{weightValue}
                              <span style={{ fontFamily: "Inter", fontWeight: "400", fontStyle: "normal", fontSize: 14, lineHeight: "20px", color: palette.grayscale["600"]}}> g</span>
                            </Typography>
                            <Typography style={{ ...typography.extraSmall.medium, color: palette.grayscale["600"] }}>{t("weight")}</Typography>
                          </div>
                          <ButtonBase style={{ padding: "12px", borderLeft: `1px solid ${palette.grayscale.border}` }} onClick={() => setIsShowingKeyboard(!isShowingKeyboard)} >
                            <KeyboardIcon20 />
                          </ButtonBase>
                        </div>
                      </div>
                      <div style={{ display: "flex", justifyContent: "center", paddingTop: 25 }}>
                        <Typography style={{ ...typography.body.regular }}>x</Typography>
                      </div>
                      <div style={{ display: "flex", border: `1px solid ${palette.grayscale.border}`, padding: 12, borderRadius: 12  }}>
                        <div style={{ display: "flex", flexDirection: "column", width: "100%", flexWrap: "wrap" }}>
                          <Typography style={{ ...typography.large.regular }}>
                            {unitPrice}
                            <span style={{ fontFamily: "Inter", fontWeight: "400", fontStyle: "normal", fontSize: 14, lineHeight: "20px", color: palette.grayscale["600"]}}> €/g</span>
                          </Typography>
                          <Typography style={{ ...typography.extraSmall.medium, color: palette.grayscale["600"] }}>{t("order-item-base-price-label")}</Typography>
                        </div>
                      </div>
                      <div style={{ display: "flex", justifyContent: "center", paddingTop: 25 }}>
                        <Typography style={{ ...typography.body.regular }}>=</Typography>
                      </div>
                      <div style={{ display: "flex", border: `1px solid ${palette.grayscale.border}`, padding: 12, borderRadius: 12  }}>
                        <div style={{ display: "flex", flexDirection: "column", width: "100%", flexWrap: "wrap", alignItems: "flex-end" }}>
                        <Typography style={{ ...typography.large.regular }}>{calculatedPrice ? calculatedPrice.toFixed(2) : '--'}
                          <span style={{ fontFamily: "Inter", fontWeight: "400", fontStyle: "normal", fontSize: 14, lineHeight: "20px", color: palette.grayscale["600"]}}> €</span>
                        </Typography>
                        <Typography style={{ ...typography.extraSmall.medium, color: palette.grayscale["600"] }}>{t("receipt-total-label")}</Typography>
                        </div>
                      </div>

                  </div>

                </div>
              )}
              {!isEmpty(resolvedOptions) && (
                <div>
                  <div>
                    {resolvedOptions
                      .filter(option => !isEmpty(option.items))
                      .map((option) => <MenuItemAddition key={option.id} {...option} update={updateOptions} readOnly={readOnly} prevState={prevSelectedOptionsById[option.id]} limitReached={!canAddAddition} />)}
                  </div>
                </div>
              )}
              {!isEmpty(resolvedExtras) && (
                <div>
                  <div>
                    {resolvedExtras
                      .filter(extra => !isEmpty(extra.items))
                      .map((extra) => <MenuItemAddition key={extra.id} {...extra} update={updateExtras} readOnly={readOnly} prevState={prevSelectedExtrasById[extra.id]} limitReached={!canAddAddition} />)}
                  </div>
                </div>
              )}
              {!isEmpty(remarks) && (
                <div style={{ marginBottom: 16, paddingBottom: 16, borderBottom: `1px solid ${palette.grayscale["250"]}` }}>
                  <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%", marginBottom: remarksCollapsed ? 0 : 16 }} disableRipple disableTouchRipple onClick={toggleRemarksCollapsed}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t('order-item-details-remarks-section-title')}
                    </Typography>
                    <div style={{ display: "flex", transform: remarksCollapsed ? 'rotate(180deg)' : "none" }}>
                      <CollapseIcon20Grayscale400 />
                    </div>
                  </ButtonBase>
                  {!remarksCollapsed && (
                    <div className={classes.remarkList}>
                      <ul>
                        {remarks.map(({ id, description: remarkDescription }) => (
                          <li key={id} className={classes.remarkDescription}>
                            <Typography style={{ ...typography.body.regular }}>
                              {remarkDescription}
                            </Typography>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
              {coursesEnabled && hasCourses && !readOnly && (
                <div style={{ marginBottom: 16, paddingBottom: 16, borderBottom: `1px solid ${palette.grayscale["250"]}` }}>
                  <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%", marginBottom: coursesCollapsed ? 0 : 16 }} disableRipple disableTouchRipple onClick={toggleCoursesCollapsed}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t('courses')}
                    </Typography>
                    <div style={{ display: "flex", transform: coursesCollapsed ? 'rotate(180deg)' : "none" }}>
                      <CollapseIcon20Grayscale400 />
                    </div>
                  </ButtonBase>
                  {!coursesCollapsed && (
                    <div style={{ marginTop: 16, display: "grid", gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gridGap: 16 }}>
                      <ButtonBase key={`course_none`} onClick={() => updateSelectedCourse(null)} disableRipple style={{ padding: 12, border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 12 }}>
                        <div style={{ display: "flex", alignItems: "top", justifyContent: "space-between", width: "100%" }}>
                          <Typography style={{ ...typography.body.regular }}>
                            {t('none')}
                          </Typography>
                          <Checkbox checked={!selectedCourseNumber} />
                        </div>
                      </ButtonBase>
                      {courses.map((course, index) => {
                        const { number, nameI18n } = (course || {})
                        return (
                          <ButtonBase key={`course_${index}_${number}`} onClick={() => updateSelectedCourse(course)} disableRipple style={{ padding: 12, border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 12 }}>
                            <div style={{ display: "flex", alignItems: "top", justifyContent: "space-between", width: "100%" }}>
                              <Typography style={{ ...typography.body.regular }}>
                                {nameI18n ? nameI18n.de : `${t('course')} ${number}`}
                              </Typography>
                              <Checkbox checked={selectedCourseNumber === number} />
                            </div>
                          </ButtonBase>
                        )
                      })}
                    </div>
                  )}
                </div>
              )}
              {!readOnly && customExtrasEnabled && (
                <div style={{ marginBottom: 16, paddingBottom: 16, borderBottom: `1px solid ${palette.grayscale["250"]}` }}>
                  <ButtonBase
                    style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%", marginBottom: customAdditionsCollapse ? 0 : 16 }}
                    disableRipple disableTouchRipple onClick={toggleCustomAdditionsCollapse}
                  >
                    <Typography style={{ ...typography.body.medium }}>
                      {t('order-item-details-custom-addition-section-title')}
                    </Typography>
                    <div style={{ display: "flex", transform: customAdditionsCollapse ? 'rotate(180deg)' : "none" }}>
                      <CollapseIcon20Grayscale400 />
                    </div>
                  </ButtonBase>
                  {!customAdditionsCollapse && (
                    <div>
                      <div className={classes.customAdditionsInput}>
                        <div style={{ display: "flex", justifyContent: "flex-end", marginTop: 12, gap: 16 }}>
                          <Field
                            placeholder={t('order-item-details-custom-addition-field-placeholder')}
                            name="customAdditions"
                            value={customAddition}
                            onChange={updateCustomAddition}
                            variant="filled"
                            autoComplete="off"
                          />
                          <ButtonBase
                            style={{
                              padding: '11px 16px',
                              border: `1px solid ${palette.grayscale["350"]}`,
                              borderRadius: 12
                            }}
                            onClick={openCustomAdditionCreateModal}
                          >
                            <Typography style={{ ...typography.body.regular }}>
                              {t("common-create")}
                            </Typography>
                          </ButtonBase>
                        </div>
                      </div>
                      <div style={{ marginTop: 16 }}>
                        {
                          customExtras?.items?.length > 0 && (
                            <MenuItemAddition
                              hideHeader={true}
                              id={customExtras.id}
                              {...customExtras}
                              items={customExtras.items.filter(item => item.name && item.name.toLowerCase().includes((customAddition || "").toLowerCase())).slice(0, 5)}
                              update={updateExtras}
                              readOnly={readOnly}
                              limitReached={!canAddAddition}
                            />
                          )
                        }
                      </div>
                    </div>
                  )}
                </div>
              )}
              {!readOnly && (
                <div style={{ marginBottom: 16, paddingBottom: 16, borderBottom: `1px solid ${palette.grayscale["250"]}` }}>
                  <ButtonBase
                    style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%", marginBottom: notesCollapsed ? 0 : 16 }}
                    disableRipple disableTouchRipple onClick={toggleNotesCollapsed}
                  >
                    <Typography style={{ ...typography.body.medium }}>
                      {t('order-item-details-notes-section-title')}
                    </Typography>
                    <div style={{ display: "flex", transform: notesCollapsed ? 'rotate(180deg)' : "none" }}>
                      <CollapseIcon20Grayscale400 />
                    </div>
                  </ButtonBase>
                  {!notesCollapsed && (
                    <div>
                      <div className={classes.notesInput}>
                        <Field
                          placeholder={t('order-item-details-notes-section-notes-filed-placeholder')}
                          name="note"
                          value={notes}
                          onChange={updateNotes}
                          variant="filled"
                          id="leviee-welcome-name-input"
                          autoComplete="off"
                          multiline
                        />
                      </div>
                      {predefinedNotes && predefinedNotes.length > 0 && (
                        <div style={{ marginTop: 16, display: "grid", gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gridGap: 16 }}>
                          {predefinedNotes.map(note => {
                            const { id, name } = note;
                            const selected = selectedNotes.some(i => i.id === id)
                            const onClick = () => selected ? unselectNote(note) : selectNote(note)
                            return (
                              <ButtonBase key={id} onClick={onClick} disableRipple style={{ padding: 12, border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 12 }}>
                                <div style={{ display: "flex", alignItems: "top", justifyContent: "space-between", width: "100%" }}>
                                  <Typography style={{ ...typography.body.regular }}>{name}</Typography>
                                  <Checkbox checked={selected} />
                                </div>
                              </ButtonBase>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          <BottomPanel size="l" isSticky>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "flex-end", width: "100%", padding: "12px 16px" }}>
              {orderItemIsSoldByWeight && (
                <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, padding: "12px 24px", borderRadius: 12, }} onClick={removeItem}>
                  <Typography style={{ ...typography.body.medium, color: palette.negative["600"] }}>
                    {t('order-item-remove-btn')}
                  </Typography>
                </ButtonBase>
              )}
              {!ongoing && !menuItemIsSoldByWeight && !orderItemIsSoldByWeight && (
                <QuantityInput quantity={qtd} increment={onMore} decrement={onLess} />
              )}
              <ButtonBase style={{ marginLeft: 12, padding: "12px 24px", borderRadius: 12, background: disableConfirmButton ? palette.grayscale["400"] : palette.primary["500"] }} disabled={disableConfirmButton} onClick={onAddOrderItem}>
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                  {t(orderItemId ? 'common-update' : 'order-item-details-add-to-order-btn-label')} {toBePaidAmount ? `${toBePaidAmount.toFixed(2)}€` : null}
                </Typography>
              </ButtonBase>
            </div>
          </BottomPanel>
        </div>
      </Modal>
      {isShowingKeyboard && <ScaleModal open={isShowingKeyboard} onClose={() => setIsShowingKeyboard(false)} onSubmit={setWeightValue} /> }
      {customAdditionCreateModalOpen && <CustomItemCreationModal initialName={customAddition} open={customAdditionCreateModalOpen} onClose={closeCustomAdditionCreateModal} titleI18n={'create-custom-addition'} />}
  </>
  )
};

export default withTranslation('common')(MenuItemDetailsModal);
