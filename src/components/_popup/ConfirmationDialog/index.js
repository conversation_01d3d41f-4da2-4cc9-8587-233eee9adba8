import React from 'react';
import palette from '../../../../styles/palette';
import typography from '../../../../styles/typography';
import Modal from '../Modal';
import shadows from '../../../../styles/shadows';
import Typography from "@material-ui/core/Typography";
import { ButtonBase } from '@material-ui/core';
import { withTranslation } from "../../../../i18n";

const ConfirmationDialog = ({
  open,
  title,
  messages,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  t
}) => {
  return (
    <Modal
      open={open}
      onClose={onCancel}
      PaperProps={{
        style: {
          background: palette.grayscale["100"],
          borderRadius: 16,
          ...shadows.large,
          width: 400,
          maxWidth: "90%",
          padding: 0
        }
      }}
    >
      <div style={{
        padding: 24,
        textAlign: "left"
      }}>
        <Typography style={{
          ...typography.body.medium,
          marginBottom: 4,
          color: palette.grayscale["800"]
        }}>
          {title}
        </Typography>

        {messages?.[0] && (
          <Typography style={{
            ...typography.body.regular,
            marginBottom: '16px',
            color: palette.grayscale["600"],
            lineHeight: 1.5
          }}>
            {messages?.[0]}
          </Typography>
        )}

        {!!messages?.[1] && (
          <Typography style={{
            ...typography.body.regular,
            color: palette.grayscale["600"],
            lineHeight: 1.5
          }}>
            {messages?.[1]}
          </Typography>
        )}

        <div style={{
          display: "flex",
          gap: 12,
          justifyContent: "right",
          borderTop: `1px dashed ${palette.grayscale["350"]}`,
          paddingTop: 12,
          marginTop: 20
        }}>
          <ButtonBase
            onClick={onCancel}
            style={{
              padding: "12px 24px",
              borderRadius: 12,
              background: 'transparent',
            }}
          >
            <Typography style={{
              ...typography.body.medium,
              color: palette.grayscale["800"]
            }}>
              {cancelText || t("common-cancel")}
            </Typography>
          </ButtonBase>

          <ButtonBase
            onClick={onConfirm}
            style={{
              padding: "12px 24px",
              borderRadius: 12,
              background: palette.primary["500"], 
            }}
          >
            <Typography style={{
              ...typography.body.medium,
              color: palette.grayscale["100"]
            }}>
              {confirmText || t("common-continue")}
            </Typography>
          </ButtonBase>
        </div>
      </div>
    </Modal>
  );
};

export default withTranslation("common")(ConfirmationDialog);