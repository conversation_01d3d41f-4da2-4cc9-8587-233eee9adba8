import React, { Fragment, useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import Keyboard from "../../Keyboard";
import { formatToLocaleNumber } from "../../../utils/formatToLocaleNumber";
import isValidEmail from "../../../utils/isValidEmail";
import { ButtonBase, NativeSelect } from "@material-ui/core";
import Radio from "../../_toggles/Radio";
import { getOpenInvoiceTaxRates } from "../../../api";
import { useSelector } from "react-redux";
import { restaurantSelectors } from "../../../../redux/selectors";
import NumericalInput from "../../Buttons/NumericalInput";

const defaultData = {
  customer: {
    name: "",
    email: "",
    company: "",
    taxNumber: "",
    vatId: "",
    address: {
      street: '',
      number: '',
      zipCode: '',
      city: '',
      country: "DE",
    },
  },
  taxRateId: "",
  serviceFeePercentage: 0,
  amount: 0,
};

const CreateInvoiceModal = ({ t, open, onClose, submit, standalone, hasServiceFee = true, saving, data, ...otherProps }) => {
  const prefilledData = Object.assign({}, (data || {}))
  const [form, setForm] = useState({ ...defaultData, ...prefilledData });
  const [calculatorAmount, setCalculatorAmount] = useState('');

  const restaurantId = useSelector(restaurantSelectors.getRestaurantId)
  const [taxRates, setTaxRates] = useState([])

  useEffect(() => {
    if (standalone) {
      getOpenInvoiceTaxRates(restaurantId).then(({ data }) => {
        const { items } = data;
        setTaxRates(items)
      }).catch(() => {})
    }
  }, [standalone])

  const onChange = (e) => {
    let val = e.target.value
    setForm({ ...form, [e.target.name]: val })
  };

  const onAddressChange = (e) => {
    let val = e.target.value
    const currentAddress = form?.customer?.address
    const updatedAddress = { ...(currentAddress || {}), [e.target.name]: val }
    const updatedCustomer = { ...form.customer, address: updatedAddress }
    setForm({ ...form, customer: updatedCustomer })
  };

  const onCustomerChange = (e) => {
    let val = e.target.value
    const updated = { ...(form.customer || {}), [e.target.name]: val }
    setForm({ ...form, customer: updated })
  };

  const onDelete = () => {
    if (calculatorAmount) {
      try {
        let settingFormVal = "";
        settingFormVal = calculatorAmount.slice(0, -1)
        setCalculatorAmount(settingFormVal)
      } catch (ignore) {}
    }
  }

  const onKeyboardClick = (char) => {
    if (!calculatorAmount && (char === "0" || char === "00")) {
      return;
    }

    let settingFormVal = ""

    if (calculatorAmount.length < 7) {
      settingFormVal = calculatorAmount + char
      setCalculatorAmount(settingFormVal)
    }
  }

  const onDone = () => {
    let data = { ...form }
    data.amount = getCalculatorAmount()
    submit(data);
  }

  const handleClose = () => {
    setForm({ ...data })
    onClose();
  }

  const updateQuantityMore = () => {
    setForm({ ...form, serviceFeePercentage: form.serviceFeePercentage + 1 })
  }

  const updateQuantityLess = () => {
    setForm({ ...form, serviceFeePercentage: form.serviceFeePercentage ? form.serviceFeePercentage - 1 : 0 })
  }

  //const errorOnVAT = form?.customer?.vatId ? !isValidVAT(form?.customer?.vatId) : null

  const errorOnEmail = form?.customer?.email ? !isValidEmail(form?.customer?.email) : null

  const canSubmit = !!form.customer
    && !!form.customer.name
    && !!form.customer.name.length < 2
    && !!form.customer.email
    && !!form.customer.address
    && !!form.customer.address.street
    && !!form.customer.address.number
    && !!form.customer.address.zipCode
    && !!form.customer.address.city
    && !!form.customer.address.country
    && !errorOnEmail
    && (!!calculatorAmount || !standalone)
    && (!!form.taxRateId || !standalone)
    && (!!form.description || !standalone)
    && !saving
    //&& !errorOnVAT

  const getCalculatorAmount = () => {
    return parseInt(calculatorAmount || 0) / 100
  }

  const getAddressField = () => {
    const resolvedAddress = form?.customer?.address || {};

    return (
      <div style={{ flex: 1 }}>
        <div style={{ display: "flex", maxWidth: 520, width: "100%" }}>
          <div style={{ flex: 1, marginRight: 8 }}>
            <Field
              onChange={onAddressChange}
              value={resolvedAddress.street}
              name="street"
              label={t('address-street')}
              required={true}
              disabled={saving}
            />
          </div>
          <div style={{ marginLeft: 8, maxWidth: 100 }}>
            <Field
              onChange={onAddressChange}
              value={resolvedAddress.number}
              name="number"
              label={t('address-number')}
              required={true}
              disabled={saving}
            />
          </div>
        </div>
        <div style={{ display: "flex", maxWidth: 520, width: "100%", marginRight: 8, marginTop: 16 }}>
          <div style={{ flex: 1 }}>
            <Field
              onChange={onAddressChange}
              label={t("address-zip-code")}
              value={resolvedAddress.zipCode}
              name="zipCode"
              required={true}
              disabled={saving}
            />
          </div>
          <div style={{ marginLeft: 16, flex: 1 }}>
            <Field
              onChange={onAddressChange}
              label={t("address-city")}
              value={resolvedAddress.city}
              name="city"
              required={true}
              disabled={saving}
            />
          </div>
        </div>
        <div style={{ display: "flex", maxWidth: 520, width: "100%", marginTop: 16 }}>
          <div style={{ flex: 1 }}>
            <NativeSelect
              labelId="country-label"
              id="country-select"
              name="country"
              value={resolvedAddress.country}
              onChange={onAddressChange}
              style={{ maxWidth: 220 }}
              disabled={saving}
              InputLabelProps={{
                shrink: true,
              }}
              inputProps={{
                name: 'country',
                id: 'country-selector'
              }}
              input={<Field label={t('restaurant-address-country')} />}
            >
              <option value="DE">{t('germany')}</option>
              <option value="DK">{t('denmark')}</option>
              <option value="NL">{t('netherlands')}</option>
              <option value="BE">{t('belgium')}</option>
              <option value="AT">{t('austria')}</option>
              <option value="FR">{t('france')}</option>
              <option value="IT">{t('italy')}</option>
              <option value="CH">{t('switzerland')}</option>
              <option value="PL">{t('poland')}</option>
            </NativeSelect>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Modal
      open={open}
      onClose={handleClose}
      {...otherProps}
      fullScreen
      style={{ marginTop: 20 }}
      PaperProps={{ style: { background: "transparent", ...shadows.large, zIndex: 9999 } }}
    >
      <ModalBar title={t("open-invoices")} onClose={handleClose} onDone={canSubmit ? onDone : null} />
      <div style={{ height: "100%", overflow: "auto" }}>
        <div style={{
          minHeight: "100%",
          background: palette.grayscale["200"], paddingBottom: 24, paddingLeft: 12, paddingRight: 12, paddingTop: standalone ? 0 : 40 }}>
          <div style={{ maxWidth: "430px", margin: "0 auto" }}>

            {/* to pay amount */}
            {standalone && (
              <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 40, paddingBottom: 40, flex: 1 }}>
                <Typography style={{ ...typography.body.medium, marginBottom: 16, textAlign: "center" }}>
                  {t("invoice-amount")}
                </Typography>
                <Typography style={{ ...typography.x.paymentAmount, textAlign: "center" }}>
                <span
                  style={{ color: !calculatorAmount ? palette.grayscale["400"] : "inherit" }}>{calculatorAmount ? formatToLocaleNumber(getCalculatorAmount()) : "0,00"}</span>€
                </Typography>
              </div>
            )}

            {/* keyboard */}
            {standalone && (
              <div style={{ marginTop: 24 }}>
                <Keyboard
                  style={{ ...shadows.base }}
                  specialChar="00"
                  onDelete={onDelete}
                  onClick={onKeyboardClick}
                  disabled={saving}
                />
              </div>
            )}

            {standalone && (
              <div style={{ marginTop: 24 }}>
                <Field
                  value={form.orderId}
                  label={`${t("order")}`}
                  name="orderId"
                  onChange={onChange}
                  variant="filled"
                  autoComplete="off"
                  disabled={saving}
                />
              </div>
            )}

            {standalone && (
              <div style={{ marginTop: 24 }}>
                <Field
                  value={form.description}
                  label={`${t("description")}`}
                  name="description"
                  onChange={onChange}
                  variant="filled"
                  autoComplete="off"
                  disabled={saving}
                />
              </div>
            )}

            {standalone && (
              <div style={{ marginTop: 24 }}>
                <Typography style={{ ...typography.body.medium }}>{t("tax")}</Typography>
                <div style={{
                  display: "flex",
                  flexDirection: "row",
                  flexWrap: "wrap"
                }}>
                  {taxRates && taxRates.map(itm => {
                    return (
                      <ButtonBase disableRipple disableTouchRipple style={{
                        marginRight: 16,
                        marginTop: 8,
                        minWidth: 147,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        background: palette.grayscale["100"],
                        padding: 12,
                        ...shadows.base,
                        borderRadius: 12
                      }} key={itm.id} onClick={() => setForm({ ...form, taxRateId: itm.id })}>
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <Typography style={{ ...typography.body.regular, marginLeft: 4 }}>
                            {itm.display_name}
                          </Typography>
                        </div>
                        <Radio checked={itm.id === form?.taxRateId} />
                      </ButtonBase>
                    );
                  })}
                </div>
              </div>
            )}

            {/* name */}
            <div style={{ marginTop: standalone ? 24 : 0 }}>
              <Field
                label={`${t("name")}`}
                name="name"
                value={form?.customer?.name}
                onChange={onCustomerChange}
                variant="filled"
                autoComplete="off"
                required={true}
                disabled={saving}
              />
            </div>

            {/*email*/}
            <div style={{ marginTop: 24 }}>
              <Field
                label={`${t("email")}`}
                name="email"
                value={form?.customer?.email}
                onChange={onCustomerChange}
                required={true}
                error={errorOnEmail}
                disabled={saving}
              />
            </div>

            {/*company*/}
            <div style={{ marginTop: 24 }}>
              <Field
                label={`${t("company")}`}
                name="company"
                value={form?.customer?.company}
                onChange={onCustomerChange}
                disabled={saving}
              />
            </div>

            <div style={{ marginTop: 24 }}>
              <Field
                label={`${t("tax-number")}`}
                name="taxNumber"
                value={form?.customer?.taxNumber}
                onChange={onCustomerChange}
                disabled={saving}
              />
            </div>

            <div style={{ marginTop: 24 }}>
              <Field
                label={`${t("vat-id")}`}
                name="vatId"
                value={form?.customer?.vatId}
                onChange={onCustomerChange}
                disabled={saving}
                //error={errorOnVAT}
              />
            </div>

            {/* address */}
            <div style={{ marginTop: 24 }}>
              {getAddressField()}
            </div>

            {/* notes */}
            <div style={{ marginTop: 24, paddingTop: 24, borderTop: `1px dashed ${palette.grayscale["350"]}` }}>
              <Field
                value={form.notes}
                multiline
                label={`${t("notes")}`}
                name="notes"
                onChange={onChange}
                variant="filled"
                autoComplete="off"
                disabled={saving}
              />
            </div>
            {hasServiceFee && (
              <div style={{ marginTop: 24 }}>
                <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>
                  {t('service-fee')} %
                </Typography>
                <NumericalInput counter={form.serviceFeePercentage} onMore={updateQuantityMore} onLess={updateQuantityLess} />
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(CreateInvoiceModal);
