import React, { useState } from "react";
import { withTranslation } from "../../../../i18n";
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import { NativeSelect} from "@material-ui/core";
import { measurementUnitOptions } from "../../../utils/const";

const data = {
  name: null,
  numeration: null,
  lowStockThreshold: 0,
  unit: null
};

const InventoryCategoryModal = ({ t, open, onClose, value = {}, setValue, deleteValue, titleI18n }) => {
  const [inventoryCategory, setInventoryCategory] = useState({ ...data, ...value });
  
  const onDone = () => {
    setValue(inventoryCategory);
    onClose();
  };
  
  const onDelete = (id) => {
    deleteValue(id);
    onClose();
  }
  
  const onChange = (stateKey, value) => {
    let val = value;
  
    if (['unit'].indexOf(stateKey) > -1) {
      if (!val) {
        val = null;
      }
    }
    
    setInventoryCategory({ ...inventoryCategory, [stateKey]: val });
  };
  
  const canCreate = inventoryCategory && inventoryCategory.name && inventoryCategory.unit && (!inventoryCategory.lowStockThreshold || inventoryCategory.lowStockThreshold >= 0)
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent", ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} onDone={canCreate ? onDone : null} />
      <div style={{
        height: "100%",
        overflow: "auto",
        background: palette.grayscale["200"],
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20
      }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
          <div style={{ flex: 1 }}>
            <div style={{ display: "flex", maxWidth: 520, width: "100%" }}>
              <div style={{ flex: 1 }}>
                <Field
                  label={t("name")}
                  placeholder={'Pasta'}
                  value={inventoryCategory.name}
                  onChange={(e) => onChange("name", e.target.value)}
                  required
                />
              </div>
            </div>
            <div style={{ display: "flex", maxWidth: 100, width: "100%", marginTop: 16 }}>
              <div style={{ flex: 1 }}>
                <Field
                  label={t("numeration")}
                  placeholder={'A3'}
                  value={inventoryCategory.numeration}
                  onChange={(e) => onChange("numeration", e.target.value)}
                />
              </div>
            </div>
            <div style={{ display: "flex", maxWidth: 100, width: "100%", marginTop: 16 }}>
              <div style={{ flex: 1 }}>
                <Field
                  label={t("low-stock-threshold")}
                  placeholder={10}
                  value={inventoryCategory.lowStockThreshold}
                  onChange={(e) => onChange("lowStockThreshold", e.target.value)}
                  type="number"
                />
              </div>
            </div>
            <div style={{ display: "flex", maxWidth: 200, width: "100%", marginTop: 16 }}>
              <div style={{ flex: 1 }}>
                <NativeSelect
                  name="categoryId"
                  value={inventoryCategory.unit}
                  onChange={(e) => onChange("unit", e.target.value)}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  inputProps={{
                    name: 'inventoryCategoryUnit',
                    id: 'inventoryCategoryUnit-selector'
                  }}
                  input={<Field label={t('measurement-unit')} />}
                >
                  <option value={''}>{t('select')}</option>
                  {measurementUnitOptions.map(u => <option value={u.value}>{t(u.i18nKey)}</option>)}
                </NativeSelect>
              </div>
            </div>
            {/*{value && value.id && (*/}
            {/*  <div style={{ display: "flex", flexDirection: "column", alignItems: "top", marginTop: 32, paddingTop: 32, borderTop: '1px solid #E8E7E6' }}>*/}
            {/*    <div style={{ marginRight: 0, marginBottom: 16, width: "100%" }}>*/}
            {/*      <Typography style={{  ...typography.body.medium }}>{t('delete-product-category')}</Typography>*/}
            {/*      <Typography style={{  ...typography.body.regular, marginTop: 4 }}>{t('this-actions-is-not-reversible')}</Typography>*/}
            {/*    </div>*/}
            {/*    <div style={{ flex: 1 }}>*/}
            {/*      <div style={{ display: "flex", width: "100%" }}>*/}
            {/*        <Confirm*/}
            {/*          title={t("delete-product-category")}*/}
            {/*          description={t("are-you-sure-description-to-delete-item")}*/}
            {/*        >*/}
            {/*          {confirm => (*/}
            {/*            <ButtonBase style={{ padding: '12px 24px', width: "100%", background: palette.negative["500"], borderRadius: 12 }} disableRipple disableTouchRipple onClick={confirm(() => onDelete(value.id))}>*/}
            {/*              <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>*/}
            {/*                {t('common-delete')}*/}
            {/*              </Typography>*/}
            {/*            </ButtonBase>*/}
            {/*          )}*/}
            {/*        </Confirm>*/}
            {/*      </div>*/}
            {/*    </div>*/}
            {/*  </div>*/}
            {/*)}*/}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default withTranslation("common")(InventoryCategoryModal);
