import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import { Table } from "../../Administration/Terminal/Table";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import BottomBar from "../../_navigation/BottomBar";
import { useDispatch, useSelector } from "react-redux";
import { restaurantSelectors, terminalSelectors } from "../../../../redux/selectors";
import { ButtonBase, Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import orderStatus from "../../../utils/orderStatus";
import { cancelOrder, fastCheckoutOrder } from "../../../api";
import isEmpty from "../../../utils/isEmpty";
import Loading from "../../Loading";
import { expressActions, printersActions } from "../../../../redux/actions";
import { orderTypes } from "../../../../redux/constants";
import ConfirmationDialog from "../ConfirmationDialog";
import { noop } from "lodash";

const ExpressTerminalModal = ({ t, open, onClose, onReset, isToGo, orderId, ...otherProps }) => {
  const dispatch = useDispatch();
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { paymentChannels = [] } = restaurant;
  const { order = {} } = useSelector(terminalSelectors.getOrder);
  const { total, status, items = [], number, orderingDeviceType } = order;
  const hasItems = !isEmpty(items);
  const { processingPayment } = useSelector(terminalSelectors.getProcessingPayment);
  const [session, setSession] = useState(1);

  const [checkoutOrderId, setCheckoutOrderId] = useState(null);
  const [oneClickCheckout, setOneClickCheckout] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);

  const [step, setStep] = useState(1);
  const startCheckout = () => {
    setCheckoutOrderId(order.id);
    setStep(2);
  }

  const getBack = () => step === 2 ? () => {
    setCheckoutOrderId(null);
    setStep(1);
  } : null;

  const handleClose = () => {
    setStep(1);
    setCheckoutOrderId(null);
    onClose();
  }

  useEffect(() => {
    if (!open) return;
    if (status === orderStatus.CANCELLED) {
      setCheckoutOrderId(null);
      setStep(1);
      onReset();
      setSession(session + 1);
    }
    if ([orderStatus.CLOSED, orderStatus.PREPARING, orderStatus.READY].indexOf(status) > -1) {
      setStep(1);
      setCheckoutOrderId(null);
      onReset();
      setSession(session + 1);
    }
  }, [status, open])

  const checkoutAndPay = () => {
    const defaultPaymentChannel = isEmpty(paymentChannels) ? "CASH" : paymentChannels[0]
    setOneClickCheckout(true);
    if (defaultPaymentChannel === "CASH") {
      dispatch(printersActions.openCashDrawer())
    } else {
      dispatch(printersActions.pingPrinters())
    }
    fastCheckoutOrder(order.id, defaultPaymentChannel).then(() => {
      setStep(1);
      setCheckoutOrderId(null);
      onReset();
      setSession(session + 1);
      setOneClickCheckout(false);
    });
  }
  
  const handleCancelOrder = () => {
    setShowCancelConfirmation(true);
  };

  const confirmCancelOrder = () => {
    cancelOrder(orderId)
      .then(onClose)
      .catch(noop);

    setShowCancelConfirmation(false);
  };

  const cancelCancelOrder = () => {
    setShowCancelConfirmation(false);
  };

  const getTitle = () => {
    if (number && isToGo) {
      return `${t("to-go-express-mode")} ${t("order-number", { number })}`
    }
    if (number && !isToGo) {
      return `${t("express")} ${t("order-number", { number })}`
    }
    if (!number && !isToGo) {
      return `${t('express')} - ${t("add-items")}`
    }
    if (!number && isToGo) {
      return `${t('to-go-express-mode')} - ${t("add-items")}`
    }
  }

  return (
    <>
      <Modal
        open={open}
        onClose={handleClose}
        {...otherProps}
        fullScreen
        style={{ marginTop: 16 }}
        PaperProps={{ style: { background: "transparent",  ...shadows.large } }}
      >
        {processingPayment && <Loading />}
        <ModalBar title={getTitle()} onClose={handleClose} />
        <div style={{ height: "100%", overflow: "auto" }} key={session}>
          <Table orderType={orderTypes.EXPRESS.key} embedded orderId={orderId} checkoutOrderId={checkoutOrderId} oneClickCheckout={oneClickCheckout} isToGo={isToGo} setView={() => {}} />
        </div>
        <BottomBar onBack={getBack()} title={t("step-count-of-total", { count: step, total: 2 })} right={(!!total || hasItems) && (step === 1) && [
          // <ButtonBase
          //   key={"pay-and-finish"}
          //   disableRipple
          //   disableTouchRipple
          //   onClick={checkoutAndPay}
          //   style={{
          //     paddingTop: 12,
          //     paddingBottom: 12,
          //     paddingLeft: 16,
          //     paddingRight: 16,
          //     display: "flex",
          //     alignItems: "center",
          //     background: "transparent",
          //     borderRadius: 12,
          //     whiteSpace: "nowrap"
          //   }}
          // >
          //   <Typography style={{ ...typography.body.medium }}>
          //     {t('pay-and-finish')}
          //   </Typography>
          // </ButtonBase>,
          <>
          {(orderingDeviceType === 'KIOSK') && order?.actions?.includes('CANCEL') && (
            <ButtonBase
              key={"cancel-order"}
              disableRipple
              disableTouchRipple
              onClick={handleCancelOrder}
              style={{
                paddingTop: 12,
                paddingBottom: 12,
                display: "flex",
                alignItems: "center",
                background: palette.grayscale["200"],
                borderRadius: 12,
                paddingLeft: 16,
                paddingRight: 16,
              }}
            >
              <Typography style={{ ...typography.body.medium, color: palette.negative["600"] }}>
                {t('cancel-order')}
              </Typography>
            </ButtonBase>
          )}
          <ButtonBase
            key={"checkout"}
            disableRipple
            disableTouchRipple
            onClick={startCheckout}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 16,
              paddingRight: 16,
              display: "flex",
              alignItems: "center",
              background: palette.primary["500"],
              marginLeft: 12,
              borderRadius: 12
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {t('checkout')}
            </Typography>
          </ButtonBase>
          </>
        ]}/>
      </Modal>
      <ConfirmationDialog
        open={showCancelConfirmation}
        title={t('cancel-this-order')}
        messages={[t('cancel-order-confirmation-message')]}
        confirmText={t('cancel-order')}
        cancelText={t('go-back')}
        onConfirm={confirmCancelOrder}
        onCancel={cancelCancelOrder}
      />
    </>
  )
}

export default withTranslation("common")(ExpressTerminalModal);
