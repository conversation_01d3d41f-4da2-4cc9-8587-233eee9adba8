import React, { useEffect, useState } from "react";
import { withTranslation } from "../../../../i18n";
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import Field from "../../form/Field";
import PhoneNumberInput from "../../_input/PhoneNumberInput";
import isValidEmail from "../../../utils/isValidEmail";
import isEmpty from "../../../utils/isEmpty";

const data = {
  name: null,
  phone: null,
  email: null
};

const ShareholderUpdateModal = ({ t, open, onClose, value = {}, setValue, titleI18n }) => {
  const [shareholder, setShareholder] = useState({ ...data });
  
  const onDone = () => {
    setValue(shareholder);
    onClose();
  };
  
  const onChange = (stateKey, value) => {
    setShareholder({ ...shareholder, [stateKey]: value });
  };
  
  const canCreate = shareholder && shareholder.phone && shareholder.email && shareholder.name && isValidEmail(shareholder.email)
  const errorOnEmail = !isValidEmail(shareholder.email)
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent", ...shadows.large, width: 500, maxWidth: "90%" } }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} onDone={canCreate ? onDone : null} />
      <div style={{
        height: "100%",
        overflow: "auto",
        background: palette.grayscale["200"],
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20
      }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 40 }}>
          <div style={{ flex: 1 }}>
            <div style={{ display: "flex", maxWidth: 520, width: "100%" }}>
              <div style={{ flex: 1 }}>
                <Field
                  label={t("name")}
                  placeholder={'Max Musterman'}
                  value={shareholder.name}
                  onChange={(e) => onChange("name", e.target.value)}
                />
              </div>
            </div>
            <div style={{ display: "flex", maxWidth: 520, width: "100%", marginTop: 16 }}>
              <div style={{ flex: 1 }}>
                <Field
                  label={t("email")}
                  value={shareholder.email}
                  placeholder={'<EMAIL>'}
                  onChange={(e) => onChange("email", e.target.value)}
                  error={!isEmpty(shareholder.email) && errorOnEmail}
                />
              </div>
            </div>
            <div style={{ display: "flex", maxWidth: 520, width: "100%", marginTop: 16 }}>
              <div style={{ flex: 1 }}>
                <PhoneNumberInput
                  label={t("restaurant-phone")}
                  value={shareholder.phone}
                  setValue={(value) => onChange("phone", value)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default withTranslation("common")(ShareholderUpdateModal);
