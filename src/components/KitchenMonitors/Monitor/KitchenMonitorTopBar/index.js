import React, { useEffect, useState } from "react";
import { Button } from "../../../Buttons";
import {
  ArrowLeft32,
  KitchenMonitorSettingsCog22,
  SliderIcon,
} from "../../../../utils/icons";
import Typography from "@material-ui/core/Typography";
import IconButton from "@material-ui/core/IconButton";
import { withTranslation } from "../../../../../i18n";
import SegmentedControlBaseTabs from "./SegmentedButtons";
import { useRouter } from "next/router";
import { views } from "../../../../utils/administrationRoutes";
import { useSelector } from "react-redux";
import { configurationSelectors } from "../../../../../redux/selectors";

export const filterModes = {
  OPEN_TICKETS: {
    key: "OPEN_TICKETS",
    i18nKey: "kitchen-monitor-top-bar-open-tickets-btn"
  },
  COMPLETED: {
    key: "COMPLETED",
    i18nKey: "kitchen-monitor-top-bar-completed-btn"
  },
  INVENTORY: {
    key: "INVENTORY",
    i18nKey: "kitchen-monitor-top-bar-inventory-btn"
  }
};

export const filterModeOptions = Object.values(filterModes);

export const filterModesWithoutInventory = Object.fromEntries(
  Object.entries(filterModes).filter(([key]) => key !== "INVENTORY")
);
export const filterModeOptionsWithoutInventory = Object.values(filterModesWithoutInventory);

export const MonitorTopBar = withTranslation("common")(({
  t,
  setView,
  openSummary,
  openItems = 15,
  completedItems = 22,
  hasQuantityGroup = false,
  monitorData = {
    name: "Unnamed"
  },
  openConfig,
  tabs = []
}) => {
  const router = useRouter();
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "");
  
  const [value, setValue] = useState("OPEN_TICKETS");
  const showInventory = useSelector(configurationSelectors.getConfigurationShowInventory)
  
  useEffect(() => {
    setView(value);
  }, [value]);

  const openTab = tabs.find(x => x.type === "OPEN");
  const completedTab = tabs.find(x => x.type === "COMPLETED");
  
  const onBack = () => {
    router.push(`${resolvedAsPath}?v=${views.KITCHEN_MONITORS_MANAGER}`, undefined, { shallow: true });
  }

  return (
    <div style={{
      padding: 12,
      display: "flex",
      justifyContent: "space-between"
    }}>
      <div style={{
        display: "flex",
        gap: 8
      }}>
        {
          hasQuantityGroup ? (
            <Button
              onClick={openSummary}
              variant="contained"
              startIcon={<SliderIcon />}
              style={{
                background: "white",
                height: 48,
                padding: "0 24px",
                zIndex: 2000
              }}
            >
              <Typography style={{
                //styleName: Medium/Medium;
                fontFamily: "Inter",
                fontSize: 16,
                fontWeight: 500,
                color: "black"
              }}>
                {t("kitchen-monitor-top-bar-summary-btn")}
              </Typography>
            </Button>
          ) : null
        }
      </div>
      <div>
        <SegmentedControlBaseTabs
          background="rgba(51, 51, 50, 0.06)"
          tabs={showInventory? filterModeOptions : filterModeOptionsWithoutInventory}
          value={value}
          setValue={setValue}
          openItems={openTab?.numberOfOrders || 0}
          completedItems={completedTab?.numberOfOrders || 0}
        />
      </div>
      <div>
        <Button
          variant="contained"
          onClick={onBack}
          style={{
            background: "white",
            height: 48,
            padding: "0px 16px 0px 12px",
            borderTopRightRadius: 0,
            borderBottomRightRadius: 0,
          }}
        >
          <ArrowLeft32 />
          <Typography style={{
            //styleName: Medium/Medium;
            fontFamily: "Inter",
            fontSize: 16,
            fontWeight: 500,
            color: "black",
            marginLeft: 6
          }}>
            {monitorData.name}
          </Typography>
        </Button>
        <IconButton
          onClick={openConfig}
          style={{
          borderRadius: 10,
          borderTopLeftRadius: 0,
          borderBottomLeftRadius: 0,
          borderLeft: "0.5px solid rgba(232, 231, 230, 1)",
          background: "white",
          width: 48,
          height: 48
        }}>
          <KitchenMonitorSettingsCog22 />
        </IconButton>
      </div>
    </div>
  );
});
