import React from "react";
import {
  DeleteIcon24,
  MinusIcon16, PlusIcon16
} from "../../../../utils/icons";
import { withTranslation } from "../../../../../i18n";
import shadows from "../../../../../styles/shadows";
import palette from "../../../../../styles/palette";
import ButtonBase from "@material-ui/core/ButtonBase";
import ColourLegend from "../KitchenMonitorColourLegend";
import { Typography } from "@material-ui/core";


const numbers = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"];


export const KitchenMonitorBottomBar = withTranslation("common")(({
  t,
  showLegend,
  decreaseZoom,
  increaseZoom,
  toggleLegend,
  stepFlow,
  numberValue,
  onDelete,
  onNumberChange,
  displayNumberSelector,
  view
}) => {
  
  const isShowingInventory = view === "INVENTORY";
  
  return (
      <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", padding: "0px 16px" }}>
        
        <div style={{ display: "flex", alignItems: "flex-end", gap: 12 }}>
          <ButtonBase style={{ display: "flex", justifyContent: "center", backgroundColor: '#000000', borderRadius: 50, width: 44, height: 44 }} onClick={toggleLegend} >
            <div style={{ color: palette.grayscale.white, width: 20, height: 20, fontSize: 20, marignBottom: 2 }}>?</div>
          </ButtonBase>
          {showLegend && (
            <div style={{
              position: "absolute",
              marginLeft: 55,
            }}>
              <ColourLegend stepFlow={stepFlow} />
            </div>
          )}
        </div>
       
        {displayNumberSelector && !showLegend && !isShowingInventory && (
          <div style={{ display: "flex", gap: 6, alignItems: "center", backgroundColor: '#000000', borderRadius: 50, padding: "4px 18px" }}>
            <Typography style={{  fontSize: 16, fontWeight: 400, lineHeight: "22px", color: palette.grayscale.white }}>{t("number-selector-header")}</Typography>
            <ButtonBase style={{ display: "flex", justifyContent: "center", backgroundColor: "#313131", height: 36, minWidth: 60, borderRadius: 10, padding: "8px 10px" }}>
              <Typography style={{  fontSize: 18, fontWeight: 400, lineHeight: "22px", color: palette.grayscale.white }}>{numberValue}</Typography>
            </ButtonBase>
            {numbers.map((item, i) => {
              return (
                <ButtonBase key={i} style={{ display: "flex", justifyContent: "center", backgroundColor: palette.primary["500"], borderRadius: 50, width: 44, height: 44 }} onClick={() => onNumberChange(item)}>
                 <Typography style={{  fontSize: 18, fontWeight: 400, lineHeight: "22px", color: palette.grayscale.white }}>{item}</Typography>
                </ButtonBase>
              )
            })}
            <ButtonBase style={{ display: "flex", justifyContent: "center", backgroundColor: palette.primary["500"], borderRadius: 50, width: 44, height: 44 }} onClick={onDelete}>
              <div style={{ marginTop: 4 }}><DeleteIcon24 color={palette.grayscale.white}/></div>
            </ButtonBase>
          </div>
        )}
       
        <div style={{ display: "flex", alignItems: "center", flexDirection: "column" }}>
          <div style={{
            display: "flex",
            alignItems: "center",
            flexDirection: "column",
            ...shadows.base,
            marginBottom: 8,
            borderRadius: 10,
            backgroundColor: palette.grayscale["100"]
          }}>
            <ButtonBase
              disableTouchRipple
              disableRipple
              onClick={increaseZoom}
              style={{
                padding: 8,
                minWidth: 0,
                borderRadius: 0,
                backgroundColor: "transparent",
                borderBottom: "1px solid #EBEAEA"
              }}
            >
              <PlusIcon16 />
            </ButtonBase>
            <ButtonBase
              disableTouchRipple
              disableRipple
              onClick={decreaseZoom}
              style={{ padding: 8, minWidth: 0, backgroundColor: "transparent", borderRadius: 0 }}
            >
              <MinusIcon16 />
            </ButtonBase>
          </div>
        </div>
       
      </div>
  );
});
