{"monitor": {"id": "01HZVFBCZCE4RNSWTSQ9A206DT", "name": "my monitor"}, "items": [{"id": "666ab06dde712a7b4f8205dd", "creationTime": "2024-06-13T08:40:13.953+00:00", "modificationTime": "2024-06-13T09:20:46.811+00:00", "number": 57, "restaurantId": "62b1b639230d5d186d059699", "userId": "<EMAIL>", "accountId": "01G632X5RHD4BG76WY9RA03B38", "isWaiter": true, "tableId": "65f1f3016c89761087a75fa6", "tableCode": "150", "status": "OPEN", "type": "DINE_IN", "mode": "LIVE", "timeZone": "Europe/Berlin", "duration": 41, "remainingDuration": 79, "estimatedDiningTime": 120, "estimatedPreparationTime": 45, "courseConfig": {"enabled": true, "courses": [{"number": 1, "nameI18n": {"en": "Starter", "de": "Vorspeise", "zh": ""}}, {"number": 2, "nameI18n": {"en": "Main Course", "de": "Hauptgang", "zh": "主菜"}}, {"number": 3, "nameI18n": {"en": "", "de": "Dessert", "zh": ""}}]}, "partySize": 2, "participantOrderingBlockedUntil": "2024-06-13T11:25:00", "orderActions": [{"titleI18n": {"de": "Test", "en": "Test", "zh": "Test"}, "contentI18n": {"de": "Testing content", "en": "Testing content", "zh": "Testing content"}}], "preparationTime": "1m", "groups": [{"header": {"label": "Vorspeise"}, "items": [{"id": "666ab07cde712a7b4f8205e0", "creationTime": "2024-06-13T08:40:28.832+00:00", "modificationTime": "2024-06-13T09:21:29.940+00:00", "name": "Mittagsbuffet", "nameI18n": {"de": "Mittagsbuffet ", "en": "Lunch buffet"}, "description": "Montag - Samstag", "descriptionI18n": {"de": "Montag - Samstag", "en": "Monday to Saturday"}, "internalNameI18n": {}, "numeration": "", "unitPrice": 18.9, "category": "DISH", "volume": "", "menuId": "64c3a30802dd3403c0c279e4", "code": "8c69a902-fa8e-41a1-bb14-8685eea84f5c", "userId": "<EMAIL>", "accountId": "01G632X5RHD4BG76WY9RA03B38", "restaurantId": "62b1b639230d5d186d059699", "printerCategory": "KITCHEN", "offline": true, "orderId": "666ab06dde712a7b4f8205dd", "qtd": 1, "status": "CONFIRMED", "taxCategory": "NORMAL", "taxRate": 19, "options": [{"id": "660f0c5f3e1e2563d9e8e810", "creationTime": "2024-04-04T20:23:59.674+00:00", "modificationTime": "2024-04-04T20:23:59.674+00:00", "restaurantId": "62b1b639230d5d186d059699", "name": "<PERSON>/<PERSON><PERSON><PERSON>el", "nameI18n": {"de": "<PERSON>/<PERSON><PERSON><PERSON>el", "en": "Still/Fizzy"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "qtd": 1, "rounds": 0, "items": [{"id": "660f0c9e3e1e2563d9e8e811", "creationTime": "2024-04-04T20:25:02.224+00:00", "modificationTime": "2024-04-04T20:25:02.224+00:00", "restaurantId": "62b1b639230d5d186d059699", "optionId": "660f0c5f3e1e2563d9e8e810", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameI18n": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "Fizzy"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "numeration": "", "thumbnailUrl": "", "unitPrice": 2, "qtd": 1, "category": "BEVERAGE", "dineTaxCategory": "NORMAL", "takeawayTaxCategory": "REDUCED", "taxCategory": "NORMAL", "taxRate": 19, "min": 1, "max": 1, "remarkAnnotations": [], "total": 2}], "printerIds": ["62b35cffebdf8b045e36a738"]}], "extras": [{"id": "660eb2e39337e63ec865fa0b", "creationTime": "2024-04-04T14:02:11.251+00:00", "modificationTime": "2024-04-04T14:02:11.251+00:00", "restaurantId": "62b1b639230d5d186d059699", "name": "Soßen", "nameI18n": {"de": "Soßen", "en": "<PERSON><PERSON><PERSON>"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "max": 3, "rounds": 0, "printerIds": [], "items": [{"id": "660eb3749337e63ec865fa0f", "creationTime": "2024-04-04T14:04:36.500+00:00", "modificationTime": "2024-04-04T14:04:36.500+00:00", "restaurantId": "62b1b639230d5d186d059699", "extraId": "660eb2e39337e63ec865fa0b", "name": "<PERSON><PERSON><PERSON>", "nameI18n": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>"}, "internalNameI18n": {}, "descriptionI18n": {}, "thumbnailUrl": "", "restricted": false, "disabled": false, "hidden": false, "numeration": "", "unitPrice": 1, "qtd": 3, "category": "DISH", "dineTaxCategory": "NORMAL", "takeawayTaxCategory": "REDUCED", "taxCategory": "NORMAL", "taxRate": 19, "max": 20, "remarkAnnotations": [], "total": 3}]}], "ongoing": false, "nested": false, "aggregatedQtd": 0, "ongoingExtrasQuantityMax": 99, "course": {"number": 1, "nameI18n": {"en": "Starter", "de": "Vorspeise", "zh": ""}}, "hasHighQuantitySelector": false, "metadata": {"consumptions": [{"categoryId": "01HZ0BESS3Z30WZX564CBJHET5", "name": "Mittagsbuffet", "unit": "PC", "amount": 1}]}, "kitchenInfo": {"sentToKitchenAt": "2024-06-13T09:20:00Z", "preparationStartedAt": "2024-06-13T09:21:00Z", "status": "PREPARING", "preparationTime": "1m", "timingStatus": "ON_TIME"}, "additionsTotal": 5, "baseTotal": 18.9, "optionItems": [{"id": "660f0c9e3e1e2563d9e8e811", "creationTime": "2024-04-04T20:25:02.224+00:00", "modificationTime": "2024-04-04T20:25:02.224+00:00", "restaurantId": "62b1b639230d5d186d059699", "optionId": "660f0c5f3e1e2563d9e8e810", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameI18n": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "Fizzy"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "numeration": "", "thumbnailUrl": "", "unitPrice": 2, "qtd": 1, "category": "BEVERAGE", "dineTaxCategory": "NORMAL", "takeawayTaxCategory": "REDUCED", "taxCategory": "NORMAL", "taxRate": 19, "min": 1, "max": 1, "remarkAnnotations": [], "total": 2}], "extraItems": [{"id": "660eb3749337e63ec865fa0f", "creationTime": "2024-04-04T14:04:36.500+00:00", "modificationTime": "2024-04-04T14:04:36.500+00:00", "restaurantId": "62b1b639230d5d186d059699", "extraId": "660eb2e39337e63ec865fa0b", "name": "<PERSON><PERSON><PERSON>", "nameI18n": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>"}, "internalNameI18n": {}, "descriptionI18n": {}, "thumbnailUrl": "", "restricted": false, "disabled": false, "hidden": false, "numeration": "", "unitPrice": 1, "qtd": 3, "category": "DISH", "dineTaxCategory": "NORMAL", "takeawayTaxCategory": "REDUCED", "taxCategory": "NORMAL", "taxRate": 19, "max": 20, "remarkAnnotations": [], "total": 3}], "total": 23.9}]}, {"header": {"label": "Hauptgang"}, "items": [{"id": "666ab080de712a7b4f8205e1", "creationTime": "2024-06-13T08:40:32.971+00:00", "modificationTime": "2024-06-13T09:20:46.923+00:00", "name": "Mekatzer 0,33L", "nameI18n": {"de": "Mekatzer 0,33L"}, "description": "", "descriptionI18n": {"de": ""}, "internalNameI18n": {}, "numeration": "301", "unitPrice": 4, "category": "BEVERAGE", "volume": "0.33", "menuId": "64946d1402cb4201cbe5d459", "code": "f4c95fff-dfbb-4582-9021-371a7e3d5f5f", "userId": "<EMAIL>", "accountId": "01G632X5RHD4BG76WY9RA03B38", "restaurantId": "62b1b639230d5d186d059699", "printerCategory": "BAR", "offline": true, "orderId": "666ab06dde712a7b4f8205dd", "qtd": 1, "status": "CONFIRMED", "taxCategory": "NORMAL", "taxRate": 19, "ongoing": false, "nested": false, "aggregatedQtd": 0, "ongoingExtrasQuantityMax": 99, "course": {"number": 2, "nameI18n": {"en": "Main Course", "de": "Hauptgang", "zh": "主菜"}}, "hasHighQuantitySelector": false, "includedInPrice": false, "metadata": {"consumptions": [{"categoryId": "01HZ0BESYT7KHNCEDF0J08JMNM", "name": "Mekatzer 0,33L", "unit": "PC", "amount": 1}]}, "kitchenInfo": {"sentToKitchenAt": "2024-06-13T09:20:00Z", "status": "PENDING", "preparationTime": "1m", "timingStatus": "ON_TIME"}, "additionsTotal": 0, "baseTotal": 4, "optionItems": [], "extraItems": [], "total": 4}, {"id": "666ab081de712a7b4f8205e2", "creationTime": "2024-06-13T08:40:33.605+00:00", "modificationTime": "2024-06-13T09:20:46.926+00:00", "name": "Mekatzer 0,5L", "nameI18n": {"de": "Mekatzer 0,5L"}, "description": "", "descriptionI18n": {"de": ""}, "internalNameI18n": {}, "numeration": "302", "unitPrice": 5, "category": "BEVERAGE", "volume": "0.5", "menuId": "64946d1402cb4201cbe5d459", "code": "dd44158c-f07d-45b4-89d9-b4989c26dc52", "userId": "<EMAIL>", "accountId": "01G632X5RHD4BG76WY9RA03B38", "restaurantId": "62b1b639230d5d186d059699", "printerCategory": "BAR", "offline": true, "orderId": "666ab06dde712a7b4f8205dd", "qtd": 1, "status": "CONFIRMED", "taxCategory": "NORMAL", "taxRate": 19, "ongoing": false, "nested": false, "aggregatedQtd": 0, "ongoingExtrasQuantityMax": 99, "course": {"number": 2, "nameI18n": {"en": "Main Course", "de": "Hauptgang", "zh": "主菜"}}, "hasHighQuantitySelector": false, "includedInPrice": false, "metadata": {"consumptions": [{"categoryId": "01HZ0BESYZKTG85ZYE6DSARNMW", "name": "Mekatzer 0,5L", "unit": "PC", "amount": 1}]}, "kitchenInfo": {"sentToKitchenAt": "2024-06-13T09:20:00Z", "status": "SERVED", "preparationTime": "1m", "timingStatus": "ON_TIME"}, "additionsTotal": 0, "baseTotal": 5, "optionItems": [], "extraItems": [], "total": 5}]}, {"header": {"label": "Dessert"}, "items": [{"id": "666ab06ede712a7b4f8205df", "creationTime": "2024-06-13T08:40:14.126+00:00", "modificationTime": "2024-06-13T09:20:46.904+00:00", "name": "Buddah Bowl", "nameI18n": {"de": "Buddah Bowl"}, "description": "spinach, carrots, corn, edamame, spring onions", "descriptionI18n": {"de": "spinach, carrots, corn, edamame, spring onions"}, "internalNameI18n": {}, "numeration": "", "unitPrice": 9, "category": "DISH", "volume": "", "menuId": "660fa2543e1e2563d9e8e85a", "code": "0d4d0147-0322-485e-9853-3ff5c4518f60", "userId": "<EMAIL>", "accountId": "01G632X5RHD4BG76WY9RA03B38", "restaurantId": "62b1b639230d5d186d059699", "printerCategory": "KITCHEN", "offline": true, "orderId": "666ab06dde712a7b4f8205dd", "qtd": 1, "status": "CONFIRMED", "taxCategory": "NORMAL", "taxRate": 19, "options": [{"id": "660f113c3e1e2563d9e8e816", "creationTime": "2024-04-04T20:44:44.200+00:00", "modificationTime": "2024-04-04T20:44:44.200+00:00", "restaurantId": "62b1b639230d5d186d059699", "name": "<PERSON><PERSON>", "nameI18n": {"de": "<PERSON><PERSON>"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "qtd": 1, "rounds": 0, "items": [{"id": "660f11663e1e2563d9e8e817", "creationTime": "2024-04-04T20:45:26.828+00:00", "modificationTime": "2024-04-04T20:46:19.062+00:00", "restaurantId": "62b1b639230d5d186d059699", "optionId": "660f113c3e1e2563d9e8e816", "name": "<PERSON><PERSON>", "nameI18n": {"de": "<PERSON><PERSON>", "en": "Chicken"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "numeration": "", "thumbnailUrl": "", "unitPrice": 4, "qtd": 1, "category": "DISH", "dineTaxCategory": "NORMAL", "takeawayTaxCategory": "REDUCED", "taxCategory": "NORMAL", "taxRate": 19, "min": 1, "max": 1, "remarkAnnotations": [], "total": 4}], "printerIds": ["62b35d1aebdf8b045e36a739"]}, {"id": "660fa2e93e1e2563d9e8e85c", "creationTime": "2024-04-05T07:06:17.041+00:00", "modificationTime": "2024-04-05T07:06:17.041+00:00", "restaurantId": "62b1b639230d5d186d059699", "name": "Bowl-bases", "nameI18n": {"de": "Bowl-bases"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "qtd": 1, "rounds": 0, "items": [{"id": "660fa30a3e1e2563d9e8e85d", "creationTime": "2024-04-05T07:06:50.995+00:00", "modificationTime": "2024-04-05T07:06:50.995+00:00", "restaurantId": "62b1b639230d5d186d059699", "optionId": "660fa2e93e1e2563d9e8e85c", "name": "<PERSON><PERSON><PERSON><PERSON> reis", "nameI18n": {"de": "<PERSON><PERSON><PERSON><PERSON> reis"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "numeration": "", "thumbnailUrl": "", "unitPrice": 2, "qtd": 1, "category": "DISH", "dineTaxCategory": "NORMAL", "takeawayTaxCategory": "REDUCED", "taxCategory": "NORMAL", "taxRate": 19, "min": 1, "max": 1, "remarkAnnotations": [], "total": 2}], "printerIds": ["62b35d1aebdf8b045e36a739"]}], "ongoing": false, "nested": false, "aggregatedQtd": 0, "course": {"number": 3, "nameI18n": {"en": "", "de": "Dessert", "zh": ""}}, "hasHighQuantitySelector": false, "metadata": {"consumptions": [{"categoryId": "01HZ0BESY9BQC79CN2P3GD8PWR", "name": "Buddah Bowl", "unit": "PC", "amount": 1}]}, "kitchenInfo": {"sentToKitchenAt": "2024-06-13T09:20:00Z", "status": "PENDING", "preparationTime": "1m", "timingStatus": "ON_TIME"}, "additionsTotal": 6, "baseTotal": 9, "optionItems": [{"id": "660f11663e1e2563d9e8e817", "creationTime": "2024-04-04T20:45:26.828+00:00", "modificationTime": "2024-04-04T20:46:19.062+00:00", "restaurantId": "62b1b639230d5d186d059699", "optionId": "660f113c3e1e2563d9e8e816", "name": "<PERSON><PERSON>", "nameI18n": {"de": "<PERSON><PERSON>", "en": "Chicken"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "numeration": "", "thumbnailUrl": "", "unitPrice": 4, "qtd": 1, "category": "DISH", "dineTaxCategory": "NORMAL", "takeawayTaxCategory": "REDUCED", "taxCategory": "NORMAL", "taxRate": 19, "min": 1, "max": 1, "remarkAnnotations": [], "total": 4}, {"id": "660fa30a3e1e2563d9e8e85d", "creationTime": "2024-04-05T07:06:50.995+00:00", "modificationTime": "2024-04-05T07:06:50.995+00:00", "restaurantId": "62b1b639230d5d186d059699", "optionId": "660fa2e93e1e2563d9e8e85c", "name": "<PERSON><PERSON><PERSON><PERSON> reis", "nameI18n": {"de": "<PERSON><PERSON><PERSON><PERSON> reis"}, "internalNameI18n": {}, "descriptionI18n": {}, "restricted": false, "disabled": false, "hidden": false, "numeration": "", "thumbnailUrl": "", "unitPrice": 2, "qtd": 1, "category": "DISH", "dineTaxCategory": "NORMAL", "takeawayTaxCategory": "REDUCED", "taxCategory": "NORMAL", "taxRate": 19, "min": 1, "max": 1, "remarkAnnotations": [], "total": 2}], "extraItems": [], "total": 15}]}], "cancelledAndRemovedItemsTotal": 0, "confirmedItemsTotal": 0, "itemsTotal": 0, "totalDue": 0, "total": 0}]}