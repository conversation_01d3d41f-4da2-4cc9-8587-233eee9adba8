import React, { useEffect, useState } from "react";
import { withTranslation } from "../../../../../i18n";
import Modal from "../../../_popup/Modal";
import shadows from "../../../../../styles/shadows";
import palette from "../../../../../styles/palette";
import typography from "../../../../../styles/typography";
import Typography from "@material-ui/core/Typography";
import ModalBar from "../../../_navigation/ModalBar";
import Select from "@material-ui/core/Select";
import {
  ArrowRight20NoBackground,
  ChevronDown20new,
  KitchenMonitorItemStatusCompleted,
  KitchenMonitorItemStatusNew,
  KitchenMonitorItemStatusOnGoing, KitchenMonitorItemStatusServed,
  PlusIcon16,
  TrashIcon20White
} from "../../../../utils/icons";
import { MenuProps } from "../../../../utils/const";
import MenuItem from "@material-ui/core/MenuItem";
import useStyles from "./styles"
import { getKitchenMonitorOrders, resolvePrinters, updateKitchenMonitor } from "../../../../api";
import { useSelector } from "react-redux";
import { accountSelectors, restaurantSelectors } from "../../../../../redux/selectors";
import { useRouter } from "next/router";
import Field from "../../../form/Field";
import isEmpty from "../../../../utils/isEmpty";
import Chip from "@material-ui/core/Chip";
import FieldWithTranslation from "../../../form/FieldWithTranslation";
import Switch from "../../../_toggles/Switch";
import { ButtonBase } from "@material-ui/core";
import Checkbox from "../../../_toggles/Checkbox";

const languageOptions = [
  {
    value: "de",
    label: "german"
  },
  {
    value: "en",
    label: "english"
  },
  {
    value: "zh",
    label: "chinese"
  }
];

const defaultData = {
  cardSplitItemsSize: 7,
  categories: ["DISH"],
  id: "",
  name: "",
  showSummary: false,
  showWaiterName: false,
  displayOptionsExtrasMultiplier: false,
  displayCustomItemPrice: false,
  enableSingleClickBulkActions: false,
  printerId: "",
  enableCancelledItemPrintout: false,
  displayNumberSelector: false,
  advanceSingleItemPerClick: false,
  menuLanguage: "de",
  statusFlow: {
    statuses: [
      {
        status: "PENDING",
        skip: false,
        automaticPrint: false
      },
      {
        status: "PREPARING",
        skip: false,
        automaticPrint: false
      },
      {
        status: "READY",
        skip: false,
        automaticPrint: false
      },
      {
        status: "SERVED",
        skip: false,
        automaticPrint: false
      },
    ]
  },
  summaryGroups: [
    {
      labelI18n: { de: "", en: "", zh: "" },
      statuses: []
    },
    {
      labelI18n: { de: "", en: "", zh: "" },
      statuses: []
    }
  ],
  zoomLevel: 1
};

const itemNumberOptions = [
  {value: 5},
  {value: 6},
  {value: 7},
  {value: 8},
  {value: 9},
  {value: 10},
]

const categoriesOptions = [
  {
    value: "DISH",
    i18nKey: "menu-editor-form-category-field-option-dish"
  },
  {
    value: "BEVERAGE",
    i18nKey: "menu-editor-form-category-field-option-beverage"
  }
];

const itemStatusOptions = [
  {
    value: "PENDING",
    i18nKey: "payouts-table-status-PENDING"
  },
  {
    value: "PREPARING",
    i18nKey: "status-preparing"
  },
  {
    value: "READY",
    i18nKey: "status-ready"
  },
  /*{
    value: "CANCELLED",
    i18nKey: "cancelled"
  }*/
]

const orderStatusSteps = [
  {
    value: "PENDING",
    i18nKey: "payouts-table-status-PENDING",
    icon: <KitchenMonitorItemStatusNew width={24} height={24} color={"#242423"} />
  },
  {
    value: "PREPARING",
    i18nKey: "status-preparing",
    icon: <KitchenMonitorItemStatusOnGoing width={24} height={24} color={"#4F93DB"} fill={"#4F93DB"} />
  },
  {
    value: "READY",
    i18nKey: "status-ready",
    icon: <KitchenMonitorItemStatusCompleted width={24} height={24} fill={"#428271"} color={"#ffffff"} />
  },
  {
    value: "SERVED",
    i18nKey: "served",
    icon:  <KitchenMonitorItemStatusServed width={24} height={24} color={"#BDBDBD"} />
  },
]

const skippableSteps = [
  {
    value: "PREPARING",
    i18nKey: "status-preparing",
    icon: <KitchenMonitorItemStatusOnGoing width={24} height={24} color={"#4F93DB"} fill={"#4F93DB"} />
  },
  {
    value: "READY",
    i18nKey: "status-ready",
    icon: <KitchenMonitorItemStatusCompleted width={24} height={24} fill={"#428271"} color={"#ffffff"} />
  },
]

const KitchenMonitorConfigurationModal = ({ t, open, onClose, triggerReFetchData }) => {
  const classes = useStyles();
  const router = useRouter();
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const { monitorId } = router.query;

  const account = useSelector(accountSelectors.getAccountMemo);
  const { isManaged = false } = (account || {})
  
  const [configForm, setConfigForm] = useState(defaultData)
  
  const [selectedStatusSteps, setSelectedStatusSteps] = useState([]);
  
  const getConfig = () => {
    getKitchenMonitorOrders(restaurantId, monitorId)
      .then(({ data }) => {
        setConfigForm(data.monitor ?? defaultData);
      })
      .catch(console.error);
  };
  
  const submitConfig = () => {
    updateKitchenMonitor(restaurantId, configForm)
      .then(() => {
        getConfig()
        triggerReFetchData()
        onClose()
      })
      .catch(console.error)
  };
  
  const [printers, setPrinters] = useState([]);
  
  useEffect(() => {
    resolvePrinters(restaurantId).then(({ data }) => setPrinters(data)).catch(() => {
    })
  }, [restaurantId]);
  
  const onChange = (e) => {
    const updatedConfigForm = { ...configForm, [e.target.name]: e.target.value}
    setConfigForm(updatedConfigForm)
  };
  
  useEffect(() => {
    getConfig()
  },[]);
  
  useEffect(() => {
    if (configForm.statusFlow && !isEmpty(configForm.statusFlow)) {
      const stepsFromConfig = configForm.statusFlow.statuses
        .filter((statusObj) => !statusObj.skip)
        .map((statusObj) => statusObj.status);
      
      setSelectedStatusSteps(stepsFromConfig);
    } else {
      const defaultSteps = ["PENDING", "PREPARING", "READY", "SERVED"];
      setSelectedStatusSteps(defaultSteps);
      const updatedConfig = { ...configForm, statusFlow: { statuses : [ ...defaultData.statusFlow.statuses ] }};
      setConfigForm(updatedConfig);
    }
  }, [configForm]);
  
  
  const updateCategories = (e) => {
    let val = e.target.value;
    val = [...new Set(val ?? [])]
    
    const updatedConfigForm = { ... configForm, categories: val}
    setConfigForm(updatedConfigForm)
  };
  
  const addSummaryGroup = () => {
    const newGroup = {
      labelI18n: { de: "", en: "", zh: "" },
      statuses: []
    };
    const updatedConfig =  { ... configForm, summaryGroups: [...configForm.summaryGroups, newGroup] }
    setConfigForm(updatedConfig)
    updateKitchenMonitor(restaurantId, updatedConfig )
      .then(getConfig)
      .catch(console.error)
  };
  
  const deleteSummaryGroup = (groupToDelete) => {
    const isCompleteMatch = (group, groupToDelete) => {
      return Object.keys(groupToDelete).every(key => group[key] === groupToDelete[key]);
    };
    const filteredGroups = configForm.summaryGroups.filter(g => !isCompleteMatch(g, groupToDelete));
    const updatedConfiguration =  { ... configForm, summaryGroups: filteredGroups }
    setConfigForm(updatedConfiguration)
  };
  
  const isValid = !isEmpty(configForm.name)
  
  const [summaryGroupTranslated, setSummaryGroupTranslated] = useState([]);
  
  const onGroupRename = (index) => {
    const isChecked = summaryGroupTranslated.includes(index)
    if(!isChecked){
      setSummaryGroupTranslated([...summaryGroupTranslated, index])
    }
    else {
      const filteredSummaryGroup = summaryGroupTranslated.filter(i => index !== i)
      setSummaryGroupTranslated(filteredSummaryGroup)
    }
  };
  
  const onLabelI18nChange = (e, groupIdentifier) => {
    const labelI18n = { ...configForm.summaryGroups[groupIdentifier].labelI18n, [e.target.name]: e.target.value };
    const updatedConfigFrom = { ...configForm }
    updatedConfigFrom.summaryGroups[groupIdentifier].labelI18n = labelI18n
    setConfigForm(updatedConfigFrom)
  };
  
  const updateSummaryStatuses = (e, groupIdentifier) => {
    let val = e.target.value;
    val = [...new Set(val ?? [])]
    
    const updatedConfigFrom = { ...configForm }
    updatedConfigFrom.summaryGroups[groupIdentifier].statuses = val
    setConfigForm(updatedConfigFrom)
  };
  
  const handleToggle = (stateKey, value) => {
    const updatedConfiguration = { ...configForm, [stateKey]: value };
    setConfigForm(updatedConfiguration)
  };
  
  const addSelectedStep = (status) => {
    let updatedStatusFlow = { statuses:  [ ...configForm.statusFlow.statuses ]}
    const statusIndex = updatedStatusFlow.statuses.findIndex((s) => s.status === status);
    
    if (!selectedStatusSteps.includes(status)) {
      const updatedSteps = [...selectedStatusSteps];
      updatedSteps.splice(statusIndex, 0, status);
      setSelectedStatusSteps(updatedSteps)
      
      const updatedStatuses = [...configForm.statusFlow.statuses];
      updatedStatuses[statusIndex] = { status: status, skip: false, automaticPrint: false };
      
      updatedStatusFlow = { ...configForm.statusFlow, statuses: updatedStatuses, };
      
      setConfigForm({...configForm, statusFlow: updatedStatusFlow})
    } else {
      const filteredSteps = selectedStatusSteps.filter(i => i !== status)
      setSelectedStatusSteps(filteredSteps)
      
      const updatedStatuses = [...configForm.statusFlow.statuses];
      if (statusIndex !== -1) {
        updatedStatuses[statusIndex] = { ...updatedStatuses[statusIndex], skip: true, automaticPrint: false };
      }
      
      updatedStatusFlow = { ...configForm.statusFlow, statuses: updatedStatuses, };
      
      setConfigForm({...configForm, statusFlow: updatedStatusFlow})
    }
  };
  
  const onPrintingChange = (status) => {
    const updatedStatuses = configForm.statusFlow.statuses.map((step) => {
      if (step.status === status) {
        return { ...step, automaticPrint: !step.automaticPrint };
      }
      return step;
    })
    
    const updatedStatusFlow = {
      ...configForm.statusFlow,
      statuses: updatedStatuses,
    };
    setConfigForm({ ...configForm, statusFlow: updatedStatusFlow });
  };
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      fullWidth={true}
      PaperProps={{
        style: {
          background: "transparent",
          ...shadows.large
        }
      }}
    >
      <ModalBar onClose={onClose} onDone={isValid ? submitConfig : null} title={t("settings-tabs-configuration")}/>
      <div
        style={{
          height: "100%",
          overflow: "auto",
          background: palette.grayscale["200"],
          borderRadius: "0px 0px 20px 20px",
          padding: 20,
        }}
      >
        {/* general settings */}
        <div style={{
          height: "100%",
          overflow: "auto",
          margin: "0 auto",
          background: palette.grayscale["200"],
          //maxWidth: 500,
        }}>
          {/*<div style={{ marginBottom: 24 }}>
            <ColourLegend />
          </div>*/}
          <div>
            <Typography style={{ ...typography.body.medium, marginBottom: 24 }}>{t("kitchen-monitor-settings")}</Typography>
          </div>
          {/* name */}
          <div style={{ marginBottom: 24 }}>
            <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("restaurant-editor-name-field-label")}</Typography>
            <Field
              name={'name'}
              value={configForm.name}
              onChange={onChange}
            />
          </div>
          
          {/*cardSplitItemsSize */}
          <div>
            <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("number-of-items-breakpoint-for-kitchen-monitor-field-lable")}</Typography>
            <div className={classes.chipSelectWrapper}>
              <Select
                variant={"outlined"}
                style={{ borderRadius: 12, backgroundColor: palette.grayscale["100"], height: 44 }}
                value={configForm.cardSplitItemsSize}
                name="cardSplitItemsSize"
                IconComponent={ChevronDown20new}
                onChange={onChange}
                MenuProps={MenuProps}
              >
                {itemNumberOptions.map(item => (
                  <MenuItem key={item.value} value={item.value} >
                    {item.value}
                  </MenuItem>
                ))}
              </Select>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 8 }}>{t("set-number-of-items-breakpoint-for-kitchen-monitor")}</Typography>
          </div>

          {/* Menu Language */}
          <div style={{ marginBottom: 24, marginTop: 24 }}>
            <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("menu-language")}</Typography>
            <div className={classes.chipSelectWrapper}>
              <Select
                variant={"outlined"}
                style={{ borderRadius: 12, backgroundColor: palette.grayscale["100"], height: 44 }}
                value={configForm.menuLanguage || "de"}
                name="menuLanguage"
                IconComponent={ChevronDown20new}
                onChange={onChange}
                MenuProps={MenuProps}
              >
                {languageOptions.map(item => (
                  <MenuItem key={item.value} value={item.value}>
                    <Typography style={{ ...typography.body.regular }}>{t(item.label)}</Typography>
                  </MenuItem>
                ))}
              </Select>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 8 }}>{t("select-which-language-to-use-for-menu-item-names")}</Typography>
          </div>
          

          {/* categories */}
          <div style={{ marginTop: 24 }} className={classes.chipSelectWrapper}>
            <Select
              value={configForm.categories || []}
              onChange={(e) => updateCategories(e)}
              multiple
              MenuProps={MenuProps}
              IconComponent={ChevronDown20new}
              input={<Field id="select-chip" label={t("category")} select />}
              renderValue={(selected) => (
                <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                  {selected.map((value) => {
                    const category = categoriesOptions.find(c => c.value === value) || {};
                    return (
                      <Chip
                        key={value}
                        label={t(category.i18nKey)}
                        style={{ margin: 2 }}
                      />
                    )
                  })}
                </div>
              )}
            >
              {categoriesOptions.map(category => (
                <MenuItem value={category.value} key={category.value}>
                  <div className={classes.tag}>
                    <Typography>{t(category.i18nKey)}</Typography>
                  </div>
                </MenuItem>
              ))}
            </Select>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 8 }}>{t("select-categories-shown-on-kitchen-monitor")}</Typography>
          </div>
          <div style={{ marginTop: 16, display: "flex", flexDirection: "column", gap: 16 }}>
            <Typography style={{ ...typography.body.medium }}>{t("steps-for-order-status-progression")}</Typography>
            <div style={{ display: "flex", alignItems: "center", width: "100%", justifyContent: "space-between" }}>
              {orderStatusSteps.map((step, index) => {
                const { value, i18nKey, icon } = step;
                const selected = selectedStatusSteps.includes(value);
                const isLast = index === orderStatusSteps.length - 1;
                
                return (
                  <div style={{  display: selected ? "contents" : "none", }}>
                    <div key={value} style={{
                      opacity: selected ? 1 : 0.8,
                      backgroundColor: palette.grayscale["100"],
                      borderRadius: 10,
                      padding: 12, ...shadows.base,
                      border: `1px solid ${palette.grayscale["350"]}`,
                      display: "flex",
                      alignItems: "center",
                      gap: 6
                    }}>
                      {icon}
                      <Typography style={{ ...typography.body.medium }}>{t(i18nKey)}</Typography>
                    </div>
                    {!isLast && (
                      <ArrowRight20NoBackground />
                    )}
                  </div>
                );
              })}
            </div>
            <div>
              <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("step-skipping")}</Typography>
              <Typography style={{ ...typography.body.regular, marginBottom: 16 }}>{t("decide-which-step-you-would-like-to-skip")}</Typography>
              <div style={{ display: "flex", alignItems: "center", width: "100%", gap: 16, flex: 1 }}>
                {skippableSteps.map((step) => {
                  const stepIsSkipped = !selectedStatusSteps.includes(step.value);
                  
                  return (
                    <div style={{ display: "flex", alignItems: "center", gap: 12, flex: 1 }}>
                      <div style={{
                        marginBottom: 8,
                        display: "flex",
                        padding: 12,
                        width: "100%",
                        backgroundColor: palette.grayscale["100"],
                        borderRadius: 12, ...shadows.base
                      }}>
                        <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "flex-start", width: "100%" }}
                                    onClick={() => addSelectedStep(step.value)} disableRipple disableTouchRipple>
                          <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-start" }}>
                            <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 8, width: "100%" }}>
                              <Typography style={{ ...typography.body.medium }}>
                                {t("skipp-this-step")}: {t(step.i18nKey)}
                              </Typography>
                              <Checkbox checked={stepIsSkipped} />
                            </div>
                            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], textAlign: "left" }}>
                              {t("skipp-this-step-description")}
                            </Typography>
                          </div>
                        </ButtonBase>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
            <div>
              <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("print-settings")}</Typography>
              <Typography style={{ ...typography.body.regular, marginBottom: 16 }}>{t("control-your-settings-regarding-printing-kitchen-monitor")}</Typography>
              <div className={classes.switchSetting} style={{ marginTop: 24, marginBottom: 24 }}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("print-cancelled-items-kitchen-monitor-toggle")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("print-cancelled-items-kitchen-monitor-toggle-description")}</Typography>
                  </div>
                  <Switch checked={configForm.enableCancelledItemPrintout ?? false}
                          onClick={() => handleToggle("enableCancelledItemPrintout", !configForm.enableCancelledItemPrintout)}/>
                </div>
              </div>
              <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("bon-printing")}</Typography>
              <Typography style={{ ...typography.body.regular, marginBottom: 16 }}>{t("bon-printing-description")}</Typography>
              <div style={{ marginTop: 16, marginBottom: 16 }}>
                <div className={classes.selectWrapper} style={{ width: "100%" }}>
                  <Typography style={{
                    ...typography.body.medium,
                    marginBottom: 8
                  }}>{t("menu-editor-form-printer-field-label")}</Typography>
                  <Select
                    id={"select"}
                    variant={"outlined"}
                    name="printerId"
                    value={configForm.printerId}
                    IconComponent={ChevronDown20new}
                    MenuProps={MenuProps}
                    style={{
                      borderRadius: 12,
                      backgroundColor: palette.grayscale["100"],
                      height: 44
                    }}
                    onChange={onChange}
                  >
                    <MenuItem
                      key={""}
                      value={""}
                      style={{ minHeight: 32 }}
                    >
                      <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("no-printers-selected")}</Typography>
                    </MenuItem>
                    {printers.filter(printer => printer.printerCategory === "MONITOR").map((printer) => (
                      <MenuItem
                        key={printer.id}
                        value={printer.id}
                      >
                        <Typography style={{ ...typography.body.regular }}>{printer.label}</Typography>
                      </MenuItem>
                    ))}
                  </Select>
                  <Typography style={{ marginTop: 8, ...typography.body.regular, color: palette.grayscale["600"] }}>{t("kitchen-monitor-printing-hint")}</Typography>
                </div>
              </div>
              {orderStatusSteps.filter((step) => selectedStatusSteps.includes(step.value) && step.value !== "PENDING")
                .map((step) => {
                  const matchingStep = configForm.statusFlow.statuses.find((s) => s.status === step.value);
                  const isPrinterSelected = matchingStep ? matchingStep.automaticPrint : false;
                  
                  return (
                    <div>
                      
                      <div style={{ display: "flex", alignItems: "center", gap: 24 }}>
                        <div style={{ minWidth: 160 }}>
                          <Typography style={{ ...typography.body.medium }}>
                            {t("common-step")}: {t(step.i18nKey)}</Typography>
                        </div>
                        <div style={{
                          marginBottom: 12,
                          display: "flex",
                          padding: 12,
                          minWidth: 200,
                          backgroundColor: palette.grayscale["100"],
                          borderRadius: 12, ...shadows.base,
                          width: "100%",
                        }}>
                          <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "flex-end" }}
                                      onClick={() => onPrintingChange(step.value)} disableRipple disableTouchRipple>
                            <Checkbox  checked={isPrinterSelected} />
                            <Typography style={{ ...typography.body.regular, marginLeft: 6 }}>
                              {t("enabled-printing-for-this-step")}
                            </Typography>
                          </ButtonBase>
                        </div>
                      </div>
                    
                    </div>
                  );
                })}
              {/* displayCustomItemPrice  */}
              <div className={classes.switchSetting} style={{ marginTop: 24, marginBottom: 24 }}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("display-custom-item-price")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("display-custom-item-price-description")}</Typography>
                  </div>
                  <Switch checked={configForm.displayCustomItemPrice ?? false}
                          onClick={() => handleToggle("displayCustomItemPrice", !configForm.displayCustomItemPrice)} />
                </div>
              </div>
              {/* displayOptionsExtrasMultiplier  */}
              <div className={classes.switchSetting} style={{ marginTop: 24, marginBottom: 24 }}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("display-options-extras-multiplier")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("display-options-extras-multiplier-description")}</Typography>
                  </div>
                  <Switch checked={configForm.displayOptionsExtrasMultiplier ?? false}
                          onClick={() => handleToggle("displayOptionsExtrasMultiplier", !configForm.displayOptionsExtrasMultiplier)} />
                </div>
              </div>
              {/* enableSingleClickBulkActions */}
              <div className={classes.switchSetting} style={{ marginTop: 24, marginBottom: 24 }}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("enable-single-click-bulk-actions-toggle")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("enable-single-click-bulk-actions-toggle-description")}</Typography>
                  </div>
                  <Switch checked={configForm.enableSingleClickBulkActions ?? false}
                          onClick={() => handleToggle("enableSingleClickBulkActions", !configForm.enableSingleClickBulkActions)} />
                </div>
              </div>
              {/* display number selector */}
              <div className={classes.switchSetting} style={{ marginTop: 24, marginBottom: 24 }}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("display-number-selector-toggle")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("display-number-selector-toggle-description")}</Typography>
                  </div>
                  <Switch checked={configForm.displayNumberSelector ?? false} disabled={!isManaged}
                          onClick={() => handleToggle("displayNumberSelector", !configForm.displayNumberSelector)} />
                </div>
              </div>
              {/* send only one item per click */}
              <div className={classes.switchSetting} style={{ marginTop: 24, marginBottom: 24 }}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("advance-one-item-per-click-toggle")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("advance-one-item-per-click-toggle-description")}</Typography>
                  </div>
                  <Switch checked={configForm.advanceSingleItemPerClick ?? false} disabled={!isManaged}
                          onClick={() => handleToggle("advanceSingleItemPerClick", !configForm.advanceSingleItemPerClick)} />
                </div>
              </div>
            </div>
          
          </div>
        </div>
        {/* summary settings */}
        <div style={{
          height: "100%",
          overflow: "auto",
          margin: "0 auto",
          background: palette.grayscale["200"],
          //maxWidth: 500,
          marginTop: 24,
          paddingTop: 24,
          borderTop: `1px dashed ${palette.grayscale["350"]}`
        }}>
          <div style={{ marginBottom: 24 }}>
            <Typography style={{ ...typography.body.medium }}>{t("kitchen-monitor-settings-summary")}</Typography>
          </div>
          {/* summary toggle */}
          <div className={classes.switchSetting} style={{ marginTop: 24, marginBottom: 24 }}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("show-summary-toggle")}</Typography>
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.grayscale["600"]
                }}>{t("show-summary-toggle-description")}</Typography>
              </div>
              <Switch checked={configForm.showSummary ?? false}
                      onClick={() => handleToggle("showSummary", !configForm.showSummary)}/>
            </div>
          </div>
          {/* summary group names + statuses */}
          <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("summary-groups")}</Typography>
          <Typography style={{ ...typography.body.regular, marginBottom: 24 }}>{t("kitchen-monitor-settings-summary-description")}</Typography>
          {/* group 1 */}
          {Array.from({ length: configForm.summaryGroups.length }).map((_, index) => {
              
              return (
                <div style={{ marginBottom: 32, padding: 12, borderRadius: 10, backgroundColor: palette.grayscale.white }}>
                  <FieldWithTranslation
                    label={t(`kitchen-monitor-summary-group-title`, { number: index + 1 })}
                    placeholder={t("menu-editor-name-field-placeholder")}
                    checked={summaryGroupTranslated.includes(index)}
                    onCheckboxClick={() => onGroupRename(index)}
                    value={configForm.summaryGroups[index].labelI18n}
                    onChange={(e) => onLabelI18nChange(e, index)}
                  />
                  <div style={{ marginTop: 12 }} className={classes.chipSelectWrapper}>
                    <Select
                      value={configForm.summaryGroups[index].statuses || []}
                      onChange={(e) => updateSummaryStatuses(e, index)}
                      multiple
                      MenuProps={MenuProps}
                      IconComponent={ChevronDown20new}
                      input={<Field id="select-chip" label={t("order-approval-status-label")} select />}
                      renderValue={(selected) => (
                        <div style={{ display: "flex", flexWrap: "wrap" }}>
                          {selected.map((value) => {
                            const status = itemStatusOptions.find(s => s.value === value) || {};
                            return (
                              <Chip
                                key={value}
                                label={t(status.i18nKey)}
                                style={{ margin: 2 }}
                              />
                            );
                          })}
                        </div>
                      )}
                    >
                      {itemStatusOptions.filter(option => selectedStatusSteps.includes(option.value))
                        .map(category => (
                          <MenuItem value={category.value} key={category.value}>
                            <div className={classes.tag}>
                              <Typography>{t(category.i18nKey)}</Typography>
                            </div>
                          </MenuItem>
                        ))}
                    </Select>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"],
                      marginTop: 8
                    }}>{t("select-status-shown-in-this-group-kitchen-monitor")}</Typography>
                  </div>
                  <div style={{
                    marginBottom: 20,
                    background: palette.grayscale["250"],
                    borderRadius: 12,
                    padding: "16px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginTop: 12
                  }}>
                    <Typography
                      style={{ ...typography.body.regular, maxWidth: 290 }}>{t("delete-table-description")}</Typography>
                    <div>
                      <ButtonBase style={{ borderRadius: 12, padding: "6px 12px", background: palette.negative["500"] }}
                                  disableRipple disableTouchRipple
                                  onClick={() => deleteSummaryGroup(configForm.summaryGroups[index])}>
                        <TrashIcon20White />
                        <Typography
                          style={{ ...typography.body.medium, color: palette.grayscale["100"], marginLeft: 4 }}>
                          {t("delete-groups-btn-label")}
                        </Typography>
                      </ButtonBase>
                    </div>
                  </div>
                </div>
              )
            }
          )}
          <div style={{
            borderTop: `1px dashed ${palette.grayscale["350"]}`,
            padding: 12,
            display: "flex",
            justifyContent: "flex-end"
          }}>
            <ButtonBase
              style={{ border: `1px solid ${palette.grayscale.border}`, padding: "6px 8px", borderRadius: 12 }}
              onClick={addSummaryGroup}>
              <PlusIcon16 />
              <Typography style={{ ...typography.body.regular }}>{t("add-group")}</Typography>
            </ButtonBase>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(KitchenMonitorConfigurationModal);
