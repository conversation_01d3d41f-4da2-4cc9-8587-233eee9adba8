import React, {useEffect, useState} from "react";
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableBody from '@material-ui/core/TableBody';
import Table from '@material-ui/core/Table';
import {withTranslation} from '../../../../i18n';
import useStyles, { useMenuStyles } from "./styles";
import {
  createKitchenMonitor, deleteKitchenMonitor,
  getKitchenMonitors,
  updateKitchenMonitor
} from "../../../api";
import Button from '@material-ui/core/Button';
import { useDispatch, useSelector } from "react-redux";
import { accountSelectors, restaurantSelectors } from "../../../../redux/selectors";
import { noop, permissionIdentifiers } from "../../../utils/const";
import SecondaryBar from "../../_navigation/SecondaryBar";
import isEmpty from "../../../utils/isEmpty";
import EmptyScreen from "../../_placeholder/EmptyScreen";
import { NoReservation120 } from "../../../utils/icons";
import { appActions } from "../../../../redux/actions";
import { ButtonBase, Typography, useMediaQuery } from "@material-ui/core";
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";
import KitchenMonitorModal from "../../_popup/KitchenMonitorModal";
import MoreOptionsButton from "../../_buttons/MoreOptionsButton";
import CustomMenu from "../../_popup/CustomMenu";
import { Confirm } from "../../Confirmation";
import MenuItem from "@material-ui/core/MenuItem";
import { useRouter } from "next/router";
import { views } from "../../../utils/administrationRoutes";

const Dashboard = ({ t }) => {
  const router = useRouter();
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "")

  const isMobile = useMediaQuery('(max-width:500px)');
  
  const menuClasses = useMenuStyles();
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const canUpdateGeneralSettings = permissionIds.includes(permissionIdentifiers.CAN_UPDATE_GENERAL_SETTINGS.value)
  
  const [monitors, setMonitors] = useState(null);
  const loadedSize = (monitors || []).length
  const total = loadedSize
  const [monitor, setMonitor] = useState(null);

  const navigateToMonitor = (monitor) => {
    router.push(`${resolvedAsPath}?v=${views.KITCHEN_MONITORS_MANAGER}&monitorId=${monitor.id}`, undefined, { shallow: true }).then(() => {}).catch(() => {})
  }
  
  const [saving, setSaving] = useState(false);
  
  const [selectedMonitorMenu, setSelectedMonitorMenu] = useState({ anchor: null, monitor: null });
  
  const openMoreMenu = (e, monitor) => setSelectedMonitorMenu({ anchor: e.currentTarget, monitor: monitor })
  const closeMoreMenu = () => setSelectedMonitorMenu({ anchor: null, monitor: null });

  const fetchMonitors = () => {
    getKitchenMonitors(restaurantId).then(({ data }) => setMonitors(data ? data.items : null)).catch(noop);
  };

  useEffect(() => {
    fetchMonitors();
  }, []);

  const submitMonitor = (form) => {
    const call = form.id ? updateKitchenMonitor : createKitchenMonitor;
    if (form.id === "") {
      delete form.id;
    }
    call(restaurantId, form)
      .then(() => setMonitor(null))
      .then(() => {
        setSaving(false)
        if(form.id){
          dispatch(appActions.setNotification('kitchen-monitor-updated', "success"))
        }else{
          dispatch(appActions.setNotification('kitchen-monitor-created', "success"))
        }
        fetchMonitors();
      })
      .catch(() => {
        if(form.id){
          dispatch(appActions.setNotification('kitchen-monitor-update-error', "error"))
        }else{
          dispatch(appActions.setNotification('kitchen-monitor-creation-error', "error"))
        }
      })
      .finally(() => setMonitor(null));
  };
  
  const onDeleteMonitor = () => {
    if(selectedMonitorMenu && selectedMonitorMenu.monitor && selectedMonitorMenu.monitor.id){
      deleteKitchenMonitor(restaurantId, selectedMonitorMenu.monitor)
        .then(() => {
          fetchMonitors();
          dispatch(appActions.setNotification("kitchen-monitor-delete-success", "success"))
        })
        .catch(() => {
          dispatch(appActions.setNotification("kitchen-monitor-delete-error", "error"))
        });
    }
  };

  const onStartEditingMonitor = () => {
    if(selectedMonitorMenu && selectedMonitorMenu.monitor && selectedMonitorMenu.monitor.id){
      setMonitor(selectedMonitorMenu.monitor);
    }
  };
  
  const isMonitorSelected = id => monitor && monitor.id === id;
  
  if (!canUpdateGeneralSettings) {
    return null;
  }
  
  const startCreating = () => {
    setMonitor({ id: "" })
  };
  
  const getMobileList = () => {
    return (
      <>
        <div style={{ display: "flex", flexDirection: "column" }}>
          {monitors && monitors.map(it => {
            const { id, name, pairingCode } = it;
      
            return (
              <ButtonBase key={id}>
                <div style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  width: "100%",
                  borderBottom: `1px solid ${palette.grayscale.divider}`,
                  padding: 12
                }}>
                  <div style={{ display: "flex", flexDirection: "column", textAlign: "left", flex: 1 }}
                       onClick={() => setMonitor(it)}>
                    <Typography style={{ ...typography.body.medium }}>{name}</Typography>
                    <Typography style={{ ...typography.body.regular, marginTop: 2 }}>{pairingCode}</Typography>
                  </div>
                  <div>
                    <MoreOptionsButton onClick={(e) => openMoreMenu(e, it)} />
                  </div>
                </div>
              </ButtonBase>
            );
          })}
    
          <Typography style={{
            ...typography.body.regular,
            padding: 12,
            color: palette.grayscale["600"]
          }}>{`${loadedSize}${total > loadedSize ? `/${total}` : ""} ${t("ordering-monitors-loaded-label")}`}</Typography>
        </div>
        <CustomMenu
          anchorEl={selectedMonitorMenu.anchor}
          keepMounted
          open={Boolean(selectedMonitorMenu.anchor)}
          onClose={closeMoreMenu}
          classes={{
            paper: menuClasses.menu
          }}
        >
          <MenuItem classes={{ root: menuClasses.menuItem }} onClick={onStartEditingMonitor}>
            <div className={menuClasses.content}>
              <div className={menuClasses.left}>
                <Typography style={{ ...typography.body.regular }}>
                  {t("order-item-edit-btn")}
                </Typography>
              </div>
            </div>
          </MenuItem>
          <Confirm
            closeMenu={closeMoreMenu}
            title={t("delete-ordering-monitor-title")}
            body={(
              <Typography color="textSecondary" variant="body2">
                <br />
                {t("delete-table-description")}
                <br />
                {t("are-you-sure-description-to-delete-ordering-monitor")}
              </Typography>
            )}
          >
            {confirm => (
              <MenuItem classes={{ root: menuClasses.menuItem }} onClick={confirm(onDeleteMonitor)}>
                <div className={menuClasses.content}>
                  <div className={menuClasses.left}>
                    <Typography style={{ ...typography.body.regular, color: palette.negative["500"] }}>
                      {t("order-item-edit-btn")}
                    </Typography>
                  </div>
                </div>
              </MenuItem>
            )}
          </Confirm>
        </CustomMenu>
      </>
    )
  }

  const renderList = () => {
    return (
      <>
      <TableContainer>
        <Table stickyHeader aria-label="menu editor table">
          <TableHead>
            <TableRow>
              <TableCell>{t("name")}</TableCell>
              <TableCell />
              <TableCell />
            </TableRow>
          </TableHead>
          <TableBody>
            {monitors.map(r => {
              const { id, name } = r;

              return (
                <TableRow key={id} hover selected={isMonitorSelected(id)} className={classes.clickable}>
                  <TableCell align="left" onClick={() => navigateToMonitor(r)}>{name}</TableCell>
                  <TableCell align="left" onClick={() => navigateToMonitor(r)}></TableCell>
                  {/*<TableCell align="left" onClick={() => navigateToMonitor(id)}>{shortcutId}-{pairingCode}</TableCell>*/}
                  <TableCell align="left" onClick={(e) => openMoreMenu(e, r)}>
                    <MoreOptionsButton />
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <CustomMenu
        anchorEl={selectedMonitorMenu.anchor}
        keepMounted
        open={Boolean(selectedMonitorMenu.anchor)}
        onClose={closeMoreMenu}
        classes={{
          paper: menuClasses.menu
        }}
      >
        <MenuItem classes={{ root: menuClasses.menuItem }} onClick={onStartEditingMonitor}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography style={{ ...typography.body.regular }}>
                {t("order-item-edit-btn")}
              </Typography>
            </div>
          </div>
        </MenuItem>
        <Confirm
          closeMenu={closeMoreMenu}
          title={t("delete-ordering-monitor-title")}
          body={(
            <Typography color="textSecondary" variant="body2">
              <br />
              {t("delete-table-description")}
              <br />
              {t("are-you-sure-description-to-delete-ordering-monitor")}
            </Typography>
          )}
        >
          {confirm => (
            <MenuItem classes={{ root: menuClasses.menuItem }} onClick={confirm(onDeleteMonitor)}>
              <div className={menuClasses.content}>
                <div className={menuClasses.left}>
                  <Typography style={{ ...typography.body.regular, color: palette.negative["500"] }}>
                    {t('common-delete')}
                  </Typography>
                </div>
              </div>
            </MenuItem>
          )}
        </Confirm>
      </CustomMenu>
    </>
    )
  }
  
  const getContent = () => {
    if (isEmpty(monitors)) {
      return (
        <div className={classes.content}>
          <EmptyScreen
            icon={<NoReservation120 />}
            titleI18nKey="no-monitors"
            descriptionI18nKey="click-the-button-below-to-add-a-monitor"
            action={{ i18nKey: "new", onClick: startCreating }}
            tutorial={{
              url: "https://www.youtube.com/watch?v=NcRifDitRnX"
            }}
          />
        </div>
      )
    }
    
    if (isMobile) {
      return getMobileList()
    }
  
    return (
      <div className={classes.content}>
        {renderList()}
      </div>
    );
  }
  
  const getSecondaryBarActions = () => (
    <div style={{ display: "flex", alignItems: "center" }}>
      <Button className={classes.createBtn} onClick={startCreating} disableRipple>{t('new')}</Button>
    </div>
  )

  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        <SecondaryBar title={t('kitchen-monitors')} right={getSecondaryBarActions()} />
        {getContent()}
      </div>
      {monitor && (
        <KitchenMonitorModal open={monitor} onClose={() => setMonitor(null)} values={monitor} submit={submitMonitor} saving={saving} setSaving={setSaving} />
      )}
    </div>
  );
};

export default withTranslation("common")(Dashboard);
