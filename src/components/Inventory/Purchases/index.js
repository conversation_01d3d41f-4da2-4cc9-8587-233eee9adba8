import React, { memo, useEffect, useState } from "react";
import {withTranslation} from '../../../../i18n';
import useStyles, { useBoardStyles, useTabsStyles } from "./styles";
import { inventoryViews, noop, supplierPurchaseStatuses } from "../../../utils/const";
import SecondaryBar from "../../_navigation/SecondaryBar";
import { TableFooter, Typography, useMediaQuery } from "@material-ui/core";
import typography from "../../../../styles/typography";
import { DownloadIcon, NoReservation120, SparksIcon20 } from "../../../utils/icons";
import { useDispatch, useSelector } from "react-redux";
import { purchasesSelectors, restaurantSelectors } from "../../../../redux/selectors";
import isEmpty from "../../../utils/isEmpty";
import EmptyScreen from "../../_placeholder/EmptyScreen";
import { appActions, purchasesActions } from "../../../../redux/actions";
import { operationViews } from "../../../../redux/constants";
import Button from "@material-ui/core/Button";
import { toI18nKey } from "../../../utils/toI18nKey";
import Badge from "../../_tags/Badge";
import CustomersBadge from "../../_tags/CustomersBadge";
import ReservationTimeBadge from "../../_tags/ReservationTimeBadge";
import palette from "../../../../styles/palette";
import clsx from "clsx";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import Pagination from "@material-ui/lab/Pagination";
import { createSupplierInvoice, getSupplierInvoices, uploadSupplierInvoiceFile } from "../../../api";
import { useRouter } from "next/router";
import { views } from "../../../utils/administrationRoutes";
import PurchaseReceipt from "./PurchaseReceipt";
import PurchaseStatusBadge from "../../_tags/PurchaseStatusBadge";
import SupplierPurchaseModal from "../../_popup/SupplierPurchaseModal";
import moment from "moment/moment";
import DateBadge from "../../_tags/DateBadge";
import InvoiceProcessingStatusBadge from "../../_tags/InvoiceProcessingStatusBadge";
import PurchaseCompletionDownloadModal from "../../_popup/PurchaseCompletionDownloadModal";

let timer;

// const now = moment();
// const momentInstance = moment.lang('en', {
//   calendar: {
//     lastDay: '[Yesterday] LT',
//     sameDay: 'LT',
//     nextDay: '[Tomorrow,] LT',
//     lastWeek: 'ddd LT',
//     nextWeek: 'ddd LT',
//     sameElse: function() {
//       if (this.years() === now.years()) {
//         return 'MMM D'
//       } else {
//         return 'M/D/YY';
//       }
//     }
//   }
// });

const nowMoment = moment()

const Purchases = ({ t }) => {
  const router = useRouter();
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "");
  
  const classes = useStyles();
  const boardClasses = useBoardStyles();
  const tabsClasses = useTabsStyles();
  const isMobile = useMediaQuery('(max-width:960px)');
  
  const dispatch = useDispatch();
  
  const view = useSelector(purchasesSelectors.getView);
  const updateView = (updatedView) => dispatch(purchasesActions.setView(updatedView))
  const [pageOffset, setPageOffset] = useState(0);
  const pageSize = 20;
  
  const total = useSelector(purchasesSelectors.getTotal);
  
  const [purchases, setPurchases] = useState({ total: 0, pages: 0, items: null });
  const { lanes = [], lanesActiveGroup = [] } = useSelector(purchasesSelectors.getBoard);
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const [creating, setCreating] = useState(false);
  const [fetching, setFetching] = useState(true);
  
  const empty = !total && !purchases?.total;
  
  const startCreating = () => setCreating(true);
  
  const [purchaseId, setPurchaseId] = useState(false);

  const navigateToPurchaseOrder = (purchaseOrderId) => {
    router.push(`${resolvedAsPath}?v=${views.INVENTORY}&purchaseOrderId=${purchaseOrderId}`, undefined, { shallow: true }).then(r => {});
  }

  const openPurchase = (id) => {
    navigateToPurchaseOrder(id)
  }
  
  const closePurchase = () => setPurchaseId(false);
  
  const fetchPurchasesList = () => {
    setFetching(true);
    getSupplierInvoices(restaurantId, pageSize, pageOffset)
      .then(({ data: fetchedSupplierInvoices = {} }) => {
        setPurchases(fetchedSupplierInvoices || {})
      })
      .catch(noop)
      .finally(() => setFetching(false));
  }
  
  const fetch = () => {
    if (view === 'BOARD') {
      dispatch(purchasesActions.get(view, pageOffset, pageSize));
      if (!creating) {
        timer = setInterval(() => dispatch(purchasesActions.get(view)), 10000);
      }
    } else {
      fetchPurchasesList();
      if (!creating) {
        timer = setInterval(fetchPurchasesList, 10000);
      }
    }
  }
  
  useEffect(() => {
    fetch();
    return function cleanup() {
      clearInterval(timer)
    }
  }, [view, pageOffset])
  
  const [tab, setTab] = useState(0);
  
  const createInvoice = (invoiceData) => {
    createSupplierInvoice(restaurantId, invoiceData).then(({ data }) => {
      dispatch(appActions.setNotification('supplier-invoice-created', "success"))
      navigateToPurchaseOrder(data.id)
    }).catch(() => {
      dispatch(appActions.setNotification('supplier-invoice-creation-error', "error"))
    })
  }

  const uploadInvoice = (file, isSetupOnly) => {
    dispatch(appActions.setNotification('processing-invoice', "loading"))
    uploadSupplierInvoiceFile(restaurantId, file, isSetupOnly).then(({ data }) => {
      dispatch(appActions.setNotification('supplier-invoice-created', "success"))
      fetch()
    }).catch(() => {
      dispatch(appActions.setNotification('supplier-invoice-creation-error', "error"))
    })
  }
  
  const setBoardActiveGroup = (laneIndex, groupId) => () => {
    dispatch(purchasesActions.setBoardActiveGroup(laneIndex, groupId))
  }
  
  const renderBoardLane = (groups, lanesActiveGroup, index) => (
    <div className={boardClasses.lane} key={`lane-${index}`}>
      {groups.map(({ id: groupId, total, invoices = [] }) => {
        const groupI18nKey = toI18nKey(groupId);
        const expanded = !isEmpty(lanesActiveGroup) && lanesActiveGroup[index] ? lanesActiveGroup[index].groupId === groupId : false;
        
        return (
          <div className={boardClasses.group} style={expanded ? { flex:  1 } : null} key={groupId}>
            <div className={boardClasses.groupHeader} onClick={setBoardActiveGroup(index, groupId)}>
              <div style={{ display: "flex", alignItems: "center", }}>
                <Typography style={{ ...typography.body.medium }}>{t(groupI18nKey)}</Typography>
                <span style={{ marginLeft: 4, display: "inline-flex" }}>
									<Badge quantity={total}/>
								</span>
              </div>
            </div>
            {!isEmpty(invoices) && expanded && (
              <div className={boardClasses.cards}>
                {invoices.map(({ id, supplier = {}, note, formattedCreationDate, creator, number, poNumber }) => {
                  const { name: supplierName = "" } = (supplier || {});
                  const { fullName } = (creator || {})
                  return (
                    <div key={id} className={boardClasses.card} onClick={() => setPurchaseId(id)}>
                      <div style={{ display: "flex", alignItems: "center", flexDirection: "row", justifyContent: "space-between" }}>
                        <div>
                          <Typography style={{ ...typography.body.medium }}>
                            {poNumber || `#${number || "-"}`}
                          </Typography>
                          <div style={{ display: "flex", alignItems: "center", marginTop: 6, height: 22 }}>
                            {/*<div style={{ display: "flex" }}>*/}
                            {/*  {t('started-on', { date: new Date() })}*/}
                            {/*</div>*/}
                            <div style={{ marginLeft: 0 }}>
                              <ReservationTimeBadge formattedStartEndLocalTime={formattedCreationDate}/>
                            </div>
                            <div style={{ marginLeft: 4 }}>
                              <CustomersBadge customers={fullName} />
                            </div>
                          </div>
                          {!!note && (
                            <div>
                              <Typography style={{ ...typography.body.regular, marginTop: 4, fontStyle: "italic", color: palette.grayscale["500"] }}>
                                {note}
                              </Typography>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
  
  const renderBoard = () => {
    if (!total || isEmpty(lanes)) {
      return null;
    }
    
    return (
      <div className={boardClasses.container}>
        {isMobile && (
          <div className={tabsClasses.categories}>
            {lanes.map(({ groups = [] }, index) => {
              if (isEmpty(groups)) {
                return null;
              }
              
              const laneTotal = groups.map(it => it.numberOfReservations ?? 0).reduce((partialSum, a) => partialSum + a, 0);
              const firstGroup = groups[0]
              const laneI18nKey = toI18nKey(firstGroup.id || "")
              return (
                <Button
                  key={`lane-${index}`}
                  disableRipple
                  disableTouchRipple
                  className={clsx(tabsClasses.category, { [tabsClasses.categorySelected]: tab === index })}
                  onClick={() => setTab(index)}
                >
                  <span className={clsx(tabsClasses.categoryContent, { [tabsClasses.categoryContentSelected]: tab === index })}>
                    {t(laneI18nKey)}
                  </span>
                  <span className={clsx(tabsClasses.count, { [tabsClasses.countSelected]: tab === index })}>
                    {laneTotal}
                  </span>
                </Button>
              );
            })}
          </div>
        )}
        
        <div className={boardClasses.content}>
          {!isMobile && lanes.map(({ groupId: activeGroupId, groups = [] }, index) => renderBoardLane(groups, lanesActiveGroup, index))}
          {isMobile && !isEmpty(lanes[tab]) && !isEmpty(lanes[tab].groups) && renderBoardLane(lanes[tab].groups, lanesActiveGroup, tab)}
        </div>
      </div>
    )
  }

  const [downloadingPDF, setDownloadingPDF] = useState(false);
  
  const renderList = () => {
    const noPurchases = !purchases || !purchases.total;
    if (noPurchases) {
      return null;
    }
    
    return (
      <div style={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        paddingBottom: 16
      }}>
        <TableContainer>
          <Table stickyHeader aria-label="reservations-table">
            <TableHead>
              <TableRow>
                <TableCell align="left">{t('po-number')}</TableCell>
                <TableCell align="left">{t('date')}</TableCell>
                <TableCell align="left">{t('processing-status')}</TableCell>
                <TableCell align="right">{t('supplier')}</TableCell>
                <TableCell align="left">{t('status')}</TableCell>
                <TableCell align="left">{t('invoice')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {purchases.items.map(({ supplier = {}, invoice }) => {
                const { name: supplierName = "" } = (supplier || {});
                const { id, number, poNumber, note, status, isSetupOnly, createdAt, aiGenerated, processingStatus, completionDocumentName } = (invoice || {})
                
                const momentTime = moment(createdAt)
                const dateFromCurrentYear = momentTime.year() === nowMoment.year()
                const formattedDate = moment(createdAt).format(dateFromCurrentYear ? 'MMM DD, HH:mm' : 'MMM DD YYYY')
                
                return (
                  <TableRow key={id} style={{ cursor: "pointer" }}>
                    <TableCell onClick={() => openPurchase(id)}>
                      <Typography style={{ ...typography.body.regular }}>
                        {poNumber || `#${number || "-"}`}
                      </Typography>
                    </TableCell>
                    <TableCell onClick={() => openPurchase(id)}>
                      <DateBadge formattedStartEndLocalTime={formattedDate} />
                    </TableCell>
                    <TableCell onClick={() => openPurchase(id)}>
                      {aiGenerated ? (
                        <InvoiceProcessingStatusBadge status={processingStatus} />
                      ) : (
                        <span style={{ color: palette.grayscale["600"] }}>{t("added-manually")}</span>
                      )}
                    </TableCell>
                    <TableCell onClick={() => openPurchase(id)} align="right">
                      <Typography style={{ ...typography.body.regular }}>
                        {supplierName}
                      </Typography>
                    </TableCell>
                    <TableCell onClick={() => openPurchase(id)}>
                      <PurchaseStatusBadge status={isSetupOnly ? supplierPurchaseStatuses.SETUP.value : status} />
                    </TableCell>
                    <TableCell>
                      {completionDocumentName && (
                        <Button style={{
                          minWidth: 0,
                          padding: '5px 8px 5px 12px',
                          border: "1px solid #D9D9D8",
                          borderRadius: "10px",
                        }} disableRipple disableTouchRipple onClick={() => setDownloadingPDF(id)}>
                          <Typography style={{...typography.body.medium, color: palette.grayscale["800"], marginRight: 2 }}>{t("download-pdf")}</Typography>
                          <DownloadIcon />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell>{t('table-count', { count: purchases.total })}</TableCell>
                <TableCell />
                <TableCell />
                <TableCell />
                <TableCell />
                <TableCell />
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
        <div className={classes.pagination}>
          <Pagination count={purchases.pages} variant="text" shape="rounded" onChange={(e, v) => setPageOffset((v - 1) * pageSize)}/>
        </div>
      </div>
    )
  }
  
  const getContent = () => {
    if (empty) {
      return (
        <div className={classes.content} data-testid="inventory-purchases">
          <EmptyScreen
            icon={<NoReservation120 />}
            titleI18nKey="no-purchases"
            descriptionI18nKey="click-the-button-below-to-create-a-purchase"
            action={{ i18nKey: "create-purchase", onClick: startCreating }}
          />
        </div>
      )
    }
    
    return (
      <div className={classes.content}>
        {/*{view === operationViews.BOARD.key && renderBoard()}*/}
        {view === operationViews.LIST.key && renderList()}
      </div>
    );
  }
  
  const getSecondaryBarActions = () => (
    <div style={{ display: "flex", alignItems: "center" }}>
      {/*<OperationViewDropdown style={{ marginRight: 8 }} view={view} setView={updateView} views={[operationViews.BOARD.key, operationViews.LIST.key]} />*/}
      <Button
        className={classes.createBtn}
        onClick={startCreating}
        disableRipple
        style={{ padding: "6px 16px 6px 7px" }}
      >
        <span style={{ marginRight: 4, display: "inline-flex" }}>
          <SparksIcon20 color={palette.grayscale["100"]}/>
        </span>
        {t("new")}
      </Button>
    </div>
  )
  
  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        <SecondaryBar title={t(inventoryViews.PURCHASES.i18nKey)} right={getSecondaryBarActions()} contentStyle={{
          paddingLeft: 0,
          paddingRight: 0,
          marginTop: 0,
          borderBottom: "none",
        }}/>
        {getContent()}
        {creating && (
          <SupplierPurchaseModal
            open={creating}
            onClose={() => setCreating(false)}
            onSubmit={createInvoice}
            onUpload={uploadInvoice}
          />
        )}
        {purchaseId && (
          <PurchaseReceipt
            isOpen={purchaseId}
            close={closePurchase}
            purchaseReceiptId={purchaseId}
            callback={() => fetch()}
          />
        )}
        {!!downloadingPDF && (
          <PurchaseCompletionDownloadModal
            open={!!downloadingPDF}
            onClose={() => setDownloadingPDF(null)}
            documentId={downloadingPDF} />
        )}
      </div>
    </div>
  );
};

export default withTranslation('common')(memo(Purchases))
