import { makeStyles} from '@material-ui/core/styles';

const useStyles = makeStyles(theme => ({
  content: {
    padding: theme.spacing(2)
  },
  actions: {
    textAlign: 'right',
    '& > button': {
      marginLeft: 8
    }
  },
  page: {
    padding: theme.spacing(2),
    minHeight: `calc(100vh - 49px)`
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    justifyContent: 'space-between'
  },
  toolbox: {
    marginLeft: theme.spacing(2)
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  spacing: {
    flex: 1
  },
  box: {
    padding: theme.spacing(1)
  },
  info: {
    marginTop: theme.spacing(2),
    overflow: 'hidden'
  },
  cursor: {
    cursor: 'pointer'
  },
  taken: {
    '&&': {
      background: '#FFBCBC'
    }
  },
  selected: {
    '&&': {
      border: '2px solid #dbdede'
    }
  },
  requestingPayment: {
    '&&': {
      background: '#FEF7EB',
      border: '2px solid #fde7a1'
    }
  },
  infoHeader: {
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
  },
  data: {
    padding: theme.spacing(2),
    display: 'flex',
    flexDirection: 'column'
  },
  field: {
    width: '100%'
  },
  capitalize: {
    textTransform: 'capitalize'
  },
  fieldGroup: {
    paddingBottom: theme.spacing(4)
  },
  uploaderContainer: {
    cursor: 'pointer',
    height: 180,
    width: '100%',
    borderRadius: 6,
    background: '#F7F8F9',
    backgroundPosition: 'center',
    backgroundSize: 'cover',
    display: 'flex',
    alignItems: 'center',
    '& > p': {
      textAlign: 'center',
      width: '100%'
    }
  },
  clearImage: {
    marginTop: theme.spacing(1),
    width: '100%',
    fontSize: 14,
    padding: '4px 6px',
    borderRadius: theme.spacing(1)
  },
  formControl: {
    minWidth: '100%'
  },
  fullWidth: {
    width: '100%'
  },
  selectActionBtn: {
    paddingTop: theme.spacing(2),
  },
  subItem: {
    paddingLeft: theme.spacing(2)
  },
  tag: {
    display: 'flex',
    padding: theme.spacing(1),
    '&:first-child': {
      paddingLeft: 0
    }
  },
  tagImg: {
    marginRight: theme.spacing(1) / 2,
    width: 18,
    height: 18
  },
  chips: {
    display: 'flex',
    flexWrap: 'wrap',
  },
  chip: {
    margin: 2,
  },
}));

const useMenuStyles = makeStyles((theme) => ({
  list: {
    paddingTop: 0,
    paddingBottom: 0,
    maxHeight: 400
  },
  paper: {
    backgroundColor: theme.palette.common.white,
    borderRadius: 12,
    minWidth: 340,
    maxWidth: "100%",
    boxShadow: "0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04)",
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 2,
    paddingBottom: 2,
    transition: "none"
  },
  menuItemRoot: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    "&:hover": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  },
  borderTop: {
    borderTop: "1px solid #EFEFEE"
  },
  borderBottom: {
    borderBottom: "1px solid #EFEFEE"
  },
  content: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  },
  selection: {
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
    marginRight: 6
  },
  option: {
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#333332",
  },
  optionTitle: {
    fontWeight: 500
  },
  selected: {
    fontWeight: 500,
    color: "#FF7C5C"
  },
  right: {
    display: "flex",
    alignItems: "center"
  },
  customRangeRightContent: {
    display: "flex",
    alignItems: "center"
  },
  calendarSelectedRange: {
    marginRight: 2,
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
  }
}));

const useRadioButtonStyles = makeStyles((theme) => ({
  root: {
    padding: 0,
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
  icon: {
    borderRadius: '50%',
    width: 16,
    height: 16,
    border: "1px solid #D8D7D6",
    backgroundColor: theme.palette.common.white,
    '$root.Mui-focusVisible &': {
      outlineOffset: 2,
    },
    'input:disabled ~ &': {
      boxShadow: 'none',
      background: 'rgba(206,217,224,.5)',
    },
  },
  checkedIcon: {
    border: "none",
    backgroundColor: '#FF7C5C',
    '&:before': {
      display: 'block',
      width: 16,
      height: 16,
      backgroundImage: `radial-gradient(${theme.palette.common.white},${theme.palette.common.white} 25%,transparent 27%)`,
      content: '""',
    },
  },
}));

export default useStyles;

export {
  useMenuStyles,
  useRadioButtonStyles
}
