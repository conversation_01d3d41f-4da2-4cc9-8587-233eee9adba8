import React from "react";
import { usePairingStyle } from "./styles";
import { withTranslation } from "../../../i18n";
import Typography from "@material-ui/core/Typography";
import clsx from "clsx";

const PairingStatus = ({ t, status }) => {
  const classes = usePairingStyle();
  return (
    <div>
      <div className={classes.container}>
        <div className={classes.content}>
          <div className={clsx(classes.statusDot, { [classes.statusDotClosed]: !status })} />
          <Typography className={classes.status}>{t(status ? "paired" : "unpaired")}</Typography>
        </div>
      </div>
    </div>
  )
};

export default withTranslation('common')(PairingStatus);