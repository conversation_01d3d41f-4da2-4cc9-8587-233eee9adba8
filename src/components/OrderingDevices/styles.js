import { makeStyles } from '@material-ui/core/styles';

const drawerWidth = 240;

const useStyles = makeStyles(theme => ({
  wrapper: {
    height: "100%"
  },
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100%"
  },
  content: {
    margin: 12,
    minHeight: `calc(100vh - 49px)`
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  buttonRoot: {
    fontSize: 14,
    padding: '4px 6px',
    borderRadius: theme.spacing(1)
  },
  icon: {
    color: 'rgba(0, 0, 0, 0.54)'
  },
  user: {
    display: 'flex',
    alignItems: 'center'
  },
  avatarRoot: {
    width: 16,
    height: 16
  },
  username: {
    marginLeft: theme.spacing(1)
  },
  tabs: {
    marginBottom: theme.spacing(2)
  },
  itemImg: {
    height: 24,
    width: 24,
    marginRight: 6,
    marginLeft: 6,
    objectFit: 'cover',
    borderRadius: 4
  },
  itemTitle: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: 12
  },
  clickable: {
    cursor: 'pointer'
  },
  actions: {
    '& > button:not(:first-child)': {
      marginLeft: 8
    }
  },
  numeration: {
    fontWeight: 500,
    marginRight: 8,
    marginLeft: 8,
  },
  createBtn: {
    background: "#FF7C5C",
    fontStyle: "normal",
    fontWeight: "500",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#F9F9F9",
    borderRadius: 10,
    '&:hover': {
      background: "#FF7C5C",
    },
    textTransform: "capitalize",
    padding: '6px 16px'
  },
}));

export const useMenuStyles = makeStyles(theme => ({
  container: {},
  content: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    cursor: "pointer"
  },
  avatarBtn: {
    padding: 0
  },
  avatar: {
    width: 24,
    height: 24,
    backgroundColor: "#929191",
    color: theme.palette.common.white,
    fontSize: 12,
    lineHeight: '12px',
    fontWeight: 500,
    textTransform: 'uppercase'
  },
  name: {
    marginLeft: 8,
    fontStyle: 'normal',
    fontWeight: 'normal',
    fontSize: '14px',
    lineHeight: '20px',
    letterSpacing: "-0.0014em"
  },
  left: {
    display: "flex",
    alignItems: "center",
  },
  right: {
    display: "flex",
    justifyContent: "flex-end"
  },
  menu: {
    backgroundColor: theme.palette.common.white,
    borderRadius: 12,
    width: 252,
    maxWidth: "100%",
    boxShadow: "0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04)",
    maxHeight: 425 // shows half of the next menu item
  },
  menuItem: {
    padding: "12px 16px",
    "&:focus": {
      background: "transparent"
    }
  }
}));

export const usePairingStyle = makeStyles(theme => ({
  container: {
    display: "flex",
    alignItems: "center",
  },
  content: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    cursor: "pointer",
    background: "#E5E4E3",
    borderRadius: 12,
    paddingTop: 1,
    paddingRight: 6,
    paddingLeft: 6,
    paddingBottom: 1
  },
  status: {
    fontStyle: "normal",
    fontWeight: "500",
    fontSize: "11px",
    lineHeight: "16px",
    letterSpacing: "0.02em",
    textTransform: "uppercase",
    color: "#242423",
    marginLeft: 4,
    whiteSpace: "nowrap"
  },
  statusDot: {
    background: "#73AF9F",
    width: 8,
    height: 8,
    borderRadius: "100%"
  },
  statusDotClosed: {
    background: "#F06060"
  }
}));


export default useStyles;
