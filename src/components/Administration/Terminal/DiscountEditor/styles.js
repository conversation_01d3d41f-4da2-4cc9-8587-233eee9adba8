import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../../../styles/theme';
import { fade } from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  content: {
    padding: theme.spacing(2),
    minHeight: '100vh'
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    justifyContent: 'space-between'
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  layout: {},
  img: {
    height: 180,
    width: '100%',
    objectFit: 'cover',
    borderRadius: 4,
  },
  imgError: {
    height: 0,
    width: 0,
    display: 'none'
  },
  text: {},
  name: {
    marginTop: 8
  },
  description: {
    marginTop: 8,
    color: colors.leviee.greyscale.darkGray
  },
  pricing: {
    marginTop: 12,
    display: 'flex',
    flexDirection: 'row',
    '& > p:not(:first-child)': {
      marginLeft: theme.spacing(1)
    }
  },
  price: {
    color: colors.leviee.greyscale.darkGray
  },
  regularPrice: {
    color: colors.leviee.greyscale.midGray,
    textDecoration: 'line-through'
  },
  action: {
    flex: 1,
    textAlign: 'right'
  },
  notesDescription: {
    marginTop: 2,
    color: fade(colors.leviee.main.dark, 0.48)
  },
  remarksDescription: {
    marginTop: 2,
    color: fade(colors.leviee.main.dark, 0.48)
  },
  remarkDescription: {
    color: colors.leviee.greyscale.darkGray
  },
  remarks: {},
  remarkList: {
    marginTop: 8,
  },
  orderActionLayout: {
    width: '100%'
  },
  divider: {
    borderBottom: '1px solid rgba(4, 23, 47, 0.06)',
    marginTop: 24,
    marginBottom: 24
  },
  orderQtdLayout: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(2, 2, 1, 2)
  },
  field: {
    width: '100%'
  },
  fieldGroup: {
    paddingTop: 6,
    paddingBottom: 6
  },
  optionBody: {
    marginTop: 16
  },
  option: {
    display: 'flex',
    alignItems: 'inherit',
    textAlign: 'inherit'
  },
  optionRadioBtn: {
    marginRight: 24,
    marginTop: 6
  }
}));

export default useStyles;
