import React, { useState } from 'react';
import { withTranslation } from '../../../../../i18n';
import Typography from '@material-ui/core/Typography';
import Button from '@material-ui/core/Button';
import {Confirm} from '../../../Confirmation';
import {Panel} from '../../Panel';
import initials from "initials";
import IconButton from '@material-ui/core/IconButton';
import {CloseIcon} from '../../../../utils/icons';
import BottomPanel from '../../../BottomPanel';
import {ButtonContainer} from '../../../Containers';
import {MainButton} from '../../../Buttons';
import useStyles from './styles';
import isEmpty from "../../../../utils/isEmpty";
import { ButtonBase } from "@material-ui/core";
import typography from "../../../../../styles/typography";
import palette from "../../../../../styles/palette";
import Avatar from "@material-ui/core/Avatar";

const ApproveParticipantAction = withTranslation('common')(({ t, customer = {}, approve }) => {
  const classes = useStyles();
  const { firstName } = (customer ?? {});

  return (
    <Confirm
      title={t('waiter-table-overview-approve-table-action-confirmation-dialog-title')}
      body={(
        <Typography color="textSecondary" variant="body2">
          {t('waiter-table-overview-approve-table-action-confirmation-dialog-description-line1')}
          <br />
          <br />
          {t('waiter-table-overview-approve-table-action-confirmation-dialog-description-line2')}
        </Typography>
      )}
    >
      {confirm => (
        <Button variant="contained" color="secondary" size="small" disableElevation onClick={confirm(approve)} className={classes.approvalAction}>
          {`${t('waiter-table-overview-approve-table-action-btn')}${!isEmpty(customer) ? ` ${firstName}` : ''}`}
        </Button>
      )}
    </Confirm>
  )
});

const ParticipantsManager = ({ t, orderId, customerId = '', participants = [], approve, clear, promote }) => {
  const classes = useStyles();

  const [isParticipantManagersDrawerOpen, setParticipantManagerDrawer] = useState(false);

  const showParticipantManagerDrawer = () => setParticipantManagerDrawer(true);
  const hideParticipantManagerDrawer = () => setParticipantManagerDrawer(false);

  const unverifiedParticipants = participants.filter(p => !p['approvalId']);
  const verifiedParticipants = participants.filter(p => !!p['approvalId']);

  const onApprove = (participantId) => () => approve(orderId, participantId);

  const onPromote = (participantId) => () => promote(orderId, participantId)

  const onClear = () => clear(orderId)

  const ViewParticipantsAction = () => (
    <div style={{ width: "100%", display: "flex", alignItems: "flex-end", flexDirection: "column"  }}>
      <ButtonBase style={{ border: `1px solid ${palette.grayscale.border}`, padding: "5px 12px 5px 8px", borderRadius: 10 }} onClick={showParticipantManagerDrawer}>
        <Avatar classes={{ root: classes.avatarRoot }} src={"https://storage.googleapis.com/leviee_public/avatar/Illustration-10-yellow.svg"} />
        <Avatar classes={{ root: classes.avatarRoot }} style={{ marginLeft: -8 }} src={"https://storage.googleapis.com/leviee_public/avatar/Illustration-7-yellow.svg"} />
        <Avatar classes={{ root: classes.avatarRoot }} style={{ marginLeft: -8 }} src={"https://storage.googleapis.com/leviee_public/avatar/Illustration-3-purple.svg"} />
        <Avatar classes={{ root: classes.avatarRoot }} style={{ marginLeft: -8 }}>
          <Typography style={{ ...typography.extraSmall.medium }}>
            {participants ? participants.length : '-'}
          </Typography>
        </Avatar>
        <Typography style={{ ...typography.body.medium, marginLeft: 2, whiteSpace: "nowrap" }}>
          {t('common-guests')}
        </Typography>
      </ButtonBase>
    </div>
  );

  if (isEmpty(participants)) {
    return null;
  }

  // if (hasLength(participants, 1) && isEmpty(verifiedParticipants)) {
  //   const { id, customer } = participants[0];
  //   return (
  //     <div className={classes.layout}>
  //       <ApproveParticipantAction approve={onApprove(id)} customer={customer}/>
  //     </div>
  //   )
  // }

  return (
    <>
      <div className={classes.layout}>
        <ViewParticipantsAction />
      </div>
      <Panel open={isParticipantManagersDrawerOpen} close={hideParticipantManagerDrawer} variant="temporary">
        <div className={classes.content}>
          <div className={classes.header}>
            <Typography variant="h3">{t('participants-manager-label')}</Typography>
            <IconButton classes={{ root: classes.iconButtonRoot }} onClick={hideParticipantManagerDrawer}>
              <CloseIcon />
            </IconButton>
          </div>
          {participants.map(({ id, customer = {}, approvalId }) => (
            <div className={classes.participant}>
              <div className={classes.customer} key={id}>
                <Avatar
                  alt={`${customer.firstName}`}
                  className={classes.avatar}
                >
                  {initials(`${customer.firstName} ${customer.lastName}`)}
                </Avatar>
                <Typography variant="body2">{`${customer.firstName} ${(customer.lastName || '').slice(0, 1)}`}</Typography>
              </div>
              {!approvalId && <ApproveParticipantAction approve={onApprove(id)} />}
              {(customer.id !== customerId) && approvalId && (
                <Button variant="contained" color="secondary" size="small" disableElevation onClick={onPromote(id)} className={classes.approvalAction}>
                  {t('waiter-table-overview-promote-customer-action-btn')}
                </Button>
              )}
            </div>
          ))}
        </div>
        <BottomPanel isSticky>
          <ButtonContainer>
            <MainButton disabled={clear === null}  onClick={onClear}>{t('terminal-orders-clear-order-action-btn')}</MainButton>
          </ButtonContainer>
        </BottomPanel>
      </Panel>
    </>
  )
};

export default withTranslation('common')(ParticipantsManager);
