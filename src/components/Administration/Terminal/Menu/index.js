import React from 'react';
import Typography from '@material-ui/core/Typography';
import {withTranslation} from '../../../../../i18n';
import ContainerCard from '../../../../v2/components/ContainerCard';
import useStyles from './styles';
import {colorIdentifiers, getColorCode} from "../../../../utils/const";

const Menu = ({
  t,
  open,
  disabled,
  title,
  internalTitle,
  color = colorIdentifiers.GREY_600,
  count,
  emoji
}) => {
  const classes = useStyles();

  return (
    <ContainerCard disabled={disabled} onClick={open} style={{ color: '#F9F9F9', backgroundColor: getColorCode(color) }}>
      <div className={classes.card}>
        <div className={classes.header}>
          <Typography className={classes.name} variant="body2">
            <span className={classes.nameContent}>
              {internalTitle || title}
            </span>
            <span className={classes.count}>
              {count}
            </span>
          </Typography>
        </div>
        <div className={classes.emoji}>
          {emoji}
        </div>
        {/*<Button color="secondary" variant="contained" disableElevation size="small"*/}
        {/*        disabled={disabled}*/}
        {/*        className={classes.btn}*/}
        {/*        onClick={open}*/}
        {/*>*/}
        {/*  {t('terminal-menu-open-menu-btn-label')}*/}
        {/*</Button>*/}
      </div>
    </ContainerCard>
  );
};

export default withTranslation('common')(Menu)
