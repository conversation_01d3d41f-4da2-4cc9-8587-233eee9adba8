import React, { useState } from 'react';
import { withTranslation } from '../../../../../i18n';
import {MainButton} from '../../../Buttons';
import useStyles from './styles';
import shadows from "../../../../../styles/shadows";
import Modal from "../../../_popup/Modal";
import ModalBar from "../../../_navigation/ModalBar";
import palette from "../../../../../styles/palette";
import SegmentedControlBaseTabs from "../../../_tabs/SegmentedControlBaseTabs";
import { paymentSuggestions, tipModeOptions, tipModes } from "../../../../../redux/constants";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../styles/typography";
import { formatNumber } from "../../../../utils/formatNumber";
import { isEmpty } from "../../../../../redux/helpers";
import { removeDuplicates } from "../../../../utils/ArrayUtils";
import { ButtonBase } from "@material-ui/core";
import Keyboard from "../../../Keyboard";

const ChangeCalculator = ({ t, isOpen, close, total = 0, onAdd, disabled, changeLabel, noDiff }) => {
  const classes = useStyles();
  const [intVal, setIntVal] = useState('');
  const [decVal, setDecVal] = useState('');
  const [dec, setDec] = useState(false);

  const [val, setVal] = useState('')
  const [percent, setPercent] = useState('');
  
  const [mode, setMode] = useState(tipModes.DEDUCTED.key);
  const updateMode = (m) => {
    setMode(m);
    setVal('');
    setPercent('');
  };

  const onChange = (char) => {
    if (!val && (char === "0" || char === "00")) {
      return;
    }
    
    if (char === "00") {
      onDec();
      return;
    }

  
    // only add character if there is space for 1 chars
    if (val.length <= 9) {
      setVal(val + char)
    }
  }
  
  const onPercentageChange = (char) => {
    if (!percent && (char === "0" || char === "00")) {
      return;
    }
    
    if (char === "00") {
      onDec();
      return;
    }
    
    // only add character if there is space for 1 chars
    if (percent.length <= 9) {
      validateAndSetPercent(percent + char)
    }
  }

  const onSuggestion = (suggestion) => {
    // clear up and add just the suggestion
    setVal( suggestion.toFixed(0))
  }
  
  const onPercentageSuggestion = (percent) => {
    // const calculated = total * percent / 100
    setPercent(percent.toFixed(0))
  }

  const onDelete = () => {
    if (val) {
      setVal(val.slice(0, -1))
    }
  
    if (percent) {
      setPercent(percent.slice(0, -1))
    }
  }

  const onDec = () => {
    // only add 2 zeros if there is space for 2 chars
    if (val.length < 9) {
      setVal(val + '00')
    }
  
    if (percent.length <= 3) {
      validateAndSetPercent(percent + '00')
    }
  }
  
  const validateAndSetPercent = (newPercent) => {
    if (!newPercent) {
      setPercent('')
    } else {
      const percentInt = (parseInt(newPercent || 0) / 100)
      if (percentInt <= 100) {
        setPercent(newPercent)
      }
    }
  }

  // const onChange = (char) => {
  //   if (dec) {
  //     if (decVal.length < 2) {
  //       setDecVal(decVal + `${char}`)
  //     }
  //   }
  //
  //   else {
  //     if (intVal.length < 4) {
  //       setIntVal(intVal + `${char}`)
  //     }
  //   }
  // }

  // const onDec = () => setDec(true)
  //
  // const onDelete = () => {
  //   if (dec) {
  //     const v = decVal ? decVal.slice(0, -1) : decVal;
  //     setDecVal(v)
  //     if (v.length === 0) {
  //       setDec(false)
  //     }
  //   } else {
  //     const v = intVal ? intVal.slice(0, -1) : intVal
  //     setIntVal(v)
  //   }
  // }

  // const change = Math.max(0, parseFloat(`${intVal || '0'}.${decVal || '0'}`) - total)
  
  const changeMode = mode === tipModes.DEDUCTED.key
  const percentageMode = mode === tipModes.PERCENTAGE.key
  
  const floatingVal = (parseInt(val || 0) / 100);
  const percentageAsInt = (parseInt(percent || 0) / 100);
  const percentageVal = ((total * percentageAsInt / 100) + total);
  const change = changeMode ? floatingVal - total : percentageVal - total;
  
  if (changeMode) {
    console.log(`Calculator val ${val} | floatingVal ${floatingVal} | total ${total} | change ${change}`)
  }
  
  if (percentageMode) {
    console.log(`Calculator percent ${percent} | percentageAsInt ${percentageAsInt} | percentageVal ${percentageVal} | total ${total} | change ${change}`)
  }
  
  const suggestions = paymentSuggestions(total)
  
  const percentageSuggestions = [{
    value: 5 * 100,
    label: '5'
  }, {
    value: 10 * 100,
    label: '10'
  }, {
    value: 15 * 100,
    label: '15'
  }, {
    value: 20 * 100,
    label: '20'
  }]
  
  const tipOrChangeAmount = Math.max(0, change).toFixed(2);
  const disabledToSubmitPercentage = !percent || (!!total && (change < 0));
  const disabledToSubmitChange = !val || (!!total && (change < 0))
  const disabledToSubmit = changeMode ? disabledToSubmitChange : disabledToSubmitPercentage
  
  const handleAdd = () => {
    if (changeMode) {
      if (disabled || disabledToSubmitChange) {} else {
        onAdd(floatingVal)
      }
    }
  
    if (percentageMode) {
      if (disabled || disabledToSubmitPercentage) {} else {
        onAdd(percentageVal)
      }
    }
  }
  
  return (
    <Modal
      open={isOpen}
      onClose={close}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 420, maxWidth: "90%" } }}
    >
      <ModalBar title={t(total ? "add-tip" : 'add-payment-btn')} onClose={close} onDone={disabledToSubmit ? null : handleAdd} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: 20, borderBottomRightRadius: 20 }}>
        {!!total && [
          <div style={{ paddingTop: 16, paddingLeft: 16, paddingRight: 16 }}>
            <SegmentedControlBaseTabs tabs={tipModeOptions} value={mode} setValue={updateMode} />
          </div>
        ]}
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 40, paddingBottom: 40 }}>
          <Typography style={{ ...typography.body.medium, marginBottom: 16, textAlign: "center" }}>
            {t(changeMode ? 'define-amount-to-pay' : 'define-percentage-to-pay')}
          </Typography>
          <Typography style={{ ...typography.x.paymentAmount, textAlign: "center" }}>
            <span style={{ color: (change <= 0) ? palette.grayscale["400"] : "inherit" }}>
              {changeMode ? formatNumber(floatingVal) : formatNumber(percentageAsInt)}
            </span>{`${changeMode ? '€' : '%'}`}
          </Typography>
        </div>
        {!!total && [
          <div style={{ borderTop: `1px dashed ${palette.grayscale["400"]}`, marginLeft: 20, marginRight: 20, paddingBottom: 16 }}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginTop: 16 }}>
              <Typography style={{ ...typography.body.medium }}>{t('amount-due')}</Typography>
              <Typography style={{ ...typography.body.medium }}>
                {formatNumber(total)}€
              </Typography>
            </div>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginTop: 12 }}>
              <Typography style={{ ...typography.body.medium }}>{t('tip')}</Typography>
              <Typography style={{ ...typography.body.medium }}>
                {tipOrChangeAmount}€
              </Typography>
            </div>
          </div>
        ]}
        {!!total && changeMode && !isEmpty(suggestions) && (
          <div style={{ display: "flex",
            flexDirection: "row",
            // flexWrap: "nowrap",
            overflowX: "auto",
            paddingBottom: 12,
            paddingLeft: 20, paddingRight: 20 }}>
            {removeDuplicates(suggestions).map(({ value, label }) => (
              <ButtonBase key={value} style={{
                paddingTop: 6,
                paddingBottom: 6,
                paddingLeft: 12,
                paddingRight: 12,
                background: palette.grayscale["100"],
                ...shadows.base,
                marginRight: 8,
                borderRadius: 12
              }} onClick={() => onSuggestion(value)}>
                <Typography style={{ ...typography.body.regular }}>{label}€</Typography>
              </ButtonBase>
            ))}
          </div>
        )}
        {!!total && percentageMode && !isEmpty(percentageSuggestions) && (
          <div style={{ display: "flex",
            flexDirection: "row",
            // flexWrap: "nowrap",
            overflowX: "auto",
            paddingBottom: 12,
            paddingLeft: 20, paddingRight: 20 }}>
            {removeDuplicates(percentageSuggestions).map(({ value, label }) => (
              <ButtonBase key={value} style={{
                paddingTop: 6,
                paddingBottom: 6,
                paddingLeft: 12,
                paddingRight: 12,
                background: palette.grayscale["100"],
                ...shadows.base,
                marginRight: 8,
                borderRadius: 12
              }} onClick={() => onPercentageSuggestion(value)}>
                <Typography style={{ ...typography.body.regular }}>{label}%</Typography>
              </ButtonBase>
            ))}
          </div>
        )}
        <div>
          {changeMode && <Keyboard onClick={onChange} onDelete={onDelete} style={{ background: palette.grayscale["100"], boxShadow: "none", borderRadius: 0 }}/>}
          {percentageMode && <Keyboard onClick={onPercentageChange} onDelete={onDelete} style={{ background: palette.grayscale["100"], boxShadow: "none", borderRadius: 0 }}/>}
        </div>
        <div className={classes.orderActionLayout} style={{ paddingLeft: 12, paddingRight: 12, paddingTop: 16 }}>
          {changeMode && (
            <MainButton
              onClick={() => onAdd(floatingVal)}
              loading={disabled}
              disabled={disabledToSubmitChange}>
              {t('common-confirm')}
            </MainButton>
          )}
          {percentageMode && (
            <MainButton
              onClick={() => onAdd(percentageVal)}
              loading={disabled}
              disabled={disabledToSubmitPercentage}>
              {t('common-confirm')}
            </MainButton>
          )}
        </div>
      </div>
    </Modal>
  )
};

export default withTranslation('common')(ChangeCalculator);
