import { makeStyles } from "@material-ui/core/styles";
import {bottomNavigation, offset, tabs} from '../../../../../styles/theme';
import palette from "../../../../../styles/palette";
import shadows from "../../../../../styles/shadows";

const useStyles = makeStyles(theme => ({
  content: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    backgroundColor: "#E8E7E6",
    overflow: 'hidden',
    display: "flex",
    flexDirection: "column",
    position: "relative",
  },
  listRoot: {
    width: "100%",
    backgroundColor: theme.palette.background.paper,
    position: "relative",
    overflow: "auto"
    // maxHeight: 300,
  },
  listSection: {
    backgroundColor: 'inherit',
  },
  ul: {
    backgroundColor: 'inherit',
    padding: 0,
    overflow: 'hidden'
  },
  user: {
    display: 'flex',
    alignItems: 'center'
  },
  tableUser: {
    display: 'flex',
    alignItems: 'baseline',
    marginBottom: theme.spacing(2) + 4,
    justifyContent: 'space-between',
  },
  group: {
    cursor: 'pointer'
  },
  username: {
    marginLeft: theme.spacing(1)
  },
  menuRoot: {
    zIndex: 1400
  },
  svgRoot: {
    padding: theme.spacing(1) - 2,
    boxSizing: 'content-box'
  },
  tinyBtn: {
    fontSize: 12,
    lineHeight: '14px',
    padding: 6,
    minWidth: 0
  },
  // avatar: {
  //   background: 'rgba(0, 0, 0, 0.06)',
  //   color: 'rgba(0, 0, 0, 0.4)',
  //   fontSize: 14,
  //   fontWeight: 500
  // },
  summary: {
    // padding: theme.spacing(2),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 8
  },
  tableHeader: {
    textAlign: 'right',
    marginBottom: theme.spacing(2),
    '& > p': {
      fontWeight: 500
    }
  },
  paymentDetails: {
    textAlign: 'right'
  },
  payInput: {
    marginTop: theme.spacing(3)
  },
  payInputBase: {
    width: '100%',
    // padding: '4px 4px',
    alignItems: 'center',
    // fontSize: theme.typography.h1.fontSize,
    textAlign: 'center'
  },
  payDescription: {
    width: '100%',
    padding: '4px 4px',
    alignItems: 'center',
    textAlign: 'center',
    marginBottom: theme.spacing(2)
  },
  noCheckout: {
    marginTop: theme.spacing(2),
    textAlign: 'center',
    marginBottom: theme.spacing(2)
  },
  noCheckoutDescription: {
    marginBottom: theme.spacing(2)
  },
  userCheckoutSettings: {
    textAlign: 'right'
  },
  btn: {
    width: '100%'
  },
  customer: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(1)
  },
  avatar: {
    marginRight: theme.spacing(2) - 4,
    background: 'rgba(0, 0, 0, 0.06)',
    color: 'rgba(0, 0, 0, 0.4)',
    fontSize: 14,
    fontWeight: 500
  },
  selectableOrderItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    '&+&': {
      marginTop: 12
    },
    '& > div': {
      flex: 1,
      marginRight: 12
    },
    background: "#F9F9F9",
    borderRadius: 12,
    padding: 12,
    boxShadow: "0px 4px 8px rgb(0 0 0 / 2%), 0px 0px 2px rgb(0 0 0 / 4%), 0px 0px 1px rgb(0 0 0 / 2%)"
  },
  selector: {
    marginRight: 12
  },
  checkBoxRoot: {
    '&&': {
      padding: '8px 6px'
    }
  },
  orderItem: {
    flex: 1
  },
  formControlLabel: {
    flex: 1
  },
  bottomActions: {
    width: '100%'
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    // marginBottom: theme.spacing(2),
    justifyContent: 'space-between'
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  totalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 44
  },
  subtotalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 32
  },
  userRow: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  customerReceipt: {
    marginTop: theme.spacing(4)
  },
  discount: {
    marginTop: 12
  },
  discountActionBtn: {
    width: '100%',
    lineHeight: '28px'
  },
  discountActionBody: {
    display: 'flex',
    flex: 1,
    alignItems: 'center'
  },
  discountBtnLabel: {
    paddingTop: 8,
    paddingBottom: 8,
    marginLeft: 28,
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  },
  noMargin: {
    '& > div': {
      marginRight: 0
    }
  },
  paymentToggle: {
    paddingTop: 16,
    display: 'flex',
    justifyContent: 'center'
  },
  participants: {
    paddingTop: 8
  },
  tip: {
    paddingTop: 8
  },
  payment: {
    paddingTop: theme.spacing(1),
    display: 'flex',
    alignItems: 'center',
    '& > button': {
      marginLeft: theme.spacing(2)
    }
  },
  containedButtonRoot: {
    fontSize: 14,
    padding: '4px 8px',
    borderRadius: theme.spacing(1) - 2
  },
  paymentMethods :{
    display: "flex",
    flexDirection: "column",
    flexWrap: "wrap"
  },
  paymentMethod: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
  },
  paymentMethodOption: {
    marginTop: 8,
    minWidth: 147,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    background: palette.grayscale["100"],
    padding: 12,
    ...shadows.base,
    borderRadius: 12
  },
  paymentItem: {
    marginTop: 8,
    minWidth: 147,
    // display: "flex",
    // alignItems: "center",
    // justifyContent: "space-between",
    background: palette.grayscale["100"],
    ...shadows.base,
    borderRadius: 12
  },
  receiptOption: {
    marginTop: 8,
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    background: palette.grayscale["100"],
    padding: 12,
    ...shadows.base,
    borderRadius: 12
  },
}));

export default useStyles;
