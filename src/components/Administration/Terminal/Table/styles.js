import { makeStyles } from '@material-ui/core/styles';
import {fade} from "@material-ui/core";
import {appBarHeight, colors} from "../../../../../styles/theme";
import palette from "../../../../../styles/palette";
import typography from "../../../../../styles/typography";
import shadows from "../../../../../styles/shadows";

const offset = 49;

export const useBackBtnStyles = makeStyles(theme => ({
  container: {
    padding: 0,
    paddingLeft: 4,
    minWidth: 0,
    "&:hover": {
      background: "transparent"
    },
    display: "flex",
    alignItems: "center"
  },
  content: {
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "40px",
    color: "#626972"
  }
}))
export const useAppBarStyles = makeStyles(theme => ({
  appBar: {
    borderBottom: '1px solid #f3f4f4',
    zIndex: theme.zIndex.drawer + 1
  },
  content: {
    height: appBarHeight,
    paddingLeft: 12,
    paddingRight: 12,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  fixedSpacing: {
    paddingTop: appBarHeight + 1
  },
  left: {
    display: 'flex',
    alignItems: "center",
    flexBasis: '30%'
  },
  center: {
    flexBasis: '40%'
  },
  right: {
    flexBasis: '30%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center'
  },
  orderInfo: {
    display: "flex",
    alignItems: "center"
  },
  badge: {
    marginLeft: 4,
    whiteSpace: "nowrap",
    background: palette.grayscale["300"],
    borderRadius: "12px",
    paddingLeft: 6,
    paddingRight: 6
  },
  badgeContent: {
    ...typography.extraSmall.medium,
    textTransform: "none"
  },
  btn: {
    minWidth: 0,
    padding: 5,
    border: "1px solid #D9D9D8",
    borderRadius: "10px",
    "&:hover": {
      background: "transparent"
    },
    "&+&": {
      marginLeft: 12
    }
  },
  btnWithLabel: {
    paddingLeft: 11,
    paddingRight: 11
  },
  btnLabel: {
    fontStyle: "normal",
    fontWeight: "500",
    fontSize: "14px",
    lineHeight: "20px",
    color: "#333332",
    whiteSpace: "nowrap",
    letterSpacing: "-0.14%"
  },
  btnLabelWithIcon: {
    marginLeft: 2
  },
  btnDivider: {
    marginLeft: 12,
    paddingLeft: 12,
    position: "relative",
    "&:after": {
      content: '""',
      position: "absolute",
      left: 0,
      bottom: 4,
      width: 1,
      height: 24,
      borderRight: "1px solid #D9D9D8"
    }
  }
}));
export const useMenuStyles = makeStyles((theme) => ({
  list: {
    paddingTop: 0,
    paddingBottom: 0
  },
  paper: {
    backgroundColor: theme.palette.common.white,
    borderRadius: 12,
    minWidth: 340,
    maxWidth: "100%",
    boxShadow: "0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04)",
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 2,
    paddingBottom: 2,
    transition: "none"
  },
  menuItemRoot: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    "&:hover": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  },
  container: {
    paddingTop: 10 // due to line height of Menu 20 but Search line height 32, we remove 6 from 16
  },
  content: {},
  header: {
    display: "flex",
    alignItems: "center"
  },
  headerTitle: {
    marginLeft: 2,
    fontStyle: 'normal',
    fontWeight: 500,
    fontSize: '14px',
    lineHeight: '20px'
  },
  left: {
    display: "flex",
    alignItems: "center"
  },
  right: {
    display: "flex",
    flex: 1,
    alignItems: "center",
    justifyContent: "flex-end"
  },
  breadcrumbContainer: {
    display: "flex",
    alignItems: "center",
    padding: 0,
    minWidth: 0,
    "&:hover": {
      background: "transparent"
    }
  },
  faded: {
    color: "#737372"
  }
}))

export const useMenuItemStyles = makeStyles(() => ({
  menuList: {
    paddingTop: 0,
    paddingBottom: 0
  },
  menuPaper: {
    backgroundColor: palette.grayscale["100"],
    borderRadius: 12,
    minWidth: 250,
    maxWidth: "100%",
    ...shadows.large,
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 2,
    paddingBottom: 2,
    transition: "none"
  },
  menuItemRoot: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    "&:hover": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  },
  content: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  },
  contentWithIcon: {
    display: "flex",
    alignItems: "center"
  },
  optionWithIcon: {
    marginLeft: 8
  },
}));

const useStyles = makeStyles(theme => ({
  wrapper: {
    height: "100%"
  },
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100%"
  },
  content: {
    flex: 1,
    height: '100%',
    overflow: "auto",
    background: palette.grayscale["200"]
  },
  contentBlocked: {
    position: 'relative',
    "&:after": {
      content: '""',
      position: 'absolute',
      left:0,
      right:0,
      top:0,
      bottom:0,
      zIndex:9998,
      background: palette.grayscale["200"],
      opacity: '50%'
    }
  },
  layout: {
    display: 'flex',
    flexDirection: 'row',
    // height: `calc(100vh - ${offset}px)`
  },
  menu: {
    overflow: 'auto',
    flex: 1,
    position: 'relative'
  },
  rightPanel: {
    flex: 1,
    maxWidth: 400,
    borderLeft: '1px solid #f3f4f4',
    position: 'relative',
    overflow: 'auto',
    '@media (max-width: 1024px)': {
      maxWidth: 300,
    }
  },
  relative: {
    position: 'relative',
    width: '100%',
    height: '100%',
    // paddingRight: 12,
    // paddingTop: 12,
    // paddingBottom: 12
  },
  bottomActions: {
    width: '100%'
  },
  compositeBtnContainer: {
    paddingLeft: 16,
    paddingRight: 20
  },
  compositeBtnLayout: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  compositeBtnExtend: {
    flex: 1,
    textAlign: 'center'
  },
  compositeBtnCountLabel: {
    marginLeft: 8,
    color: fade(colors.leviee.main.white, 0.72),
    flex: '1'
  },
  compositeBtnSidebar: {
    display: 'flex',
    alignItems: 'center'
  },
  compositeBtnDivider: {
    borderLeft: `1px solid ${fade(colors.leviee.main.white, 0.12)}`,
    width: 1,
    height: 48
  },
  compositeBtnPriceLabel: {
    paddingLeft: 20
  },
  compositeSecondaryBtn: {
    marginLeft: theme.spacing(1)
  }
}));

export default useStyles;
