import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../../../styles/theme';
import { fade } from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  content: {
    padding: theme.spacing(2),
    minHeight: '100vh'
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    justifyContent: 'space-between'
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  layout: {},
  img: {
    height: 180,
    width: '100%',
    objectFit: 'cover',
    borderRadius: 4,
  },
  imgError: {
    height: 0,
    width: 0,
    display: 'none'
  },
  text: {},
  name: {
    marginTop: 8,
    whiteSpace: 'break-spaces'
  },
  description: {
    marginTop: 8,
    color: colors.leviee.greyscale.darkGray,
    whiteSpace: 'normal'
  },
  pricing: {
    marginTop: 12,
    display: 'flex',
    flexDirection: 'row',
    '& > p:not(:first-child)': {
      marginLeft: theme.spacing(1)
    }
  },
  price: {
    color: colors.leviee.greyscale.darkGray
  },
  regularPrice: {
    color: colors.leviee.greyscale.midGray,
    textDecoration: 'line-through'
  },
  action: {
    flex: 1,
    textAlign: 'right'
  },
  notesDescription: {
    marginTop: 2,
    color: 0.48, //TODO set back the -> fade(colors.leviee.main.dark, 0.48),
    whiteSpace: 'normal'
  },
  remarksDescription: {
    marginTop: 2,
    color: fade(colors.leviee.main.dark, 0.48)
  },
  remarkDescription: {
    color: colors.leviee.greyscale.darkGray
  },
  remarks: {},
  remarkList: {
    marginTop: 8,
  },
  orderActionLayout: {
    width: '100%'
  },
  divider: {
    borderBottom: `1px solid ${fade(colors.leviee.main.dark, 0.06)}`,
    marginTop: 8,
    marginBottom: 8
  },
  orderQtdLayout: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(2, 2, 1, 2)
  },
  predefinedNotes: {
    marginTop: 12
  },
  predefinedNotesItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 48
  },
  notesInput: {
    marginTop: 12
  }
}));

export default useStyles;
