import { makeStyles } from '@material-ui/core/styles';
import { drawerModalStyle } from '../../../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 0,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minHeight: '100%',
    bottom: 0,
    display: "flex",
    flexDirection: "column"
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    background: theme.palette.common.white,
    flex: 1,
    padding: 12,
    position: 'relative',
    width: '100%',
    // height: `calc(100% - 48px)`
  }
}));

export default useStyles;
