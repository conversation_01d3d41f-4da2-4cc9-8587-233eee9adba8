import React, {useEffect, useState} from 'react';
import TextField from '@material-ui/core/TextField';
import { Panel } from '../../Panel';
import { withTranslation } from '../../../../../i18n';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Checkbox from '@material-ui/core/Checkbox';
import Button from '@material-ui/core/Button';
import Grid from '@material-ui/core/Grid';
import InputLabel from '@material-ui/core/InputLabel';
import Select from '@material-ui/core/Select';
import MenuItem from '@material-ui/core/MenuItem';
import FormControl from '@material-ui/core/FormControl';
import Dropzone from 'react-dropzone';
import {uploadMenuImage} from '../../../../api';
import Typography from '@material-ui/core/Typography';
import { CloseIcon, TrashIcon20 } from "../../../../utils/icons";
import IconButton from '@material-ui/core/IconButton';
import Compressor from "compressorjs";
import {Paragraph} from '../../../Text';
import {Input} from '@material-ui/core';
import Chip from '@material-ui/core/Chip';
import useStyles from './styles';
import Field from "../../../form/Field";
import isEmpty from "../../../../utils/isEmpty";

const languages = [
  'de', 'en', 'zh'
];

const taxCategories = [{
  value: "NORMAL",
  i18nKey: "menu-editor-form-tax-category-field-option-normal"
}, {
  value: "REDUCED",
  i18nKey: "menu-editor-form-tax-category-field-option-reduced"
}];

const itemCategories = [{
  value: "DISH",
  i18nKey: "menu-editor-form-category-field-option-dish"
}, {
  value: "BEVERAGE",
  i18nKey: "menu-editor-form-category-field-option-beverage"
}];

const partners = [{
  value: "UBER_EATS",
  i18nKey: "UberEats"
}, {
  value: "WOLT",
  i18nKey: "Wolt"
}, {
  value: "LIEFERANDO",
  i18nKey: "Lieferando"
}];

const printerCategory = [{
  value: "BAR",
  i18nKey: "menu-editor-form-printer-category-field-option-bar"
}, {
  value: "KITCHEN",
  i18nKey: "menu-editor-form-printer-category-field-option-kitchen"
}];

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 10.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const data = {
  numeration: "",
  name: "",
  nameI18n: {},
  description: "",
  descriptionI18n: {},
  internalName: "",
  internalNameI18n: {},
  unitPrice: 0,
  category: "DISH",
  printerCategory: "KITCHEN",
  volume: "",
  thumbnailUrl: "",
  ongoing: false,
  restricted: false,
  disabled: false,
  hidden: false,
  hasHighQuantitySelector: false,
  remarkAnnotations: [],
  menuId: "",
  sharedMenuId: "",
  options: [],
  extras: [],
  tagIds: [],
  dineTaxCategory: null,
  takeawayTaxCategory: null,
  order: null,
  favorite: false,
  partnerPrices: null,
  alcoholPercentage: 0,
  partnerIds: ['UBER_EATS', 'WOLT', 'LIEFERANDO'],
  ongoingExtrasQuantityMax: 99
};

const MenuItemPanel = ({ t, open, close, values, restaurantCode, menus = [], extras = [], options = [], tags = [], remarks = [], submit }) => {
  const classes = useStyles();

  const selectedMenu = isEmpty(menus) ? "" : menus[0].id;
  const consolidatedData = isEmpty(values) ? data : { ...data, ...values, menuId: values.menuId || selectedMenu };
  const [form, setForm] = useState(consolidatedData);

  const filteredOptions = options.filter(i => !form.options || !form.options.some(e => e.id === i.id ) )
  const filteredExtras = extras.filter(i => !form.extras || !form.extras.some(e => e.id === i.id ) )
  const optionsWithItems = filteredOptions.filter(i => !isEmpty(i.items));
  const extrasWithItems = filteredExtras.filter(i => !isEmpty(i.items));

  const [selectedOption, setOption] = useState('');
  const [selectedExtra, setExtra] = useState('');
  
  const [customizeInternal, setCustomizeInternal] = useState(false);
  
  useEffect(() => {
    if (!isEmpty(values) && !isEmpty(values.internalNameI18n)) {
      setCustomizeInternal(true);
    }
  }, [])

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  
  const onPartnerPriceChange = (e) => {
    let val = e.target.value;
    if (val === "") {
      val = null
    }
    const partnerPrices = { ...(form.partnerPrices ?? {}), [e.target.name]: val }
    setForm({ ...form, partnerPrices })
  };

  const onNameI18nChange = (e) => {
    const nameI18n = { ...form.nameI18n, [e.target.name]: e.target.value };
    setForm({ ...form, nameI18n })
  };

  const onInternalNameI18nChange = (e) => {
    const internalNameI18n = { ...form.internalNameI18n, [e.target.name]: e.target.value };
    setForm({ ...form, internalNameI18n })
  };

  const onDescriptionI18nChange = (e) => {
    const descriptionI18n = { ...form.descriptionI18n, [e.target.name]: e.target.value };
    setForm({ ...form, descriptionI18n })
  };

  const onRemarkAnnotationsChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value })
  };

  const onCheckboxChange = (e) => setForm({ ...form, [e.target.name]: e.target.checked });

  const onSubmit = () => {
    form.name = form.nameI18n.de;
    form.internalName = form.internalNameI18n.de;
    form.description = form.descriptionI18n.de;
    submit(form)
  };

  const duplicate = () => {
    delete form.id
    delete form.code
    delete form.creationTime
    delete form.modificationTime
    delete form.version
    onSubmit()
  }

  const isValid = !!form.nameI18n.de;

  const [uploading, isUploading] = useState(false);

  const sliceText = (val = '', max) => val ? `${val.substring(0, max-1)}${val.length > max ? '...' : ''}` : '';

  const onUpload = (file) => {
    new Compressor(file, {
      quality: 0.6,
      success(result) {
        uploadMenuImage(restaurantCode, result).then(({ data }) => setForm({ ...form, thumbnailUrl: data })).catch(() => {})
      },
      error(err) {}
    });

  };

  const getThumbnailUploader = () => {
    return (
      <Dropzone onDrop={acceptedFiles => !isEmpty(acceptedFiles) && onUpload(acceptedFiles[0])} accept={'image/jpeg, image/png, image/jpg'} maxSize={5000000}>
        {({getRootProps, getInputProps}) => (
          <section className={classes.uploaderWrapper}>
            <div {...getRootProps()} className={classes.uploaderContainer} style={form.thumbnailUrl ? { backgroundImage: `url('${form.thumbnailUrl}')` } : null}>
              <input {...getInputProps()} />
              {!form.thumbnailUrl && <p style={{ whiteSpace: 'break-spaces' }}>{t('menu-editor-form-thumbnail-field-placeholder')}</p>}
            </div>
            {form.thumbnailUrl && (
              <Button
                onClick={() => setForm({ ...form, thumbnailUrl: "" })}
                color="primary"
                size="small"
                disableElevation
                className={classes.clearImage}
                startIcon={<TrashIcon20 />}
              >
                {t('menu-editor-form-thumbnail-field-clear-image-label')}
              </Button>
            )}
          </section>
        )}
      </Dropzone>
    )
  };

  const addOption = () => {
    let options = form.options || [];
    const exists = options.some(i => i.id === selectedOption.id);
    if (!exists) {
      options = options.concat([selectedOption]);
      setForm({ ...form, options  })
    }
    setOption(null);
  };

  const removeOption = (id) => {
    let options = form.options;
    const index = options.findIndex(i => i.id === id);
    options.splice(index, 1);
    setForm({...form, options })
  };

  const addExtra = () => {
    let extras = form.extras || [];
    const exists = extras.some(i => i.id === selectedExtra.id);
    if (!exists) {
      extras = extras.concat([selectedExtra]);
      setForm({ ...form, extras  })
    }
    setExtra(null)
  };

  const removeExtra = (id) => {
    let extras = form.extras;
    const index = extras.findIndex(i => i.id === id);
    extras.splice(index, 1);
    setForm({...form, extras })
  };

  const onOptionChange = (id, e) => {
    let options = form.options;
    const index = options.findIndex(i => i.id === id);
    options[index][e.target.name] = e.target.value;
    setForm({...form, options })
  };

  const onOptionChangeWithLimit = (id, e, min = -99, max = 999) => {
    let options = form.options;
    const index = options.findIndex(i => i.id === id);
    let value = e.target.value;
    if (value < min) value = min;
    if (value > max) value = max;
    options[index][e.target.name] = value;
    setForm({...form, options })
  };

  const onOptionItemChange = (optionId, id, e) => {
    let options = form.options;
    const optionIndex = options.findIndex(i => i.id === optionId);
    const itemIndex = options[optionIndex].items.findIndex(i => i.id === id);
    let value = e.target.value;
    options[optionIndex].items[itemIndex][e.target.name] = value
    setForm({...form, options })
  };

  const onOptionItemChangeWithLimit = (optionId, id, e, min = -99, max = 999) => {
    let options = form.options;
    const optionIndex = options.findIndex(i => i.id === optionId);
    const itemIndex = options[optionIndex].items.findIndex(i => i.id === id);
    let value = e.target.value;
    if (value < min) value = min;
    if (value > max) value = max;
    options[optionIndex].items[itemIndex][e.target.name] = value
    setForm({...form, options })
  };

  const onExtraChangeWithLimit = (id, e, min = -99, max = 999) => {
    let extras = form.extras;
    const index = extras.findIndex(i => i.id === id);
    let value = e.target.value;
    if (value < min) value = min;
    if (value > max) value = max;
    extras[index][e.target.name] = value;
    setForm({...form, extras })
  };

  const onExtraItemChange = (extraId, id, e) => {
    let extras = form.extras;
    const extraIndex = extras.findIndex(i => i.id === extraId);
    const itemIndex = extras[extraIndex].items.findIndex(i => i.id === id);
    let value = e.target.value;
    extras[extraIndex].items[itemIndex][e.target.name] = value
    setForm({...form, extras })
  };

  const onExtraItemChangeWithLimit = (extraId, id, e, min = -99, max = 999) => {
    let extras = form.extras;
    const extraIndex = extras.findIndex(i => i.id === extraId);
    const itemIndex = extras[extraIndex].items.findIndex(i => i.id === id);
    let value = e.target.value;
    if (value < min) value = min;
    if (value > max) value = max;
    extras[extraIndex].items[itemIndex][e.target.name] = value
    setForm({...form, extras })
  };

  return (
    <Panel open={open} close={close} variant="temporary">
      <div className={classes.content}>
        <div className={classes.header}>
          <Typography variant="h3">{form.id ? sliceText(form.name, 16) : t('menu-editor-actions-create-menu-item')}</Typography>
          <IconButton classes={{ root: classes.iconButtonRoot }} onClick={close}>
            <CloseIcon />
          </IconButton>
        </div>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <InputLabel shrink>{t('menu-editor-form-thumbnail-field-label')}</InputLabel>
            {getThumbnailUploader()}
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.formControl}>
              <InputLabel id="demo-simple-select-label">{t('menu-editor-form-menu-field-label')}</InputLabel>
              <Select
                name="menuId"
                value={form.menuId || selectedMenu}
                onChange={onChange}
              >
                {menus.map(({ id, title }) => (
                  <MenuItem key={id} value={id}>{title}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <Field
              className={classes.field}
              InputLabelProps={{ shrink: true }}
              label={t('menu-editor-form-numeration-field-label')}
              value={form.numeration}
              name="numeration"
              onChange={onChange}
            />
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          {/*<Grid item xs={12}>*/}
          {/*  <TextField*/}
          {/*    className={classes.field}*/}
          {/*    InputLabelProps={{ shrink: true }}*/}
          {/*    label={t('menu-editor-form-title-field-label')}*/}
          {/*    value={form.name}*/}
          {/*    name="name"*/}
          {/*    onChange={onChange}*/}
          {/*  />*/}
          {/*</Grid>*/}
          {languages.map(language => (
            <Grid item xs={12}>
              <Field
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={`${t(`menu-editor-form-title-i18n-field-label`, { language: language.toUpperCase() })}${language === 'de' ? '*' : ''}`}
                value={form.nameI18n[language]}
                name={language}
                onChange={onNameI18nChange}
              />
            </Grid>
          ))}
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          {/*<Grid item xs={12}>*/}
          {/*  <TextField*/}
          {/*    className={classes.field}*/}
          {/*    InputLabelProps={{ shrink: true }}*/}
          {/*    label={t('menu-editor-form-internal-title-field-label')}*/}
          {/*    value={form.internalName}*/}
          {/*    name="internalName"*/}
          {/*    onChange={onChange}*/}
          {/*  />*/}
          {/*</Grid>*/}
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={customizeInternal}
                  onChange={(e) => setCustomizeInternal(e.target.checked)}
                  name="customizeInternal"
                  color="primary"
                />
              }
              label={t('menu-editor-form-customize-internal-field-label')}
            />
          </Grid>
          {customizeInternal && languages.map(language => (
            <Grid item xs={12}>
              <Field
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={t(`menu-editor-form-internal-title-i18n-field-label`, { language: language.toUpperCase() })}
                value={form.internalNameI18n[language]}
                name={language}
                onChange={onInternalNameI18nChange}
              />
            </Grid>
          ))}
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          {/*<Grid item xs={12}>*/}
          {/*  <TextField*/}
          {/*    className={classes.field}*/}
          {/*    InputLabelProps={{ shrink: true }}*/}
          {/*    label={t('menu-editor-form-description-field-label')}*/}
          {/*    value={form.description}*/}
          {/*    name="description"*/}
          {/*    onChange={onChange}*/}
          {/*  />*/}
          {/*</Grid>*/}
          {languages.map(language => (
            <Grid item xs={12}>
              <Field
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={t(`menu-editor-form-description-i18n-field-label`, { language: language.toUpperCase() })}
                value={form.descriptionI18n[language]}
                name={language}
                onChange={onDescriptionI18nChange}
                multiline
              />
            </Grid>
          ))}
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.formControl} style={{ width: "100%" }}>
              <Select
                name="remarkAnnotations"
                value={form.remarkAnnotations || []}
                onChange={onRemarkAnnotationsChange}
                multiple
                disabled={isEmpty(remarks)}
                input={<Field id="select-multiple-chip" select label={t("order-item-details-remarks-section-descriptions")} />}
                MenuProps={MenuProps}
                renderValue={(selected) => (
                  <div className={classes.chips}>
                    {selected.map((value) => {
                      const remark = remarks.find(t => t.annotation === value) || {}
                      return (
                        <Chip
                          key={value}
                          label={`${remark.annotation}. ${t(remark.description)}`}
                          className={classes.chip}
                          style={{ whiteSpace: "break-spaces" }}
                        />
                      )
                    })}
                  </div>
                )}
              >
                {remarks.map(remark => (
                  <MenuItem value={remark.annotation} key={remark.annotation}>
                    <div className={classes.tag}>
                      <Paragraph>{`${remark.annotation}. ${t(remark.description)}`}</Paragraph>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.formControl}>
              <Select
                name="partnerIds"
                value={form.partnerIds || []}
                onChange={onChange}
                multiple
                input={<Field id="select-multiple-partner-ids-chip" select label={t('delivery-partners')} />}
                MenuProps={MenuProps}
                renderValue={(selected) => (
                  <div className={classes.chips}>
                    {selected.map((value) => {
                      const partner = partners.find(t => t.value === value) || {}
                      return (
                        <Chip
                          key={value}
                          label={t(partner.i18nKey)}
                          className={classes.chip}
                        />
                      )
                    })}
                  </div>
                )}
              >
                {partners.map(partner => (
                  <MenuItem value={partner.value} key={partner.value}>
                    <div className={classes.tag}>
                      <Paragraph>{t(partner.i18nKey)}</Paragraph>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <TextField
              className={classes.field}
              InputLabelProps={{ shrink: true }}
              label={t('menu-editor-form-price-field-label')}
              name="unitPrice"
              type="number"
              value={form.unitPrice}
              onChange={onChange}
            />
          </Grid>
          {form.partnerIds && form.partnerIds.indexOf('UBER_EATS') > -1 && (
            <Grid item xs={12}>
              <TextField
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={`${t('menu-editor-form-price-field-label')} UberEats`}
                name="UBER_EATS"
                type="number"
                value={form.partnerPrices && form.partnerPrices.UBER_EATS ? form.partnerPrices.UBER_EATS : null}
                onChange={onPartnerPriceChange}
              />
            </Grid>
          )}
          {form.partnerIds && form.partnerIds.indexOf('WOLT') > -1 && (
            <Grid item xs={12}>
              <TextField
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={`${t('menu-editor-form-price-field-label')} Wolt`}
                name="WOLT"
                type="number"
                value={form.partnerPrices && form.partnerPrices.WOLT ? form.partnerPrices.WOLT : null}
                onChange={onPartnerPriceChange}
              />
            </Grid>
          )}
          {form.partnerIds && form.partnerIds.indexOf('LIEFERANDO') > -1 && (
            <Grid item xs={12}>
              <TextField
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={`${t('menu-editor-form-price-field-label')} Lieferando`}
                name="LIEFERANDO"
                type="number"
                value={form.partnerPrices && form.partnerPrices.LIEFERANDO ? form.partnerPrices.LIEFERANDO : null}
                onChange={onPartnerPriceChange}
              />
            </Grid>
          )}
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <TextField
              className={classes.field}
              InputLabelProps={{ shrink: true }}
              label={t('menu-editor-form-order-field-label')}
              value={form.order}
              name="order"
              onChange={onChange}
              type="number"
            />
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.formControl}>
              <InputLabel id="demo-simple-select-label">{t('menu-editor-form-printer-category-field-label')}</InputLabel>
              <Select
                name="printerCategory"
                value={form.printerCategory}
                onChange={onChange}
              >
                {printerCategory.map(category => (
                  <MenuItem key={category.value} value={category.value}>{t(category.i18nKey)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl className={classes.formControl}>
              <InputLabel id="demo-simple-select-label">{t('menu-editor-form-category-field-label')}</InputLabel>
              <Select
                name="category"
                value={form.category}
                onChange={onChange}
              >
                {itemCategories.map(category => (
                  <MenuItem value={category.value}>{t(category.i18nKey)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          {form.category === "BEVERAGE" && (
            <Grid item xs={12}>
              <TextField
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={t('menu-editor-form-volume-field-label')}
                name="volume"
                value={form.volume}
                onChange={onChange}
              />
            </Grid>
          )}
          {form.category === "BEVERAGE" && (
            <Grid item xs={12}>
              <TextField
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={t('alcohol-percentage')}
                name="alcoholPercentage"
                value={form.alcoholPercentage}
                onChange={onChange}
                type="number"
              />
            </Grid>
          )}
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.formControl}>
              <InputLabel id="demo-simple-select-label">{t('menu-editor-form-dine-tax-category-field-label')}</InputLabel>
              <Select
                name="dineTaxCategory"
                value={form.dineTaxCategory}
                onChange={onChange}
              >
                {taxCategories.map(category => (
                  <MenuItem value={category.value}>{t(category.i18nKey)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl className={classes.formControl}>
              <InputLabel id="demo-simple-select-label">{t('menu-editor-form-takeaway-tax-category-field-label')}</InputLabel>
              <Select
                name="takeawayTaxCategory"
                value={form.takeawayTaxCategory}
                onChange={onChange}
              >
                {taxCategories.map(category => (
                  <MenuItem value={category.value}>{t(category.i18nKey)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.formControl}>
              <InputLabel id="demo-simple-select-label">{t('menu-editor-form-tags-field-label')}</InputLabel>
              <Select
                name="tagIds"
                value={form.tagIds || []}
                onChange={onChange}
                multiple
                input={<Input id="select-multiple-chip" />}
                MenuProps={MenuProps}
                renderValue={(selected) => (
                  <div className={classes.chips}>
                    {selected.map((value) => {
                      const tag = tags.find(t => t.id === value) || {}
                      return (
                        <Chip
                          key={value}
                          label={tag.label}
                          icon={<img className={classes.tagImg} src={`/icons/tags/${tag.identifier}.svg`} alt={tag.identifier} />}
                          className={classes.chip}
                        />
                      )
                    })}
                  </div>
                )}
              >
                {!isEmpty(tags) && tags.map(tag => (
                  <MenuItem value={tag.id} key={tag.id}>
                    <div className={classes.tag}>
                      <img className={classes.tagImg} src={`/icons/tags/${tag.identifier}.svg`} alt={tag.identifier} />
                      <Paragraph>{tag.label}</Paragraph>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <Field
              className={classes.field}
              InputLabelProps={{ shrink: true }}
              label={t('additions-max')}
              name="ongoingExtrasQuantityMax"
              type="number"
              value={form.ongoingExtrasQuantityMax}
              onChange={onChange}
            />
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.ongoing}
                  onChange={onCheckboxChange}
                  name="ongoing"
                  color="primary"
                />
              }
              label={t('menu-editor-form-ongoing-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.restricted}
                  onChange={onCheckboxChange}
                  name="restricted"
                  color="primary"
                />
              }
              label={t('menu-editor-form-restricted-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.hidden}
                  onChange={onCheckboxChange}
                  name="hidden"
                  color="primary"
                />
              }
              label={t('menu-editor-form-hidden-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.disabled}
                  onChange={onCheckboxChange}
                  name="disabled"
                  color="primary"
                />
              }
              label={t('menu-editor-form-disabled-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.favorite}
                  onChange={onCheckboxChange}
                  name="favorite"
                  color="primary"
                />
              }
              label={t('menu-editor-form-favourite-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.hasHighQuantitySelector}
                  onChange={onCheckboxChange}
                  name="hasHighQuantitySelector"
                  color="primary"
                />
              }
              label={t('item-ordered-in-high-quantity')}
            />
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.formControl}>
              <InputLabel>{t('menu-editor-form-shared-menu-field-label')}</InputLabel>
              <Select
                name="sharedMenuId"
                value={form.sharedMenuId}
                onChange={onChange}
              >
                <MenuItem key='empty' value={''}>{t('menu-editor-form-shared-menu-field-option-none')}</MenuItem>
                {(menus || []).filter(i => i.id !== form.menuId || i.id !== selectedMenu).map(({ id, title }) => (
                  <MenuItem key={id} value={id}>{title}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </div>
      <div className={classes.content}>
        <div className={classes.header}>
          <Typography variant="h3">{t('menu-editor-options-section')}</Typography>
        </div>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.fullWidth}>
              <InputLabel id="demo-simple-select-label">{t('menu-editor-form-options-field-label')}</InputLabel>
              <Select
                value={selectedOption ? selectedOption.id : ''}
                onChange={e => setOption(optionsWithItems.find(i => i.id === e.target.value))}
                disabled={isEmpty(optionsWithItems)}
              >
                {optionsWithItems.map(({ id, name }) => (
                  <MenuItem key={id} value={id}>{name}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <div className={classes.selectActionBtn}>
              <Button onClick={addOption} disabled={!selectedOption} color="secondary" variant="contained" size="small" disableElevation>
                {t('common-add')}
              </Button>
            </div>
          </Grid>
        </Grid>
        {!isEmpty(form.options) && (
          <Grid container spacing={1} className={classes.fieldGroup}>
            {form.options.map(option  => (
              <>
                <Grid item xs={12}>
                  <Typography>{option.name}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    className={classes.field}
                    InputLabelProps={{ shrink: true }}
                    label={t('menu-editor-form-quantity-field-label')}
                    value={option.qtd}
                    name="qtd"
                    type="number"
                    onChange={e => onOptionChangeWithLimit(option.id, e, 1)}
                  />
                </Grid>
                {!isEmpty(option.items) && option.items.map(item => (
                  <>
                    <Grid item xs={12} className={classes.subItem}>
                      <Typography variant="body2">{item.name}</Typography>
                    </Grid>
                    <Grid item xs={4} className={classes.subItem}>
                      <TextField
                        className={classes.field}
                        InputLabelProps={{ shrink: true }}
                        label={t('menu-editor-form-price-field-label')}
                        name="unitPrice"
                        type="number"
                        value={item.unitPrice}
                        onChange={e => onOptionItemChange(option.id, item.id, e)}
                      />
                    </Grid>
                    <Grid item xs={4} className={classes.subItem}>
                      <TextField
                        className={classes.field}
                        InputLabelProps={{ shrink: true }}
                        label={t('menu-editor-form-min-field-label')}
                        value={item.min}
                        name="min"
                        type="number"
                        onChange={e => onOptionItemChangeWithLimit(option.id, item.id, e, 1)}
                      />
                    </Grid>
                    <Grid item xs={4} className={classes.subItem}>
                      <TextField
                        className={classes.field}
                        InputLabelProps={{ shrink: true }}
                        label={t('menu-editor-form-max-field-label')}
                        value={item.max}
                        name="max"
                        type="number"
                        onChange={e => onOptionItemChangeWithLimit(option.id, item.id, e, 1)}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControl className={classes.formControl}>
                        <InputLabel id="demo-simple-select-label">{t('menu-editor-form-dine-tax-category-field-label')}</InputLabel>
                        <Select
                          name="dineTaxCategory"
                          value={item.dineTaxCategory}
                          onChange={e => onOptionItemChange(option.id, item.id, e)}
                        >
                          {taxCategories.map(category => (
                            <MenuItem value={category.value}>{t(category.i18nKey)}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                      <FormControl className={classes.formControl}>
                        <InputLabel id="demo-simple-select-label">{t('menu-editor-form-takeaway-tax-category-field-label')}</InputLabel>
                        <Select
                          name="takeawayTaxCategory"
                          value={item.takeawayTaxCategory}
                          onChange={e => onOptionItemChange(option.id, item.id, e)}
                        >
                          {taxCategories.map(category => (
                            <MenuItem value={category.value}>{t(category.i18nKey)}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </>
                ))}
                <Grid item xs={12}>
                  <div className={classes.selectActionBtn}>
                    <Button onClick={() => removeOption(option.id)} color="secondary" variant="contained" size="small" disableElevation>
                      {t('common-delete')}
                    </Button>
                  </div>
                </Grid>
              </>
            ))}
          </Grid>
        )}
      </div>
      <div className={classes.content}>
        <div className={classes.header}>
          <Typography variant="h3">{t('menu-editor-extras-section')}</Typography>
        </div>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl className={classes.fullWidth}>
              <InputLabel id="demo-simple-select-label">{t('menu-editor-form-extras-field-label')}</InputLabel>
              <Select
                value={selectedExtra ? selectedExtra.id : ''}
                onChange={e => setExtra(extrasWithItems.find(i => i.id === e.target.value))}
                disabled={isEmpty(extrasWithItems)}
              >
                {extrasWithItems.map(({ id, name }) => (
                  <MenuItem key={id} value={id}>{name}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <div className={classes.selectActionBtn}>
              <Button onClick={addExtra} disabled={!selectedExtra} color="secondary" variant="contained" size="small" disableElevation>
                {t('common-add')}
              </Button>
            </div>
          </Grid>
        </Grid>
        {!isEmpty(form.extras) && (
          <Grid container spacing={1} className={classes.fieldGroup}>
            {form.extras.map(extra  => (
              <>
                <Grid item xs={12}>
                  <Typography>{extra.name}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    className={classes.field}
                    InputLabelProps={{ shrink: true }}
                    label={t('menu-editor-form-max-field-label')}
                    value={extra.max}
                    name="max"
                    type="number"
                    onChange={e => onExtraChangeWithLimit(extra.id, e, 1)}
                  />
                </Grid>
                {!isEmpty(extra.items) && extra.items.map(item => (
                  <>
                    <Grid item xs={12} className={classes.subItem}>
                      <Typography variant="body2">{item.name}</Typography>
                    </Grid>
                    <Grid item xs={4} className={classes.subItem}>
                      <TextField
                        className={classes.field}
                        InputLabelProps={{ shrink: true }}
                        label={t('menu-editor-form-price-field-label')}
                        name="unitPrice"
                        type="number"
                        value={item.unitPrice}
                        onChange={e => onExtraItemChange(extra.id, item.id, e)}
                      />
                    </Grid>
                    <Grid item xs={4} className={classes.subItem}>
                      <TextField
                        className={classes.field}
                        InputLabelProps={{ shrink: true }}
                        label={t('menu-editor-form-max-field-label')}
                        value={item.max}
                        name="max"
                        type="number"
                        onChange={e => onExtraItemChangeWithLimit(extra.id, item.id, e, 1)}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControl className={classes.formControl}>
                        <InputLabel id="demo-simple-select-label">{t('menu-editor-form-dine-tax-category-field-label')}</InputLabel>
                        <Select
                          name="dineTaxCategory"
                          value={item.dineTaxCategory}
                          onChange={e => onExtraItemChange(extra.id, item.id, e)}
                        >
                          {taxCategories.map(category => (
                            <MenuItem value={category.value}>{t(category.i18nKey)}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                      <FormControl className={classes.formControl}>
                        <InputLabel id="demo-simple-select-label">{t('menu-editor-form-takeaway-tax-category-field-label')}</InputLabel>
                        <Select
                          name="takeawayTaxCategory"
                          value={item.takeawayTaxCategory}
                          onChange={e => onExtraItemChange(extra.id, item.id, e)}
                        >
                          {taxCategories.map(category => (
                            <MenuItem value={category.value}>{t(category.i18nKey)}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </>
                ))}
                <Grid item xs={12}>
                  <div className={classes.selectActionBtn}>
                    <Button onClick={() => removeExtra(extra.id)} color="secondary" variant="contained" size="small" disableElevation>
                      {t('common-delete')}
                    </Button>
                  </div>
                </Grid>
              </>
            ))}
          </Grid>
        )}
      </div>
      <div className={classes.content}>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <div className={classes.actions}>
              {values.id && (
                <Button onClick={duplicate} disabled={!isValid} color="primary" size="small" disableElevation>
                  {t('common-duplicate')}
                </Button>
              )}
              <Button onClick={close} color="primary" size="small" disableElevation>
                {t('common-close')}
              </Button>
              <Button onClick={onSubmit} disabled={!isValid} color="secondary" variant="contained" size="small" disableElevation>
                {t('common-save')}
              </Button>
            </div>
          </Grid>
        </Grid>
      </div>
    </Panel>
  )
};

export default withTranslation('common')(MenuItemPanel);
