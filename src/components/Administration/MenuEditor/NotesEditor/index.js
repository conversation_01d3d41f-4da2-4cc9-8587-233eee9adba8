import React, {useEffect, useState} from "react";
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableBody from '@material-ui/core/TableBody';
import Table from '@material-ui/core/Table';
import {withTranslation} from '../../../../../i18n';
import useStyles from "./styles";
import { createNote, resolveNotes, updateNote } from '../../../../api';
import Button from '@material-ui/core/Button';
import NotePanel from './NotePanel';

export const NotesEditor = withTranslation('common')(({ t, restaurantId }) => {
  const classes = useStyles();

  const [notes, setNotes] = useState([]);

  const [note, setNote] = useState(null);

  const fetchNotes = () => {
    resolveNotes(restaurantId)
      .then(({ data = [] }) => {
        setNotes(data);
      })
      .catch(() => {});
  };

  useEffect(() => {
    fetchNotes();
  }, []);

  const submitNote = (form) => {
    const call = form.id ? updateNote : createNote;
    if (form.id === "") {
      delete form.id;
    }
    form.restaurantId = restaurantId
    call(form)
      .then(() => setNote(null))
      .then(() => {
        fetchNotes();
      })
      .catch(() => {});
  };

  const isNoteSelected = id => note && note.id === id;

  return (
    <>
      <div className={classes.actions}>
        <Button classes={{ root: classes.buttonRoot }} color="primary" onClick={() => setNote({ id: "" })} >
          {t('menu-editor-actions-create-note')}
        </Button>
      </div>
      <TableContainer>
        <Table stickyHeader className={classes.table} aria-label="menu editor table">
          <TableHead>
            <TableRow>
              <TableCell>{t('menu-editor-table-header-title')}</TableCell>
              <TableCell>{t('menu-editor-table-header-type')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {notes.map(r => {
              const { id, name, type = "ORDER" } = r;

              return (
                <>
                  <TableRow key={id} hover selected={isNoteSelected(id)} onClick={() => setNote(r)} className={classes.clickable}>
                    <TableCell align="left">{name}</TableCell>
                    <TableCell align="left">{t(`note-type-${(type ?? "ORDER").toLowerCase()}`)}</TableCell>
                  </TableRow>
                </>
              )
            })}
          </TableBody>
        </Table>
      </TableContainer>
      {note && <NotePanel open={note} close={() => setNote(null)} values={note} submit={submitNote} />}
    </>
  );
});
