import { makeStyles } from '@material-ui/core/styles';

const drawerWidth = 240;

const useStyles = makeStyles(theme => ({
  content: {
    padding: theme.spacing(2)
  },
  actions: {
    textAlign: 'right',
    '& > button': {
      marginLeft: 8
    }
  },
  page: {
    padding: theme.spacing(2),
    minHeight: `calc(100vh - 49px)`
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    justifyContent: 'space-between'
  },
  toolbox: {
    marginLeft: theme.spacing(2)
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  spacing: {
    flex: 1
  },
  box: {
    padding: theme.spacing(1)
  },
  info: {
    marginTop: theme.spacing(2),
    overflow: 'hidden'
  },
  cursor: {
    cursor: 'pointer'
  },
  taken: {
    '&&': {
      background: '#FFBCBC'
    }
  },
  selected: {
    '&&': {
      border: '2px solid #dbdede'
    }
  },
  requestingPayment: {
    '&&': {
      background: '#FEF7EB',
      border: '2px solid #fde7a1'
    }
  },
  infoHeader: {
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
  },
  data: {
    padding: theme.spacing(2),
    display: 'flex',
    flexDirection: 'column'
  },
  field: {
    width: '100%'
  },
  capitalize: {
    textTransform: 'capitalize'
  },
  fieldGroup: {
    paddingBottom: theme.spacing(4)
  }
}));

export default useStyles;
