import React, {useState} from 'react';
import TextField from '@material-ui/core/TextField';
import { Panel } from '../../../Panel';
import { withTranslation } from '../../../../../../i18n';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Checkbox from '@material-ui/core/Checkbox';
import Button from '@material-ui/core/Button';
import Grid from '@material-ui/core/Grid';
import useStyles from './styles';
import Typography from '@material-ui/core/Typography';
import IconButton from '@material-ui/core/IconButton';
import { CloseIcon } from '../../../../../utils/icons';
import FormControl from '@material-ui/core/FormControl';
import {FormGroup, FormLabel} from '@material-ui/core';
import isEmpty from "../../../../../utils/isEmpty";

const languages = [
  'de', 'en', 'zh'
];

const data = {
  name: "",
  nameI18n: {},
  description: "",
  descriptionI18n: {},
  internalName: "",
  internalNameI18n: {},
  restricted: false,
  disabled: false,
  hidden: false,
  collapsed: false,
  max: 99,
  order: null,
  rounds: 0,
  // base applies only for ongoing case, where base items should not be calculated in the qtd weather
  // user selected anything or not
  base: false
};

const ExtraPanel = ({ t, open, close, printers = [], values, submit }) => {
  const classes = useStyles();

  const consolidatedData = isEmpty(values) ? data : { ...data, ...values };
  const [form, setForm] = useState(consolidatedData);

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const onNameI18nChange = (e) => {
    const nameI18n = { ...form.nameI18n, [e.target.name]: e.target.value };
    setForm({ ...form, nameI18n })
  };

  const onInternalNameI18nChange = (e) => {
    const internalNameI18n = { ...form.internalNameI18n, [e.target.name]: e.target.value };
    setForm({ ...form, internalNameI18n })
  };

  const onDescriptionI18nChange = (e) => {
    const descriptionI18n = { ...form.descriptionI18n, [e.target.name]: e.target.value };
    setForm({ ...form, descriptionI18n })
  };

  const onCheckboxChange = (e) => setForm({ ...form, [e.target.name]: e.target.checked });

  const onSubmit = () => {
    form.name = form.nameI18n.de;
    form.internalName = form.internalNameI18n.de;
    form.description = form.descriptionI18n.de;
    submit(form)
  };

  const isValid = !!form.nameI18n.de;

  const sliceText = (val = '', max) => val ? `${val.substring(0, max-1)}${val.length > max ? '...' : ''}` : '';

  const [customizeInternal, setCustomizeInternal] = useState(false);

  const togglePrinterSelection = (e) => {
    if (e.target.checked) {
      setForm({ ...form, printerIds: (form.printerIds || []).concat([e.target.name]) })
    } else {
      setForm({ ...form, printerIds:(form.printerIds || []).filter(i => i !== e.target.name) })
    }
  }

  return (
    <Panel open={open} close={close} variant="temporary">
      <div className={classes.content}>
        <div className={classes.header}>
          <Typography variant="h3">{form.id ? sliceText(form.name, 16) : t('menu-editor-actions-create-extra')}</Typography>
          <IconButton classes={{ root: classes.iconButtonRoot }} onClick={close}>
            <CloseIcon />
          </IconButton>
        </div>
        <Grid container spacing={1} className={classes.fieldGroup}>
          {/*<Grid item xs={12}>*/}
          {/*  <TextField*/}
          {/*    className={classes.field}*/}
          {/*    InputLabelProps={{ shrink: true }}*/}
          {/*    label={t('menu-editor-form-name-field-label')}*/}
          {/*    value={form.name}*/}
          {/*    name="name"*/}
          {/*    onChange={onChange}*/}
          {/*  />*/}
          {/*</Grid>*/}
          {languages.map(language => (
            <Grid item xs={12}>
              <TextField
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={`${t(`menu-editor-form-name-i18n-field-label`, { language: language.toUpperCase() })}${language === 'de' ? '*' : ''}`}
                value={form.nameI18n[language]}
                name={language}
                onChange={onNameI18nChange}
              />
            </Grid>
          ))}
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          {/*<Grid item xs={12}>*/}
          {/*  <TextField*/}
          {/*    className={classes.field}*/}
          {/*    InputLabelProps={{ shrink: true }}*/}
          {/*    label={t('menu-editor-form-internal-name-field-label')}*/}
          {/*    value={form.internalName}*/}
          {/*    name="internalName"*/}
          {/*    onChange={onChange}*/}
          {/*  />*/}
          {/*</Grid>*/}
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={customizeInternal}
                  onChange={(e) => setCustomizeInternal(e.target.checked)}
                  name="customizeInternal"
                  color="primary"
                />
              }
              label={t('menu-editor-form-customize-internal-field-label')}
            />
          </Grid>
          {customizeInternal && languages.map(language => (
            <Grid item xs={12}>
              <TextField
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={t(`menu-editor-form-internal-name-i18n-field-label`, { language: language.toUpperCase() })}
                value={form.internalNameI18n[language]}
                name={language}
                onChange={onInternalNameI18nChange}
              />
            </Grid>
          ))}
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          {/*<Grid item xs={12}>*/}
          {/*  <TextField*/}
          {/*    className={classes.field}*/}
          {/*    InputLabelProps={{ shrink: true }}*/}
          {/*    label={t('menu-editor-form-description-field-label')}*/}
          {/*    value={form.description}*/}
          {/*    name="description"*/}
          {/*    onChange={onChange}*/}
          {/*  />*/}
          {/*</Grid>*/}
          {languages.map(language => (
            <Grid item xs={12}>
              <TextField
                className={classes.field}
                InputLabelProps={{ shrink: true }}
                label={t(`menu-editor-form-description-i18n-field-label`, { language: language.toUpperCase() })}
                value={form.descriptionI18n[language]}
                name={language}
                onChange={onDescriptionI18nChange}
              />
            </Grid>
          ))}
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <TextField
              className={classes.field}
              InputLabelProps={{ shrink: true }}
              label={t('menu-editor-form-max-field-label')}
              value={form.max}
              name="max"
              onChange={onChange}
            />
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControl component="fieldset" className={classes.formControl}>
              <FormLabel component="legend">{t('menu-editor-form-printer-field-label')}</FormLabel>
              <FormGroup>
                {(printers ?? []).filter(p => ['KITCHEN', 'BAR'].indexOf(p.printerCategory) > -1 ).map(p => {
                  const checked = (form.printerIds || []).indexOf(p.id) > -1
                  return (
                    <FormControlLabel
                      key={p.id}
                      control={<Checkbox checked={checked}
                                         onChange={togglePrinterSelection} name={p.id}/>}
                      label={p.label}
                    />
                  )
                })}
              </FormGroup>
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={1} className={classes.fieldGroup}>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.restricted}
                  onChange={onCheckboxChange}
                  name="restricted"
                  color="primary"
                />
              }
              label={t('menu-editor-form-restricted-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.hidden}
                  onChange={onCheckboxChange}
                  name="hidden"
                  color="primary"
                />
              }
              label={t('menu-editor-form-hidden-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.disabled}
                  onChange={onCheckboxChange}
                  name="disabled"
                  color="primary"
                />
              }
              label={t('menu-editor-form-disabled-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.collapsed}
                  onChange={onCheckboxChange}
                  name="collapsed"
                  color="primary"
                />
              }
              label={t('menu-editor-form-collapsed-field-label')}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              className={classes.field}
              InputLabelProps={{ shrink: true }}
              label={t('menu-editor-form-order-field-label')}
              value={form.order}
              name="order"
              onChange={onChange}
              type="number"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              className={classes.field}
              InputLabelProps={{ shrink: true }}
              label={t('menu-editor-form-rounds-field-label')}
              value={form.rounds}
              name="rounds"
              type="number"
              onChange={onChange}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={form.base}
                  onChange={onCheckboxChange}
                  name="base"
                  color="primary"
                />
              }
              label={t('ongoing-item-base')}
            />
          </Grid>
          <Grid item xs={12}>
            <div className={classes.actions}>
              <Button onClick={close} color="primary" size="small" disableElevation>
                {t('common-close')}
              </Button>
              <Button onClick={onSubmit} disabled={!isValid} color="secondary" variant="contained" size="small" disableElevation>
                {t('common-save')}
              </Button>
            </div>
          </Grid>
        </Grid>
      </div>
    </Panel>
  )
};

export default withTranslation('common')(ExtraPanel);
