import React, {useState} from "react";
import useStyles from "./styles";
import { withStyles } from '@material-ui/core';
import InputBase from '@material-ui/core/InputBase';
import { withTranslation } from '../../../../../i18n';
import MomentAdapter from "@material-ui/pickers/adapter/moment";
import moment from "moment";
import { DateRangePicker, DateRangeDelimiter, LocalizationProvider} from '@material-ui/pickers';

const SimpleInput = withStyles((theme) => ({
  root: {
    cursor: 'pointer',
    'label + &': {
      marginTop: theme.spacing(3),
    },
  },
  input: {
    maxWidth: 90,
    borderRadius: 10,
    position: 'relative',
    backgroundColor: theme.palette.background.paper,
    border: '1px solid #ced4da',
    padding: '5px 12px 5px 12px',
    lineHeight: "20px",
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    '&:focus': {
      borderRadius: 4,
      boxShadow: 'none'
    },
  },
}))(InputBase);

export const DateRangeSelector = withTranslation('common')(({ t, dateRange, onChange }) => {
  const classes = useStyles();
  const [isOpen, open] = useState(false);

  return (
    <LocalizationProvider dateLibInstance={moment} dateAdapter={MomentAdapter} locale={"de"}>
      <DateRangePicker
        value={dateRange}
        onChange={onChange}
        animateYearScrolling
        variant="inline"
        format="DD/MM/yyyy"
        margin="normal"
        mask={null}
        autoOk
        disableFuture
        inputVariant={'outlined'}
        clearable={false}
        renderInput={(startProps, endProps) => (
          <React.Fragment>
            <SimpleInput {...startProps} />
            <DateRangeDelimiter>-</DateRangeDelimiter>
            <SimpleInput {...endProps} />
          </React.Fragment>
        )}
      />
    </LocalizationProvider>
  );
});
