import React, { Fragment, useEffect, useState } from "react";
import Button from '@material-ui/core/Button';
import {withTranslation} from '../../../../i18n';
import { useDispatch, useSelector } from "react-redux";
import {
  accountSelectors,
  configurationSelectors,
  restaurantSelectors,
  terminalSelectors
} from "../../../../redux/selectors";
import {noop, permissionIdentifiers} from "../../../utils/const";
import {
  cancelOrder,
  createOrderParticipant,
  printOrder,
  printPayment,
  printTerminalPaymentReceipt,
  refundOrder,
  sendEmailToCustomerToGenerateReceipt,
  updateCustomer, updateDriver
} from "../../../api";
import moment from "moment";
import Loading from "../../Loading";
import {getOrderItemsByStatuses, status} from "../../../utils/categorizeOrderItems";
import clsx from "clsx";
import Typography from "@material-ui/core/Typography";
import Moment from "react-moment";
import {Confirm} from "../../Confirmation";
import { AppBar, ButtonBase, CircularProgress, Drawer } from "@material-ui/core";
import Avatar from "@material-ui/core/Avatar";
import {getUserAvatar, getUserFullName} from "../../../utils/profiles";
import assetPath from "../../../assetPath";
import {Panel} from "../Panel";
import OrderRecoveryEditor from "../Terminal/OrderRecoveryEditor";
import CustomerEditor from "../Terminal/CustomerEditor";
import isEmpty from "../../../utils/isEmpty";
import {formatNumber} from "../../../utils/formatNumber";
import {
  CloseDialogIcon20,
  CollapseIcon,
  CopyIcon20,
  DriverIcon20,
  EditIcon20,
  MoreOptionsIcon,
  ReceiptDarkIcon20,
  RevenueDarkIcon,
  SparksIcon20, TrashIcon20Red,
  UserIcon20
} from "../../../utils/icons";
import MenuItem from "@material-ui/core/MenuItem";
import getAcronym from "../../../utils/getAcronym";
import useStyles, {useExpendableCardStyles, useMenuStyles, useOverviewStyles} from "./styles";
import PaymentStatusBadge from "../../_tags/PaymentStatusBadge";
import { orderTypes, takeawayPartners } from "../../../../redux/constants";
import { appActions, terminalActions } from "../../../../redux/actions";
import PDFReceiptModal from "../../_popup/PDFReceiptModal";
import MoreOptionsButton from "../../_buttons/MoreOptionsButton";
import palette from "../../../../styles/palette";
import ReceiptItem from "../../ReceiptItem";
import CustomMenu from "../../_popup/CustomMenu";
import UpdateOrderItemsTableModal from "../../_popup/UpdateOrderItemsTableModal";
import PartySizePicker from "../../PartySizePicker";
import PickupTimeBadge from "../../_tags/PickupTimeBadge";
import { useRouter } from "next/router";
import typography from "../../../../styles/typography";
import DriverAssignmentModal from "../../_popup/DriverAssignmentModal";
import { LieferandoIcon20, UberEatsIcon20, WoltIcon20 } from "../../../utils/partnerIcons";
import OrderStatusTakeawayBadge from "../../_tags/OrderStatusTakeawayBadge";
import ConfirmationDialog from "../../_popup/ConfirmationDialog";

const partners = [{
  value: "UBER_EATS",
  i18nKey: "UberEats",
  icon: <UberEatsIcon20/>
}, {
  value: "WOLT",
  i18nKey: "Wolt",
  icon: <WoltIcon20/>
}, {
  value: "LIEFERANDO",
  i18nKey: "Lieferando",
  icon: <LieferandoIcon20/>
}];

const CollapseWrapper = ({ overviewClasses, expendableCardClasses, payment, t, children}) => {
  const [open, setOpen] = useState(false);
  const toggle = () => setOpen(!open);
  return (
    <div className={overviewClasses.wrapper} style={{ marginBottom: 0 }}>
      <div className={expendableCardClasses.container} style={{ marginBottom: 0, paddingRight: 0, paddingBottom: 0 }}>
        <div className={expendableCardClasses.header} onClick={toggle}>
          <div className={expendableCardClasses.left}>
            <ReceiptDarkIcon20 />
            <Typography className={expendableCardClasses.headerText}>
              {t('items')}
            </Typography>
            <span className={expendableCardClasses.counterBadge}>{isEmpty(payment?.orderItemIds) ? 0 : payment?.orderItemIds?.length}</span>
          </div>
          <div className={clsx(expendableCardClasses.expandBtn, { [expendableCardClasses.rotate]: !open })}>
            <CollapseIcon />
          </div>
        </div>
      </div>
      {
        open && (
          <div style={{ marginTop: 12 }}>
            {children}
          </div>
        )
      }
    </div>
  )
}

const ReceiptInformation = ({ t, orderId, isOpen, close, callback, preparationTimePicker, topActions = [], bottomActions = [], menuActions = [], tablesById, goBackToFloorplan, showDriverInformation }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const classes = useStyles();
  const overviewClasses = useOverviewStyles();
  const expendableCardClasses = useExpendableCardStyles();
  const menuClasses = useMenuStyles();

  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId, name: restaurantName } = restaurant;

  const { configuration = {} } = useSelector(configurationSelectors.getConfiguration);
  const { frontEndSettings = { resolvePaidItemsUnderPaymentInReceipts: false } } = configuration;
  const { takeawayConfig = {} } = configuration;

  const { allowDriverAssignment } = takeawayConfig;

  const [anchor, setAnchor] = useState(null);
  const openMenu = (e) => setAnchor(e.currentTarget);
  const closeMenu = () => setAnchor(null);

  const [selectedPayment, setSelectedPayment] = useState({ anchor: null, id: null, payment: null });
  const openPaymentMenu = (e, id, payment) => setSelectedPayment({ anchor: e.currentTarget, id, payment });
  const closePaymentMenu = () => setSelectedPayment({ anchor: null, id: null });

  const [customersExpanded, setCustomersExpanded] = useState(false);
  const [detailsExpanded, setDetailsExpanded] = useState(true);
  const [paymentsExpanded, setPaymentsExpanded] = useState(false);
  
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);

  const toggleCustomers = () => setCustomersExpanded(!customersExpanded);
  const toggleDetails = () => setDetailsExpanded(!detailsExpanded);
  const togglePayments = () => setPaymentsExpanded(!paymentsExpanded);

  const { order = {} } = useSelector(terminalSelectors.getOrder);
  const {
    total, creationTime, modificationTime, refundedOrderId, refundOrderId, recoveredOrderId, pickupTime, takeawayDate, type, customer = {}, accountId, items = [], actions,
    notes = '', receiptStripeUrl = '', participants = [], tableCode, tableId, status: orderStatus, offlineItems: orderOfflineItems, paymentStatus, payments, toGo, partnerOrderInfo = {}, partnerId = takeawayPartners.ALLO.key, activity, drivers = [],
    pagerIdentifier
  } = order;
  const { phoneCode = '' } = (partnerOrderInfo || {});

  const { account = {} } = useSelector(accountSelectors.getAccount);
  const { id, email } = account;
  const isInternalUser = email && email.indexOf("@allo.restaurant") !== -1;

  const blockMovingItem = !isEmpty(order.payments) && (order.paymentOption === "SPLIT" || order.paymentOption === "CUSTOM" )
  const isCancelledOrder = orderStatus === "CANCELLED"
  const hasItems = !isEmpty(items)

  const formattedTableNumber = (!tableId || isEmpty(tablesById) || isEmpty(tablesById[tableId]) || !tablesById[tableId].label) ? tableCode : (tablesById[tableId] || {}).label

  const [paymentAmount, setPaymentAmount] = useState(0);
  const [tipAmount, setTipAmount] = useState(0);
  const [isCreatingCustomer, setCreatingCustomer] = useState(false);
  const openCustomerEditor = () => {
    setCreatingCustomer(true);
    setSelectedCustomer({ anchor: null, customer: selectedCustomer.customer })
    setAnchor(null);
  };
  const closeCustomerEditor = () => setCreatingCustomer(false);

  const [selectedCustomer, setSelectedCustomer] = useState({ anchor: null, customer: null });
  const openCustomerMenu = (e, customer) => setSelectedCustomer({ anchor: e.currentTarget, customer });
  const closeCustomerMenu = () => setSelectedCustomer({ anchor: null, customer: null });

  const [refunding, setRefunding] = useState(false);

  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const canRecoverOrder = permissionIds.includes(permissionIdentifiers.CAN_RECOVER_ORDER.value)
  const canRefundOrder = permissionIds.includes(permissionIdentifiers.CAN_REFUND_ORDER.value)

  const [isRecoverPanelOpen, setRecoverPanel] = useState(null)
  const closeRecoverPanel = () => setRecoverPanel(null)
  const openRecoverPanel = () => {
    setRecoverPanel(orderId)
    setAnchor(null)
  }

  const [selectedTableRotation, setTableRotation] = useState(null)
  const closeTableRotation = () => setTableRotation(null)

  const [generatingPdfReceipt, setGeneratingPdfReceipt] = useState(false);
  const [receiptPaymentId, setReceiptPaymentId] = useState(null);
  const closeGeneratingPdfReceipt = () => {
    setGeneratingPdfReceipt(false);
    setReceiptPaymentId(null);
  }

  const [isShowingDriverAssignmentModal, setIsShowingDriverAssignmentModal] = useState(false);
  const [driverExpanded, setDriverExpanded] = useState(true);
  const toggleDriver = () => setDriverExpanded(!driverExpanded);

  const onAssignDriver = (driver) => {
    const driverIds = !isEmpty(driver)? [driver.userIds[0]] : [];
    updateDriver(orderId, driverIds)
      .then(() => {
        setIsShowingDriverAssignmentModal(false)
        dispatch(terminalActions.getOrder(orderId));
      })
      .catch((e) => {
        console.log(e)
      })
  };

  const [selectedDriver, setSelectedDriver] = useState({ anchor: null, driver: null });
  const openDriverMenu = (e, driver) => setSelectedDriver({ anchor: e.currentTarget, driver });
  const closeDriverMenu = () => setSelectedDriver({ anchor: null, driver: null });

  const onEditingDriver = () => {
    setIsShowingDriverAssignmentModal(true);
    closeDriverMenu();
  };

  const onRemoveDriver = () => {
    updateDriver(orderId, [])
      .then(() => {
        closeDriverMenu();
        dispatch(terminalActions.getOrder(orderId));
      })
      .catch((e) => {
        console.log(e)
      })
  }

  const sendPdfReceipt = (email) => {
    closeGeneratingPdfReceipt();
    dispatch(appActions.setNotification(
      receiptPaymentId ? 'sending-selected-payment-receipt-to-email' : 'sending-pdf-receipt-to-email',
      'success',
      { email }))
    sendEmailToCustomerToGenerateReceipt(restaurantId, orderId, receiptPaymentId, email)
      .then(() => {
        dispatch(appActions.setNotification(
          'receipt-generated-for-email',
          'success',
          { email }))
      })
      .catch(() => {
        dispatch(appActions.setNotification(
          'receipt-not-generated-for-email',
          'error',
          { email }))
      })
  }

  const fetch = () => {
    dispatch(terminalActions.getOrder(orderId, true, true));
  }

  useEffect(() => {
    let tipTotal = 0
    let amountTotal = 0;

    (payments ?? []).forEach(p => {
      tipTotal += p.tipAmount
      amountTotal += (p.amount + (p.linkedAmount || 0))
    })

    setTipAmount(tipTotal)
    setPaymentAmount(amountTotal)
  }, [JSON.stringify(payments)])

  useEffect(() => {
    fetch();
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset'
      // reset only when closed and not dine in
      if ((orderStatus !== "OPEN") && (type !== "DINE_IN")) {
        dispatch(terminalActions.reset());
      }
    };
  }, [orderId]);

  const createCustomer = (customer) => {
    if (customer && customer.id) {
      updateCustomer(restaurantId, customer).then(() => {
        fetch();
        closeCustomerEditor();
      }).catch(noop);
    } else {
      createOrderParticipant(restaurantId, orderId, customer).then(() => {
        fetch();
        closeCustomerEditor();
      }).catch(noop);
    }
  }

  const [printingReceipt, setPrintingReceipt] = useState(false);

  const print = (refundable, customerId) => {
    setPrintingReceipt(true);
    printOrder(orderId, refundable, customerId).then(() => {}).catch(() => {
      setPrintingReceipt(false);
    }).finally(() => {
      setTimeout(() => { setPrintingReceipt(false); }, 10000);
    })
  };

  const refund = () => {
    setRefunding(true)
    refundOrder(orderId).then(() => {
      callback();
      setRefunding(false);
      close();
    }).catch(() => {})
  };

  const handleCancelOrder = () => {
    setShowCancelConfirmation(true);
  };

  const confirmCancelOrder = () => {
    cancelOrder(orderId)
      .then(close)
      .catch(noop);

    setShowCancelConfirmation(false);
  };

  const cancelCancelOrder = () => {
    setShowCancelConfirmation(false);
  };

  const handlePrintPayment = (paymentId, refundable) => () => printPayment(orderId, paymentId, refundable).then(() => {
    closePaymentMenu();
  }).catch(() => {})

  const handlePrintTerminalPaymentReceipt = (paymentId) => () => printTerminalPaymentReceipt(orderId, paymentId).then(() => {
    closePaymentMenu();
  }).catch(() => {})

  if (isEmpty(order)) {
    return <Loading />;
  }

  const getTotal = (items) => {
    let total = 0;
    items.forEach(({ total: itemTotal }) => total += itemTotal);
    return total
  };

  const isSameDay = (date1, date2) => moment(date1).isSame(date2, 'day');

  const userItems = (order?.items || []).filter(t => !t.offline);
  const orderCancelled = order?.status === 'CANCELLED'
  const isOngoing = ["CLOSED", "CANCELLED"].indexOf(orderStatus) === -1;
  const { itemsWithStatusByUser } = getOrderItemsByStatuses(userItems, isOngoing ? [status.UNCONFIRMED, status.CONFIRMED, status.COOKING, status.SERVING, status.SERVED, status.PAID, status.CANCELLED] : [status.PAID, status.CANCELLED, status.REMOVED, status.CONFIRMED]); // added confirmed here to show confirmed items in refunded orders
  const offlineItems = orderOfflineItems ?? (order?.items || []).filter(t => !!t.offline);
  const formattedStartTime = <Moment format={'DD.MM.YYYY HH:mm'}>{creationTime}</Moment>;
  const formattedEndTime = <Moment format={isSameDay(creationTime, modificationTime) ? 'HH:mm' : 'DD.MM HH:mm'}>{modificationTime}</Moment>;

  const byCustomerId = (arr = []) => {
    if (isEmpty(arr)) {
      return {}
    }
    return arr.reduce((acc, next) => {
      acc[next.customerId] = next.customer;
      return acc;
    }, {})
  }

  const profiles = {
    [order.customerId]: order.customer || {},
    ...byCustomerId(order.participants || [])
  };

  const guest = !order.customerId;

  const zeroPad = (num, places) => String(num).padStart(places, '0')

  const isDineIn = type === 'DINE_IN'
  const isPickup = type === 'PICKUP'
  const isDelivery = type === 'DELIVERY'
  const isTakeaway = isPickup || isDelivery
  const isExpress = type === 'EXPRESS'

  const orderHasDrivers = order.drivers && !isEmpty(order.drivers)
  const shouldAllowDriverAssignmentBtn = isDelivery && allowDriverAssignment && isEmpty(drivers) && isOngoing
  const showDriverSection = isDelivery && (orderHasDrivers || allowDriverAssignment);

  const getWaiterName = (createdByAccount) => {
    if (isEmpty(createdByAccount)) {
      return ""
    }

    return `${createdByAccount.firstName ?? ""} ${createdByAccount.lastName ?? ""}`
  }

  const getTerminalPaymentReceiptMenuItem = () => {
    const { payment } = (selectedPayment || {})
    const { paymentProvider, isTerminalPayment, isManualPaymentConfirmation } = (payment || {})

    if (paymentProvider === "STRIPE" && isTerminalPayment && !isManualPaymentConfirmation) {
      return (
        <MenuItem onClick={handlePrintTerminalPaymentReceipt(selectedPayment.id)} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t('print-terminal-payment-receipt')}
              </Typography>
            </div>
          </div>
        </MenuItem>
      )
    }

    return null
  }

  const copyDateToClipboard = (value) => {
    navigator.clipboard.writeText(value)
      .then(()=>{
        dispatch(appActions.setNotification("copy-success", "success"))
      })
      .catch(() => {
        dispatch(appActions.setNotification("copy-error", "error"))
      })
  };

  const formatAddress = (address) => {
    const number = address?.number ? `${address.number},` : '';

    return `${address?.street || '-'} ${number} ${address?.zipCode || ''}`
  }

  const renderItemCard = (orderItem) => {
    return (
      <Fragment>
        <ReceiptItem
          isReceipt
          isPreview
          description={`No: ${orderItem.qtd} - ${orderItem.unitPrice}€`}
          {...orderItem}
          orderType={type}
          orderStatus={orderStatus}
          orderTableId={order.tableId}
        />
        {!isEmpty(orderItem.nestedOrderItems) && (
          <div className={classes.nestedItems}>
            {orderItem.nestedOrderItems.map((nItem, index) => (
              <ReceiptItem
                isReceipt
                isPreview
                key={nItem.id}
                nestedCount={index + 1}
                code={nItem.code}
                id={nItem.id}
                orderId={order.id}
                {...nItem}
                description={`No: ${nItem.qtd} - ${nItem.unitPrice}€`}
                status={nItem.status}
                blockMovingItem={blockMovingItem}
              />
            ))}
          </div>
        )}
      </Fragment>
    )
  }

  const renderItemsForPayment = (payment) => {
    const { orderItemIds = [] } = payment;

    if (orderItemIds.length === 0) return null; // If no item then no need to render
    return (
      <CollapseWrapper
        t={t}
        payment={payment}
        expendableCardClasses={expendableCardClasses}
        overviewClasses={overviewClasses}
      >
        <Fragment>
          {orderItemIds.map(id => {
            const orderItem = order.items.find(i => i.id === id);
            if (!orderItem) return null; // No item no render.
            return (
              <div style={{ marginLeft: 12 }}>
                {renderItemCard(orderItem)}
              </div>
            )
          })}
        </Fragment>
      </CollapseWrapper>
    )
  }



  const getDriverInformation = () => {
    if (showDriverSection) {
      return (
        <div className={overviewClasses.wrapper}>
          <div className={expendableCardClasses.container}>
            <div className={expendableCardClasses.header} onClick={toggleDriver}>
              <div className={expendableCardClasses.left}>
                <DriverIcon20 color={palette.grayscale["500"]}/>
                <Typography className={expendableCardClasses.headerText}>
                  {t("drivers")}
                </Typography>
                <span
                  className={expendableCardClasses.counterBadge}>{isEmpty(drivers) ? 0 : drivers.length}
                </span>
              </div>
              <div className={clsx(expendableCardClasses.expandBtn, { [expendableCardClasses.rotate]: !driverExpanded })}>
                <CollapseIcon />
              </div>
            </div>
            {driverExpanded && shouldAllowDriverAssignmentBtn && (
              <div className={expendableCardClasses.itemCard}>
                <div className={expendableCardClasses.customerHeader}>
                  <div className={expendableCardClasses.left}>
                    <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"]}}>
                      {t("no-driver-assigned")}
                    </Typography>
                  </div>

                  <div className={expendableCardClasses.right}>
                    <ButtonBase
                      style={{ border: `1px solid ${palette.grayscale.border}`, padding: "6px 8px", borderRadius: 10 }}
                      onClick={() => setIsShowingDriverAssignmentModal(true)}
                    >
                      <Typography style={{ ...typography.body.regular }}>{t("assign-driver")}</Typography>
                    </ButtonBase>
                  </div>
                </div>
              </div>
            )}
            {driverExpanded && !isEmpty(drivers) && drivers.map(driver => {
              const resolvedName = (driver.firstName || driver.lastName)? `${driver.firstName ?? ""} ${driver.lastName ?? ""}` : driver.email? driver.email : driver.phone
              return (
                <div className={expendableCardClasses.itemCard}>
                  <div className={expendableCardClasses.customerHeader}>
                    <div className={expendableCardClasses.left}>
                      <Avatar
                        alt={`${driver.firstName ?? ""} ${driver.lastName ?? ""}`}
                        src={driver.pictureUrl ?? driver.avatar}
                        classes={{ root: expendableCardClasses.customerAvatar }}
                        defaultValue="allO"
                      >
                        {getAcronym(`${driver.firstName ?? ""} ${driver.lastName ?? ""}`)}
                      </Avatar>
                      <Typography className={expendableCardClasses.customerFullName}>
                        {resolvedName}
                      </Typography>
                    </div>
                    {driver.externalId && (
                      <div className={expendableCardClasses.right}>
                        {partners.find(partner => partner.value === order.partnerId)?.icon ?? null}
                      </div>
                    )}
                    {!driver.externalId && isOngoing && (
                      <div className={expendableCardClasses.right}>
                        <Button variant="outlined"
                                className={clsx(classes.iconButton, expendableCardClasses.paymentsMenuButton)}
                                onClick={(e) => openDriverMenu(e, driver)} disableRipple>
                          <MoreOptionsIcon />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )
    }
  };

  return (
    <Drawer
      variant="temporary"
      anchor="right"
      open={isOpen}
      onClose={close}
      className={clsx(classes.drawer)}
      classes={{
        paper: clsx({ [classes.drawerOpen]: open, [classes.drawerClose]: !open })
      }}
    >
      <div className={classes.drawerContent}>
        <AppBar
          position="static"
          elevation={0}
          className={clsx(classes.appBar)}
          color="inherit"
          data-transform="translate"
        >
          <div className={classes.headerContainer} style={{ overflow: "auto" }}>
            <div className={classes.headerLeft} style={{ overflow: "auto" }}>
              <Typography className={classes.bodyMediumTypography}>
                #{order.number ? zeroPad(order.number, 3) : '-'}
              </Typography>
              {isTakeaway && (
                <div style={{ marginLeft: 4 }}>
                  <PickupTimeBadge orderType={type} date={takeawayDate} time={pickupTime} partnerId={partnerId} />
                </div>
              )}
              {type === orderTypes.EXPRESS.key && (
                <div style={{ marginLeft: 4 }}>
                  <PaymentStatusBadge status={paymentStatus} />
                </div>
              )}
            </div>
            <div className={classes.headerRight}>
              {isOngoing && (
                <Button variant="outlined" className={classes.iconButton} onClick={() => print(false, order.customerId)} disableRipple disabled={printingReceipt} style={{ opacity: printingReceipt ? 0.5 : 1 }}>
                  <Typography className={classes.outlinedButtonTxt}>
                    {printingReceipt ? t('printing') : t('waiter-common-print')}
                  </Typography>
                </Button>
              )}
              {topActions.map(({ onClick, i18nKey }) => (
                <Button variant="outlined" className={classes.iconButton} onClick={onClick} disableRipple>
                  <Typography className={classes.outlinedButtonTxt}>
                    {t(i18nKey)}
                  </Typography>
                </Button>
              ))}
              <MoreOptionsButton onClick={openMenu} style={{ marginLeft: 8 }} />
              <Button variant="outlined" className={clsx(classes.iconButton, classes.closeIconButton)} style={{ marginLeft: 8 }} onClick={close} disableRipple>
                <CloseDialogIcon20 />
              </Button>
            </div>
          </div>
        </AppBar>
        <div className={classes.receiptContent}>
          {preparationTimePicker}
          <PartySizePicker restaurantId={restaurantId} order={order} />
          <div className={overviewClasses.wrapper}>
            <div className={overviewClasses.container}>
              {isCancelledOrder && <div className={overviewClasses.row} style={{ flexWrap: 'nowrap' }}>
                <div className={overviewClasses.column}>
                  <Typography className={overviewClasses.cardSubtitle}>{t('order-update-current-status-label')}</Typography>
                  <div style={{ display: "flex", marginTop: 4 }}>
                    <OrderStatusTakeawayBadge status={"CANCELLED"} formattedStatus={t('cancelled')} />
                  </div>
                </div>
                <div className={overviewClasses.column}>
                  <Typography className={overviewClasses.cardSubtitle}>{t('receipt-cancellation-reason')}</Typography>
                  <Typography className={overviewClasses.cardTitle} style={{ textWrap: 'wrap', marginTop: 4 }}>{activity?.closingReason || '-'}</Typography>
                </div>
              </div>}
              <div className={overviewClasses.row}>
                <div className={overviewClasses.column}>
                  <Typography className={overviewClasses.cardSubtitle}>{t('order-history-table-header-order-type')}</Typography>
                  {type && (
                    <Typography className={overviewClasses.cardTitle}>
                      {t(`menu-editor-form-order-type-field-option-${type.toLowerCase().replace("_", "-")}`)}
                      {(type === "EXPRESS" && toGo) && (
                        <span style={{ marginLeft: 4 }}>{t('to-go')}</span>
                      )}
                    </Typography>
                  )}
                </div>
                {(isDineIn || (isExpress && tableCode)) && (
                  <div className={overviewClasses.column}>
                    <Typography className={overviewClasses.cardSubtitle}>{t('order-history-table-header-table-number')}</Typography>
                    <Typography className={overviewClasses.cardTitle}>{formattedTableNumber}</Typography>
                  </div>
                )}
                <div className={overviewClasses.column}>
                  <Typography className={overviewClasses.cardSubtitle}>{t('order-history-table-header-order-started')}</Typography>
                  <Typography className={overviewClasses.cardTitle}>{formattedStartTime}</Typography>
                </div>
                {!isOngoing && (
                  <div className={overviewClasses.column}>
                    <Typography className={overviewClasses.cardSubtitle}>{t('order-history-table-header-order-ended')}</Typography>
                    <Typography className={overviewClasses.cardTitle}>{formattedEndTime}</Typography>
                  </div>
                )}
              </div>
              <div className={overviewClasses.row}>
                {!isTakeaway && (
                  <div className={overviewClasses.column}>
                    <Typography className={overviewClasses.cardSubtitle}>{t('receipt-participants-label')}</Typography>
                    <Typography className={overviewClasses.cardTitle}>{participants.length}</Typography>
                  </div>
                )}
                <div className={overviewClasses.column}>
                  <Typography className={overviewClasses.cardSubtitle}>{t('order-history-table-header-order-notes')}</Typography>
                  <Typography className={overviewClasses.cardTitle} style={{ whiteSpace: "break-spaces" }}>{notes || "-"}</Typography>
                </div>
                {pagerIdentifier && (
                  <div className={overviewClasses.column}>
                    <Typography className={overviewClasses.cardSubtitle}>{t('common-pager')}</Typography>
                    <Typography className={overviewClasses.cardTitle} style={{ whiteSpace: "break-spaces" }}>{pagerIdentifier || "-"}</Typography>
                  </div>
                )}
                {isTakeaway && (
                  <div className={overviewClasses.column}>
                    <Typography className={overviewClasses.cardSubtitle}>{t(isPickup ? 'common-pickupTime' : 'common-deliveryTime')}</Typography>
                    <Typography className={overviewClasses.cardTitle}>{`${pickupTime ? moment.utc(pickupTime, "HH:mm:ss").format('HH:mm') : t('order-item-pickupTime-asap-label')} ${takeawayDate ? `(${moment(takeawayDate).format('DD.MM.yyyy')})`: ''}`}</Typography>
                  </div>
                )}
                {isDelivery && !isEmpty(customer) && (
                  <div className={overviewClasses.column}>
                    <Typography className={overviewClasses.cardSubtitle}>{t('common-address')}</Typography>
                    {customer?.address && (
                      <Typography className={overviewClasses.cardTitle}>{formatAddress(customer.address)}</Typography>
                    )}
                  </div>
                )}
                {isTakeaway && (
                  <>
                    <div className={overviewClasses.column}>
                      <Typography className={overviewClasses.cardSubtitle}>{t('pickup-order-creation-form-phone-field-label')}</Typography>
                      <Typography className={overviewClasses.cardTitle}>{customer?.phone || '-'}</Typography>
                    </div>
                    {phoneCode && <div className={overviewClasses.column}>
                      <Typography className={overviewClasses.cardSubtitle}>{t('common-phone-code')}</Typography>
                      <Typography className={overviewClasses.cardTitle}>{phoneCode || ''}</Typography>
                    </div>}
                  </>
                )}
              </div>
              {!isOngoing && isInternalUser && (
                <div className={overviewClasses.row}>
                  <div className={overviewClasses.column} onClick={() => copyDateToClipboard(orderId)} style={{ cursor: "pointer"}}>
                    <Typography className={overviewClasses.cardSubtitle}>order_id</Typography>
                    <div style={{ display: "flex", alignItems: "center" }} >
                      <Typography className={overviewClasses.cardTitle} style={{ marginRight: 4 }}>{orderId}</Typography>
                      <CopyIcon20 />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          {showDriverInformation && getDriverInformation()}
          {!isEmpty(participants) && (
            <div className={overviewClasses.wrapper}>
              <div className={expendableCardClasses.container}>
                <div className={expendableCardClasses.header} onClick={toggleCustomers}>
                  <div className={expendableCardClasses.left}>
                    <UserIcon20 />
                    <Typography className={expendableCardClasses.headerText}>
                      {t("administration-layout-customers-nav-label")}
                    </Typography>
                    <span
                      className={expendableCardClasses.counterBadge}>{isEmpty(participants) ? 0 : participants.length}</span>
                  </div>
                  <div
                    className={clsx(expendableCardClasses.expandBtn, { [expendableCardClasses.rotate]: !customersExpanded })}>
                    <CollapseIcon />
                  </div>
                </div>
                {customersExpanded && participants.map(({ customer = {} }) => (
                  <div className={expendableCardClasses.itemCard}>
                    <div className={expendableCardClasses.customerHeader}>
                      <div className={expendableCardClasses.left}>
                        <Avatar
                          alt={`${customer.firstName ?? ""} ${customer.lastName ?? ""}`}
                          src={customer.avatar}
                          classes={{ root: expendableCardClasses.customerAvatar }}
                          defaultValue="allO"
                        >
                          {getAcronym(`${customer.firstName} ${customer.lastName ?? ""}`)}
                        </Avatar>
                        <Typography className={expendableCardClasses.customerFullName}>
                          {`${customer.firstName} ${customer.lastName ?? ""}`}
                        </Typography>
                      </div>
                      <div className={expendableCardClasses.right}>
                        <Button variant="outlined"
                                className={clsx(classes.iconButton, expendableCardClasses.paymentsMenuButton)}
                                onClick={(e) => openCustomerMenu(e, customer)} disableRipple>
                          <MoreOptionsIcon />
                        </Button>
                      </div>
                    </div>
                    <div className={expendableCardClasses.tags}>
                      {customer.id === order.customerId && (
                        <Typography className={expendableCardClasses.tag}>
                          {t('host')}
                        </Typography>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          <div className={overviewClasses.wrapper}>
            <div className={expendableCardClasses.container}>
              <div className={expendableCardClasses.header} onClick={toggleDetails}>
                <div className={expendableCardClasses.left}>
                  <ReceiptDarkIcon20 />
                  <Typography className={expendableCardClasses.headerText}>
                    {t('activity-title-order-details')}
                  </Typography>
                </div>
                <div className={expendableCardClasses.right}>
                  <Typography className={expendableCardClasses.bodyRegularTypography}>
                    {formatNumber(total)}€
                  </Typography>
                  <div className={clsx(expendableCardClasses.expandBtn, { [expendableCardClasses.rotate]: !detailsExpanded })}>
                    <CollapseIcon />
                  </div>
                </div>
              </div>
              {detailsExpanded && (
                <div className={expendableCardClasses.detailsBody}>
                  {Object.keys(itemsWithStatusByUser).filter(c => !!profiles[c]).map(customerId => (
                    <>
                      {!guest && (
                        <div className={classes.userSection} key={customerId}>
                          <div className={classes.orderHeader}>
                            <div className={classes.user}>
                              <Avatar alt={getUserFullName(profiles[customerId])} src={assetPath(`/documents/${getUserAvatar(profiles[customerId])}`)} classes={{ root: expendableCardClasses.customerAvatar }} />
                              <Typography variant={'body1'} className={classes.username}>
                                {getUserFullName(profiles[customerId])}
                              </Typography>
                            </div>
                            <Typography variant={'body1'} className={classes.name}>
                              {`${(getTotal(itemsWithStatusByUser[customerId]) || 0).toFixed(2)}€`}
                            </Typography>
                          </div>
                        </div>
                      )}
                      {itemsWithStatusByUser[customerId].map(item => (
                        <>
                          <ReceiptItem
                            isReceipt
                            isPreview
                            key={item.id}
                            code={item.code}
                            id={item.id}
                            orderId={orderId}
                            {...item}
                            description={`No: ${item.qtd} - ${item.unitPrice}€`}
                            orderType={type}
                            orderStatus={orderStatus}
                            orderTableId={order.tableId}
                            blockMovingItem={blockMovingItem}
                          />
                          {!isEmpty(item.nestedOrderItems) && (
                            <div className={classes.nestedItems}>
                              {item.nestedOrderItems.map((nItem, index) => (
                                <ReceiptItem
                                  isReceipt
                                  isPreview
                                  key={nItem.id}
                                  nestedCount={index + 1}
                                  code={nItem.code}
                                  id={nItem.id}
                                  orderId={order.id}
                                  {...nItem}
                                  description={`No: ${nItem.qtd} - ${nItem.unitPrice}€`}
                                  status={nItem.status}
                                  blockMovingItem={blockMovingItem}
                                />
                              ))}
                            </div>
                          )}
                        </>
                      ))}
                    </>
                  ))}
                  {!isEmpty(offlineItems) && (
                    <>
                      <div className={classes.userSection}>
                        <div className={classes.user}>
                          <Avatar alt={restaurantName} src={assetPath(``)} classes={{ root: expendableCardClasses.customerAvatar }}>
                            {getAcronym(restaurantName)}
                          </Avatar>
                          <Typography variant={'body2'} className={classes.username}>
                            {restaurantName}
                          </Typography>
                        </div>
                      </div>
                      {offlineItems.map(item => (
                        <>
                          <ReceiptItem
                            isReceipt
                            isPreview
                            key={item.id}
                            code={item.code}
                            id={item.id}
                            orderId={orderId}
                            {...item}
                            description={`No: ${item.qtd} - ${item.unitPrice}€`}
                            orderType={type}
                            orderStatus={orderStatus}
                            orderTableId={order.tableId}
                            blockMovingItem={blockMovingItem}
                          />
                          {!isEmpty(item.nestedOrderItems) && (
                            <div className={classes.nestedItems}>
                              {item.nestedOrderItems.map((nItem, index) => (
                                <ReceiptItem
                                  isReceipt
                                  isPreview
                                  key={nItem.id}
                                  nestedCount={index + 1}
                                  code={nItem.code}
                                  id={nItem.id}
                                  orderId={order.id}
                                  {...nItem}
                                  description={`No: ${nItem.qtd} - ${nItem.unitPrice}€`}
                                  status={nItem.status}
                                  blockMovingItem={blockMovingItem}
                                />
                              ))}
                            </div>
                          )}
                        </>
                      ))}
                    </>
                  )}

                  <div className={classes.amountBreakdown}>
                    <div className={classes.summaryRow}>
                      <Typography className={overviewClasses.cardTitle}>
                        {t('receipt-sub-total-label')}
                      </Typography>
                      <Typography className={overviewClasses.cardTitle}>
                        {`${(total || 0).toFixed(2)}€`}
                      </Typography>
                    </div>
                    <div className={classes.summaryRow}>
                      <Typography className={overviewClasses.cardTitle}>
                        {t('receipt-tip-label')}
                      </Typography>
                      <Typography className={overviewClasses.cardTitle}>
                        {`${(tipAmount || 0).toFixed(2)}€`}
                      </Typography>
                    </div>
                    <div className={classes.summaryRow}>
                      <Typography className={clsx(overviewClasses.cardTitle, classes.fontMedium)}>
                        {t('receipt-total-label')}
                      </Typography>
                      <Typography className={clsx(overviewClasses.cardTitle, classes.fontMedium)}>
                        {`${((paymentAmount || 0) + (tipAmount || 0)).toFixed(2)}€`}
                      </Typography>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          {!isEmpty(payments) && (
            <div className={overviewClasses.wrapper}>
              <div className={expendableCardClasses.container}>
                <div className={expendableCardClasses.header} onClick={togglePayments}>
                  <div className={expendableCardClasses.left}>
                    <RevenueDarkIcon />
                    <Typography className={expendableCardClasses.headerText}>
                      {t('administration-layout-payments-nav-label')}
                    </Typography>
                    <span className={expendableCardClasses.counterBadge}>{isEmpty(payments) ? 0 : payments.length}</span>
                  </div>
                  <div className={clsx(expendableCardClasses.expandBtn, { [expendableCardClasses.rotate]: !paymentsExpanded })}>
                    <CollapseIcon />
                  </div>
                </div>
                {paymentsExpanded && payments.map((payment, index) => {
                  const { id, amount, tipAmount, linkedAmount = 0, paymentChannel, waiterAccountId, createdByAccount, creationTime, isManualPaymentConfirmation, paymentIntentId } = (payment || {})
                  return (
                    <Fragment>
                      <div className={expendableCardClasses.itemCard}>
                        <div className={expendableCardClasses.paymentRow}>
                          <Typography className={expendableCardClasses.paymentText}>
                            {t(`payment-channels-${paymentChannel.toLowerCase()}`)}
                          </Typography>
                          <div className={expendableCardClasses.paymentRight}>
                            <Typography className={expendableCardClasses.paymentText}>
                              {formatNumber(amount + tipAmount + linkedAmount)}€
                            </Typography>
                            <Button variant="outlined"
                                    className={clsx(classes.iconButton, expendableCardClasses.paymentsMenuButton)}
                                    onClick={(e) => openPaymentMenu(e, id, payment)} disableRipple>
                              <MoreOptionsIcon />
                            </Button>
                          </div>
                        </div>
                        <div style={{
                          display: "flex",
                          alignItems: "center",
                          flexWrap: "wrap",
                          gap: 4,
                          marginTop: 8
                        }}>
                          {!!creationTime && (
                            <div className={expendableCardClasses.tags}>
                              <Typography className={expendableCardClasses.tag}>
                                {moment(creationTime).format('DD.MM.YYYY HH:mm')}
                              </Typography>
                            </div>
                          )}
                          {!!waiterAccountId && !isManualPaymentConfirmation && (
                            <div className={expendableCardClasses.tags}>
                              <Typography className={expendableCardClasses.tag}>
                                {getWaiterName(createdByAccount)}
                              </Typography>
                            </div>
                          )}
                          {!!isManualPaymentConfirmation && (
                            <div className={expendableCardClasses.tags}>
                              <Typography className={expendableCardClasses.tag}
                                          style={{ background: palette.negative["100"] }}>
                                {t('confirmed-by-name', { name: getWaiterName(createdByAccount) })}
                              </Typography>
                            </div>
                          )}
                          {!!tipAmount && (
                            <div className={expendableCardClasses.tags}>
                              <Typography className={expendableCardClasses.tag}>
                                {`${t('receipt-tip-label')} ${formatNumber(tipAmount)}€`}
                              </Typography>
                            </div>
                          )}
                          {!!linkedAmount && (
                            <div className={expendableCardClasses.tags}>
                              <Typography className={expendableCardClasses.tag} style={{ background: palette.secondary.green["100"] }}>
                                {`${t('payment-channels-gutschein')} ${formatNumber(linkedAmount)}€`}
                              </Typography>
                            </div>
                          )}
                        </div>
                        {!isOngoing && isInternalUser && paymentIntentId && (
                          <div className={overviewClasses.row}>
                            <div className={overviewClasses.column} onClick={() => router.push(`https://dashboard.stripe.com/payments/${paymentIntentId}`)} style={{ cursor: "pointer"}}>
                              <Typography className={overviewClasses.cardSubtitle}>stripe_payment_id</Typography>
                              <div style={{ display: "flex", alignItems: "center" }} >
                                <Typography className={overviewClasses.cardTitle} style={{ marginRight: 4 }}>{paymentIntentId}</Typography>
                                <SparksIcon20 />
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      {
                        frontEndSettings?.resolvePaidItemsUnderPaymentInReceipts && !isOngoing && (
                          <div style={{ marginBottom: payments.length - 1 === index ? 0 : 24}}>
                            {renderItemsForPayment(payment)}
                          </div>
                        )
                      }
                    </Fragment>
                  )
                })}
              </div>
            </div>
          )}
        </div>
        {bottomActions}
      </div>
      {isCreatingCustomer && (
        <Panel open={isCreatingCustomer} close={closeCustomerEditor} variant="temporary">
          <CustomerEditor
            close={closeCustomerEditor}
            submit={createCustomer}
            customer={selectedCustomer.customer}
          />
        </Panel>
      )}
      {isRecoverPanelOpen && !isEmpty(order) && (
        <Panel open={isRecoverPanelOpen} close={closeRecoverPanel} variant="temporary">
          <OrderRecoveryEditor
            orderType={order.type}
            orderId={orderId}
            close={closeRecoverPanel}
            restaurantId={order.restaurantId}
          />
        </Panel>
      )}
      {selectedTableRotation && (
        <UpdateOrderItemsTableModal
          open={selectedTableRotation}
          onClose={closeTableRotation}
          tableId={selectedTableRotation}
          restaurantId={restaurantId}
          reload
          order={order}
        />
      )}
      {isShowingDriverAssignmentModal &&
        <DriverAssignmentModal
          open={isShowingDriverAssignmentModal}
          onClose={() => setIsShowingDriverAssignmentModal(false)}
          restaurantId={restaurant.id}
          assignedDriver={drivers?.[0]}
          submit={onAssignDriver}
        />}
      {generatingPdfReceipt && (
        <PDFReceiptModal open={generatingPdfReceipt} onClose={closeGeneratingPdfReceipt} submit={sendPdfReceipt} restaurantId={restaurantId} orderId={orderId} paymentId={selectedPayment ? selectedPayment.id : null}/>
      )}
      <CustomMenu
        id="receipt-menu"
        anchorEl={anchor}
        keepMounted
        open={Boolean(anchor)}
        onClose={closeMenu}
        classes={{
          paper: menuClasses.menu
        }}
      >
        {/*<MenuItem onClick={openCustomerEditor} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t('assign-customer')}
              </Typography>
            </div>
          </div>
        </MenuItem>*/}
        <MenuItem onClick={() => {
          print(false, order.customerId);
          setAnchor(null);
        }} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t('waiter-common-print')}
              </Typography>
            </div>
          </div>
        </MenuItem>
        {actions?.includes?.('CANCEL') && (
          <MenuItem onClick={handleCancelOrder}>
            <Typography className={menuClasses.name}>
              {t('cancel-order')}
            </Typography>
          </MenuItem>
        )}
        {!isOngoing && (
          <MenuItem onClick={() => {
            print(true, order.customerId);
            setAnchor(null);
          }} classes={{ root: menuClasses.menuItem }}>
            <div className={menuClasses.content}>
              <div className={menuClasses.left}>
                <Typography className={menuClasses.name}>
                  {t('waiter-common-print-business-receipt')}
                </Typography>
              </div>
            </div>
          </MenuItem>
        )}
        {!isOngoing && !(refundedOrderId || refundOrderId) && !orderCancelled && (
          <MenuItem onClick={() => {
            setGeneratingPdfReceipt(true);
            setAnchor(null);
          }} classes={{ root: menuClasses.menuItem }}>
            <div className={menuClasses.content}>
              <div className={menuClasses.left}>
                <Typography className={menuClasses.name}>
                  {t('pdf-receipt')}
                </Typography>
              </div>
            </div>
          </MenuItem>
        )}
        {!isOngoing && canRefundOrder && !(refundedOrderId || refundOrderId) && !orderCancelled && (
          <Confirm
            closeMenu={closeMenu}
            title={t('receipt-refund-order-action-confirmation-dialog-title')}
            body={(
              <Typography color="textSecondary" variant="body2">
                {t('receipt-refund-order-action-confirmation-dialog-description-line1')}
                <br />
                <br />
                {t('receipt-refund-order-action-confirmation-dialog-description-line2')}
              </Typography>
            )}
          >
            {confirm => (
              <MenuItem onClick={confirm(refund)} classes={{ root: menuClasses.menuItem }} disabled={refunding || !hasItems}>
                <div className={menuClasses.content}>
                  <div className={menuClasses.left}>
                    <Typography className={menuClasses.name}>
                      {t('refund-order-btn-label')}
                    </Typography>
                  </div>
                  {refunding && <CircularProgress size={18} className={classes.btnProgress} />}
                </div>
              </MenuItem>
            )}
          </Confirm>
        )}
        {!isOngoing && canRecoverOrder && !(refundedOrderId || refundOrderId) && !orderCancelled && (
          <MenuItem onClick={openRecoverPanel} classes={{ root: menuClasses.menuItem }} disabled={!hasItems}>
            <div className={menuClasses.content}>
              <div className={menuClasses.left}>
                <Typography className={menuClasses.name}>
                  {t('recover-order-btn-label')}
                </Typography>
              </div>
            </div>
          </MenuItem>
        )}
        {isOngoing && !isEmpty(order) && !!order.tableId && (
          <MenuItem onClick={() => {
            closeMenu();
            setTableRotation(order.tableId)
          }} classes={{ root: menuClasses.menuItem }}>
            <div className={menuClasses.content}>
              <div className={menuClasses.left}>
                <Typography className={menuClasses.name}>
                  {t('terminal-table-rotation-label')}
                </Typography>
              </div>
            </div>
          </MenuItem>
        )}
        {!isEmpty(menuActions) && menuActions.map(({ i18nKey, onClick }) => (
          <MenuItem onClick={() => {
            closeMenu();
            onClick();
          }} classes={{ root: menuClasses.menuItem }}>
            <div className={menuClasses.content}>
              <div className={menuClasses.left}>
                <Typography className={menuClasses.name}>
                  {t(i18nKey)}
                </Typography>
              </div>
            </div>
          </MenuItem>
        ))}
      </CustomMenu>
      <CustomMenu
        id="payment-menu"
        anchorEl={selectedPayment.anchor}
        keepMounted
        open={Boolean(selectedPayment.anchor)}
        onClose={closePaymentMenu}
        classes={{
          paper: menuClasses.menu
        }}
      >
        <MenuItem onClick={handlePrintPayment(selectedPayment.id)} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t('waiter-common-print')}
              </Typography>
            </div>
          </div>
        </MenuItem>
        <MenuItem onClick={handlePrintPayment(selectedPayment.id, true)} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t('waiter-common-print-business-receipt')}
              </Typography>
            </div>
          </div>
        </MenuItem>
        {getTerminalPaymentReceiptMenuItem()}
        {!isOngoing && !(refundedOrderId || refundOrderId) && !orderCancelled && (
          <MenuItem onClick={() => {
            setGeneratingPdfReceipt(true);
            closePaymentMenu();
            setReceiptPaymentId(selectedPayment.id);
          }} classes={{ root: menuClasses.menuItem }}>
            <div className={menuClasses.content}>
              <div className={menuClasses.left}>
                <Typography className={menuClasses.name}>
                  {t('pdf-receipt')}
                </Typography>
              </div>
            </div>
          </MenuItem>
        )}
      </CustomMenu>
      <CustomMenu
        id="customer-menu"
        anchorEl={selectedCustomer.anchor}
        keepMounted
        open={Boolean(selectedCustomer.anchor)}
        onClose={closeCustomerMenu}
        classes={{
          paper: menuClasses.menu
        }}
      >
        <MenuItem onClick={openCustomerEditor} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t('order-item-edit-btn')}
              </Typography>
            </div>
          </div>
        </MenuItem>
      </CustomMenu>
      <CustomMenu
        id="driver-menu"
        anchorEl={selectedDriver.anchor}
        keepMounted
        open={Boolean(selectedDriver.anchor)}
        onClose={closeDriverMenu}
        classes={{
          paper: menuClasses.menu
        }}
      >
        <MenuItem onClick={onEditingDriver} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left} style={{ gap: 2 }}>
              <EditIcon20 />
              <Typography style={{ ...typography.body.regular }}>
                {t('order-item-edit-btn')}
              </Typography>
            </div>
          </div>
        </MenuItem>
        <MenuItem onClick={onRemoveDriver} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left} style={{ gap: 4 }}>
              <TrashIcon20Red />
              <Typography style={{ ...typography.body.regular, color: palette.negative["500"] }}>
                {t('remove')}
              </Typography>
            </div>
          </div>
        </MenuItem>
      </CustomMenu>
      <ConfirmationDialog
        open={showCancelConfirmation}
        title={t('cancel-this-order')}
        messages={[t('cancel-order-confirmation-message')]}
        confirmText={t('cancel-order')}
        cancelText={t('go-back')}
        onConfirm={confirmCancelOrder}
        onCancel={cancelCancelOrder}
      />
    </Drawer>
  )
};

export default withTranslation('common')(ReceiptInformation);
