import React, {useEffect, useState} from "react";
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableBody from '@material-ui/core/TableBody';
import Table from '@material-ui/core/Table';
import {withTranslation} from '../../../../i18n';
import {
  createRecord,
  resolveCashRegister,
  updateRecord,
  createCashRegister,
  searchRecords,
  exportDATEV,
  getKontoConfigurations,
  getReportsAsOptions,
  searchRecordsForReport,
  exportDATEVWithAttachmentOption
} from "../../../api";
import Button from '@material-ui/core/Button';
import RecordPanel from './RecordPanel';
import Typography from '@material-ui/core/Typography';
import RegisterPanel from './RegisterPanel';
import Moment from 'react-moment';
import moment from 'moment';
import { useDispatch, useSelector } from "react-redux";
import {
  accountSelectors,
  configurationSelectors,
  reportingSelectors,
  restaurantSelectors
} from "../../../../redux/selectors";
import {permissionIdentifiers} from "../../../utils/const";
import useStyles from "./styles";
import isEmpty from "../../../utils/isEmpty";
import CurrencyBadge from "../../_tags/CurrencyBadge";
import DatePickerWithDailyReport from "../../DatePickerWithDailyReport";
import posthog from "posthog-js";
import DatevExportUpdateModal from "../../_popup/DatevExportUpdateModal";
import { appActions, reportingActions } from "../../../../redux/actions";


export const CashRegister = withTranslation('common')(({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId, code: restaurantCode } = restaurant;
  const { id: accountId } = useSelector(accountSelectors.getAccountId);
  const accountEmail = useSelector(accountSelectors.getAccountEmail);
  
  const { configuration = {} } = useSelector(configurationSelectors.getConfiguration)
  const { hasDATEVExport } = configuration;
  
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const canUpdateCashRegister = permissionIds.includes(permissionIdentifiers.CAN_UPDATE_CASH_REGISTER.value)
  const canViewCashRegister = permissionIds.includes(
    permissionIdentifiers.CAN_VIEW_CASH_REGISTER.value
  )
  
  const { dateTimePicker = {} } = useSelector(reportingSelectors.getDateTimePicker);
  const { dateType, dateRange = {} } = dateTimePicker;
  const { id: dateRangeId, start, end } = dateRange;

  const [register, setRegister] = useState({});
  const [creatingRegister, setCreatingRegister] = useState(false);
  const [registerFetched, setRegisterFetched] = useState(false);

  const [records, setRecords] = useState([]);
  const [record, setRecord] = useState(null);
  
  const [kontoConfigurations, setKontoConfigurations] = useState([]);
  const [reportOptions, setReportOptions] = useState([]);

  const [selectedDateRange, handleDateRangeChange] = React.useState([]);
  const [selectedReport, setSelectedReport] = React.useState(null)
  
  const [isShowingEmailModal, setIsShowingEmailModal] = useState(false)
  
  const onDateRangeChange = (value) => {
    handleDateRangeChange(value);
    setSelectedReport(null)
  }
  
  const getServerDate = (dt) => {
    if (!dt) {
      return ""
    }
    return dt.split("+")[0]
  }

  const fetch = () => {
    resolveCashRegister(restaurantId)
      .then(({ data = {} }) => {
        setRegister(data)
        setRegisterFetched(true)
        if (!isEmpty(selectedReport)) {
          searchRecordsForReport(data.id, `${getServerDate(selectedReport.start)}Z`, `${getServerDate(selectedReport.end)}Z`)
            .then(({ data: r = [] }) => {
            setRecords(r)
          })
            .catch(() => {});
        } else {
          searchRecords(data.id, moment(selectedDateRange[0]).format('yyyy-MM-DD'), moment(selectedDateRange[1]).format('yyyy-MM-DD'))
            .then(({ data: r = [] }) => {
              setRecords(r)
            })
            .catch(() => {});
        }
      })
      .catch(() => {});
  };
  
  const fetchKontoConfigurations = () => {
    getKontoConfigurations(restaurantId)
      .then(({ data }) => setKontoConfigurations(data))
      .catch(() => {})
  }
  
  const fetchReportOptions = () => {
    getReportsAsOptions(restaurantId)
      .then(({ data }) => {
        // due to data getting lost between 2 reports, here we put the start of latest equal to the end of previous
        for (let i = 0; i < data.length - 1; i++) {
          data[i].start = data[i+1].end
        }
        setReportOptions(data)
      })
      .catch(() => {})
  }
  
  useEffect(() => {
    window.scrollTo(0, 0);
    fetchKontoConfigurations();
    fetchReportOptions();
  }, []);

  useEffect(() => {
    fetch();
  }, [selectedDateRange, JSON.stringify(selectedReport)]);
  
  useEffect(() => {
    handleDateRangeChange([start, end])
  },[dateRange, dateType])

  const createRegister = (form) => {
    if (!canUpdateCashRegister) {
      return
    }
    if (form.id === "") {
      delete form.id;
    }
    createCashRegister(restaurantId, form)
      .then(({ data }) => setRegister(data))
      .then(() => {
        setCreatingRegister(false);
      })
      .catch(() => {});
  }

  const submitRecord = (form) => {
    if (!canUpdateCashRegister) {
      return
    }
    const call = form.id ? updateRecord : createRecord;
    form.restaurantId = restaurantId
    if (form.id === "") {
      delete form.id;
    }
    call(register.id, form)
      .then((response) => {
        const data = (response?.data || {});
        if (!form.id && data) {
          try {
            posthog.capture("cash_journal.entry_added", {
              entry_id: data?.id,
              entry_amount: data?.amount,
              entry_type: data?.type,
              created_at: data?.date,
              user_id: data?.createdByAccountId,
              document_added: !!data?.documentUrl
            });
          } catch (e) {
            console.log(e)
          }
        }
      })
      .then(() => {
        setRecord(null)
        fetch();
      })
      .catch(() => {});
  };
  
  const onDATEVExport = () => {
    if (isEmpty(register)) {
      return
    }
    exportDATEV(restaurantId, register.id, moment(selectedDateRange[0]).format('yyyy-MM-DD'), moment(selectedDateRange[1]).format('yyyy-MM-DD'))
      .then((response) => {
        const { headers = {}, data } = response;
        const filename = (headers['content-disposition'] ?? "filename=export")
          .replaceAll('\"', "")
          .replaceAll("-", "_")
          .split("filename=")[1];
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', filename);
        link.setAttribute('id', "download-btn");
        link.setAttribute('target', "_blank");
        document.body.appendChild(link);
        link.click();
        link.parentNode.removeChild(link);
      })
      .then(() => {
        try {
          posthog.capture("cash_journal.exported", {
            export_format: "CSV",
            exported_at: new Date().toISOString(),
            user_id: accountId
          })
        } catch (error) {
          console.log(error)
        }
      })
      .catch(() => {})
  }
  
  const requestDATEVExport = (email, withAttachments) => {
    if (isEmpty(register)) {
      return
    }
    exportDATEVWithAttachmentOption(restaurantId, register.id, moment(selectedDateRange[0]).format('yyyy-MM-DD'), moment(selectedDateRange[1]).format('yyyy-MM-DD'), email, withAttachments)
      .then(() => {
        try {
          posthog.capture("cash_journal.exported", {
            export_format: "CSV",
            exported_at: new Date().toISOString(),
            user_id: accountId
          })
        } catch (error) {
          console.log(error)
        }
      })
      .catch((err) => {
        console.log(err)
      })
  };
  
  const isSelected = id => record && record.id === id;
  
  return (
    <>
      <div className={classes.content} data-testid="reporting-cash-journal">
        <div className={classes.header}>
          <Typography className={classes.headerTitle}>{t('administration-layout-cash-register-nav-label')}</Typography>
          <div className={classes.spacing} />
          {!isEmpty(register) && (
            <div className={classes.rangeSelectors}>
              <DatePickerWithDailyReport range={selectedDateRange} setRange={onDateRangeChange}/>
              <div className={classes.actions}>
                <Button
                  classes={{ root: classes.buttonRoot }}
                  color="primary"
                  onClick={() => setIsShowingEmailModal(true)}
                  disabled={isEmpty(register)}
                >
                  {t('menu-editor-actions-export-menu-items')}
                </Button>
                {canUpdateCashRegister && (
                  <Button
                    classes={{ root: classes.buttonRoot }}
                    color="primary"
                    onClick={() => setRecord({ id: "" })}
                    disabled={!register}
                    className={classes.createButton}
                  >
                    {t('cash-register-actions-create-record')}
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
        {isEmpty(register) && registerFetched && (
          <div className={classes.emptyPlaceholder}>
            <Typography gutterBottom className={classes.placeholderText}>{t('cash-registry-not-setup')}</Typography>
            <Button disableElevation color={'secondary'} onClick={() => setCreatingRegister(true)} variant="contained">
              {t('cash-register-actions-create-register-btn')}
            </Button>
          </div>
        )}
        {!isEmpty(register) && canViewCashRegister && (
          <>
            <div className={classes.reportCards}>
              <div className={classes.card}>
                <Typography className={classes.cardTitle}>{`${(register.balance ?? 0).toFixed(2)} €`}</Typography>
                <Typography className={classes.cardSub}>{t('cash-register-total')}</Typography>
              </div>
            </div>
            <TableContainer>
              <Table stickyHeader className={classes.table} aria-label="menu editor table">
                <TableHead>
                  <TableRow>
                    <TableCell/>
                    <TableCell>{t('cash-register-creation-date')}</TableCell>
                    <TableCell>{t('cash-register-date')}</TableCell>
                    <TableCell align="right">{t('cash-register-amount')}</TableCell>
                    <TableCell>{t('cash-register-name')}</TableCell>
                    <TableCell>{t('cash-register-identifier')}</TableCell>
                    <TableCell>{t('cash-register-id')}</TableCell>
                    <TableCell>{t('cash-register-type')}</TableCell>
                    <TableCell>{t('cash-register-category')}</TableCell>
                    {/*<TableCell>{t('cash-register-supplier')}</TableCell>*/}
                    <TableCell>{t('cash-register-tax-rate')}</TableCell>
                    <TableCell>{t('cash-register-description')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {!isEmpty(records) && (
                    <TableRow>
                      <TableCell>
                        <b>{t('cash-register-start-balance')}</b>
                      </TableCell>
                      <TableCell align="left">
                        <b><Moment format={'DD.MM.YYYY'}>{selectedDateRange[0]}</Moment></b>
                      </TableCell>
                      <TableCell />
                      <TableCell colSpan={1} align="right">
                        <b>{(records[0].cashRegisterBalance ?? 0).toFixed(2)} €</b>
                      </TableCell>
                      <TableCell colSpan={7} />
                    </TableRow>
                  )}
                  {records.map(r => {
                    const { id, documentId, date, amount = 0, name, type = '', category = '', taxRate = '', description, creationTime } = r;
                    const fixedAmount = (amount ?? 0).toFixed(2)
                    return (
                        <TableRow key={id} hover selected={isSelected(id)} onClick={() => setRecord(r)} className={classes.clickable}>
                          <TableCell />
                          <TableCell align="left">
                            <Moment format={'DD.MM.YYYY'}>{creationTime}</Moment>
                          </TableCell>
                          <TableCell align="left">
                            <Moment format={'DD.MM.YYYY'}>{date}</Moment>
                          </TableCell>
                          <TableCell align="right"><CurrencyBadge amount={fixedAmount}/></TableCell>
                          <TableCell align="left">{name}</TableCell>
                          <TableCell align="left">{documentId}</TableCell>
                          <TableCell align="left">{id.slice(-6)}</TableCell>
                          <TableCell align="left">{t(`cash-register-type-${type.toLowerCase()}`)}</TableCell>
                          <TableCell align="left">{t(`cash-register-category-${category.toLowerCase()}`)}</TableCell>
                          {/*<TableCell align="left">{supplier.name}</TableCell>*/}
                          <TableCell align="left">{taxRate ? t(`cash-register-tax-rate-${taxRate.toLowerCase()}`) : '--'}</TableCell>
                          <TableCell align="left">{description}</TableCell>
                        </TableRow>
                    )
                  })}
                  {!isEmpty(records) && (
                    <TableRow>
                      <TableCell colSpan={1}>
                        <b>{t('cash-register-end-balance')}</b>
                      </TableCell>
                      <TableCell align="left">
                        <b><Moment format={'DD.MM.YYYY'}>{selectedDateRange[1]}</Moment></b>
                      </TableCell>
                      <TableCell />
                      <TableCell align="right">
                        <b>{((records[records.length - 1].cashRegisterBalance ?? 0) + (records[records.length - 1].amount ?? 0)).toFixed(2)} €</b>
                      </TableCell>
                      <TableCell colSpan={8} />
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}
      </div>
      {record && <RecordPanel open={record} close={() => setRecord(null)} restaurantCode={restaurantCode} values={record} submit={submitRecord} kontoConfigurations={kontoConfigurations} triggerRefetch={fetch}/>}
      {creatingRegister && canUpdateCashRegister && <RegisterPanel open={creatingRegister} close={() => setCreatingRegister(false)} submit={createRegister} />}
      {isShowingEmailModal && <DatevExportUpdateModal  open={isShowingEmailModal} onClose={() => setIsShowingEmailModal(false)} titleI18n={"email"} type={"email"} fieldProps={{ label: t("reservation-email-label"),placeholder: t('enter-your-email-address') }} value={accountEmail} onExport={onDATEVExport} onExportPerEmail={(email, withAttachments) => requestDATEVExport(email, withAttachments)} />}
    </>
  );
});
