import React, { useState } from 'react'
import clsx from 'clsx'
import { withTranslation } from '../../../../../../../i18n'
import MenuItem from '@material-ui/core/MenuItem'
import Typography from '@material-ui/core/Typography'
import { PlusIconFilled20 } from '../../../../../../utils/icons'
import { ButtonBase, Radio } from '@material-ui/core'
import { useMenuStyles, useRadioButtonStyles } from './styles'
import isEmpty from '../../../../../../utils/isEmpty'
import palette from '../../../../../../../styles/palette'
import typography from '../../../../../../../styles/typography'
import Checkbox from '../../../../../_toggles/Checkbox'
import Badge from '../../../../../_tags/Badge'
import { paymentChannelTypeOptions, transactionsTypeOptions, sortingBy } from './filterTypes'
import CustomMenu from "../../../../../_popup/CustomMenu";

function RadioButton(props) {
  const classes = useRadioButtonStyles()

  return (
    <Radio
      className={classes.root}
      disableRipple
      color="default"
      checkedIcon={<span className={clsx(classes.icon, classes.checkedIcon)} />}
      icon={<span className={classes.icon} />}
      {...props}
    />
  )
}


const SortAndFilterBar = ({
  t,
  filters,
  sorting,
  setFilters,
  setSorting,
  accountsById,
  tablesById
}) => {
  const menuClasses = useMenuStyles()
  const [addFilterAnchor, setAddFilterAnchor] = useState(null)
  const openAddFilterMenu = (e) => setAddFilterAnchor(e.currentTarget)
  const closeAddFilterMenu = () => setAddFilterAnchor(null)

  const {
    transactionType,
    paymentChannels
  } = (filters || {})

  const hasTransactionTypeFilter = !isEmpty(transactionType)
  const hasPaymentChannelFilter = !isEmpty(paymentChannels)
  // ----------------------------------------------------


  const [addPaymentChannelFilter, setPaymentChannelFilter] = useState(false)
  const updatePaymentChannelsFilter = (v) => {
    setPaymentChannelFilter(v)
    closeAddFilterMenu()
  }

  const [addPaymentChannelsFilterAnchor, setPaymentChannelsFilterAnchor] = useState(null)
  const openPaymentChannelsMenu = e => setPaymentChannelsFilterAnchor(e.currentTarget)

  const closePaymentChannelsFilterMenu = () => {
    setPaymentChannelsFilterAnchor(null)
    if (!paymentChannels) {
      setPaymentChannelFilter(false)
    }
  }

  // ----------------------------------------------------

  const [sortByAnchor, setSortByAnchor] = useState(null)
  const openSortByMenu = (e) => setSortByAnchor(e.currentTarget)
  const closeSortByMenu = () => setSortByAnchor(null)

  // ----------------------------------------------------
  const [addTransactionsFilter, setTransactionsFilter] = useState(false)
  const updateTransactionsFilter = (v) => {
    setTransactionsFilter(v)
    closeAddFilterMenu()
  }

  // ----------------------------------------------------
  const [addTableIdsFilter, setAddTableIdsFilter] = useState(false)
  const updateAddTableIdsFilter = (v) => {
    setAddTableIdsFilter(v)
    closeAddFilterMenu()
  }

  const [addAccountIdsFilter, setAddAccountIdsFilter] = useState(false)


  const [addFilterByTableIdAnchor, setAddFilterByTableIdAnchor] = useState(
    null)
  const openAddFilterByTableIdMenu = (e) => setAddFilterByTableIdAnchor(e.currentTarget)
  const closeAddFilterByTableIdMenu = () => {
    setAddFilterByTableIdAnchor(null)
    if (!hasTransactionTypeFilter) {
      setAddTableIdsFilter(false)
    }
  }

  const updateSorting = (sortField, sortDirection) => {
    setSorting({ sortField, sortDirection });
    closeSortByMenu();
  }

  const clearSorting = () => {
    setSorting({ sortField: null, sortDirection: null });
    closeSortByMenu();
  }

  const updateFiltering = (filterField, filterId) => {
    const {
      transactionType,
      paymentChannels
    } = (filters || {})
    let filterCategory = ((filters || {})[filterField] || [])
    if (filterCategory.indexOf(filterId) > -1) {
      filterCategory = filterCategory.filter(i => i !== filterId)
    } else {
      filterCategory.push(filterId)
    }
    setFilters({
      transactionType,
      paymentChannels,
      [filterField]: filterCategory
    })
  }

  const getFilters = () => {
    const showAddFilter = (isEmpty(paymentChannels) || isEmpty(transactionType)) &&
      ( !addTransactionsFilter || !addPaymentChannelFilter)
    return (
      <>
        {(addTransactionsFilter) && (
          <ButtonBase
            style={{
              padding: hasTransactionTypeFilter ?
                '6px 10px 6px 10px' :
                '5px 9px 5px 5px',
              borderRadius: hasTransactionTypeFilter ? 12 : 11,
              border: hasTransactionTypeFilter ?
                null :
                `1px solid ${palette.grayscale['350']}`,
              background: hasTransactionTypeFilter ? palette.grayscale['800'] : null,
              marginRight: 8
            }}
            onClick={openAddFilterByTableIdMenu}
            disableRipple
            disableTouchRipple
          >
            <PlusIconFilled20 />
            <Typography style={{
              ...typography.body.medium,
              marginLeft: 2,
              color: hasTransactionTypeFilter ? palette.grayscale['100'] : null,
              whiteSpace: 'nowrap'
            }}>
              {t('cash-register-type')}
            </Typography>
            {hasTransactionTypeFilter && (
              <div style={{
                display: 'flex',
                marginLeft: 8
              }}>
                <Badge quantity={transactionType.length} />
              </div>
            )}
          </ButtonBase>
        )}

        {
          addPaymentChannelFilter && (
            <ButtonBase
              style={{
                padding: hasPaymentChannelFilter ?
                  '6px 10px 6px 10px' :
                  '5px 9px 5px 5px',
                borderRadius: hasPaymentChannelFilter ? 12 : 11,
                border: hasPaymentChannelFilter ?
                  null :
                  `1px solid ${palette.grayscale['350']}`,
                background: hasPaymentChannelFilter ? palette.grayscale['800'] : null,
                marginRight: 8
              }}
              onClick={openPaymentChannelsMenu}
              disableRipple
              disableTouchRipple
            >
              <PlusIconFilled20 />
              <Typography style={{
                ...typography.body.medium,
                marginLeft: 2,
                color: hasPaymentChannelFilter ? palette.grayscale['100'] : null,
                whiteSpace: 'nowrap'
              }}>
                {t('payment-by-channel-breakdown')}
              </Typography>
              {hasPaymentChannelFilter && (
                <div style={{
                  display: 'flex',
                  marginLeft: 8
                }}>
                  <Badge quantity={paymentChannels.length} />
                </div>
              )}
            </ButtonBase>
          )
        }

        {showAddFilter && (
          <ButtonBase style={{
            padding: '5px 9px 5px 5px',
            borderRadius: 11,
            border: `1px solid ${palette.grayscale['350']}`,
            whiteSpace: 'nowrap'
          }} onClick={openAddFilterMenu}>
            <PlusIconFilled20 />
            <Typography style={{
              ...typography.body.medium,
              marginLeft: 2
            }}>
              {t('add-filter')}
            </Typography>
          </ButtonBase>
        )}
      </>
    )
  }

  const getSorting = () => {
    const {
      sortField,
      sortDirection
    } = (sorting || {})
    if (sortField && sortDirection && sortingBy[`${sortField}_${sortDirection}`]) {
      const sortedBy = sortingBy[`${sortField}_${sortDirection}`]
      return (
        <ButtonBase
          style={{
            padding: '6px 6px 6px 10px',
            borderRadius: 12,
            marginRight: 8,
            background: palette.grayscale['800']
          }}
          onClick={openSortByMenu}
        >
          <Typography
            style={{
              ...typography.body.medium,
              marginRight: 2,
              color: palette.grayscale['100']
            }}
          >
            {t('sorted-by')}
            <span style={{ textTransform: 'lowercase' }}>
              {` ${t(sortedBy.i18nKey)}`}
            </span>
          </Typography>
          <div style={{
            display: 'flex',
            rotate: '45deg'
          }}>
            <PlusIconFilled20 />
          </div>
        </ButtonBase>
      )
    }
    return (
      <ButtonBase style={{
        padding: '5px 9px 5px 5px',
        borderRadius: 11,
        border: `1px solid ${palette.grayscale['350']}`,
        marginRight: 8
      }} onClick={openSortByMenu}
        disableRipple
        disableTouchRipple
      >
        <PlusIconFilled20 />
        <Typography style={{
          ...typography.body.medium,
          marginLeft: 2
        }}>
          {t('sort-by')}
        </Typography>
      </ButtonBase>
    )
  }

  return (
    <div>
      <div style={{
        marginBottom: 12,
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        overflow: 'auto'
      }}>
        {getSorting()}
        {getFilters()}
      </div>
      {
        sortByAnchor && (
          <CustomMenu
            id="sort-by-menu"
            anchorEl={sortByAnchor}
            open={Boolean(sortByAnchor)}
            onClose={closeSortByMenu}
            classes={{ list: menuClasses.list, paper: menuClasses.paper }}
          >
            <MenuItem
              key={"sort-by-item"}
              classes={{ root: menuClasses.menuItemRoot }}
              className={clsx(menuClasses.borderBottom)}
              disableRipple
            >
              <div className={menuClasses.content}>
                <Typography className={clsx(menuClasses.option, menuClasses.optionTitle)}>
                  {t('sort-by')}
                </Typography>
                <ButtonBase
                  disableRipple
                  disableTouchRipple
                  style={{ background: palette.grayscale["100"] }}
                  onClick={clearSorting}
                >
                  <Typography style={{ ...typography.body.medium, color: palette.primary["500"] }}>
                    {t('clear')}
                  </Typography>
                </ButtonBase>
              </div>
            </MenuItem>
            {Object.values(sortingBy).map(({ sortField, sortDirection, i18nKey }) => {
              const selected = sorting.sortField === sortField && sorting.sortDirection === sortDirection
              return (
                (
                  <MenuItem
                    key={sortField}
                    onClick={() => updateSorting(sortField, sortDirection)}
                    classes={{ root: menuClasses.menuItemRoot }}
                    disableRipple
                  >
                    <div className={menuClasses.content}>
                      <div>
                        <Typography className={clsx(menuClasses.option, { [menuClasses.selected]: selected })}>
                          {t(i18nKey)}
                        </Typography>
                      </div>
                      <div className={menuClasses.right}>
                        <RadioButton checked={selected} />
                      </div>
                    </div>
                  </MenuItem>
                )
              )
            })}
          </CustomMenu>
        )
      }
      {addFilterAnchor && (
        <CustomMenu
          id="add-filter-menu"
          anchorEl={addFilterAnchor}
          open={Boolean(addFilterAnchor)}
          onClose={closeAddFilterMenu}
          classes={{
            list: menuClasses.list,
            paper: menuClasses.paper
          }}
        >
          {
            <MenuItem
              key={'sort-by-item'}
              classes={{ root: menuClasses.menuItemRoot }}
              className={clsx(menuClasses.borderBottom)}
              disableRipple
            >
              <div className={menuClasses.content}>
                <Typography
                  className={clsx(menuClasses.option, menuClasses.optionTitle)}>
                  {t('add-filter')}
                </Typography>
              </div>
            </MenuItem>
          }

          {!addTransactionsFilter && (
            <MenuItem
              key={'add-filter-table-ids'}
              onClick={() => updateTransactionsFilter(true)}
              classes={{ root: menuClasses.menuItemRoot }}
              disableRipple
            >
              <div className={menuClasses.content}>
                <div>
                  <Typography className={clsx(menuClasses.option)}>
                    {t('cash-register-type')}
                  </Typography>
                </div>
              </div>
            </MenuItem>
          )}
          {!addPaymentChannelFilter && (
            <MenuItem
              key={'add-filter-payment-channels'}
              onClick={() => updatePaymentChannelsFilter(true)}
              classes={{ root: menuClasses.menuItemRoot }}
              disableRipple
            >
              <div className={menuClasses.content}>
                <div>
                  <Typography className={clsx(menuClasses.option)}>
                    {t('payment-by-channel-breakdown')}
                  </Typography>
                </div>
              </div>
            </MenuItem>
          )}
        </CustomMenu>
      )}
      {addPaymentChannelsFilterAnchor && (
        <CustomMenu
          id="addFiltersTransactionsFilter"
          anchorEl={addPaymentChannelsFilterAnchor}
          open={Boolean(addPaymentChannelsFilterAnchor)}
          onClose={closePaymentChannelsFilterMenu}
          classes={{
            list: menuClasses.list,
            paper: menuClasses.paper
          }}
          keepMounted={false}
          getContentAnchorEl={null}
        >
          <MenuItem
            key={'filter-by-table-item'}
            classes={{ root: menuClasses.menuItemRoot }}
            className={clsx(menuClasses.borderBottom)}
            disableRipple
          >
            <div className={menuClasses.content}>
              <Typography className={clsx(menuClasses.option, menuClasses.optionTitle)}>
                {t('filter-tables')}
              </Typography>
            </div>
          </MenuItem>
          {
            paymentChannelTypeOptions.map(({
              value,
              i18nKey
            }) => {
              const selected = filters && filters.paymentChannels && filters.paymentChannels.indexOf(value) > -1
              return (
                <MenuItem
                  key={value}
                  onClick={() => updateFiltering('paymentChannels', value)}
                  classes={{ root: menuClasses.menuItemRoot }}
                  disableRipple
                >
                  <div className={menuClasses.content}>
                    <div>
                      <Typography
                        className={clsx(menuClasses.option, {
                          [menuClasses.selected]: selected
                        })}
                      >
                        {t(i18nKey)}
                      </Typography>
                    </div>
                    <div className={menuClasses.right}>
                      <Checkbox checked={selected} />
                    </div>
                  </div>
                </MenuItem>
              )
            })
          }
        </CustomMenu>
      )}
      {addFilterByTableIdAnchor && (
        <CustomMenu
          id="addFiltersTransactionsFilter"
          anchorEl={addFilterByTableIdAnchor}
          open={Boolean(addFilterByTableIdAnchor)}
          onClose={closeAddFilterByTableIdMenu}
          classes={{
            list: menuClasses.list,
            paper: menuClasses.paper
          }}
          keepMounted={false}
          getContentAnchorEl={null}
        >
          <MenuItem
            key={'filter-by-table-item'}
            classes={{ root: menuClasses.menuItemRoot }}
            className={clsx(menuClasses.borderBottom)}
            disableRipple
          >
            <div className={menuClasses.content}>
              <Typography className={clsx(menuClasses.option, menuClasses.optionTitle)}>
                {t('filter-tables')}
              </Typography>
            </div>
          </MenuItem>
          {
            transactionsTypeOptions.map(({
              value,
              i18nKey
            }) => {
              const selected = filters && filters.transactionType && filters.transactionType.indexOf(value) > -1
              return (
                <MenuItem
                  key={value}
                  onClick={() => updateFiltering('transactionType', value)}
                  classes={{ root: menuClasses.menuItemRoot }}
                  disableRipple
                >
                  <div className={menuClasses.content}>
                    <div>
                      <Typography
                        className={clsx(menuClasses.option, {
                          [menuClasses.selected]: selected
                        })}
                      >
                        {t(i18nKey)}
                      </Typography>
                    </div>
                    <div className={menuClasses.right}>
                      <Checkbox checked={selected} />
                    </div>
                  </div>
                </MenuItem>
              )
            })
          }
        </CustomMenu>
      )}
    </div>
  )
}

export default withTranslation('common')(SortAndFilterBar)
