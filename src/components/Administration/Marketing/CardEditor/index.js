import React, {useEffect, useState} from "react";
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableBody from '@material-ui/core/TableBody';
import Table from '@material-ui/core/Table';
import {withTranslation} from '../../../../../i18n';
import useStyles from "./styles";
import {
  createCard, createCardBulk, deleteGiftCard,
  getAccounts,
  getCardsReport, getReportsAsOptions,
  resolveCards,
  resolveCustomers,
  updateCard
} from "../../../../api";
import Button from '@material-ui/core/Button';
import Pagination from "@material-ui/lab/Pagination";
import Typography from "@material-ui/core/Typography";
import isEmpty from "../../../../utils/isEmpty";
import { useDispatch, useSelector } from "react-redux";
import { reportingSelectors, restaurantSelectors } from "../../../../../redux/selectors";
import SearchBar from "../../../SearchBar";
import GiftCardModal from "../../../_popup/GiftCardModal";
import CardsSortAndFilterBar from "./CardsSortAndFilterBar";
import moment from "moment";
import { appActions, reportingActions } from "../../../../../redux/actions";
import GroupBySwitcher from '../../../GroupBySwitcher';
import useMediaQuery from '@material-ui/core/useMediaQuery';
import CardTransactionsTable from './CardTransactions/CardTransactionsTable';
import CurrencyBadge from "../../../_tags/CurrencyBadge";
import { ButtonBase } from "@material-ui/core";
import { getView } from "../../../../utils/administrationRoutes";
import CardTransactionsTableReporting from "../../../Reporting/Cards/CardTransactionTableReporting";
import { useRouter } from "next/router";
import { ScanQRCodeIcon } from "../../../../utils/icons";
import typography from "../../../../../styles/typography";
import SimpleQRScanModal from "../../../_popup/SimpleQRScanModal";
import { giftCardGroupOptions, giftCardGroups } from "../../../../utils/const";
import DatePickerWithDailyReport from "../../../DatePickerWithDailyReport";
import clsx from "clsx";

export const CardEditor = withTranslation('common')(({ t }) => {
  const classes = useStyles();
  const isMobile = useMediaQuery('(max-width:500px)');
  const dispatch = useDispatch();
  
  const router = useRouter();
  const { v } = router.query;
  const [currentNavigation, setCurrentNavigation] = useState(getView(v));
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId, isParent } = restaurant;
  
  const { cardsGroupBy } = useSelector(reportingSelectors.getGiftCardGroupBy);

  const [report, setReport] = useState(null);
  const [cards, setCards] = useState(null);
  const [accounts, setAccounts] = useState([]);

  const [sorting, setSorting] = useState({ sortField: null, sortDirection: null })

  const [card, setCard] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [pageOffset, setPageOffset] = useState(0);
  const [query, setQuery] = useState("");
  
  const [selectedDateRange, handleDateRangeChange] = React.useState([]);
  const [selectedReport, setSelectedReport] = React.useState(null)
  const [reportOptions, setReportOptions] = useState([]);
  const [totalBlockTransactionInfo, setTotalBlockTransactionInfo] = useState({})
  const [scanningCard, setScanningCard] = useState(false);
  const startScanningCard = () => setScanningCard(true);
  const stopScanningCard = () => setScanningCard(false);

  const pageSize = 10;

  const [tab, setTab] = useState({ value: "CARDS", i18nKey: "marketing-tabs-cards-view-selector-cards" });

  const onDateRangeChange = (value) => {
    handleDateRangeChange(value);
    setSelectedReport(null)
  }
  
  const fetchReportOptions = () => {
    getReportsAsOptions(restaurantId)
      .then(({ data }) => {
        // due to data getting lost between 2 reports, here we put the start of latest equal to the end of previous
        for (let i = 0; i < data.length - 1; i++) {
          data[i].start = data[i+1].end
        }
        setReportOptions(data)
      })
      .catch(() => {})
  }
  
  useEffect(() => {
    fetchReportOptions();
  },[]);

  const fetchCards = (sorting) => {
    getCardsReport(restaurantId).then(({ data }) => setReport(data)).catch(() => {})
    resolveCards(restaurantId, pageSize, pageOffset, query, sorting)
      .then(({ data = {} }) => {
        const { total, items, pages } = data;
        const customerIds = (items ?? []).filter(i => !!i.customer && !!i.customer.id).map(t => t.customer.id)

        if (!isEmpty(customerIds)) {
          resolveCustomers(customerIds)
            .then(({ data: customers }) => {
              items.map(item => {
                if (item.customer && item.customer.id) {
                  const customerIndex = customers.findIndex( c => c.id === item.customer.id )
                  if (customerIndex > -1) {
                    item.customer = { ...item.customer, ...customers[customerIndex] }
                  }
                }
                return item
              })
              setCards({ total, items, pages });
            })
            .catch(() => {})
        } else {
          setCards({ total, items, pages });
        }
      })
      .catch(() => {});
  };

  useEffect(() => {
    getAccounts(restaurantId, 100, 0)
      .then(({ data }) => {
        setAccounts(data.items)
      }).catch(() => {})
  }, [])

  useEffect(() => {
    if(cardsGroupBy === "CARDS") {
      fetchCards(sorting);
    }
  }, [pageOffset, query, cardsGroupBy]);

  const updateSorting = (value = {}) => {
    setSorting(value);
    fetchCards(value);
  }
  
  const selectGroupBy = (value) => {
    if (value === cardsGroupBy) return
    dispatch(reportingActions.setGiftCardGroupBy(value))
    if (value === "CARDS") {
      setTab({ value: "CARDS", i18nKey: "marketing-tabs-cards-view-selector-cards" })
    }
  
    if (value === "TRANSACTIONS") {
      setTab({ value: "TRANSACTIONS", i18nKey: "marketing-tabs-cards-view-selector-transactions" })
    }
  }

  const submitCard = (form, giftCardQtd) => {
    const call = form.id ? updateCard : giftCardQtd ? createCardBulk : createCard
    if (form.id === "") {
      delete form.id;
    }
    setSubmitting(true);
    call(form.id ? form : form, giftCardQtd)
      .then(() => setCard(null))
      .then(() => {
        if(form.id){
          dispatch(appActions.setNotification('card-updated', "success"))
        }else{
          dispatch(appActions.setNotification('card-created', "success"))
        }
        fetchCards();
      })
      .catch(() => {
        if(form.id){
          dispatch(appActions.setNotification('card-update-error', "error"))
        }else{
          dispatch(appActions.setNotification('card-creation-error', "error"))
        }
      })
      .finally(() => setSubmitting(false))
  };
  
  const deleteCard = (cardId) => {
    deleteGiftCard(restaurantId, cardId)
      .then(()=>{
        setCard(null)
        fetchCards();
        dispatch(appActions.setNotification("gift-card-successfully-deleted", "success"))
      })
      .catch(()=>{
        dispatch(appActions.setNotification("gift-card-could-not-be-deleted", "error"))
      })
  }

  const isCardSelected = id => card && card.id === id;

  const renderCardsTable = () => {
    if (currentNavigation === "reporting" && cardsGroupBy !== "CARDS") return null;
    return (
      <React.Fragment>
        <CardsSortAndFilterBar sorting={sorting} setSorting={updateSorting} />
        <TableContainer>
          <Table stickyHeader className={classes.table} aria-label="menu editor table">
            <TableHead>
              <TableRow>
                <TableCell>{t('card-status-label')}</TableCell>
                <TableCell>{t('card-code-label')}</TableCell>
                {isParent && <TableCell>{t('restaurant')}</TableCell>}
                <TableCell align="right">{t('card-initial-cash-amount-label')}</TableCell>
                <TableCell align="right">{t('card-cash-amount-label')}</TableCell>
                <TableCell>{t('card-external-code-label')}</TableCell>
                <TableCell />
                <TableCell>{t('latest-cash-in')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {cards && cards.items && cards.items.map(r => {
                const { id, status, code, externalCode, initialCashAmount, cashAmount, createdBy, lastMoneyIn, restaurant = {} } = r;
                const { name } = (restaurant || {});
                let formattedUser = "";
                if (!isEmpty(accounts)) {
                  const account = (accounts.find(a => (a['userIds'] || []).indexOf(createdBy) > -1))
                  formattedUser = account ? `${account.firstName ?? ""} ${account.lastName ?? ""}` : '';
                }
                return (
                  <TableRow key={id} hover selected={isCardSelected(id)} onClick={() => setCard(r)} className={classes.clickable}>
                    <TableCell align="left">{t(`card-status-${status.toLowerCase()}`)}</TableCell>
                    <TableCell align="left">{code}</TableCell>
                    {isParent && <TableCell>{name}</TableCell>}
                    <TableCell align="right"><CurrencyBadge amount={initialCashAmount}/></TableCell>
                    <TableCell align="right"><CurrencyBadge amount={cashAmount}/></TableCell>
                    <TableCell align="left">{externalCode}</TableCell>
                    <TableCell align="left">{formattedUser}</TableCell>
                    <TableCell align="left">{lastMoneyIn ? moment(lastMoneyIn).format("DD-MM-YYYY HH:mm") : ''}</TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </TableContainer>
        {cards && cards.pages > 1 && (
          <div className={classes.pagination}>
            <Pagination
              count={cards.pages}
              variant="text"
              shape="rounded"
              onChange={(e, v) => setPageOffset((v - 1) * pageSize)}
            />
          </div>
        )}
      </React.Fragment>
    )
  }
  

  return (
    <div className={classes.content} data-testid="reporting-gift-cards">
      <div className={clsx(classes.header, { [classes.headerMobile]: isMobile })}>
        <Typography className={classes.headerTitle}>{t('marketing-tabs-cards')}</Typography>
        <div className={classes.spacing} />
        <div className={classes.actions} style={{ flexWrap: isMobile ? "wrap" : null, gap: isMobile ? 8 : 0 }}>
          {giftCardGroups[cardsGroupBy] && (
            <GroupBySwitcher
              titleI18n="marketing-tabs-cards-view-selector-title"
              options={giftCardGroupOptions}
              selectedOption={giftCardGroups[cardsGroupBy]}
              selectOption={selectGroupBy}
              minimized={isMobile}
            />
          )}
          <SearchBar query={query} setQuery={setQuery}/>
          <ButtonBase disableRipple disableTouchRipple style={{ margin:"0px 8px 0px 2px", display: "flex", alignItems: "center" }}
                      onClick={startScanningCard}>
            <ScanQRCodeIcon />
            <Typography style={{ ...typography.body.medium, marginLeft: 2 }}>
              {t("scan")}
            </Typography>
          </ButtonBase>
          {cardsGroupBy === "TRANSACTIONS" && currentNavigation === "reporting" && (
            <div style={{ display: "flex", gap: 8, alignItems: "center" }}>
              <DatePickerWithDailyReport range={selectedDateRange} setRange={onDateRangeChange}/>
              {/*<DateRangePicker range={selectedDateRange} setRange={onDateRangeChange} />
              <FormControl className={classes.dailyReportSelector} >
                <NativeSelect
                  value={selectedReport ? selectedReport.id : ""}
                  onChange={onSelectedReportChange}
                  inputProps={{
                    id: 'daily-report-selector',
                  }}
                  input={<NativeSelectInput />}
                  disabled={isEmpty(reportOptions)}
                >
                  <option value="">{t('waiter-tax-report-active-report-headline')}</option>
                  {reportOptions.map(reportOption => (
                    <option key={reportOption.id} value={reportOption.id}>{`${t('waiter-tax-report-active-report-headline')} ${reportOption.count}`}</option>
                  ))}
                </NativeSelect>
              </FormControl>*/}
            </div>
          )}
          <Button
            classes={{ root: classes.buttonRoot }}
            color="primary"
            onClick={() => setCard({})}
            className={classes.createButton}
          >
            {t('card-editor-actions-create-card')}
          </Button>
        </div>
      </div>
      {!isEmpty(cards) && !isEmpty(report) && cardsGroupBy === "CARDS" && (
        <div className={classes.reportCards}>
          <div className={classes.card}>
            <Typography className={classes.cardTitle}>{`${(report.totalBalance ?? 0).toFixed(2)} €`}</Typography>
            <Typography className={classes.cardSub}>{t('cards-balance-label')}</Typography>
          </div>
          <div className={classes.card}>
            <Typography className={classes.cardTitle}>{`${cards.total ?? 0}`}</Typography>
            <Typography className={classes.cardSub}>{t('cards-total-count-label')}</Typography>
          </div>
        </div>
      )}
      {renderCardsTable()}
      <div style={{ overflow: "auto" }}>
        {!isEmpty(totalBlockTransactionInfo) && cardsGroupBy === "TRANSACTIONS" && currentNavigation === "reporting" && (
          <div className={clsx(classes.reportCards, { [classes.reportCardsMobile]: isMobile })}>
            <div className={clsx(classes.strippedCard, { [classes.strippedCardMobile]: isMobile })}>
              <Typography className={classes.cardTitle}>{`${(totalBlockTransactionInfo.cashInTotal ?? 0).toFixed(2)} €`}</Typography>
              <div style={{ display: "flex", alignItems: "center", gap: 8, flexWrap: "wrap" }}>
                <Typography className={classes.cardSub} style={{ whiteSpace: "nowrap" }}>{t('payment-channels-cash')}: {`${(totalBlockTransactionInfo.cashInByType.cash ?? 0).toFixed(2)} €`}</Typography>
                <Typography className={classes.cardSub} style={{ whiteSpace: "nowrap" }}>{t('external-voucher')}: {`${(totalBlockTransactionInfo.cashInByType.externalVouchers ?? 0).toFixed(2)} €`}</Typography>
                <Typography className={classes.cardSub} style={{ whiteSpace: "nowrap" }}>{t('other')}: {`${(totalBlockTransactionInfo.cashInByType.other ?? 0).toFixed(2)} €`}</Typography>
              </div>
            </div>
            <div className={clsx(classes.strippedCard, { [classes.strippedCardMobile]: isMobile })}>
              <Typography className={classes.cardTitle}>{`${(totalBlockTransactionInfo.cashOutTotal ?? 0).toFixed(2)} €`}</Typography>
              <Typography className={classes.cardSub}>{t('total-cash-out-gift-cards')}</Typography>
            </div>
            <div className={clsx(classes.strippedCard, { [classes.strippedCardMobile]: isMobile })}>
              <Typography className={classes.cardTitle}>{`${totalBlockTransactionInfo.adjustmentTotal ?? 0} €`}</Typography>
              <Typography className={classes.cardSub}>{t('total-adjustments-gift-cards')}</Typography>
            </div>
          </div>
        )}
        {currentNavigation === "reporting" && cardsGroupBy === "TRANSACTIONS" && (
          <CardTransactionsTableReporting searchQuery={query} setCard={setCard} view={tab} selectedDateRange={selectedDateRange} setSelectedDateRange={handleDateRangeChange} setTotalBlockTransactionInfo={setTotalBlockTransactionInfo} />
        )}
      </div>
      {currentNavigation === "marketing" && (
        <CardTransactionsTable searchQuery={query} setCard={setCard} view={tab} />
      )}
      {card && <GiftCardModal open={card} onClose={() => {
        setCard(null)
        fetchCards();
      }} values={card} submit={submitCard} submitting={submitting} accounts={accounts} _delete={deleteCard}/>}
      {scanningCard && (
        <SimpleQRScanModal open={scanningCard} onClose={stopScanningCard} setValue={setQuery}/>
      )}
    </div>
  );
});
