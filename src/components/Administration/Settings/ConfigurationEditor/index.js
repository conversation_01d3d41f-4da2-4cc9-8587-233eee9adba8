import getConfig from "next/config";
import { ButtonBase, useMediaQuery } from "@material-ui/core";
import React, { Fragment, useEffect, useState } from "react";
import { withTranslation } from "../../../../../i18n";
import {
  getRestaurantConfiguration,
  updateRestaurantConfigurationById,
  getSystemLanguages,
  getReservationConfig,
  updateReservationConfig, getInventoryCategories, deleteInventory, updateAccountPreferences
} from "../../../../api";
import useStyles from "./styles";
import { useDispatch, useSelector } from "react-redux";
import { accountSelectors, applicationSelectors } from "../../../../../redux/selectors";
import { MenuProps, noop, permissionIdentifiers } from "../../../../utils/const";
import { configurationActions } from "../../../../../redux/actions";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../styles/typography";
import palette from "../../../../../styles/palette";
import Switch from "../../../_toggles/Switch";
import Field from "../../../form/Field";
import TextUpdateModal from "../../../_popup/TextUpdateModal";
import { ChevronDown20new, CopyIcon20, EditIcon20, TrashIcon20White } from "../../../../utils/icons";
import FormControl from "@material-ui/core/FormControl";
import Select from "@material-ui/core/Select";
import {
  receiptModeOptions, receiptOrderColumnsOptions,
  receiptPaymentColumnsOptions
} from "../../../../../redux/constants";
import Chip from "@material-ui/core/Chip";
import MenuItem from "@material-ui/core/MenuItem";
import { Paragraph } from "../../../Text";
import isEmpty from "../../../../utils/isEmpty";
import isValidEmail from "../../../../utils/isValidEmail";
import SetupCoursesModal from "../../../_popup/SetupCoursesModal";
import packageJSON from "../../../../../package.json";
import { Confirm } from "../../../Confirmation";
import PinModal from "../../../_popup/PinModal";
import posthog from 'posthog-js'
import OpenInvoiceBankInfoUpdateModal from "../../../_popup/OpenInvoiceBankInfoUpdateModal";

/*  eslint-disable */

const paymentMethodOptions = [
  {
    key: "CARD",
    i18nKey: "payment-channels-card",
  },
  {
    key: "SEPA_DEBIT",
    i18nKey: "payment-method-sepa-debit"
  },
];

export const ConfigurationEditor = withTranslation("common")(({ t, restaurantId }) => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const isMobile = useMediaQuery("(max-width:900px)");

  const userAccount = useSelector(accountSelectors.getAccountMemo);
  const { isManaged = false, email } = (userAccount || {})

  const { publicRuntimeConfig } = getConfig();
  const isProd = publicRuntimeConfig.APPLICATION_ENV === 'production';
  
  const showResetConfiguration = !isProd
  
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const canUpdateGeneralSettings = permissionIds.includes(permissionIdentifiers.CAN_UPDATE_GENERAL_SETTINGS.value);
  const [accounts, setAccounts] = useState([]);
  const [accountsById, setAccountsById] = useState({});

  const { version: nativeVersion, embedded, device, platform, readerId } = useSelector(applicationSelectors.getParams)
  const { version: packageJSONVersion } = packageJSON || {};
  const version = `${packageJSONVersion}-${nativeVersion}`

  const isSunmiDevice = useSelector(applicationSelectors.getIsSunmiDevice)
  const [devices, setDevices] = useState(null);
  const [selectedDeviceId, setSelectedDeviceId] = useState(null);

  const handleGetDevicesEvent = (event) => {
    const parsedData = JSON.parse(event?.detail)
    setDevices(parsedData);  // Update React state with the data from the event
  };

  const handleGetSelectedDeviceEvent = (event) => {
    const parsedData = JSON.parse(event?.detail)
    setSelectedDeviceId(parsedData.deviceId)
  }

  const updateSelectedDeviceId = (event) => {
    try {
      JSBridge.setSelectedDevice(event.target.value)
      setSelectedDeviceId(event.target.value)
    } catch (e) {console.log(e)}
  }

  useEffect(() => {
    if (isSunmiDevice) {
      try {
        JSBridge.getSelectedDevice()
        JSBridge.getDevices()
      } catch (e) {console.log(e)}
      // Add event listener for the custom event 'receiveDataEvent'
      window.addEventListener('getSelectedDevice', handleGetSelectedDeviceEvent);
      window.addEventListener('getDevices', handleGetDevicesEvent);
      window.addEventListener('getMeasurement', handleGetDevicesEvent);

      // Cleanup the event listener when the component unmounts
      return () => {
        window.removeEventListener('getSelectedDevice', handleGetSelectedDeviceEvent);
        window.removeEventListener('getDevices', handleGetDevicesEvent);
      };
    }
  }, [isSunmiDevice]);

  const getDevices = () => {
    try { JSBridge.getDevices() } catch (e) {console.log(e)}
  }

  const connectSUNMISecondaryScreen = () => {
    try { JSBridge.connectSecondaryDisplay() } catch (e) {console.log(e)}
  }

  const [configurationForm, setConfigurationForm] = useState({partnerConfigs:[]});
  const [reservationConfigurationForm, setReservationConfigurationForm] = useState({});
  const [editing, setEditing] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const [contentLanguagesOptions, setContentLanguagesOptions] = useState([])

  const fetchLanguages = () => {
    getSystemLanguages()
      .then(({ data }) => {
        const { items = [] } = (data || {})
        setContentLanguagesOptions(items)
      })
      .catch(()=>{})
  }
  const handleToggle = (stateKey, value) => {
    const updatedConfiguration = { ...configurationForm, [stateKey]: value };
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };

  const handleDSLSettingsToggle = (stateKey, value) => {
    const updatedConfiguration = { ...configurationForm, dslSettings: { ...dslSettings, [stateKey]: value }};
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };

  const handleFrontendSettingsToggle = (stateKey, value) => {
    const updatedConfiguration = { ...configurationForm, frontEndSettings: { ...frontEndSettings, [stateKey]: value }};
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };

  const handleInventoryToggle = (stateKey, value) => {
    const updatedConfiguration = { ...configurationForm, inventoryConfig: { ...inventoryConfig, [stateKey]: value }};
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };

  const handleCancelledItemsActionToggle = (prevValue) => {
    const newValue = prevValue === "ALWAYS_RETURN" ? "NEVER_RETURN" : "ALWAYS_RETURN"
    const updatedConfiguration = { ...configurationForm, inventoryConfig: { ...inventoryConfig, actionOnItemCancellation: newValue }};
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  }

  const handleReceiptConfigToggle = (stateKey, value) => {
    const updatedConfiguration = { ...configurationForm, receiptConfig: { ...receiptConfig, [stateKey]: value }};
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };

  const handleAutoClosureToggle = (stateKey, value) => {
    const updatedConfiguration = {
      ...configurationForm,
      taxReportConfig: { ...configurationForm.taxReportConfig, [stateKey]: value }
    };
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };

  const handleCourseConfigEnabledToggle = (stateKey, value) => {
    const updatedConfiguration = {
      ...configurationForm,
      courseConfig: { ...configurationForm.courseConfig, [stateKey]: value }
    };
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };
  
  const handleDailyReportToggle = (stateKey, value) => {
    const updatedConfiguration = {
      ...configurationForm, dailyReportConfig: { ...configurationForm.dailyReportConfig, [stateKey]: value }
    };
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };

  const fetchConfiguration = () => {
    getRestaurantConfiguration(restaurantId)
      .then(({ data = {} }) => {
        setConfigurationForm(data ?? {});
      })
      .catch(() => {
      });
    dispatch(configurationActions.getConfiguration(restaurantId));
  };

  useEffect(() => {
    fetchConfiguration();
    fetchLanguages();
  }, []);

  const [updatingTaxReportClosureTime, setUpdatingTaxReportClosureTime] = useState(false);
  const [updatingTaxReportEmails, setUpdatingTaxReportEmails] = useState(false);
  const [updatingCourses, setUpdatingCourses] = useState(false)
  const [updatingInventoryPurchaseInjectionEmail, setUpdatingInventoryPurchaseInjectionEmail] = useState(false)
  const [updatingOpenInvoiceDueDate, setUpdatingOpenInvoiceDueDate] = useState(false);
  const [updatingOpenInvoiceAdditionalMemo, setUpdatingOpenInvoiceAdditionalMemo] = useState(false);
  const [updatingOpenInvoiceBankInfo, setUpdatingOpenInvoiceBankInfo] = useState(false)

  const updateTaxReportClosureTime = (value) => {
    const updatedConfiguration = {
      ...configurationForm,
      taxReportConfig: { ...configurationForm.taxReportConfig, closureTime: value }
    };
    updateConfiguration(updatedConfiguration);
  }

  const updateOpenInvoiceDueDate = (value) => {
    if (value < 0) return;
    const updatedConfiguration = {
      ...configurationForm,
      openInvoiceSettings: { ...configurationForm.openInvoiceSettings, daysUntilDue: value }
    };
    updateConfiguration(updatedConfiguration);
  };

  const updateOpenInvoiceAdditionalMemo = (value) => {
    const updatedConfiguration = {
      ...configurationForm,
      openInvoiceSettings: { ...configurationForm.openInvoiceSettings, additionalMemo: value }
    };
    updateConfiguration(updatedConfiguration);
  }

  const updateDefaultReceiptOption = (e) => {
    const updatedConfiguration = { ...configurationForm,
      receiptOption: {...configurationForm.receiptOption, type: e.target.value}
    };
    setConfigurationForm(updatedConfiguration)
    updateConfiguration(updatedConfiguration);
  };

  const fetchReservationSettings = () => {
    getReservationConfig(restaurantId)
      .then(({ data }) => setReservationConfigurationForm(data))
      .catch(() => {})
  }

  useEffect(() => {
    fetchReservationSettings();
  }, []);

  const updateDefaultReceiptColumns = (e, stateKey) => {
    // if (e.target.value.length > 6) {
    //   dispatch(setNotification("select-limit-has-been-reached", "error"))
    //   return;
    // }
    let val = e.target.value;
    val = [...new Set(val ?? [])]

    const updatedConfiguration = { ...configurationForm, receiptConfig:{ ...receiptConfig, [stateKey]: val }};
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  }

  const updateTaxReportEmails = (value) => {
    let emails = isEmpty(value) ? null : value.replaceAll(" ", "").split(',')
    if (!isEmpty(emails) && emails.length) {
      emails = emails.filter(e => isValidEmail(e))
      if (isEmpty(emails)) {
        emails = null
      }
    }

    const updatedConfiguration = {
      ...configurationForm,
      dailyReportConfig: { ...configurationForm.dailyReportConfig, emails: emails, automatedEmail: true }
    };
    updateConfiguration(updatedConfiguration);
  }

  const updateInventoryPurchaseInjectionEmail = (value) => {
    if (isValidEmail(value)) {
      const updatedConfiguration = {
        ...configurationForm,
        inventoryConfig: { ...configurationForm.inventoryConfig, invoiceEmail: value }
      };
      updateConfiguration(updatedConfiguration);
    }
  }

  const updateDiscountPin = (value) => {
    const updatedConfiguration = {
      ...configurationForm, discountPin: value
    };
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  }

  const updatePollPrinterAccountIds = (e) => {
    let val = e.target.value;
    val = [...new Set(val ?? [])]

    const updatedConfiguration = { ...configurationForm, pollPrinterAccountIds: val };
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };

  const updateCourses = (courses) => {
    const updatedConfiguration = {
      ...configurationForm,
      courseConfig: { enabled: true, courses }
    };
    updateConfiguration(updatedConfiguration);
  };

  const updateOpenInvoiceBankInfo = (bankInfo) => {
    const updatedConfiguration = {
      ...configurationForm,
      openInvoiceSettings: { ...configurationForm.openInvoiceSettings, bankDetails: bankInfo }
    };
    updateConfiguration(updatedConfiguration);
  };

  const updateOpenInvoicePaymentMethod = (e) => {
    const updatedConfiguration = {
      ...configurationForm,
      openInvoiceSettings: { ...configurationForm.openInvoiceSettings, paymentMethods: e.target.value }
    };
    updateConfiguration(updatedConfiguration);
  }

  const toggleAllowTableMerging = () => {
    const updatedConfig = { ...reservationConfigurationForm, allowMultipleTableSelection: !reservationConfigurationForm.allowMultipleTableSelection }
    setReservationConfigurationForm(updatedConfig)
    updateReservationConfig(restaurantId, updatedConfig )
      .then(() => {
        fetchReservationSettings()
      })
      .catch(() => {})
  }

  const updateConfiguration = (updatedConfiguration) => {
    setSubmitting(true);
    if (configurationForm && configurationForm.id) {
      updateRestaurantConfigurationById(restaurantId, updatedConfiguration)
        .then(() => setEditing(false))
        .then(() => {
          fetchConfiguration();
        })
        .catch(() => {
        })
        .finally(() => setSubmitting(false));
    }
  };

  const resetConfiguration = () => {
    const defaultConfigurationData = {
      "printByMenu": true,
      "printByMenuItem": true,
      "printByAddition": true,
      "hasExpress": false,
      "partialCheckout": true,
      "showInventory": true,
      "taxReportConfig": {
        "autoClosure": true
      },
      "checkoutAutoClose": false,
      "enableTakeawayInDineIn": false,
      "deepCopyReorderingEnabled": false,
      "restaurantServiceMode": "TABLE",
      "enableMandatoryCancelReason": false,
      "templateConfig": {
        "capabilities": [
          "DINE_IN",
          "EXPRESS",
          "TAKEAWAY",
          "RESERVATION"
        ]
      },
      "estimatedDiningTime": 120,
      "estimatedPreparationTime": 45,
      "preparationTimeInMinutes": {
        "PICKUP": 45,
        "DELIVERY": 45,
        "EXPRESS": 45,
        "UBER_EATS": 45,
        "WOLT": 45,
        "LIEFERANDO": 45
      },
      "partnerConfigs": [
        {
          "partnerId": "LIEFERANDO",
          "unseenOrder": {
            "action": "ACCEPT",
            "waitingTimeInSeconds": 15
          }
        },
        {
          "partnerId": "WOLT",
          "unseenOrder": {
            "action": "ACCEPT",
            "waitingTimeInSeconds": 15
          }
        },
        {
          "partnerId": "UBER_EATS",
          "unseenOrder": {
            "action": "ACCEPT",
            "waitingTimeInSeconds": 15
          }
        }
      ],
      "receiptOption": {
        "type": "BUSINESS"
      },
      "useUpdatedMenuEditor": true,
      "useUpdatedTerminal": true,
      "useUpdatedCheckout": true,
      "webshopConfig": {
        "printWebshopUrlQRCodeOnFinalReceipt": false,
        "printAddressQrCodeForDriver": false,
        "deliveryFeeMode": "KM_RADIUS",
        "incomingOrderSound": 1,
        "pollSoundNotificationAccountIds": [],
        "promotionText": "",
        "disableTakeawayEmailCopy": true,
        "disableNewOrderReceipt": false,
        "pickupPaymentChannels": [
          "APP",
          "CASH",
          "CARD"
        ],
        "deliveryPaymentChannels": [
          "APP",
          "CASH",
          "CARD"
        ],
        "webshopHours": {
          "TUESDAY": [
            {
              "opens": "00:00:00",
              "closes": "23:59:00"
            }
          ],
          "MONDAY": [
            {
              "opens": "01:00:00",
              "closes": "15:10:00"
            },
            {
              "opens": "15:20:00",
              "closes": "21:00:00"
            }
          ]
        },
        "hasDifferentWebshopHours": false,
        "allowGiftCards": false,
        "allowPromoCodes": false,
        "status": "ONLINE",
        "useNewWebshop": configurationForm?.webshopConfig?.useNewWebshop ?? false,
        "covers": configurationForm?.webshopConfig?.covers ?? [],
      },
      "takeawayConfig": {
        "bufferTime": 0,
        "wolt": {
          "useV2Integration": true
        },
        "allowDriverAssignment": true,
        "printDriverOnReceipt": false
      },
      "giftCardCheckoutEnabled": false,
      "giftCardSettings": {
        "suggestedAmounts": [
          {
            "type": "FIXED",
            "value": 10
          },
          {
            "type": "FIXED",
            "value": 20
          },
          {
            "type": "FIXED",
            "value": 70
          },
          {
            "type": "CUSTOM"
          }
        ]
      },
      "allowPagerInputInExpress": false,
      "courseConfig": {
        "enabled": false,
        "courses": [
          {
            "name": "adad",
            "number": 1,
            "nameI18n": {
              "en": "",
              "de": "adad",
              "zh": ""
            }
          }
        ]
      },
      "kioskConfig": {
        "languages": [
          "en",
          "de"
        ],
        "paymentMethods": [
          "CASH",
          "CARD"
        ],
        "heroImageUrl": "https://storage.googleapis.com/leviee_public/restaurants/62b1b639230d5d186d059699/images/01JS1R8C0BA0KKB9DWDZPSGRBY.jpg",
        "allowPromotions": false,
        "allowGiftCards": false,
        "enableBypassSinglePaymentMethod": false,
        "tipEnabled": false,
        "diningOptions": [
          "IN_HOUSE",
          "TO_GO"
        ]
      },
      "maxTablesPerRow": 8,
      "minHoursReportCloseInterval": 8,
      "warningThresholdReportCloseInHours": 26,
      "disableSendToKitchenConfirmation": false,
      "displayOrderedItemsTotalsInMenu": true,
      "disableFinalReceiptForSplitPayments": true,
      "enabledSingleKitchenPrintoutForTakeawayOrders": false,
      "frontEndSettings": {
        "showSmallMenuItemsInMenus": false,
        "showInvoicePreviewInCheckout": false,
        "resolvePaidItemsUnderPaymentInReceipts": false
      },
      "openInvoiceSettings": {
        "daysUntilDue": 7,
        "additionalMemo": "My Custom Memoooo",
        "paymentMethods": [
          "CARD",
          "SEPA_DEBIT"
        ],
        "bankDetails": {
          "beneficiary": "allO Company",
          "iban": "DE32323232332",
          "bic": "Bic123",
          "bank": "TestBank"
        }
      }
    }
    updateConfiguration(defaultConfigurationData)

    const defaultPreferences = {
      "id": "01G632X60J2TZPFMHAXV346BZN",
      "accountId": "01G632X5RHD4BG76WY9RA03B38",
      "menusElement": "card",
      "menuDragToScroll": false,
      "terminalView": "PLAN",
      "avatarUrl": "https://storage.googleapis.com/leviee_public/accounts/01G632X5RHD4BG76WY9RA03B38/images/8309723042025192054873.png",
      "orientation": "RIGHT",
      "terminalMenuOrientation": "LEFT",
      "readingMode": "NORMAL",
      "theme": "LIGHT_MODE",
      "language": "en",
      "topNavigationButtonSize": "LARGE",
      "numberOfCategories": 3,
      "appFontSize": 1.0,
      "responsiveMenuCategories": true,
      "dineInView": "TILE",
      "enableOrderGroupBy": true,
      "orderGroupByDefault": "NONE",
      "dineInTileSize": "TWO"
    }
    updateAccountPreferences(defaultPreferences).then(() => {}).catch(() => {})
  }

  const getTaxReportClosureTimeField = () => {
    const { taxReportConfig = {} } = (configurationForm ?? {});
    const { closureTime } = taxReportConfig;

    return (
      <Fragment>
        <Field
          value={closureTime}
          readOnly
        />
        <TextUpdateModal
          titleI18n={"configuration-editor-auto-closure-time-field-label"}
          open={updatingTaxReportClosureTime}
          onClose={() => setUpdatingTaxReportClosureTime(false)}
          value={closureTime}
          setValue={updateTaxReportClosureTime}
          type={"time"}
          fieldProps={{
            type: "time"
          }}
        />
      </Fragment>
    )
  }

  const getOpenInvoiceDueDateField = () => {

    return (
      <Fragment>
        <Field
          value={daysUntilDue}
          readOnly
        />
        <TextUpdateModal
          titleI18n={"open-invoice-due-date-field-label"}
          open={updatingOpenInvoiceDueDate}
          onClose={() => setUpdatingOpenInvoiceDueDate(false)}
          value={daysUntilDue}
          setValue={updateOpenInvoiceDueDate}
          type={"number"}
          onlyPositiveValues
          fieldProps={{
            type: "number"
          }}
        />
      </Fragment>
    )
  };

  const getOpenInvoiceAdditionalMemoField = () => {

    return (
      <Fragment>
        <Field
          value={additionalMemo}
          readOnly
        />
        <TextUpdateModal
          titleI18n={"open-invoice-additional-memo-field-label"}
          open={updatingOpenInvoiceAdditionalMemo}
          onClose={() => setUpdatingOpenInvoiceAdditionalMemo(false)}
          value={additionalMemo}
          setValue={updateOpenInvoiceAdditionalMemo}
          type={"text"}
          fieldProps={{
            type: "text"
          }}
        />
      </Fragment>
    )
  }

  const getTaxReportEmailsField = () => {
    const consolidatedEmails = isEmpty(emails) ? "" : emails.join(', ')

    return (
      <Fragment>
        <Field
          value={consolidatedEmails}
          readOnly
          placeholder={"<EMAIL>,<EMAIL>"}
        />
        <TextUpdateModal
          titleI18n={"send-reports-to-email"}
          open={updatingTaxReportEmails}
          onClose={() => setUpdatingTaxReportEmails(false)}
          value={consolidatedEmails}
          setValue={updateTaxReportEmails}
          fieldProps={{
            placeholder: "<EMAIL>,<EMAIL>"
          }}
        />
      </Fragment>
    )
  }

  const getInventoryPurchaseInjectionEmail = () => {
    const { inventoryConfig = {} } = (configurationForm ?? {});
    const { invoiceEmail = "" } = inventoryConfig;

    return (
      <Fragment>
        <Field
          value={invoiceEmail}
          readOnly
          placeholder={"<EMAIL>"}
        />
        <TextUpdateModal
          titleI18n={"send-reports-to-email"}
          open={updatingInventoryPurchaseInjectionEmail}
          onClose={() => setUpdatingInventoryPurchaseInjectionEmail(false)}
          value={invoiceEmail}
          setValue={updateInventoryPurchaseInjectionEmail}
          fieldProps={{
            placeholder: "<EMAIL>"
          }}
        />
      </Fragment>
    )
  }

  const openReaderSettings = () => {
    // try { window.JSBridge.openSettings() } catch (e) {console.log(e)}
    // try { window.JSBridge.openSettings } catch (e) {console.log(e)}
    try { JSBridge.openSettings() } catch (e) {console.log(e)}
    // try { JSBridge.openSettings } catch (e) {console.log(e)}
  }

  const [updatingDiscountPinField, setUpdatingDiscountPinField] = useState(false);
  const updatePinField = (val) => {
    setUpdatingDiscountPinField(false);
    updateDiscountPin(val)
  }

  const getDiscountPinField = () => {
    const { discountPin } = (configurationForm ?? {});

    return (
      <div>
        <Field
          value={discountPin}
          disabled
        />
        <PinModal
          open={updatingDiscountPinField}
          onClose={() => setUpdatingDiscountPinField(false)}
          setValue={updatePinField}
          length={4}
        />
      </div>
    )
  }

  const updateContentLanguages = (e) => {
    const updatedForm = { ...configurationForm, additionalLanguages: e.target.value };
    setConfigurationForm(updatedForm);
    updateConfiguration(updatedForm)
  }

  const updateDefaultLanguages = (e) => {
    const updatedForm = { ...configurationForm, defaultLanguage: e.target.value };
    setConfigurationForm(updatedForm);
    updateConfiguration(updatedForm)
  }

  const [inventoryError, setInventoryError] = useState("")
  const [inventoryCategories, setInventoryCategories] = useState({})
  const hasInventorySetUp = !isEmpty(inventoryCategories.items)

  const fetchInventoryCategories = () => {
    getInventoryCategories(restaurantId)
      .then(({ data: fetchedInventoryCategories = {} }) => {
        setInventoryCategories(fetchedInventoryCategories)
      })
      .catch(noop)
  }

  useEffect(() => {
    fetchInventoryCategories()
  },[])

  const handleDelete = () => {
    deleteInventory(restaurantId)
      .then(() => {
        //handleInventoryToggle("enableConsumption", false)
        //handleInventoryToggle("enableMenuSync", false)
      })
      .then(() => {
        fetchInventoryCategories()
      })
      .catch(({ response }) => {
        const { data } = (response || {})
        const { status, title } = (data || {})
        setInventoryError(title)
      })
  };

  const {
    taxReportConfig = {},
    receiptConfig = {},
    hasOngoingItems,
    partialCheckout,
    hideItemsWithPriceZero,
    hideMenuItemNotesFromCustomers,
    displayOrderedItemsTotalsInMenu,
    printByAddition,
    printByMenu,
    printByMenuItem,
    hasCashRegister,
    disableDailyReportPrintout,
    disableDigitalReceiptPrintout,
    disableFinalReceiptForSplitPayments,
    disableCancelledItemPrintout,
    checkoutAutoClose,
    staticExpressTerminal,
    pollPrinterAccountIds,
    useUpdatedMenuEditor,
    useUpdatedTerminal,
    useUpdatedCheckout,
    showConnectivityIssueNotification,
    allowPagerInputInExpress,
    allowTableSelectionInExpress,
    disableCancellationReceipt,
    enableTerminalPaymentReceipt,
    defaultLanguage,
    useFirstOrderingInMonthlyReports,
    enableMandatoryCancelReason,
    additionalLanguages = [],
    courseConfig = {},
    inventoryConfig = {},
    dslSettings = {},
    enableTakeawayInDineIn,
    disableSendToKitchenConfirmation,
    frontEndSettings = {},
    deepCopyReorderingEnabled,
    showInventory,
    openInvoiceSettings = {},
    customExtrasEnabled,
    dailyReportConfig = {},
  } = (configurationForm ?? {});
  const { emails = [],  includeReconciliation } = dailyReportConfig;
  const { autoClosure } = taxReportConfig;
  const { daysUntilDue, additionalMemo, bankDetails = {}, paymentMethods = [] } = openInvoiceSettings;
  const { enabled: courseConfigEnabled, courses } = courseConfig
  const { defaultOrderColumns = [], defaultPaymentColumns = [], hideCancelledOrdersWithoutPayments } = (receiptConfig || {});

  const { enableConsumption, invoiceEmail, enableMenuSync, actionOnItemCancellation } = (inventoryConfig || {})
  const { callMonitoringEnabled } = (dslSettings || {})
  const {
    showSmallMenuItemsInMenus,
    showInvoicePreviewInCheckout,
    resolvePaidItemsUnderPaymentInReceipts,
    enableMenuItemContextActions,
    showMapInTakeaway,
    showLastPaymentRequestInCheckout
  } = (frontEndSettings || {})
  const isReturnEnabled = actionOnItemCancellation === "ALWAYS_RETURN";

  if (!canUpdateGeneralSettings) {
    return null;
  }

  const TableUpdateBtn = () => (
    <div style={{ display: "flex", alignItems: "center", paddingLeft: 10, paddingRight: 10, minWidth: 100 }}>
      <EditIcon20 />
      <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], marginLeft: 2 }}>{t("common-update")}</Typography>
    </div>
  );

  const getCourses = () => {
    if (!courseConfigEnabled) {
      return null
    }

    return (
      <div style={{ marginTop: 24 }}>
        <div className={classes.flex}>
          <div style={{ marginRight: 8, marginLeft: 4 }}>
            <Typography style={{ ...typography.body.medium }}>{t("setup-courses")}</Typography>
          </div>
          <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                      style={{ padding: 0 }} onClick={() => setUpdatingCourses(true)}>
            <TableUpdateBtn />
          </ButtonBase>
        </div>
        <div className={classes.switchSetting} style={{ marginTop: 16 }}>
          {!isEmpty(courses) && courses.map((course, index) => {
            const { number, nameI18n } = (course || {})
            const { en, de, zh } = nameI18n

            const firstChild = index === 0
            return (
              <div className={classes.flex} style={{ marginTop: firstChild ? 0 : 16 }}>
                <div>
                  <Typography style={{ ...typography.body.medium }}>
                    {`${en} / ${de} / ${zh}`}
                  </Typography>
                  <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 4 }}>
                    {`${t('course')} ${number}`}
                  </Typography>
                </div>
              </div>
            )
          })}
          {updatingCourses && (
            <SetupCoursesModal
              open={updatingCourses}
              onClose={() => setUpdatingCourses(false)}
              courses={courses}
              setValue={updateCourses}
              fieldProps={{
                type: "text"
              }}
            />
          )}
        </div>
      </div>
    )
  }

  const getOpenInvoiceBankInformation = () => {
    return (
      <div>
        <div className={classes.flex} style={{ marginTop: 24 }}>
          <div style={{ marginRight: 8 }}>
            <Typography style={{ ...typography.body.medium }}>{t("bank-details-open-invoice")}</Typography>
          </div>
          {!isEmpty(bankDetails) ? (
            <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                        style={{ padding: 0 }} onClick={() => setUpdatingOpenInvoiceBankInfo(true)}>
            <TableUpdateBtn />
          </ButtonBase>) : (
            <ButtonBase style={{ padding: "5px 5px 9px 5px", border: `1px solid ${palette.grayscale.border}`, borderRadius: 10, cursor: "pointer", marginTop: 12 }}
                        onClick={() => setUpdatingOpenInvoiceBankInfo(true)}>
              <Typography style={{ ...typography.body.regular }}>{t("add-bank-info")}</Typography>
            </ButtonBase>
          )}
        </div>
        {!isEmpty(bankDetails) && (
          <div className={classes.switchSetting} style={{ marginTop: 16 }}>
            <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-start", gap: 8 }}>
              <Typography style={{ ...typography.body.medium}}>{t("common-beneficiary")}</Typography>
              <Typography style={{ ...typography.body.regular }}>{bankDetails.beneficiary}</Typography>
            </div>
            <div style={{ display: "flex", flexDirection: "column", marginTop: 12, paddingTop: 12, borderTop: `1px dashed ${palette.grayscale.border}`, gap: 24 }}>
              <div style={{ display: "flex", alignItems: "baseline", justifyContent: "space-between" }}>
                <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-start", gap: 8 }}>
                  <Typography style={{ ...typography.body.medium}}>{t("common-bank")}</Typography>
                  <Typography style={{ ...typography.body.regular }}>{bankDetails.bank}</Typography>
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  borderRadius: 12,
                  backgroundColor: bankDetails?.enabled ? palette.secondary.green["100"] : palette.negative["100"],
                  padding: '2px 8px'
                }}>
                  <div style={{
                    borderRadius: '100%',
                    width: 8,
                    height: 8,
                    backgroundColor: bankDetails?.enabled ? palette.secondary.green["400"] : palette.negative["400"],
                    marginRight: 6
                  }} />
                  <Typography style={{ ...typography.small.medium, textTransform: "capitalize"}}>{bankDetails?.enabled? t("active") :  t("printer-editor-form-disabled-field-label")}</Typography>
                </div>
              </div>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-start", gap: 8 }}>
                  <Typography style={{ ...typography.body.medium}}>{t("common-iban")}</Typography>
                  <Typography style={{ ...typography.body.regular }}>{bankDetails?.iban}</Typography>
                </div>
                <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-start", gap: 8 }}>
                  <Typography style={{ ...typography.body.medium}}>{t("common-bic")}</Typography>
                  <Typography style={{ ...typography.body.regular }}>{bankDetails?.bic}</Typography>
                </div>
              </div>
            </div>
          </div>
        )}
        {updatingOpenInvoiceBankInfo && (
          <OpenInvoiceBankInfoUpdateModal
            open={updatingOpenInvoiceBankInfo}
            onClose={() => setUpdatingOpenInvoiceBankInfo(false)}
            bankInfo={bankDetails}
            setValue={updateOpenInvoiceBankInfo}
          />
        )}
      </div>
    )
  };

  const contentStyle = {
    display: "flex",
    flexDirection: isMobile ? "column" : "row",
    alignItems: "top",
    marginTop: 32,
    paddingTop: 32,
    borderTop: "1px solid #E8E7E6"
  }

  const contentDescriptionStyle = {
    marginRight: isMobile ? 0 : 42,
    marginBottom: isMobile ? 32 : 0,
    width: isMobile ? " 100%" : 240
  }

  return (
    <>
      <div className={classes.content} data-testid="restaurant-settings-configuration-editor">
        {/* checkout settings */}
        <div style={contentStyle}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>{t("checkout-settings")}</Typography>
            <Typography
              style={{ ...typography.body.regular, marginTop: 4 }}>{t("control-checkout-settings")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            {/*autoclose express order on checkout*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("checkout-auto-close")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("directly-close-express-orders-on-checkout")}</Typography>
                </div>
                <Switch checked={checkoutAutoClose ?? false}
                        data-testid="checkout-auto-close"
                        onClick={() => handleToggle("checkoutAutoClose", !checkoutAutoClose)} />
              </div>
            </div>
            {/*start new express order after order closed automatically*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("reorder-express-terminal")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-reordering-flow-for-express-terminal-after-closing-order")}</Typography>
                </div>
                <Switch checked={staticExpressTerminal ?? false}
                        data-testid="reorder-express-terminal"
                        onClick={() => handleToggle("staticExpressTerminal", !staticExpressTerminal)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("configuration-editor-has-partial-checkout-field-label")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-partial-checkout-toggle-description")}</Typography>
                </div>
                <Switch checked={configurationForm.partialCheckout ?? false}
                        data-testid="configuration-editor-has-partial-checkout-field-label"
                        onClick={() => handleToggle("partialCheckout", !partialCheckout)} />
              </div>
            </div>
            {/*showLastPaymentRequestInCheckout*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("allow-to-select-last-payment-request")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-selecting-items-from-last-payment-request-in-checkout-to-redo-payment")}</Typography>
                </div>
                <Switch checked={showLastPaymentRequestInCheckout ?? false}
                        onClick={() => handleFrontendSettingsToggle("showLastPaymentRequestInCheckout", !showLastPaymentRequestInCheckout)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("hide-zero-price-items")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("do-not-show-items-with-zero-price-in-checkout-and-receipts")}</Typography>
                </div>
                <Switch checked={hideItemsWithPriceZero ?? false}
                        data-testid="hide-zero-price-items"
                        onClick={() => handleToggle("hideItemsWithPriceZero", !hideItemsWithPriceZero)} />
              </div>
            </div>
            {/*<div className={classes.switchSetting} style={{ marginTop: 24 }}>*/}
            {/*  <div className={classes.flex}>*/}
            {/*    <div style={{ marginRight: 8 }}>*/}
            {/*      <Typography style={{ ...typography.body.medium }}>{t("configuration-editor-has-checkout-default-toGo-label")}</Typography>*/}
            {/*      <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("enable-checkout-default-toGo-toggle-description")}</Typography>*/}
            {/*    </div>*/}
            {/*    <Switch checked={configurationForm.checkoutDefaultToGo ?? false}*/}
            {/*            onClick={() => handleToggle("checkoutDefaultToGo", !checkoutDefaultToGo)} />*/}
            {/*  </div>*/}
            {/*</div>*/}
            {/*disable popup to confirm send to kitchen*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("disable-order-confirmation")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("send-to-kitchen-directly-without-confirmation")}</Typography>
                </div>
                <Switch checked={disableSendToKitchenConfirmation ?? false}
                        data-testid="disable-order-confirmation"
                        onClick={() => handleToggle("disableSendToKitchenConfirmation", !disableSendToKitchenConfirmation)} />
              </div>
            </div>

            <div style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("discount-verification")}</Typography>
                </div>
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingDiscountPinField(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              </div>
              <div style={{ marginTop: 12 }}>
                {getDiscountPinField()}
              </div>
              <div style={{ marginTop: 8 }}>
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.grayscale["600"]
                }}>{t("set-a-verification-code-during-the-discount-process")}</Typography>
              </div>
            </div>

          </div>
        </div>

        {/* reporting settings */}
        <div style={contentStyle}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>{t("reporting-settings")}</Typography>
            <Typography
              style={{ ...typography.body.regular, marginTop: 4 }}>{t("control-reporting-settings")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("daily-report-auto-closing-toggle")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-scheduled-auto-closure-toggle-description")}</Typography>
                </div>
                <Switch checked={taxReportConfig.autoClosure ?? false}
                        data-testid="daily-report-auto-closing-toggle"
                        onClick={() => handleAutoClosureToggle("autoClosure", !autoClosure)} />
              </div>
            </div>
            <div style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("configuration-editor-auto-closure-time-field-label")}</Typography>
                </div>
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingTaxReportClosureTime(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              </div>
              <div style={{ marginTop: 12 }}>
                {getTaxReportClosureTimeField()}
              </div>
              <div style={{ marginTop: 8 }}>
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.grayscale["600"]
                }}>{t("set-scheduled-closure-time-daily-report")}</Typography>
              </div>
            </div>
            <div style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("send-reports-to-email")}</Typography>
                </div>
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingTaxReportEmails(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              </div>
              <div style={{ marginTop: 12 }}>
                {getTaxReportEmailsField()}
              </div>
              <div style={{ marginTop: 8 }}>
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.grayscale["600"]
                }}>{t("by-default-reports-will-be-sent-to-these-emails")}</Typography>
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("daily-report-autoprint-lable")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("disable-daily-report-autoprint")}</Typography>
                </div>
                <Switch checked={configurationForm.disableDailyReportPrintout ?? false}
                        data-testid="daily-report-autoprint-lable"
                        onClick={() => handleToggle("disableDailyReportPrintout", !disableDailyReportPrintout)} />
              </div>
            </div>
            
            {/* useFirstOrderingInMonthlyReports */}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("use-first-ordering-for-monthly-reports")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("when-enabled-the-first-ordering-decides-monthly-report-assignment")}</Typography>
                </div>
                <Switch checked={useFirstOrderingInMonthlyReports ?? false}
                        data-testid="use-first-ordering-for-monthly-reports"
                        onClick={() => handleToggle("useFirstOrderingInMonthlyReports", !useFirstOrderingInMonthlyReports)} />
              </div>
            </div>
            
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("digital-receipt-autoprint-lable")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("disable-digital-receipt-autoprint")}</Typography>
                </div>
                <Switch checked={configurationForm.disableDigitalReceiptPrintout ?? false}
                        data-testid="digital-receipt-autoprint-lable"
                        onClick={() => handleToggle("disableDigitalReceiptPrintout", !disableDigitalReceiptPrintout)} />
              </div>
            </div>
            <div className={classes.settings} style={{ maxWidth: 520, marginTop: 24 }}>
              {/*receipt options checkout*/}
              <div>
                <div>
                  <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper}>
                    <Select
                      value={configurationForm.receiptOption?.type || "STANDARD"}
                      onChange={updateDefaultReceiptOption}
                      MenuProps={MenuProps}
                      IconComponent={ChevronDown20new}
                      input={<Field id="select-chip" label={t("chose-default-receipt-option")} select />}
                      renderValue={(selected) => {
                        const rOption = receiptModeOptions.find(t => t.key === selected) || {};
                        return (
                          <div style={{ display: "flex", flexWrap: "wrap" }}>

                            <Chip
                              key={selected}
                              label={t(rOption.i18nKey)}
                              style={{ margin: 2 }}
                            />
                          </div>
                        );
                      }}
                    >
                      {receiptModeOptions.map(rOption => (
                        <MenuItem value={rOption.key} key={rOption.key}>
                          <div className={classes.tag}>
                            <Typography>{t(rOption.i18nKey)}</Typography>
                          </div>
                        </MenuItem>
                      ))}
                    </Select>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"],
                      marginTop: 8
                    }}>{t("default-receipt-option-configuration-description")}</Typography>
                  </FormControl>
                </div>
              </div>
              {/* includeReconciliation report */}
              <div className={classes.switchSetting} style={{ marginTop: 24 }}>
                <div className={classes.flex}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("include-reconciliation-report-toggle")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("include-reconciliation-report-toggle-description")}</Typography>
                  </div>
                  <Switch checked={includeReconciliation ?? false}
                          data-testid="include-reconciliation-report-toggle"
                          onClick={() => handleDailyReportToggle("includeReconciliation", !includeReconciliation)} />
                </div>
              </div>
            </div>
            {/* <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>
                    {t("configuration-editor-has-express-field-label")}
                  </Typography>
                  <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                    {t("enable-express-ordering")}
                  </Typography>
                </div>
                <Switch checked={configuration.hasExpress ?? false}
                        onClick={() => handleToggle("hasExpress", !hasExpress)} />
              </div>
            </div>*/}
          </div>
        </div>
        {/* receipt settings */}
        <div style={contentStyle}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>{t("receipt-settings")}</Typography>
            <Typography
              style={{ ...typography.body.regular, marginTop: 4 }}>{t("control-receipt-settings")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            <div style={{ marginTop: 24 }}>
              <div style={{ marginBottom: 24 }}>
                <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper}>
                  <Select
                    value={defaultOrderColumns || []}
                    onChange={(e) => updateDefaultReceiptColumns(e, "defaultOrderColumns")}
                    multiple
                    MenuProps={MenuProps}
                    IconComponent={ChevronDown20new}
                    input={<Field id="select-chip" label={t("choose-default-receipt-columns-orders")} select />}
                    renderValue={(selected) => (
                      <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                        {selected.map((value) => {
                          const rColumn = receiptOrderColumnsOptions.find(t => t.value === value) || {};
                          return (
                            <Chip
                              key={value}
                              label={t(rColumn.i18nKey)}
                              style={{ margin: 2 }}
                            />
                          )
                        })}
                      </div>
                    )}
                  >
                    {receiptOrderColumnsOptions.map(rOption => (
                      <MenuItem value={rOption.value} key={rOption.value}>
                        <div className={classes.tag}>
                          <Typography>{t(rOption.i18nKey)}</Typography>
                        </div>
                      </MenuItem>
                    ))}
                  </Select>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"],
                    marginTop: 8
                  }}>{t("default-receipt-columns-description")}</Typography>
                </FormControl>
              </div>
              <div>
                <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper}>
                  <Select
                    value={defaultPaymentColumns || []}
                    onChange={(e) => updateDefaultReceiptColumns(e, "defaultPaymentColumns")}
                    multiple
                    MenuProps={MenuProps}
                    IconComponent={ChevronDown20new}
                    input={<Field id="select-chip" label={t("choose-default-receipt-columns-payments")} select />}
                    renderValue={(selected) => (
                      <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                        {selected.map((value) => {
                          const rColumn = receiptPaymentColumnsOptions.find(t => t.value === value) || {};
                          return (
                            <Chip
                              key={value}
                              label={t(rColumn.i18nKey)}
                              style={{ margin: 2 }}
                            />
                          )
                        })}
                      </div>
                    )}
                  >
                    {receiptPaymentColumnsOptions.map(rOption => {
                      return (
                        <MenuItem value={rOption.value} key={rOption.value}>
                          <div className={classes.tag}>
                            <Typography>{t(rOption.i18nKey)}</Typography>
                          </div>
                        </MenuItem>
                      )
                    })}
                  </Select>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"],
                    marginTop: 8
                  }}>{t("default-receipt-columns-description")}</Typography>
                </FormControl>
              </div>
              <div className={classes.switchSetting} style={{ marginTop: 24 }}>
                <div className={classes.flex}>
                  <div style={{ marginRight: 8 }}>
                    <Typography
                      style={{ ...typography.body.medium }}>{t("hiding-cancelled-orders-without-payment-in-receipts")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("configuration-editor-hide-orders-without-payment-in-receipts")}</Typography>
                  </div>
                  <Switch checked={hideCancelledOrdersWithoutPayments ?? false}
                          data-testid="hiding-cancelled-orders-without-payment-in-receipts"
                          onClick={() => handleReceiptConfigToggle("hideCancelledOrdersWithoutPayments", !hideCancelledOrdersWithoutPayments)} />
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* menu settings */}
        <div style={contentStyle}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>{t("menu-settings")}</Typography>
            <Typography style={{ ...typography.body.regular, marginTop: 4 }}>{t("control-menu-settings")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            <div className={classes.switchSetting}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("configuration-editor-has-ongoing-items-field-label")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-ongoing-ordering")}</Typography>
                </div>
                <Switch checked={configurationForm.hasOngoingItems ?? false}
                        data-testid="configuration-editor-has-ongoing-items-field-label"
                        onClick={() => handleToggle("hasOngoingItems", !hasOngoingItems)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("takeaway-in-dine-in")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-takeaway-order-flow-during-dine-in")}</Typography>
                </div>
                <Switch checked={configurationForm.enableTakeawayInDineIn ?? false}
                        data-testid="takeaway-in-dine-in"
                        onClick={() => handleToggle("enableTakeawayInDineIn", !enableTakeawayInDineIn)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("courses")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-course-ordering")}</Typography>
                </div>
                <Switch checked={courseConfigEnabled ?? false}
                        data-testid="courses"
                        onClick={() => handleCourseConfigEnabledToggle("enabled", !courseConfigEnabled)} />
              </div>
            </div>
            {courseConfigEnabled && getCourses()}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("disable-notes-for-customers")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("do-not-allow-customers-to-add-notes-for-the-kitchen")}</Typography>
                </div>
                <Switch checked={hideMenuItemNotesFromCustomers ?? false}
                        data-testid="disable-notes-for-customers"
                        onClick={() => handleToggle("hideMenuItemNotesFromCustomers", !hideMenuItemNotesFromCustomers)} />
              </div>
            </div>
            {/* showing ordered items quantities + pending quantities*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("displaying-ordered-items-quantities-toggle-header")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("displaying-ordered-items-quantities-toggle-description")}</Typography>
                </div>
                <Switch checked={displayOrderedItemsTotalsInMenu ?? false}
                        data-testid="showing-ordered-items-quantities"
                        onClick={() => handleToggle("displayOrderedItemsTotalsInMenu", !displayOrderedItemsTotalsInMenu)} />
              </div>
            </div>
            {/* mandatory cancellation reason */}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("enable-mandatory-cancellation-reason-toggle")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-mandatory-cancellation-reason-toggle-description")}</Typography>
                </div>
                <Switch checked={enableMandatoryCancelReason ?? false}
                        data-testid="configuration-editor-enable-mandatory-cancel-reason-label"
                        onClick={() => handleToggle("enableMandatoryCancelReason", !enableMandatoryCancelReason)} />
              </div>
            </div>
          </div>
        </div>
        {/* printer settings */}
        <div style={contentStyle}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>{t("printer-settings")}</Typography>
            <Typography
              style={{ ...typography.body.regular, marginTop: 4 }}>{t("control-printer-settings")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            <div className={classes.switchSetting}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("cancellation-receipt-toggle-header")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-cancellation-receipt-toggle")}</Typography>
                </div>
                <Switch checked={disableCancellationReceipt ?? false}
                        data-testid="cancellation-receipt-toggle-header"
                        onClick={() => handleToggle("disableCancellationReceipt", !disableCancellationReceipt)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("configuration-editor-block-item-print-on-cancellation-field-label")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("disable-cancelled-item-printout")}</Typography>
                </div>
                <Switch checked={configurationForm.disableCancelledItemPrintout ?? false}
                        data-testid="cancelled-item-printout"
                        onClick={() => handleToggle("disableCancelledItemPrintout", !disableCancelledItemPrintout)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("enable-terminal-payment-receipt")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("print-payment-receipt-for-every-transaction-with-allo-pay")}</Typography>
                </div>
                <Switch checked={enableTerminalPaymentReceipt ?? false}
                        data-testid="terminal-payment-receipt"
                        onClick={() => handleToggle("enableTerminalPaymentReceipt", !enableTerminalPaymentReceipt)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("configuration-editor-print-by-menu-field-label")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-menu-printers")}</Typography>
                </div>
                <Switch checked={configurationForm.printByMenu ?? false}
                        data-testid="print-by-menu"
                        onClick={() => handleToggle("printByMenu", !printByMenu)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("print-by-menu-item")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-menu-item-printers")}</Typography>
                </div>
                <Switch checked={configurationForm.printByMenuItem ?? false}
                        data-testid="print-by-menu-item"
                        onClick={() => handleToggle("printByMenuItem", !printByMenuItem)} />
              </div>
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("configuration-editor-print-by-addition-field-label")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-menu-extras-and-options-printers")}</Typography>
                </div>
                <Switch checked={configurationForm.printByAddition ?? false}
                        data-testid="configuration-editor-print-by-addition-field-label"
                        onClick={() => handleToggle("printByAddition", !printByAddition)} />
              </div>
            </div>
            <div style={{ marginTop: 24 }}>
              <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper}>
                <Select
                  name="pollPrinterAccountIds"
                  value={pollPrinterAccountIds || []}
                  onChange={updatePollPrinterAccountIds}
                  multiple
                  MenuProps={MenuProps}
                  IconComponent={ChevronDown20new}
                  input={<Field id="select-multiple-chip" label={t("lan-printer-polling-accounts")} select />}
                  renderValue={(selected) => (
                    <div style={{ display: "flex", flexWrap: "wrap" }}>
                      {selected.map((value) => {
                        const acc = accountsById[value] || {};
                        return (
                          <Chip
                            key={value}
                            label={`${acc.firstName || ""} ${acc.lastName || ""}`}
                            style={{ margin: 2 }}
                          />
                        );
                      })}
                    </div>
                  )}
                >
                  {accounts.map(account => (
                    <MenuItem value={account.id} key={account.id}>
                      <div className={classes.tag}>
                        <Paragraph>{`${account.firstName || ""} ${account.lastName || ""}`}</Paragraph>
                      </div>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>
          </div>
        </div>
        {/* express terminal settings */}
        <div style={contentStyle}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>{t("express-terminal")}</Typography>
            <Typography style={{
              ...typography.body.regular,
              marginTop: 4
            }}>{t("update-express-ordering-and-checkout")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            {/*allowPagerInputInExpress*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("pager")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("assign-pager-during-checkout")}</Typography>
                </div>
                <Switch checked={allowPagerInputInExpress ?? false}
                        data-testid="allow-pager-input-in-express"
                        onClick={() => handleToggle("allowPagerInputInExpress", !allowPagerInputInExpress)} />
              </div>
            </div>
            {/*allowTableSelectionInExpress*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("table-selection")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("select-table-during-checkout")}</Typography>
                </div>
                <Switch checked={allowTableSelectionInExpress ?? false}
                        data-testid="allow-table-selection-in-express"
                        onClick={() => handleToggle("allowTableSelectionInExpress", !allowTableSelectionInExpress)} />
              </div>
            </div>
          </div>
        </div>
        {/* inventory settings */}
        {configurationForm?.showInventory && (
          <div style={contentStyle}>
            <div style={contentDescriptionStyle}>
              <Typography style={{ ...typography.body.medium }}>{t("inventory")}</Typography>
              <Typography
                style={{ ...typography.body.regular, marginTop: 4 }}>{t("update-inventory-settings")}</Typography>
            </div>
            <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
              {/*allow users to delete inventory - limitation for allo emails only */}
              {hasInventorySetUp && (
                <div>
                  {inventoryError && (
                    <Typography style={{
                      ...typography.body.medium,
                      color: palette.primary["500"],
                      marginBottom: 8
                    }}>{t("please-contact-support-you-are-not-authorized")}</Typography>
                  )}
                  <div
                    style={{ marginBottom: 20, background: palette.grayscale["300"], borderRadius: 12, padding: "16px" }}>
                    <Typography style={{
                      ...typography.body.medium,
                      marginBottom: 8,
                      paddingTop: 12
                    }}>{t("delete-inventory")}</Typography>
                    <div style={{
                      marginBottom: 12,
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center"
                    }}>
                      <Typography style={{ ...typography.body.regular, marginRight: 2 }}>
                        {t("delete-inventory-description")}
                      </Typography>
                      <Confirm
                        title={t("delete-inventory")}
                        body={(
                          <Typography color="textSecondary" variant="body2">
                            <br />
                            {t("delete-inventory-description")}
                            <br />
                            {t("are-you-sure-description-to-delete-note")}
                          </Typography>
                        )}
                      >
                        {Confirm => (
                          <ButtonBase
                            style={{ borderRadius: 10, padding: '6px 12px', background: palette.negative["500"] }}
                            disableRipple disableTouchRipple onClick={Confirm(handleDelete)}>
                            <TrashIcon20White />
                            <Typography
                              style={{ ...typography.body.medium, color: palette.grayscale["100"], marginLeft: 4 }}>
                              {t('common-delete')}
                            </Typography>
                          </ButtonBase>
                        )}
                      </Confirm>
                    </div>
                  </div>
                </div>
              )}
              {/*allow users to enable consumption from menu*/}
              <div className={classes.switchSetting} style={{ marginTop: 24 }}>
                <div className={classes.flex}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("automatic-consumption")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("activate-inventory-to-get-automatic-menu-updates")}</Typography>
                  </div>
                  <Switch checked={enableConsumption ?? false}
                          data-testid="automatic-consumption"
                          onClick={() => handleInventoryToggle("enableConsumption", !enableConsumption)} />
                </div>
              </div>
              {/*show email that injects purchases*/}
              <div style={{ marginTop: 24 }}>
                <div className={classes.flex}>
                  <div style={{ marginRight: 8 }}>
                    <Typography
                      style={{ ...typography.body.medium }}>{t("purchase-email")}</Typography>
                  </div>
                  {/*<ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple*/}
                  {/*            style={{ padding: 0 }} onClick={() => setUpdatingTaxReportClosureTime(true)}>*/}
                  {/*  <TableUpdateBtn />*/}
                  {/*</ButtonBase>*/}
                  <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                              style={{ padding: 0 }} onClick={() => {
                    navigator.clipboard.writeText(invoiceEmail);
                  }}>
                    <div style={{
                      display: "flex",
                      alignItems: "center",
                      paddingLeft: 10,
                      paddingRight: 10,
                      minWidth: 60
                    }}>
                      <CopyIcon20 />
                      <Typography style={{
                        ...typography.body.medium,
                        color: palette.grayscale["600"],
                        marginLeft: 2
                      }}>{t("copy")}</Typography>
                    </div>
                  </ButtonBase>
                </div>
                <div style={{ marginTop: 12 }}>
                  {getInventoryPurchaseInjectionEmail()}
                </div>
                <div style={{ marginTop: 8 }}>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("create-purchases-from-attachments-to-your-inventory-email")}</Typography>
                </div>
              </div>
              {/*allow users to enable consumption from menu*/}
              <div className={classes.switchSetting} style={{ marginTop: 24 }}>
                <div className={classes.flex}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("synchronize-with-menu")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("one-to-one-inventory-with-your-menu-items")}</Typography>
                  </div>
                  <Confirm
                    title={t('synchronize-with-menu')}
                    description={t('menu-synchronization-creates-ingredients-and-consumptions-automatically-to-setup-inventory')}
                  >
                    {confirm => (
                      <Switch checked={enableMenuSync ?? false}
                              data-testid="menu-synchronization"
                              onClick={confirm(() => handleInventoryToggle("enableMenuSync", !enableMenuSync))} />
                    )}
                  </Confirm>
                </div>
              </div>
              {/* Allow users to change consumption behavior to return or not canceled items */}
              <div className={classes.switchSetting} style={{ marginTop: 24 }}>
                <div className={classes.flex}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("return-cancelled-items")}</Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale["600"]
                    }}>{t("toggle-to-return-cancelled-items-to-inventory")}</Typography>
                  </div>
                  <Switch checked={isReturnEnabled}
                          data-testid="return-cancelled-items"
                          onClick={() => handleCancelledItemsActionToggle(actionOnItemCancellation)} />
                </div>
              </div>
            </div>
          </div>
        )}
        {/* content languages */}
        {/* HIDDEN UNTIL USED TODO */}
        <div style={{ ...contentStyle, display: "none" }}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>{t("select-content-language")}</Typography>
            <Typography style={{
              ...typography.body.regular,
              marginTop: 4
            }}>{t("set-the-language-for-the-terminal-and-content")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper}>
              <Select
                name="defaultLanguage"
                value={defaultLanguage || ""}
                onChange={updateDefaultLanguages}
                MenuProps={MenuProps}
                IconComponent={ChevronDown20new}
                input={<Field id="select-chip" label={t('default-content-languages-title')} select />}
                renderValue={(selected) => {
                  const lOption = contentLanguagesOptions.find(l => l.code === selected) || {};
                  return (
                    <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                      <Chip
                        key={selected}
                        label={t(lOption.label)}
                        style={{ margin: 2 }}
                      />
                    </div>
                  )
                }}
              >
                {(contentLanguagesOptions || []).map(language => (
                  <MenuItem value={language.code}>
                    <div className={classes.tag}>
                      <Paragraph>{t(language.label)}</Paragraph>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl style={{ width: "100%", marginTop: 24 }} className={classes.chipSelectWrapper}>
              <Select
                name="additionalLanguages"
                value={additionalLanguages || []}
                onChange={updateContentLanguages}
                multiple
                MenuProps={MenuProps}
                IconComponent={ChevronDown20new}
                input={<Field id="select-multiple-chip" label={t('content-languages-title')} select />}
                renderValue={(selected) => (
                  <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                    {selected.map((value) => {
                      const language = contentLanguagesOptions.find(l => l.code === value) || {}
                      return (
                        <Chip
                          key={value}
                          label={t(language.label)}
                          style={{ margin: 2 }}
                        />
                      )
                    })}
                  </div>
                )}
              >
                {(contentLanguagesOptions || []).map((language) => {
                  return (
                    <MenuItem value={language.code} key={language.key}>
                      <div className={classes.tag}>
                        <Paragraph>{t(language.label)}</Paragraph>
                      </div>
                    </MenuItem>
                  )
                })}
              </Select>
              <Typography style={{
                ...typography.body.regular,
                color: palette.grayscale["600"],
                marginTop: 8
              }}>{t("content-language-hint")}</Typography>
            </FormControl>
          </div>
        </div>
        {/*section for scale selection from SUNMI*/}
        {isSunmiDevice && (
          <div style={{ ...contentStyle }}>
            <div style={contentDescriptionStyle}>
              <Typography style={{ ...typography.body.medium }}>{t("select-peripheral")}</Typography>
              <Typography style={{
                ...typography.body.regular,
                marginTop: 4
              }}>{t("choose-external-device")}</Typography>
            </div>
            <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
              {!isEmpty(devices) && (
                <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper}>
                  <Select
                    name="selectedUSBDeviceId"
                    value={selectedDeviceId || ""}
                    onChange={updateSelectedDeviceId}
                    MenuProps={MenuProps}
                    IconComponent={ChevronDown20new}
                    input={<Field id="select-chip" label={t("selected-peripherial")} select />}
                    renderValue={(selected) => {
                      // const lOption = contentLanguagesOptions.find(l => l.code === selected) || {};
                      return (
                        <div style={{ display: "flex", flexWrap: "wrap" }}>
                          <Chip
                            key={selected}
                            label={selected}
                            style={{ margin: 2 }}
                          />
                        </div>
                      );
                    }}
                  >
                    {(Object.values(devices) || []).map((device, index) => (
                      <MenuItem key={`_${index}_usb_device`} value={device.deviceId}>
                        <div className={classes.tag}>
                          <Paragraph>{`${device.driverText} ${device.deviceId}`}</Paragraph>
                        </div>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
              <div style={{ marginTop: isEmpty(devices) ? 0 : 24 }}>
                <ButtonBase onClick={getDevices} style={{
                  border: `1px solid ${palette.grayscale.border}`,
                  padding: "6px 8px",
                  borderRadius: 10,
                  width: "max-content"
                }}>
                  <Typography style={{ ...typography.body.regular }}>{t("update-connected-devices")}</Typography>
                </ButtonBase>
              </div>
              <div style={{ marginTop: 24 }}>
                <ButtonBase onClick={connectSUNMISecondaryScreen} style={{
                  border: `1px solid ${palette.grayscale.border}`,
                  padding: "6px 8px",
                  borderRadius: 10,
                  width: "max-content"
                }}>
                  <Typography style={{ ...typography.body.regular }}>{t("connect-sunmi-secondary-display")}</Typography>
                </ButtonBase>
              </div>
            </div>
          </div>
        )}
        <div style={{ ...contentStyle }}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>{t("open-invoice-settings")}</Typography>
            <Typography style={{
              ...typography.body.regular,
              marginTop: 4
            }}>{t("open-invoice-settings-description")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            {/* daysUntilDue*/}
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("open-invoice-due-date-field-label")}</Typography>
              </div>
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                          style={{ padding: 0 }} onClick={() => setUpdatingOpenInvoiceDueDate(true)}>
                <TableUpdateBtn />
              </ButtonBase>
            </div>
            <div style={{ marginTop: 12 }}>
              {getOpenInvoiceDueDateField()}
            </div>
            <div style={{ marginTop: 8 }}>
              <Typography style={{
                ...typography.body.regular,
                color: palette.grayscale["600"]
              }}>{t("days-until-due-open-invoice-hint")}</Typography>
            </div>
            {/* additionalMemo*/}
            <div className={classes.flex} style={{ marginTop: 24 }}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("open-invoice-additional-memo-field-label")}</Typography>
              </div>
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                          style={{ padding: 0 }} onClick={() => setUpdatingOpenInvoiceAdditionalMemo(true)}>
                <TableUpdateBtn />
              </ButtonBase>
            </div>
            <div style={{ marginTop: 12 }}>
              {getOpenInvoiceAdditionalMemoField()}
            </div>
            {/* payment method*/}
            <div className={classes.chipSelectWrapper} style={{ marginTop: 24 }}>
              <Select
                name="paymentMethods"
                value={paymentMethods || []}
                onChange={updateOpenInvoicePaymentMethod}
                multiple
                MenuProps={MenuProps}
                input={<Field id="select-multiple-chip"  label={t('payment-methods-title')} select />}
                renderValue={(selected) => (
                  <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                    {selected.map((value) => {
                      const paymentMethod = paymentMethodOptions.find(t => t.key === value) || {}
                      return (
                        <Chip
                          key={value}
                          label={t(paymentMethod.i18nKey)}
                          style={{ margin: 2 }}
                        />
                      )
                    })}
                  </div>
                )}
              >
                {paymentMethodOptions.map(paymentMethod => (
                  <MenuItem value={paymentMethod.key} key={paymentMethod.key}>
                    <div className={classes.tag}>
                      <Paragraph>{t(paymentMethod.i18nKey)}</Paragraph>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </div>
            {/* Banking info */}
            <div style={{ marginTop: 12 }}>
              {getOpenInvoiceBankInformation()}
            </div>
          </div>
        </div>
        {/* beta features */}
        <div style={contentStyle}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>⚠️ {t("beta-features")}</Typography>
            <Typography style={{
              ...typography.body.regular,
              marginTop: 4
            }}>{t("enable-features-that-are-still-in-beta")}</Typography>
          </div>
          <div className={classes.settings} style={{ maxWidth: 520, paddingBottom: 35 }}>
            {/*showInventory*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("show-inventory")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("show-inventory-toggle-description")}</Typography>
                </div>
                <Switch checked={showInventory ?? false}
                        data-testid="show-inventory"
                        disabled
                        onClick={() => handleToggle("showInventory", !showInventory)} />
              </div>
            </div>
            {/*callMonitoringEnabled*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("enable-call-monitor")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("using-allo-local-you-can-integrate-your-landline-with-allo")}</Typography>
                </div>
                <Switch checked={callMonitoringEnabled ?? false}
                        data-testid="call-monitoring"
                        onClick={() => handleDSLSettingsToggle("callMonitoringEnabled", !callMonitoringEnabled)} />
              </div>
            </div>
            {/*useUpdatedMenuEditor*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("upgraded-menu-editor")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("use-the-latest-menu-editor-by-default")}</Typography>
                </div>
                <Switch checked={useUpdatedMenuEditor ?? false}
                        data-testid="upgraded-menu-editor"
                        onClick={() => handleToggle("useUpdatedMenuEditor", !useUpdatedMenuEditor)} />
              </div>
            </div>
            {/*useUpdatedTerminal*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("improved-order-flow")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-the-latest-version-of-the-ordering-flow-in-your-terminal")}</Typography>
                </div>
                <Switch checked={useUpdatedTerminal ?? false}
                        onClick={() => handleToggle("useUpdatedTerminal", !useUpdatedTerminal)} />
              </div>
            </div>
            {/*useUpdatedCheckout*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("improved-checkout")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-new-and-improved-checkout-flow")}</Typography>
                </div>
                <Switch checked={useUpdatedCheckout ?? false}
                        data-testid="improved-checkout"
                        onClick={() => handleToggle("useUpdatedCheckout", !useUpdatedCheckout)} />
              </div>
            </div>
            {/*showConnectivityIssueNotification*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("network-notifications")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-notifications-on-network-issues")}</Typography>
                </div>
                <Switch checked={showConnectivityIssueNotification ?? false}
                        data-testid="network-notifications"
                        onClick={() => handleToggle("showConnectivityIssueNotification", !showConnectivityIssueNotification)} />
              </div>
            </div>
            {/* table merging for reservation*/}
            {!posthog.isFeatureEnabled("hide-beta-feature") && (
              <div className={classes.switchSetting} style={{ marginTop: 24 }}>
                <div className={classes.flex}>
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t("allow-table-merging-reservation")}
                    </Typography>
                    <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                      {t("this-will-allow-table-merging-for-reservations")}
                    </Typography>
                  </div>
                  <Switch checked={reservationConfigurationForm.allowMultipleTableSelection ?? false}
                          data-testid="allow-table-merging-reservation"
                          onClick={() => toggleAllowTableMerging("allowMultipleTableSelection", !reservationConfigurationForm.allowMultipleTableSelection)} />
                </div>
              </div>
            )}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography
                    style={{ ...typography.body.medium }}>{t("disable-final-receipt-for-split-payments-toggle-header")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("disable-final-receipt-for-split-payments-toggle-description")}</Typography>
                </div>
                <Switch checked={disableFinalReceiptForSplitPayments ?? false}
                        data-testid="disable-final-receipt-for-split-payments"
                        disabled={!isManaged}
                        onClick={() => handleToggle("disableFinalReceiptForSplitPayments", !disableFinalReceiptForSplitPayments)} />
              </div>
            </div>
            {/*showSmallMenuItemsInMenus*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("show-small-menu-items")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-to-show-more-items-per-row-in-menu")}</Typography>
                </div>
                <Switch checked={showSmallMenuItemsInMenus ?? false}
                        data-testid="show-small-menu-items"
                        onClick={() => handleFrontendSettingsToggle("showSmallMenuItemsInMenus", !showSmallMenuItemsInMenus)} />
              </div>
            </div>
            {/*showInvoicePreviewInCheckout*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("show-invoice-preview-in-checkout")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-invoice-preview-for-selected-payment-items-in-checkout")}</Typography>
                </div>
                <Switch checked={showInvoicePreviewInCheckout ?? false}
                        data-testid="show-invoice-preview-in-checkout"
                        onClick={() => handleFrontendSettingsToggle("showInvoicePreviewInCheckout", !showInvoicePreviewInCheckout)} />
              </div>
            </div>
            {/*deepCopyReorderingEnabled*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("reorder-item-with-all-data")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-cloning-item-during-reorder-instead-of-simple-copy")}</Typography>
                </div>
                <Switch checked={deepCopyReorderingEnabled ?? false}
                        data-testid="reorder-item-with-all-data"
                        onClick={() => handleToggle("deepCopyReorderingEnabled", !deepCopyReorderingEnabled)} />
              </div>
            </div>
            {/*resolvePaidItemsUnderPaymentInReceipts*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("show-payment-items-in-receipts")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-receipts-to-show-paid-items-under-each-payment")}</Typography>
                </div>
                <Switch checked={resolvePaidItemsUnderPaymentInReceipts ?? false}
                        data-testid="resolve-paid-items-under-payment-in-receipts"
                        onClick={() => handleFrontendSettingsToggle("resolvePaidItemsUnderPaymentInReceipts", !resolvePaidItemsUnderPaymentInReceipts)} />
              </div>
            </div>
            {/*enableMenuItemContextActions*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("enable-quick-actions-menu")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("on-long-press-show-menu-with-actions-for-menu-items")}</Typography>
                </div>
                <Switch checked={enableMenuItemContextActions ?? false}
                        onClick={() => handleFrontendSettingsToggle("enableMenuItemContextActions", !enableMenuItemContextActions)} />
              </div>
            </div>
            {/*customExtras configuration*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("enable-custom-extras")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-custom-extras-for-item-details")}</Typography>
                </div>
                <Switch checked={customExtrasEnabled ?? false}
                        onClick={() => handleToggle("customExtrasEnabled", !customExtrasEnabled)} />
              </div>
            </div>
            {/*showMapInTakeaway*/}
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("show-takeaway-map")}</Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>{t("enable-map-in-takeaway-view-to-manage-orders")}</Typography>
                </div>
                <Switch checked={showMapInTakeaway ?? false}
                        onClick={() => handleFrontendSettingsToggle("showMapInTakeaway", !showMapInTakeaway)} />
              </div>
            </div>
          </div>
        </div>
        <div style={{ ...contentStyle, paddingBottom: 32 }}>
          <div style={contentDescriptionStyle}>
            <Typography style={{ ...typography.body.medium }}>About</Typography>
            <Typography style={{ ...typography.body.regular, marginTop: 4 }}>Version: {version}</Typography>
            <Typography style={{ ...typography.body.regular, marginTop: 4 }}>Platform: {platform}</Typography>
            <Typography style={{ ...typography.body.regular, marginTop: 4 }}>Device: {device}</Typography>
            <Typography style={{ ...typography.body.regular, marginTop: 4 }}>Embed: {embedded}</Typography>
            <Typography style={{ ...typography.body.regular, marginTop: 4 }}>Reader: {readerId}</Typography>
            {window && (
              <Typography style={{ ...typography.body.regular, marginTop: 4 }}>W: {window.innerWidth} x
                H: {window.innerHeight}</Typography>
            )}
            {!!readerId && (
              <ButtonBase onClick={openReaderSettings} style={{
                padding: 6,
                marginTop: 4,
                borderRadius: 10,
                border: `1px solid ${palette.grayscale.border}`
              }}>
                <Typography style={{ ...typography.body.regular }}>{t('reader-settings')}</Typography>
              </ButtonBase>
            )}
          </div>
        </div>
        {showResetConfiguration && (
          <div style={{ marginTop: 24, marginBottom: 24 }}>
            <ButtonBase onClick={resetConfiguration} style={{
              border: `1px solid ${palette.grayscale.border}`,
              padding: "6px 8px",
              borderRadius: 10,
              width: "max-content"
            }}>
              <Typography style={{ ...typography.body.regular }}>Reset</Typography>
            </ButtonBase>
          </div>
        )}
      </div>
    </>
  );
});

export default ConfigurationEditor;
