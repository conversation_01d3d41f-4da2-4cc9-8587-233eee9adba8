import React, {useEffect, useState} from "react";
import {withTranslation} from '../../../../../../i18n';
import moment from "moment";
import {
  removeAccountFromRestaurant,
  updateAccountPermissions,
  getAccount,
  getEmployee,
  putEmployee,
  getPermissionIdentifiers,
  getHistoricalOrders, getTables, getRestaurantsAccounts
} from "../../../../../api";
import Button from '@material-ui/core/Button';
import isEmpty from "../../../../../utils/isEmpty";
import EditUser from "./../EditUser";
import { useDispatch, useSelector } from "react-redux";
import {accountSelectors} from "../../../../../../redux/selectors";
import { includeRequest, noop, permissionIdentifiers as pIds} from "../../../../../utils/const";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../../styles/typography";
import palette from "../../../../../../styles/palette";
import {
  ArrowLeftIcon,
  CaretRightIcon,
  EditIcon20,
  NoTakeaway120x90
} from "../../../../../utils/icons";
import { views } from "../../../../../utils/administrationRoutes";
import { useRouter } from "next/router";
import shadows from "../../../../../../styles/shadows";
import { formatNumber } from "../../../../../utils/formatNumber";
import AmountUpdateModal from "../../../../_popup/AmountUpdateModal";
import { ButtonBase, MenuItem, TableFooter } from "@material-ui/core";
import byId from "../../../../../utils/byId";
import useStyles, { useMenuStyles, useMenuItemStyles } from "./styles";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import Moment from "react-moment";
import { status as orderItemStatus } from "../../../../../utils/categorizeOrderItems";
import { controlStringLength } from "../../../../../utils/sliceString";
import ReceiptInformation from "../../../ReceiptInformation";
import EmptyScreen from "../../../../_placeholder/EmptyScreen";
import MoreOptionsButton from "../../../../_buttons/MoreOptionsButton";
import { appActions } from "../../../../../../redux/actions";
import CustomMenu from "../../../../_popup/CustomMenu";

export const EmployeeOverview = withTranslation('common')(({ t, restaurantId, accountId }) => {
  const classes = useStyles();
  const menuClasses = useMenuStyles();
  const menuItemClasses = useMenuItemStyles();

  const dispatch = useDispatch();

  const router = useRouter();
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "");

  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const canUpdateGeneralSettings = permissionIds.includes(pIds.CAN_UPDATE_GENERAL_SETTINGS.value)

  const [anchor, setAnchor] = useState(null);
  const openMenu = (e) => setAnchor(e.currentTarget);
  const closeMenu = () => setAnchor(null);

  const [account, setAccount] = useState({});
  const [employee, setEmployee] = useState({});
  const { hourlyRate = 0 } = employee;
  const isAdmin = !!account.isManaged;

  const fetchAccount = () => {
    getAccount(restaurantId, accountId)
      .then(({ data }) => setAccount(data))
      .catch(noop)
  }

  const fetchEmployee = () => {
    getEmployee(restaurantId, accountId)
      .then(({ data }) => setEmployee(data))
      .catch(noop)
  }

  const getFullName = (acc) => {
    return `${acc.firstName ?? ""} ${acc.lastName ?? ""}`
  }

  const [editingAccount, setEditingAccount] = useState(null);
  const [permissionIdentifiers, setPermissionIdentifiers] = useState([]);

  const [updatingHourlyRate, setUpdatingHourlyRate] = useState(false);

  const updateHourlyRate = (amount) => {
    putEmployee(restaurantId, accountId, amount).then(() => fetchEmployee()).catch(noop)
  }

  const removeAccount = (id) => {
    removeAccountFromRestaurant(restaurantId, id)
      .then(() => {
        dispatch(appActions.setNotification('user-removed', "success"))
        setEditingAccount(null);
        onBack();
      })
      .catch(() => {
        dispatch(appActions.setNotification('user-removal-error', "error"))
      })
  }

  const [accountsById, setAccountsById] = useState({});
  const [historicalOrders, setHistoricalOrders] = useState({});
  const { items = [], count, orderTotal, paymentTotal } = historicalOrders;
  const [tablesById, setTablesById] = useState({})
  const [editingOrderId, setEditingOrderId] = useState(null);

  const getOrders = () => {
    getHistoricalOrders(restaurantId, null, moment().format('yyyy-MM-DD'), moment().format('yyyy-MM-DD'), { selectedAccountIds: [accountId] })
      .then(({ data = {} }) => {
        const orders = data.items || [];
        const payments = orders.map(o => o.payments);
        const waiterAccountIds = [...new Set(payments.flat().map(p => p.waiterAccountId))];
        if (!isEmpty(waiterAccountIds)) {
          getRestaurantsAccounts(restaurantId, 100, 0).then(({ data }) => setAccountsById(byId(data.items))).catch(() => {});
        }

        setHistoricalOrders(data)
      })
      .catch(() => {})
  }

  const fetchTables = () => {
    getTables(restaurantId, includeRequest.ALL)
      .then(({ data = {} }) => setTablesById(byId(data)))
      .catch(() => {})
  }

  useEffect(() => {
    fetchAccount();
    fetchEmployee();
    getPermissionIdentifiers().then(({ data = {} }) => setPermissionIdentifiers(data.total ? data.items: [])).catch(() => {});
    fetchTables();
    getOrders();
  }, [])

  const updateUser = (form) => {
    updateAccountPermissions(restaurantId, accountId, form.permissionIdentifiers)
      .then(() => {
        dispatch(appActions.setNotification('user-updated', "success"))
        setEditingAccount(null);
        fetchAccount(accountId);
      })
      .catch(() => {
        dispatch(appActions.setNotification('user-update-error', "error"))
      })
  }

  const getOptions = () => {
    return (
      <div style={{ marginLeft: 4 }}>
        <MoreOptionsButton onClick={openMenu} />
        <CustomMenu id="app-actions" anchorEl={anchor} keepMounted open={Boolean(anchor)} onClose={closeMenu} classes={{ list: menuClasses.list, paper: menuClasses.paper }}>
          <div>
            <MenuItem classes={{ root: menuItemClasses.menuItemRoot }} disableRipple onClick={() => {
              setEditingAccount(true);
              closeMenu();
            }}>
              <div className={menuItemClasses.content}>
                <div>
                  <Typography style={{ ...typography.body.regular }}>
                    {t('common-update')}
                  </Typography>
                </div>
              </div>
            </MenuItem>
          </div>
        </CustomMenu>
      </div>
    )
  }

  const getEmployeePanel = () => {
    return (
      <div style={{ flex: 1, width: 330, background: palette.grayscale["100"], ...shadows.base, borderRadius: 12 }}>
        <div style={{ display: "flex", width: "100%", justifyContent: "end", paddingLeft: 12, paddingRight: 12, paddingTop: 10, paddingBottom: 10}}>
          {getOptions()}
        </div>
        <div style={{ paddingLeft: 8, paddingRight: 8, marginTop: 12 }}>
          <div style={{ display: "flex", textAlign: "center", flexDirection: "column", marginBottom: 24 }}>
            <Typography style={{ ...typography.medium.medium }}>
              {getFullName(account)}
            </Typography>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 4 }}>
              {account.email ?? t('no-email')}
            </Typography>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
              {account.mobile ?? t('no-mobile')}
            </Typography>
          </div>
          <div className={classes.row}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", paddingLeft: 8, paddingRight: 8, height: 44 }}>
              <div>
                <Typography style={{ ...typography.body.medium }}>
                  {t('role')}
                </Typography>
              </div>
              <div style={{ display: "flex", alignItems: "center" }}>
                <Typography style={{ ...typography.body.regular, marginRight: 4 }}>
                  {t('waiter')}
                </Typography>
              </div>
            </div>
          </div>
          <div className={classes.row}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", paddingLeft: 8, paddingRight: 8, height: 44 }}>
              <div>
                <Typography style={{ ...typography.body.medium }}>
                  {t('status')}
                </Typography>
              </div>
              <div style={{ display: "flex", alignItems: "center" }}>
                <Typography style={{ ...typography.body.regular, marginRight: 4, textTransform: "capitalize" }}>
                  {t('active')}
                </Typography>
              </div>
            </div>
          </div>
          <div className={classes.row}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", paddingLeft: 8, paddingRight: 8, height: 44 }}>
              <div>
                <Typography style={{ ...typography.body.medium }}>
                  {t('hourly-rate')}
                </Typography>
              </div>
              <div style={{ display: "flex", alignItems: "center" }}>
                <Typography style={{ ...typography.body.regular, marginRight: 4 }}>
                  {formatNumber(hourlyRate)}€
                </Typography>
                <ButtonBase disableRipple disableTouchRipple style={{ padding: 0 }} onClick={() => setUpdatingHourlyRate(true)}>
                  <EditIcon20 />
                </ButtonBase>
              </div>
            </div>
          </div>
          <div className={classes.row}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", paddingLeft: 8, paddingRight: 8, height: 44 }}>
              <div>
                <Typography style={{ ...typography.body.medium }}>
                  {t('shifts')}
                </Typography>
              </div>
              <div style={{ display: "flex", alignItems: "center" }}>
                <Typography style={{ ...typography.body.regular, marginRight: 4 }}>
                  40h
                </Typography>
              </div>
            </div>
          </div>
          <div style={{ paddingLeft: 8, paddingTop: 8 }}>
            <ButtonBase disableRipple onClick={openPayroll}>
              <Typography className={classes.sourceBtnText}>
                {t('see-payroll-details')}
              </Typography>
              <div className={classes.sourceBtnIcon}>
                <ArrowLeftIcon />
              </div>
            </ButtonBase>
          </div>
        </div>
        <AmountUpdateModal open={updatingHourlyRate} onClose={() => setUpdatingHourlyRate(false)} value={hourlyRate} setValue={updateHourlyRate} titleI18n={"update-hourly-rate"}/>
      </div>
    )
  }

  const zeroPad = (num, places) => String(num).padStart(places, '0')

  const getOrdersList = () => {
    return (
      <TableContainer style={{ height: "100%", flex: 1, marginBottom: 0 }}>
        <Table stickyHeader className={classes.table} aria-label="simple table" style={{ height: "100%" }}>
          <TableHead>
            <TableRow>
              <TableCell align="left">{t('receipt-identifier')}</TableCell>
              <TableCell align="left">{t('order-history-table-header-order-number')}</TableCell>
              <TableCell align="left">{t('order-history-table-header-order-type')}</TableCell>
              <TableCell align="left">{t('order-history-table-header-table-number')}</TableCell>
              <TableCell align="left">{t('common-waiter')}</TableCell>
              <TableCell align="left">{t('order-history-closed-by')}</TableCell>
              <TableCell align="left">{t('order-history-table-header-order-started')}</TableCell>
              <TableCell align="left">{t('order-history-table-header-order-ended')}</TableCell>
              <TableCell align="right">{t('order-history-table-header-order-total')}</TableCell>
              <TableCell align="right">{t('order-history-table-header-table-tax')}</TableCell>
              <TableCell align="right">{t('order-history-table-header-tip')}</TableCell>
              <TableCell align="right">{t('order-history-table-header-table-total')}</TableCell>
              <TableCell align="left">{t('receipt-payment-method-label')}</TableCell>
              {/*<TableCell align="center">{t('order-history-table-header-receipt-action-label')}</TableCell>*/}
              {/*<TableCell align="center">{t('send-update-btn-label')}</TableCell>*/}
            </TableRow>
          </TableHead>
          <TableBody>
            {!isEmpty(items) && items.map((order) => {
              const { id, number, identifier, customerId, tableId, items = [], accountId,
                creationTime, modificationTime, status, paymentOption,
                total, isWaiter, payments = [], type, notificationSent, user = {} } = order;

              const formattedTableNumber = !isEmpty(tablesById) && tablesById[tableId] ? ((tablesById[tableId] || {}).label || `T${(tablesById[tableId] || {}).code}`) : '-';
              const formattedStartTime = <Moment format={'DD.MM.YYYY HH:mm'}>{creationTime}</Moment>;
              const formattedEndTime = <Moment format={'DD.MM.YYYY HH:mm'}>{modificationTime}</Moment>;

              const tip = payments.reduce((acc, next) => {
                acc = acc + next.tipAmount;
                return acc;
              }, 0);
              const tipFormatted = (tip || 0).toFixed(2);

              // total amount for order
              const orderTotalFormatted = (total || 0).toFixed(2);

              // total tax for order
              let totalTax = 0;
              const confirmedItems = (items || []).filter(it => it.status !== orderItemStatus.CANCELLED && it.status !== orderItemStatus.UNCONFIRMED);
              confirmedItems.forEach(item => {
                let rate = (item.taxRate) / 100;
                totalTax += item.baseTotal - (item.baseTotal / (1 + rate))

                if (!isEmpty(item.extraItems)) {
                  (item.extraItems).forEach(extraItem => {
                    rate = (extraItem.taxRate) / 100;
                    totalTax += (extraItem.total - (extraItem.total / (1 + rate))) * item.qtd;
                  })
                }
                if (!isEmpty(item.optionItems)) {
                  (item.optionItems).forEach(optionItem => {
                    rate = (optionItem.taxRate) / 100;
                    totalTax += (optionItem.total - (optionItem.total / (1 + rate))) * item.qtd;
                  })
                }

              })

              const totalTaxFormatted = (totalTax || 0).toFixed(2);

              // total payment for order (order + tip)
              const tableTotalFormatted = (total + tip || 0).toFixed(2);

              const paymentChannels = isEmpty(payments) ? null : payments.map(p => t(`payment-channels-${p.paymentChannel.toLowerCase()}`)).join(', ');
              const closedByUsers = isEmpty(payments) ? null : [...new Set(payments.map(p => p.waiterAccountId))].map(id => {
                if (id && !isEmpty(accountsById)) {
                  const u = accountsById[id] || {};
                  return `${u.firstName} ${u.lastName}`
                }
                return "";
              }).join(", ")

              const { firstName = '', lastName = '' } = isEmpty(accountsById) ? [] : (accountsById[accountId] ?? {})
              const userFullName = (firstName || lastName) ? `${firstName} ${lastName}` : '';
              return (
                <TableRow key={id} onClick={() => setEditingOrderId(id)} className={classes.clickable}>
                  <TableCell align="left">{identifier || '-'}</TableCell>
                  <TableCell align="left">{number ? zeroPad(number, 3) : '-'}</TableCell>
                  <TableCell align="left">{t(`menu-editor-form-order-type-field-option-${type.toLowerCase().replace("_", "-")}`)}</TableCell>
                  <TableCell align="left">{formattedTableNumber}</TableCell>
                  <TableCell align="left">{userFullName}</TableCell>
                  <TableCell align="left">{closedByUsers}</TableCell>
                  <TableCell align="left">{formattedStartTime}</TableCell>
                  <TableCell align="left">{formattedEndTime}</TableCell>
                  <TableCell align="right">{orderTotalFormatted}</TableCell>
                  <TableCell align="right">{totalTaxFormatted}</TableCell>
                  <TableCell align="right">{tipFormatted}</TableCell>
                  <TableCell align="right">{tableTotalFormatted}</TableCell>
                  <TableCell align="left">
                    {controlStringLength(paymentChannels, 15)}
                  </TableCell>
                </TableRow>
              )
            })}
            {isEmpty(items) && (
              <TableRow>
                <TableCell colSpan={20}>
                  <div style={{ padding: 60 }}>
                    <EmptyScreen
                      icon={<NoTakeaway120x90 />}
                      titleI18nKey="no-orders"
                    />
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell>{t('table-count', { count: count })}</TableCell>
              <TableCell>{t('table-count', { count: count })}</TableCell>
              <TableCell>{t('table-count', { count: count })}</TableCell>
              <TableCell />
              <TableCell />
              <TableCell />
              <TableCell>{t('table-count', { count: count })}</TableCell>
              <TableCell>{t('table-count', { count: count })}</TableCell>
              <TableCell align="right">{t('table-total', { total: formatNumber(orderTotal) })}</TableCell>
              <TableCell />
              <TableCell />
              <TableCell align="right">{t('table-total', { total: formatNumber(paymentTotal) })}</TableCell>
              <TableCell>{t('table-count', { count: count })}</TableCell>
              {/*<TableCell />*/}
              {/*<TableCell />*/}
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    )
  }

  const getContent = () => {
    return (
      <div className={classes.content}>
        <div style={{ display: "flex", flexDirection: "column", height: "100%", paddingTop: 16, paddingLeft: 16, paddingRight: 16, paddingBottom: 24 }}>
          <div style={{ height: "100%" }}>
            <div style={{ display: "flex", height: "100%", overflow: "hidden" }}>
              <div style={{
                display: "flex",
                flexDirection: "column"
              }}>
                {getEmployeePanel()}
              </div>
              <div style={{ flex: 1, marginLeft: 24, overflow: "hidden", display: "flex", flexDirection: "column" }}>
                <div className={classes.header}>
                  <Typography className={classes.headerTitle}>{t('administration-layout-receipts-nav-label')}</Typography>
                  <div className={classes.actions}>
                    {/*<DateTimePicker />*/}
                  </div>
                </div>
                {getOrdersList()}
                {editingOrderId && (
                  <ReceiptInformation
                    restaurantId={restaurantId}
                    orderId={editingOrderId}
                    isOpen={!!editingOrderId}
                    close={() => setEditingOrderId(null)}
                    callback={getOrders}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const onBack = () => {
    router.push(`${resolvedAsPath}?v=${views.TEAM_MANAGEMENT}`, undefined, { shallow: true });
  }

  const openPayroll = (id) => {
    router.push(`${resolvedAsPath}?v=${views.TEAM_MANAGEMENT}&&accountId=${accountId}&&payroll=de`, undefined, { shallow: true });
  }

  const getHeader = () => {
    return (
      <div style={{ display: "flex", alignItems: "center", paddingTop: 6, paddingBottom: 6, paddingLeft: 12, paddingRight: 12, borderBottom: "1px solid #E8E7E6", justifyContent: "space-between", minHeight: 45 }}>
        <div style={{ display: "flex", alignItems: "center" }}>
          <Button
            disableElevation
            disableRipple
            disableFocusRipple
            disableTouchRipple
            onClick={onBack}
            style={{
              backgroundColor: "transparent",
              padding: 0,
              marginRight: 2
            }}>
            <Typography style={{...typography.body.regular, color: palette.grayscale["600"], marginRight: 2 }}>
              {t("team-management")}
            </Typography>
            <CaretRightIcon />
          </Button>
          <Typography style={{...typography.body.medium }}>
            {getFullName(account)}
          </Typography>
        </div>
      </div>
    )
  }

  if (!canUpdateGeneralSettings) {
    return null;
  }

  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        {getHeader()}
        {getContent()}
      </div>
      {!isEmpty(editingAccount) && (
        <EditUser
          open={editingAccount}
          close={() => setEditingAccount(false)}
          submit={updateUser}
          _delete={removeAccount}
          permissionIdentifiers={permissionIdentifiers}
          restaurantId={restaurantId}
          accountId={accountId}
          isAdmin={isAdmin}
        />
      )}
    </div>
  );
});

export default EmployeeOverview;
