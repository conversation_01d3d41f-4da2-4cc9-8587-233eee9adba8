import React, {useEffect, useState} from "react";
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableBody from '@material-ui/core/TableBody';
import Table from '@material-ui/core/Table';
import {withTranslation} from '../../../../../i18n';
import {
  addAccountToRestaurant,
  removeAccountFromRestaurant,
  getPermissionIdentifiers, updateAccountPermissions, getRestaurantsAccounts
} from "../../../../api";
import Button from '@material-ui/core/Button';
import Pagination from "@material-ui/lab/Pagination";
import InviteUser from "./InviteUser";
import useStyles from "./styles";
import isEmpty from "../../../../utils/isEmpty";
import EditUser from "./EditUser";
import { useDispatch, useSelector } from "react-redux";
import {accountSelectors} from "../../../../../redux/selectors";
import { permissionIdentifiers as pIds } from "../../../../utils/const";
import SecondaryBar from "../../../_navigation/SecondaryBar";
import { useRouter } from "next/router";
import { views } from "../../../../utils/administrationRoutes";
import { appActions } from "../../../../../redux/actions";
import { ButtonBase, Typography, useMediaQuery } from "@material-ui/core";
import typography from "../../../../../styles/typography";
import palette from "../../../../../styles/palette";

const TeamManagement = ({ t, restaurantId }) => {
  const isMobile = useMediaQuery('(max-width:500px)');
  
  const classes = useStyles();
  const dispatch = useDispatch();

  const router = useRouter();
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "");
  
  const currentAccountId = useSelector(accountSelectors.getAccountId);
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const canUpdateGeneralSettings = permissionIds.includes(pIds.CAN_UPDATE_GENERAL_SETTINGS.value)
  
  const pageSize = 10;
  const [pageOffset, setPageOffset] = useState(0);
  const [accounts, setAccounts] = useState({ total: 0, pages: 0, items: [] });
  const { total, pages, items } = (accounts || {})
  const loadedSize = (items || []).length
  
  const [editingAccount, setEditingAccount] = useState(null);
  const [permissionIdentifiers, setPermissionIdentifiers] = useState([]);
  const [isInvitingUser, setInvitingUser] = useState(false);

  const startCreating = () => setInvitingUser(true);
  
  const fetchAccounts = () => {
    getRestaurantsAccounts(restaurantId, pageSize, pageOffset, true)
      .then(({ data = [] }) => {
        const { total: fetchedTotal, items: fetchedItems, pages: fetchedPages } = data;
        let processedItems = []
        if (!isEmpty(fetchedItems)) {
          processedItems = fetchedItems.map(accItem => {
            const { firstName = "", lastName = "" } = (accItem || {})
            const fullName = `${firstName} ${lastName}`
            return { ...accItem, fullName }
          })
        }
        
        const aggregatedItems = isMobile ? items.concat(processedItems) : processedItems
        setAccounts({ total: fetchedTotal, items: aggregatedItems, pages: fetchedPages });
      })
      .catch(() => {});
  };

  useEffect(() => {
    fetchAccounts();
  }, [pageOffset]);
  
  useEffect(() => {
    getPermissionIdentifiers()
      .then(({ data = {} }) => setPermissionIdentifiers(data.total ? data.items: []))
      .catch(() => {})
  }, [])

  const removeAccount = (id) => {
    removeAccountFromRestaurant(restaurantId, id)
      .then(() => {
        setEditingAccount(null);
        fetchAccounts();
      })
      .catch(() => {})
  }

  const inviteUser = (user) => {
    addAccountToRestaurant(restaurantId, user)
      .then(() => {
        fetchAccounts();
        dispatch(appActions.setNotification("user-invited", "success"))
      })
      .catch(() => {
        dispatch(appActions.setNotification("user-invite-error", "error"))
      })
  }

  const updateUser = (form) => {
    updateAccountPermissions(restaurantId, editingAccount.id, form.permissionIdentifiers)
      .then(() => {
        setEditingAccount(null);
        fetchAccounts();
      })
      .catch(() => {})
  }

  const openAccount = (id) => {
    router.push(`${resolvedAsPath}?v=${views.TEAM_MANAGEMENT}&&accountId=${id}`, undefined, { shallow: true });
  }
  
  const onLoadMore = () => {
    setPageOffset(loadedSize)
  }

  const getMobileList = () => {
    return (
      <div style={{ display: "flex", flexDirection: "column" }}>
        {accounts && accounts.items && accounts.items.map(it => {
          const { id, fullName, email, mobile } = it;
          
          return (
            <ButtonBase key={id} onClick={() => openAccount(id)}>
              <div style={{ display: "flex", flexDirection: "column", width: "100%", textAlign: "left", borderBottom: `1px solid ${palette.grayscale.divider}`, padding: 12 }}>
                <Typography style={{ ...typography.body.medium }}>{fullName}</Typography>
                <Typography style={{ ...typography.body.regular, marginTop: 2 }}>{email}</Typography>
                <Typography style={{ ...typography.body.regular, marginTop: 2 }}>{mobile}</Typography>
              </div>
            </ButtonBase>
          )
        })}
        
        <Typography style={{ ...typography.body.regular, padding: 12, color: palette.grayscale["600"] }}>{`${loadedSize}${total > loadedSize ? `/${total}` : ''} ${t('accounts-loaded-label')}`}</Typography>
        
        {(total > loadedSize)  && (
          <div style={{ display: "flex", justifyContent: "center", paddingTop: 12, paddingBottom: 12 }}>
            <ButtonBase onClick={onLoadMore} style={{ paddingTop: 5, paddingBottom: 5, paddingLeft: 12, paddingRight: 12, border: `1px solid ${palette.grayscale.border}`, borderRadius: 10 }}>
              <Typography style={{ ...typography.body.medium }}>
                {t('load-more')}
              </Typography>
            </ButtonBase>
          </div>
        )}
      </div>
    )
  }
  
  const getContent = () => {
    if (isMobile) {
      return getMobileList()
    }
    
    return (
      <div className={classes.content}>
        <div style={{
          display: "flex",
          flexDirection: "column",
          height: "100%",
          paddingTop: 16,
          paddingLeft: 16,
          paddingRight: 16,
          paddingBottom: 12
        }}>
          <TableContainer>
            <Table stickyHeader className={classes.table} aria-label="menu editor table">
              <TableHead>
                <TableRow>
                  <TableCell>{t('firstname')}</TableCell>
                  <TableCell>{t('lastname')}</TableCell>
                  <TableCell>{t('email')}</TableCell>
                  <TableCell>{t('mobile')}</TableCell>
                  <TableCell align="right">{t('common-permissions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {!isEmpty(items) && items.map(it => {
                  const { id, firstName, lastName, email, mobile, permissions } = it;
                  const numberOfPermissions = permissions?.length
                  const hasAllPermissions = permissions?.length === permissionIdentifiers?.length

                  return (
                    <>
                      <TableRow key={id} hover className={classes.clickable} onClick={() => openAccount(id)}>
                        <TableCell align="left">{firstName}</TableCell>
                        <TableCell align="left">{lastName}</TableCell>
                        <TableCell align="left">{email}</TableCell>
                        <TableCell align="left">{mobile}</TableCell>
                        <TableCell align="right">{hasAllPermissions ? t("full-permissions") : `${numberOfPermissions}/${permissionIdentifiers?.length} ${t("common-permissions")}`}</TableCell>
                      </TableRow>
                    </>
                  )
                })}
              </TableBody>
            </Table>
          </TableContainer>
          {pages > 1 && (
            <div className={classes.pagination}>
              <Pagination count={accounts.pages} variant="text" shape="rounded" onChange={(e, v) => setPageOffset((v - 1) * pageSize)}/>
            </div>
          )}
        </div>
      </div>
    )
  }

  const getSecondaryBarActions = () => (
    <div style={{ display: "flex", alignItems: "center" }}>
      <Button className={classes.createBtn} onClick={startCreating} disableRipple>{t('invite')}</Button>
    </div>
  )

  if (!canUpdateGeneralSettings) {
    return null;
  }

  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        <SecondaryBar title={t('team-management')} right={getSecondaryBarActions()} />
        {getContent()}
      </div>
      {isInvitingUser && (
        <InviteUser
          open={isInvitingUser}
          close={() => setInvitingUser(false)}
          submit={inviteUser}
          permissionIdentifiers={permissionIdentifiers}
        />
      )}
      {!isEmpty(editingAccount) && (
        <EditUser
          open={editingAccount}
          close={() => setEditingAccount(null)}
          submit={updateUser}
          _delete={removeAccount}
          permissionIdentifiers={permissionIdentifiers}
          restaurantId={restaurantId}
          accountId={editingAccount.id}
          currentAccountId={currentAccountId}
        />
      )}
    </div>
  );
};

export default withTranslation('common')(TeamManagement);
