import { usePostHog } from "posthog-js/react";

export function trackError(error, context = {}) {
  const posthog = usePostHog();

  try {
    if (posthog) {
      posthog.capture("error_event", {
        error_message: error?.message || "Unknown error",
        error_stack: error?.stack || "No stack trace available",
        ...context,
      });
    } else {
      console.error("Error (PostHog not initialized):", {
        error_message: error?.message,
        error_stack: error?.stack,
        ...context,
      });
    }
  } catch (trackingError) {
    console.error("Error while logging the error:", trackingError);
  }
}