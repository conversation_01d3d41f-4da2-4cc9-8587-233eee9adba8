import React, { useEffect, useState } from 'react';
import { withTranslation } from '../../../../../i18n';
import { getCardTransactions, resolveCard } from "../../../../api";
import { useSelector } from 'react-redux';
import { reportingSelectors, restaurantSelectors } from "../../../../../redux/selectors";
import TableContainer from '@material-ui/core/TableContainer';
import Table from '@material-ui/core/Table';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableHead from '@material-ui/core/TableHead';
import TableBody from '@material-ui/core/TableBody';
import Pagination from '@material-ui/lab/Pagination';
import moment from 'moment';
import CardsSortAndFilterBar from "../../../Administration/Marketing/CardEditor/CardTransactions/CardsSortAndFilterBar";
import {
  giftCardTransactionsTypeOptions,
  paymentChannelTypeOptions
} from "../../../Administration/Marketing/CardEditor/CardTransactions/CardsSortAndFilterBar/filterTypes";
import { TableFooter } from "@material-ui/core";
import useStyles from '../../../Administration/Marketing/CardEditor/styles';

/**
 *
 * @param t
 * @param {{ value: "CARDS" || "TRANSACTIONS"  }} view
 * @param setCard
 * @param {string} searchQuery
 * @returns {JSX.Element}
 */
const CardTransactionsTableReporting = ({ t, view, setCard, searchQuery, selectedDateRange, setTotalBlockTransactionInfo, setSelectedDateRange }) => {
  const classes = useStyles();
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId, isParent } = restaurant;
  
  const { dateTimePicker = {} } = useSelector(reportingSelectors.getDateTimePicker);
  const { dateType, dateRange = {} } = dateTimePicker;
  const { id: dateRangeId, start, end } = dateRange;
  
  const { cardsGroupBy } = useSelector(reportingSelectors.getGiftCardGroupBy);
  
  const [pageOffset, setPageOffset] = useState(0);
  const [sorting, setSorting] = useState({ sortField: null, sortDirection: null })
  const pageSize = 10;
  const [transactions, setTransactions] = useState({
    items: [],
    pages: 0,
    total: 0
  });
  
  const [filters, setFilters] = useState({
    transactionType: [],
    paymentChannels: []
  });
  
  const resolveCardFromCardCode = (cardCode) => {
    resolveCard(restaurantId, cardCode)
      .then(({ data }) => {
        setCard(data);
      })
      .catch(({ response }) => {
        console.log(response)
      })
  };
  
  const prepareFilterString = () => {
    let filterString = "";
    
    // We need to make it if there are no filters to get with those 3 since SELLING is not supported in transactions
    if (filters.transactionType.length === 0) {
      filterString = "&transactionTypes=CASH_IN&transactionTypes=CASH_OUT&transactionTypes=ADJUSTMENT"
    } else {
      filters.transactionType.forEach(x => filterString+= `&transactionTypes=${x}`);
    }
    
    if (filters.paymentChannels.length !== 0) {
      filters.paymentChannels.forEach(x => filterString+=`&paymentChannels=${x}`);
    }
    
    if (sorting.sortDirection !== null && sorting.sortField !== null) {
      filterString += `&sortBy=ID&direction=${sorting.sortDirection}`;
    }
    
    if (searchQuery !== "") {
      filterString+=`&cardCode=${searchQuery}`
    }
    
    return filterString;
  };
  
  const fetchTransactions = () => {
    const filteredString = prepareFilterString();
    const formattedStartDate = moment(selectedDateRange[0]).toISOString()
    const formattedEndDate = moment(selectedDateRange[1]).toISOString()
    getCardTransactions(restaurantId, pageSize, pageOffset, formattedStartDate, formattedEndDate, filteredString)
      .then(({ data }) => {
        const { adjustmentTotal, cashInByType = {}, cashInTotal, cashOutTotal } = data
        setTransactions(data);
        setTotalBlockTransactionInfo({ adjustmentTotal, cashInByType, cashInTotal, cashOutTotal })
      })
  };
  
  const updateSorting = (value = {}) => {
    setSorting(value)
  };
  
  const updateFilters = (values) => {
    setFilters(values);
  };
  
  useEffect(() => {
    if (cardsGroupBy === "TRANSACTIONS") {
      fetchTransactions();
    }
  }, [view, filters, searchQuery, sorting, pageOffset, selectedDateRange]);
  
  
  return (
    <React.Fragment>
      <CardsSortAndFilterBar
        filters={filters}
        setFilters={updateFilters}
        sorting={sorting}
        setSorting={updateSorting}
      />
      <TableContainer>
        <Table className={classes.table} stickyHeader aria-label="menu editor table">
          <TableHead>
            <TableRow>
              <TableCell>{t('card-code-label')}</TableCell>
              {isParent && <TableCell>{t('restaurant')}</TableCell>}
              <TableCell>{t("discount-amount-label")}</TableCell>
              <TableCell>{t('customers-table-header-creation-time')}</TableCell>
              <TableCell>{t('common-created-by')}</TableCell>
              <TableCell>{t('payment-method')}</TableCell>
              <TableCell>{t('transaction-type')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {transactions && transactions.items && transactions.items.map(transaction => {
              const {
                cardCode,
                paymentChannel,
                createdAt,
                amount,
                id,
                type,
                createdByAccount = {},
                restaurant = {}
              } = transaction;
              const { firstName, lastName } = (createdByAccount || {} )
              const { name } = (restaurant || {} )
              let formattedUser = `${firstName ?? ""} ${lastName ?? ""}`
              const _amount = amount ? amount : "";
              const payment = paymentChannelTypeOptions.filter(x => x.value === paymentChannel);
              const paymentText = payment.length > 0 ? payment[0].i18nKey : paymentChannel;
              const transactionType = giftCardTransactionsTypeOptions.filter(x => x.value === type);
              const typeText = transactionType.length > 0 ? transactionType[0].i18nKey : type;
              
              return (
                <TableRow
                  hover
                  key={`transaction-${id}`}
                  className={classes.clickable}
                  onClick={() => resolveCardFromCardCode(cardCode)}
                >
                  <TableCell align="left">{cardCode}</TableCell>
                  {isParent && <TableCell>{name}</TableCell>}
                  <TableCell align="left">{_amount}</TableCell>
                  <TableCell align="left">{moment(createdAt).format("DD-MM-YYYY HH:mm")}</TableCell>
                  <TableCell align="left">{formattedUser}</TableCell>
                  <TableCell align="left">{t(paymentText)}</TableCell>
                  <TableCell align="left">{t(typeText)}</TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell>{t('table-count', { count: transactions.total })}</TableCell>
              <TableCell />
              <TableCell />
              <TableCell />
              <TableCell />
              <TableCell />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
      {transactions && transactions.pages > 1 && (
        <div className={classes.pagination}>
          <Pagination
            count={transactions.pages}
            variant="text"
            shape="rounded"
            onChange={(e, v) => setPageOffset((v - 1) * pageSize)}
          />
        </div>
      )
      }
    </React.Fragment>
  )
}

export default withTranslation('common')(CardTransactionsTableReporting);
