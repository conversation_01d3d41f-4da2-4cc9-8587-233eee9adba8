import { makeStyles } from "@material-ui/core/styles";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";


const useStyles = makeStyles( theme => ({
  content: {
    paddingTop: 11,
    overflow: "hidden",
    display: "flex",
    flexDirection: "column",
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: 12,
    height: 32
  },
  headerTitle: {
    ...typography.medium.semiBold,
    whiteSpace: "nowrap"
  },
  spacing: {
    flex: 1
  },
  actions: {
    display: "flex",
    "& > div + div": {
      marginLeft: 12,
    }
  },
  selectWrapper: {
    ["& .MuiNativeSelect-select:focus"]: { borderRadius: 10 },
  },
  pagination: {
    marginTop: theme.spacing(2),
    display: 'flex',
    justifyContent: 'flex-end'
  },
}));


export default useStyles;