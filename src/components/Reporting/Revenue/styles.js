import { makeStyles } from '@material-ui/core/styles';
import typography from "../../../../styles/typography";

const useStyles = makeStyles(theme => ({
  content: {
    paddingTop: 11,
    // minHeight: `calc(100vh - 49px)`
    overflow: "hidden",
    display: "flex",
    flexDirection: "column",
    height: "100%"
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: 12,
    height: 32
  },
  headerTitle: {
    ...typography.medium.semiBold,
    whiteSpace: "nowrap"
  },
  spacing: {
    flex: 1
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  buttonRoot: {
    padding: '6px 16px',
    borderRadius: 10,
    marginLeft: 8,
    fontStyle: "normal",
    fontWeight: "500",
    fontSize: "14px",
    lineHeight: "20px",
  },
  createButton: {
    background: "#FF7C5C",
    letterSpacing: "-0.0014em",
    color: "#F9F9F9",
    '&:hover': {
      background: "#FF7C5C",
    },
    textTransform: "capitalize"
  },
  icon: {
    color: 'rgba(0, 0, 0, 0.54)'
  },
  user: {
    display: 'flex',
    alignItems: 'center'
  },
  avatarRoot: {
    width: 16,
    height: 16
  },
  username: {
    marginLeft: theme.spacing(1)
  },
  tabs: {
    marginBottom: theme.spacing(2)
  },
  itemImg: {
    height: 24,
    width: 24,
    marginRight: 6,
    marginLeft: 6,
    objectFit: 'cover',
    borderRadius: 4
  },
  itemTitle: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: 12
  },
  clickable: {
    cursor: 'pointer'
  },
  numeration: {
    fontWeight: 500,
    marginRight: 8,
    marginLeft: 8,
  },
  emptyPlaceholder: {
    minHeight: 200,
    display: 'flex',
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    flexDirection: 'column'
  },
  placeholderText: {
    marginBottom: theme.spacing(3)
  },
  card: {
    backgroundColor: "#F9F9F9",
    borderRadius: 12,
    padding: 16,
    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.02)",
    flex: "33%",
    maxWidth: 250,
    "&+&": {
      marginLeft: 16
    }
  },
  cardTitle: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "16px",
    lineHeight: "24px",
    color: "#333332"
  },
  cardSub: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "11px",
    lineHeight: "16px",
    letterSpacing: "0.02em",
    textTransform: "uppercase",
    color: "#737372",
    marginTop: 2
  },
  reportCards: {
    display: "flex",
    alignItems: "center",
    marginBottom: 16
  },
  actions: {
    display: "flex",
    alignItems: "center",
    "& > div + div": {
      marginLeft: 12,
      // paddingLeft: 12,
      // position: "relative",
      // "&:before": {
      //   content: '""',
      //   position: "absolute",
      //   left: 0,
      //   bottom: 4,
      //   width: 1,
      //   height: 24,
      //   borderLeft: "1px solid #D9D9D8"
      // }
    }
  },
  moreOptions: {
    marginLeft: 8
  },
  dailyReportSelector: {
    marginLeft: 10
  },
  menuTableCell: {
    display: "flex",
    alignItems: "center"
  },
  menuColor: {
    width: 14,
    height: 14,
    borderRadius: "100%",
    marginRight: 8
  },
  capitalize: {
    textTransform: "capitalize"
  }
}));

export const useHeaderClasses = makeStyles(() => ({
  container: {
    background: "#F9F9F9",
    padding: 16,
    borderRadius: 12,
    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.02)",
    width: "100%",
    "&+&": {
      marginTop: 16
    },
    marginBottom: 16
  },
  topBar: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    paddingBottom: 16,
    marginBottom: 16,
    borderBottom: "1px dashed #D8D7D6"
  },
  containerTitle: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#333332"
  },
  sourceBtnText: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
    marginRight: 2
  },
  sourceBtnIcon: {
    transform: "rotate(180deg)",
    display: "flex"
  },
  row: {
    display: "flex",
  },
  column: {
    display: "flex",
    flexDirection: "column",
    "&+&": {
      marginLeft: 32
    }
  },
  cardTitle: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "16px",
    lineHeight: "24px",
    color: "#333332"
  },
  cardSubtitle: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "11px",
    lineHeight: "16px",
    letterSpacing: "0.02em",
    textTransform: "uppercase",
    color: "#737372",
    marginTop: 2
  }
}));

export const useMenuStyles = makeStyles((theme) => ({
  list: {
    paddingTop: 0,
    paddingBottom: 0
  },
  paper: {
    backgroundColor: theme.palette.common.white,
    borderRadius: 12,
    minWidth: 340,
    maxWidth: "100%",
    boxShadow: "0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04)",
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 2,
    paddingBottom: 2,
    transition: "none"
  },
  menuItemRoot: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    "&:hover": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  },
  borderTop: {
    borderTop: "1px solid #EFEFEE"
  },
  borderBottom: {
    borderBottom: "1px solid #EFEFEE"
  },
  content: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  },
  selection: {
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
    marginRight: 6
  },
  option: {
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#333332",
  },
  selected: {
    fontWeight: 500,
    color: "#FF7C5C"
  },
  right: {
    display: "flex",
    alignItems: "center"
  },
  customRangeRightContent: {
    display: "flex",
    alignItems: "center"
  },
  calendarSelectedRange: {
    marginRight: 2,
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
  }
}));

export const useRadioButtonStyles = makeStyles((theme) => ({
  root: {
    padding: 0,
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
  icon: {
    borderRadius: '50%',
    width: 16,
    height: 16,
    border: "1px solid #D8D7D6",
    backgroundColor: theme.palette.common.white,
    '$root.Mui-focusVisible &': {
      outlineOffset: 2,
    },
    'input:disabled ~ &': {
      boxShadow: 'none',
      background: 'rgba(206,217,224,.5)',
    },
  },
  checkedIcon: {
    border: "none",
    backgroundColor: '#FF7C5C',
    '&:before': {
      display: 'block',
      width: 16,
      height: 16,
      backgroundImage: `radial-gradient(${theme.palette.common.white},${theme.palette.common.white} 25%,transparent 27%)`,
      content: '""',
    },
  },
}));

export default useStyles;
