import React, {useEffect, useState} from "react";
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableBody from '@material-ui/core/TableBody';
import Table from '@material-ui/core/Table';
import {withTranslation} from '../../../../i18n';
import {getAccounts, getRestaurantRevenueReport, getRestaurantRevenueSummaryReport} from '../../../api';
import Typography from '@material-ui/core/Typography';
import {useDispatch, useSelector} from "react-redux";
import {accountSelectors, reportingSelectors, restaurantSelectors} from "../../../../redux/selectors";
import {
  noop,
  revenueGroups,
  revenueGroupOptions,
  permissionIdentifiers,
  getColorCode, reportingViews, dateTimePickerRanges
} from "../../../utils/const";
import {formatNumber} from "../../../utils/formatNumber";
import DateTimePicker from "../../DateTimePicker";
import GroupBySwitcher from "../../GroupBySwitcher";
import {reportingActions} from "../../../../redux/actions";
import { Radio, TableFooter, useMediaQuery } from "@material-ui/core";
import isEmpty from "../../../utils/isEmpty";
import clsx from "clsx";
import useStyles, {useHeaderClasses, useMenuStyles, useRadioButtonStyles} from "./styles";
import MoreOptionsMenu from "../../MoreOptionsMenu";
import dynamic from 'next/dynamic';
import { controlStringLength } from "../../../utils/sliceString";
import palette from "../../../../styles/palette";
import { capitalizeText } from "../../../utils/capitalizeText";
import typography from "../../../../styles/typography";
import CurrencyBadge from "../../_tags/CurrencyBadge";
import ReportBlockingErrorModal from "../../_popup/ReportBlockingErrorModal";
import moment from "moment";

const Chart = dynamic(() => import("react-charts").then((mod) => mod.Chart), { ssr: false });

function RadioButton(props) {
  const classes = useRadioButtonStyles();

  return (
    <Radio
      className={classes.root}
      disableRipple
      color="default"
      checkedIcon={<span className={clsx(classes.icon, classes.checkedIcon)} />}
      icon={<span className={classes.icon} />}
      {...props}
    />
  );
}

export const Revenue = withTranslation('common')(({ t }) => {
  const classes = useStyles();
  const headerClasses = useHeaderClasses();
  const dispatch = useDispatch();

  const isMobile = useMediaQuery('(max-width:600px)');
  const isShort = useMediaQuery('(max-height:900px)');

  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;

  const { dateTimePicker = {} } = useSelector(reportingSelectors.getDateTimePicker);
  const { dateType, dateRange = {} } = dateTimePicker;
  const { id: dateRangeId, start, end } = dateRange;

  const { revenueGroupBy } = useSelector(reportingSelectors.getRevenueGroupBy);

  const [revenueReport, setRevenueReport] = useState({});
  const { totalRevenue, count: totalCount, orderRevenue, tips } = revenueReport;

  const [revenueSummaryReport, setRevenueSummaryReport] = useState({});
  const { finished: finishedRevenue, running: runningRevenue, expected: expectedRevenue } = revenueSummaryReport;

  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const hasPermission = permissionIds.some(permissionId => reportingViews.REVENUE.permissions.includes(permissionId))
  const hasFilterPermission = permissionIds.some(permissionId => [permissionIdentifiers.CAN_VIEW_REPORTS.value].includes(permissionId))

  const [accounts, setAccounts] = useState([]);
  const [filters, setFilters] = useState(null);
  const [selectedFilters, setSelectedFilters] = useState(null);
  
  const [showingTimeBlockerError, setShowingTimeBlockerError] = useState(false)

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    setRevenueReport({});
    dispatch(reportingActions.setReportRange(null, null));
    getRestaurantRevenueSummaryReport(restaurantId, start, end, dateType)
      .then(({ data: revenueSummaryReportData }) => {
        setRevenueSummaryReport(revenueSummaryReportData);
        const { startingReportNumber, endingReportNumber } = revenueSummaryReportData;
        dispatch(reportingActions.setReportRange(startingReportNumber, endingReportNumber));
      })
      .catch(({ response = {} }) => {
        const { status, data = {} } = (response || {});
        const { title } = (data || {});
        if (status === 422) {
          setShowingTimeBlockerError(true)
          dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.TODAY.value, start: moment().startOf('day').add(2, 'hour').format(), end: moment().endOf('day').format(), }));
        }
      })
    getRestaurantRevenueReport(restaurantId, start, end, dateType, revenueGroupBy)
      .then(({ data: revenueReportData }) => setRevenueReport(revenueReportData))
      .catch(({ response = {} }) => {
        const { status, data = {} } = (response || {});
        const { title } = (data || {});
        if (status === 422) {
          setShowingTimeBlockerError(true)
          dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.TODAY.value, start: moment().startOf('day').add(2, 'hour').format(), end: moment().endOf('day').format(), }));
        }
      })
    if ((revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && isEmpty([]) && hasFilterPermission) {
      getAccounts(restaurantId, 100, 0)
        .then(({ data = {} }) => {
          setAccounts(data.items)
          if (!isEmpty(data.items)) {
            setFilters([
              {
                value: "accountId",
                i18nKey: "account",
                options: [{ value: null, label: t('all')}]
                  .concat(data.items.map(({ id, firstName = "", lastName = "", email = "", mobile = "" }) => ({
                    value: id,
                    label: (firstName || lastName) ? `${firstName} ${lastName}` : (email ? email : mobile)
                  })))
              },
            ])
          }
        }).catch(() => {})
    }
  }, [dateType, dateRangeId, start, end, revenueGroupBy]);
  

  const getDataFiltered = (filters = {}) => {
    setSelectedFilters(filters);
    getRestaurantRevenueReport(restaurantId, start, end, dateType, revenueGroupBy, filters)
      .then(({ data: revenueReportData }) => setRevenueReport(revenueReportData))
      .catch(({ response = {} }) => {
        const { status, data = {} } = (response || {});
        const { title } = (data || {});
        if (status === 422) {
          setShowingTimeBlockerError(true)
          dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.TODAY.value, start: moment().startOf('day').add(2, 'hour').format(), end: moment().endOf('day').format(), }));
        }
      })
  }

  const setGroupBy = (value) => {
    if (value === revenueGroupBy) return;
    setRevenueReport({});
    dispatch(reportingActions.setRevenueGroupBy(value))
  }

  if (!hasPermission) {
    return null;
  }

  const updateReportingView = (value, params) => dispatch(reportingActions.setView(value, params));

  const openSource = (item = {}) => () => {
    const { menu = {}, taxType = "", paymentChannel = "", account = {}, revenue, orderType = "" } = item;
    if (revenueGroupBy === revenueGroups.MENU.value) {
      updateReportingView("orders", { menuId: menu.id, menu })
    }

    if (revenueGroupBy === revenueGroups.TAX.value) {
      if (taxType === 'NO_TAX') {
        return;
      }
      updateReportingView("orders", { taxCategory: taxType })
    }

    if (revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) {
      updateReportingView("orders", { paymentChannel, ...selectedFilters })
    }

    if (revenueGroupBy === revenueGroups.WAITER.value) {
      updateReportingView("orders", { accountId: account.id, account })
    }

    if (revenueGroupBy === revenueGroups.ORDER_TYPE.value) {
      updateReportingView("orders", { orderType })
    }

    if (revenueGroupBy === revenueGroups.DINE_PREFERENCE.value) {
      updateReportingView("orders", { orderType })
    }
  };

  const renderMenu = (menu = {}) => {
    if (isEmpty(menu)) {
      return null;
    }
    const { internalTitle, title, color } = menu;
    const resolvedColor = getColorCode(color);
    return (
      <div className={classes.menuTableCell}>
        <div className={classes.menuColor} style={{ background: resolvedColor }} />
        {internalTitle || title}
      </div>
    )
  }

  const renderGroupMenu = (groupMenu = {}, menu = {}) => {
    if (isEmpty(groupMenu) && isEmpty(menu)) {
      return null;
    }

    const { nameI18n = {}, internalNameI18n = {}, deleted } = groupMenu;
    const { titleI18n = {} } = menu
    return (
      <div className={classes.menuTableCell}>
        {nameI18n.de || internalNameI18n.de || titleI18n.de}
        {deleted && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            borderRadius: 12,
            backgroundColor: palette.negative["100"],
            padding: '2px 8px',
            marginLeft: 8
          }}>
            <Typography style={{ ...typography.small.medium }}>{t('deleted')}</Typography>
          </div>
        )}
      </div>
    )
  }

  const renderAccount = (account = {}) => {
    if (isEmpty(account)) {
      return t('allo-guest-checkout')
    }

    return `${account.firstName || ''} ${account.lastName || ""}`
  }

  const primaryAxis = React.useMemo(
    () => ({
      getValue: (datum) => datum.primary,
    }), []
  )

  const secondaryAxes = React.useMemo(
    () => [
      {
        getValue: (datum) => datum.secondary,
        stacked: true,
      },
    ], []
  )

  const getChartData = (revenueReportItems) => {
    return [
      {
        "label": `${t('group-by')} ${t(revenueGroups[revenueGroupBy].i18nKey)}`,
        "data": revenueReportItems.map(({ revenue, menu = {}, taxType = "", paymentChannel = "", account = {}, tips, orderRevenue, orderType = "", dinePreference = null }, index) => {
          let name = "";
          if (revenueGroupBy === revenueGroups.MENU.value) {
            const { internalTitle, title } = menu;
            name = internalTitle || title
          }
          if (revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) {
            name = t(`payment-channels-${paymentChannel.toLowerCase()}`) || ""
          }
          if (revenueGroupBy === revenueGroups.TAX.value) {
            name = (taxType === 'NO_TAX' ? t('tips') : t(`payment-by-tax-breakdown-${taxType.toLowerCase()}-label`)) || ""
          }
          if (revenueGroupBy === revenueGroups.WAITER.value) {
            name = !isEmpty(account) ? `${account.firstName || ""} ${account.lastName || ""}` : ""
          }
          if (revenueGroupBy === revenueGroups.ORDER_TYPE.value) {
            name = ((taxType === 'NO_TAX') ? t('tips') : t(`menu-editor-form-order-type-field-option-${orderType.toLowerCase().replace("_", "-")}`)) || ""
          }
          if (revenueGroupBy === revenueGroups.DINE_PREFERENCE.value) {
            name = capitalizeText(((taxType === 'NO_TAX') ? t('tips') : t(`menu-editor-form-order-type-field-option-${dinePreference.toLowerCase().replace("_", "-")}`)) || "")
          }
          return {
            "primary": index + 1 + ". " + controlStringLength(name, 10),
            "secondary": revenue
          }
        })
      }
    ]
  }

  return (
    <div className={classes.content} data-testid="reporting-revenue">
      <div className={classes.header}>
        <Typography className={classes.headerTitle}>{t('revenue')}</Typography>
        <div className={classes.spacing} />
        <div className={classes.actions}>
          <DateTimePicker error={showingTimeBlockerError} />
        </div>
      </div>
      <div className={headerClasses.container}>
        <div className={headerClasses.content}>
          <div className={headerClasses.row}>
            <div className={headerClasses.column}>
              <Typography className={headerClasses.cardTitle}>{formatNumber(finishedRevenue)}€</Typography>
              <Typography className={headerClasses.cardSubtitle}>{t('finished')}</Typography>
            </div>
            <div className={headerClasses.column}>
              <Typography className={headerClasses.cardTitle}>{formatNumber(runningRevenue)}€</Typography>
              <Typography className={headerClasses.cardSubtitle}>{t('running')}</Typography>
            </div>
            <div className={headerClasses.column}>
              <Typography className={headerClasses.cardTitle}>{formatNumber(expectedRevenue)}€</Typography>
              <Typography className={headerClasses.cardSubtitle}>{t('expected')}</Typography>
            </div>
          </div>
        </div>
      </div>
      {!isMobile && !isShort && revenueReport && (revenueReport.count > 0) && (
        <div className={headerClasses.container}>
          <div style={{ width: "100%", height: 300 }}>
            <Chart
              options={{
                data: getChartData(revenueReport.items),
                primaryAxis,
                secondaryAxes,
                defaultColors: [palette.primary["500"], palette.secondary.green["500"]],
                getSeriesStyle: () => ({
                  rectangle: {
                    rx: 4
                  }
                })
              }}
            />
          </div>
        </div>
      )}
      <div className={classes.header}>
        <div />
        <div className={classes.spacing} />
        <div className={classes.actions}>
          {revenueGroups[revenueGroupBy] && (
            <GroupBySwitcher options={revenueGroupOptions} selectOption={setGroupBy} selectedOption={revenueGroups[revenueGroupBy]} />
          )}
          {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && hasFilterPermission && (
            <div className={classes.moreOptions}>
              <MoreOptionsMenu
                callback={getDataFiltered}
                filters={filters} />
            </div>
          )}
        </div>
      </div>
      <TableContainer>
        <Table stickyHeader className={classes.table} aria-label="menu editor table">
          <TableHead>
            <TableRow>
              {(revenueGroupBy === revenueGroups.MENU.value) && <TableCell>{t('category')}</TableCell>}
              {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && <TableCell>{t('payment-method')}</TableCell>}
              {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && <TableCell align="right">{t('order-activity-tab-order')}</TableCell>}
              {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && <TableCell align="right">{t('receipt-tip-label')}</TableCell>}
              {(revenueGroupBy === revenueGroups.TAX.value) && <TableCell>{t('tax')}</TableCell>}
              {(revenueGroupBy === revenueGroups.WAITER.value) && <TableCell>{t('waiter')}</TableCell>}
              {(revenueGroupBy === revenueGroups.ORDER_TYPE.value) && <TableCell>{t('order-type')}</TableCell>}
              {(revenueGroupBy === revenueGroups.DINE_PREFERENCE.value) && <TableCell>{t('dine-preference')}</TableCell>}
              {(revenueGroupBy === revenueGroups.MENU_GROUP.value) && <TableCell>{t('menu-group')}</TableCell>}
              <TableCell align="right">{t('amount')}</TableCell>
            </TableRow>
          </TableHead>
          {revenueReport && (revenueReport.count > 0) && (
            <TableBody>
              {revenueReport.items.map(({ menu = {}, taxType = "", paymentChannel = "", account = {}, tips, orderRevenue, revenue, orderType = "", dinePreference, menuGroup = {} }) => (
                <TableRow key={menu.id || taxType || paymentChannel || account.id} className={classes.clickable} onClick={openSource({ menu, taxType, paymentChannel, account, orderType })}>
                  <TableCell className={classes.capitalize}>
                    {(revenueGroupBy === revenueGroups.MENU.value) ? renderMenu(menu) : ""}
                    {(revenueGroupBy === revenueGroups.TAX.value) ? (taxType === 'NO_TAX' ? t('tips') : t(`payment-by-tax-breakdown-${taxType.toLowerCase()}-label`)) : ""}
                    {(revenueGroupBy === revenueGroups.WAITER.value) ? renderAccount(account) : ""}
                    {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) ? t(`payment-channels-${paymentChannel.toLowerCase()}`) : ""}
                    {(revenueGroupBy === revenueGroups.ORDER_TYPE.value) ? ((taxType === 'NO_TAX') ? t('tips') : t(`menu-editor-form-order-type-field-option-${orderType.toLowerCase().replace("_", "-")}`)) : ""}
                    {(revenueGroupBy === revenueGroups.DINE_PREFERENCE.value) ? ((taxType === 'NO_TAX') ? t('tips') : t(`menu-editor-form-order-type-field-option-${dinePreference.toLowerCase().replace("_", "-")}`)) : ""}
                    {(revenueGroupBy === revenueGroups.MENU_GROUP.value) ? renderGroupMenu(menuGroup, menu) : ""}
                  </TableCell>
                  {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && (
                    <TableCell align="right">
                      <CurrencyBadge amount={formatNumber(orderRevenue)}/>
                    </TableCell>
                  )}
                  {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && (
                    <TableCell align="right">
                      <CurrencyBadge amount={formatNumber(tips)}/>
                    </TableCell>
                  )}
                  <TableCell align="right">
                    <CurrencyBadge amount={formatNumber(revenue)}/>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          )}
          <TableFooter>
            <TableRow>
              <TableCell>
                {t('table-count', { count: totalCount })}
              </TableCell>
              {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && (
                <TableCell align="right">
                  {t('table-total', { total: formatNumber(orderRevenue) })}
                </TableCell>
              )}
              {(revenueGroupBy === revenueGroups.PAYMENT_METHOD.value) && (
                <TableCell align="right">
                  {t('table-total', { total: formatNumber(tips) })}
                </TableCell>
              )}
              <TableCell align="right">
                {t('table-total', { total: formatNumber(totalRevenue) })}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
      {showingTimeBlockerError && <ReportBlockingErrorModal open={showingTimeBlockerError} onClose={() => setShowingTimeBlockerError(false)} />}
    </div>
  );
});
