import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles(theme => ({
  content: {
    padding: theme.spacing(2),
    minHeight: `calc(100vh - 49px)`
  },
  section: {
    maxWidth: theme.breakpoints.values.lg,
    margin: '0 auto',
    '&+&': {
      marginTop: theme.spacing(8)
    }
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2)
  },
  spacing: {
    flex: '1'
  },
  icon: {
    marginRight: theme.spacing(1),
    color: '#dbdede'
  },
  card: {
    border: '1px solid #f3f4f4',
    borderRadius: theme.spacing(1),
    padding: theme.spacing(2)
  },
  chart: {
    height: 300,
    marginTop: theme.spacing(2)
  },
  emptyPlaceholder: {
    minHeight: 200,
    display: 'flex',
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    flexDirection: 'column'
  },
  placeholderText: {
    marginBottom: theme.spacing(3)
  },
  userReport: {
    paddingTop: theme.spacing(2)
  }
}));

export default useStyles;
