import React, {useEffect, useState} from "react";
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableBody from '@material-ui/core/TableBody';
import Table from '@material-ui/core/Table';
import {withTranslation} from '../../../../i18n';
import {
  deleteFiskalDailyClosing, emailMonthlyReport, emailReport,
  generateFiskalDailyClosing,
  getAccounts, getMonthlyReports,
  getReports, printMonthlyReport,
  printReport, printWaiterReport, regenerateReport
} from "../../../api";
import Typography from '@material-ui/core/Typography';
import {useDispatch, useSelector} from "react-redux";
import {accountSelectors, restaurantSelectors} from "../../../../redux/selectors";
import {
  noop,
  permissionIdentifiers, reportingExportTypes,
  reportingViews
} from "../../../utils/const";
import {reportingActions} from "../../../../redux/actions";
import useStyles, { useMenuStyles } from "./styles";
import isEmpty from "../../../utils/isEmpty";
import byId from "../../../utils/byId";
import Moment from "react-moment";
import Button from "@material-ui/core/Button";
import CircularProgress from "@material-ui/core/CircularProgress";
import Pagination from "@material-ui/lab/Pagination";
import preventEventPropagation from "../../../utils/preventEventPropagation";
import clsx from "clsx";
import {useTabsStyles} from "../ImprovementsSource/styles";
import moment from "moment";
import {formatNumber} from "../../../utils/formatNumber";
import {YearSelector} from "../../Administration/MonthlyReport/YearSelector";
import TextUpdateModal from "../../_popup/TextUpdateModal";
import CurrencyBadge from "../../_tags/CurrencyBadge";
import MoreOptionsButton from "../../_buttons/MoreOptionsButton";
import CustomMenu from "../../_popup/CustomMenu";
import MenuItem from "@material-ui/core/MenuItem";
import typography from "../../../../styles/typography";
import posthog from "posthog-js";
import DailyReportModal from "../../_popup/DailyReportModal";

export const Exports = withTranslation('common')(({ t }) => {
  const classes = useStyles();
  const menuClasses = useMenuStyles();
  const tabClasses = useTabsStyles();
  const dispatch = useDispatch();
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  
  const accountEmail = useSelector(accountSelectors.getAccountEmail)
  const accountId = useSelector(accountSelectors.getAccountId)
  
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const hasPermission = permissionIds.some(permissionId => [permissionIdentifiers.CAN_VIEW_REPORTS.value, permissionIdentifiers.CAN_VIEW_OWN_REPORTS.value].includes(permissionId))
  const hasFullReportingPermission = permissionIds.some(permissionId => [permissionIdentifiers.CAN_VIEW_REPORTS.value].includes(permissionId))
  
  const [selectedReportType, setSelectedReportType] = useState(reportingExportTypes.DAILY_REPORT.value)
  const updateSelectedReportType = (val) => {
    setSelectedReportType(val)
  }
  
  const [reports, setReports] = useState({});
  const [monthlyReports, setMonthlyReports] = useState([]);
  const [loaded, setLoaded] = useState(false);
  const [usersById, setUsersById] = useState({});
  const [error, setError] = useState(false);
  const [printing, setPrinting] = useState(null);
  
  const [sendingByEmail, setSendingByEmail] = useState(null);
  const [reportEmail, setReportEmail] = useState(accountEmail);
  const [editingDailyReportEmail, setEditingDailyReportEmail] = useState(false);
  const [editingMonthlyReportEmail, setEditingMonthlyReportEmail] = useState(false);
  
  const [generatingDailyClosing, setGeneratingDailyClosing] = useState(null);
  const [deletingDailyClosing, setDeletingDailyClosing] = useState(null);
  
  const [pageOffset, setPageOffset] = useState(0);
  const [query, setQuery] = useState("");
  const updateQuery = (e) => setQuery(e.target.value)
  const clearQuery = () => setQuery("");
  
  const [year, setYear] = useState(new Date().getFullYear());
  const changeYear = e => setYear(e.target.value);
  
  const [anchorMonthly, setAnchorMonthly] = useState({ anchor: null, monthNumber: null });
  const [anchorDaily, setAnchorDaily] = useState({ anchor: null, id: null, identifier: null });
  const openMonthlyMenu = (e, monthNumber) => {
    preventEventPropagation(e);
    setAnchorMonthly({ anchor: e.currentTarget, monthNumber });
  }
  const closeMonthlyMenu = () => setAnchorMonthly({ anchor: null, monthNumber: null });
  
  const openDailyMenu = (e, id, identifier) => {
    preventEventPropagation(e);
    setAnchorDaily({ anchor: e.currentTarget, id, identifier });
  }
  const closeDailyMenu = () => {
    setAnchorDaily({ anchor: null, id: null, identifier: null });
  }
  
  const pageSize = 10;
  
  useEffect(() => {
    window.scrollTo(0, 0);
    fetchReports();
  }, []);
  
  const fetchReports = () => {
    getReports(restaurantId, pageSize, pageOffset, query).then(({ data }) => {
      const { total, items, pages } = data;
      setReports({ total, items, pages });
      setLoaded(true);
      if (!isEmpty(data.items)) {
        const userIds = (data.items)
          .map(i => i.closedBy)
          .filter( i => !!i);
        if (!isEmpty(userIds)){
          getAccounts(null, 100, 0, null, [...new Set(userIds)]).then(({ data: userData }) => {
            setUsersById(byId(userData.items))
          }).catch((e) => setError(e.response?.data?.title))
        }
      }
    }).catch((e) => setError(e.response?.data?.title))
  };
  
  const fetchMonthlyReports = () => {
    getMonthlyReports(restaurantId, year)
      .then(({ data = [] }) => {
        setMonthlyReports(data.filter(i => i.total > 0));
      })
      .catch((e) => setError(e?.response?.data))
  };
  
  useEffect(() => {
    if (selectedReportType === reportingExportTypes.DAILY_REPORT.value) {
      fetchReports();
    }
    if (selectedReportType === reportingExportTypes.MONTHLY_REPORT.value) {
      fetchMonthlyReports();
    }
  }, [selectedReportType, year, pageOffset]);
  
  if (!hasPermission) {
    return null;
  }

  const [dailyReportModalIsOpen, setDailyReportModalIsOpen] = useState(false);

  const printDailyReport = (id, targetAccount) => {
    setPrinting(id);
    if (hasFullReportingPermission) {
      printReport(restaurantId, id, targetAccount?.id ?? null).then(() => {}).catch((e) => setError(e.response?.data?.title))
        .finally(() => {
          setPrinting(null);
        })
    } else {
      printWaiterReport(restaurantId, id).then(() => {}).catch((e) => setError(e.response?.data?.title))
        .finally(() => {
          setPrinting(null);
        })
    }
  };

  const regenerateDailyReport = (event, id) => {
    preventEventPropagation(event);
    setPrinting(id);
    if (hasFullReportingPermission) {
      regenerateReport(restaurantId, id).then(() => {}).catch((e) => setError(e.response?.data?.title))
        .finally(() => {
          setPrinting(null);
        })
    }
  };
  
  const createFiskalDailyClosing = (event, id) => {
    preventEventPropagation(event);
    setGeneratingDailyClosing(id);
    generateFiskalDailyClosing(restaurantId, id).then(noop).catch((e) => setError(e.response?.data?.title))
      .finally(() => {
        setGeneratingDailyClosing(null);
      })
  }
  
  const deleteDailyClosing = (event, id) => {
    preventEventPropagation(event);
    setDeletingDailyClosing(id);
    deleteFiskalDailyClosing(restaurantId, id).then(noop).catch((e) => setError(e.response?.data?.title))
      .finally(() => {
        setDeletingDailyClosing(null);
      })
  }
  
  const _printMonthlyReport = (index) => {
    setPrinting(index);
    printMonthlyReport(restaurantId, year, index)
      .then(() => {})
      .catch((e) => setError(e?.response?.data))
      .finally(() => {
        setPrinting(null);
      })
  }
  
  const triggerPDFMonthlyReport = (event, month) => {
    preventEventPropagation(event);
    setSendingByEmail(month);
    setEditingMonthlyReportEmail(true);
  }
  
  const _sendPDFMonthlyReport = (email) => {
    const formattedMonth = (sendingByEmail && sendingByEmail < 10) ? `0${sendingByEmail}` : sendingByEmail;
    
    emailMonthlyReport(restaurantId, `${year}-${formattedMonth}`, email)
      .then(() => {
        try {
          posthog.capture('monthly_report.emailed', {
            report_month: `${year}-${formattedMonth}`,
            emailed_at: new Date().toString(),
            user_id: accountId
          })
        } catch (error) {
          console.error(error);
        }
      })
      .catch((e) => setError(e?.response?.data))
      .finally(() => {
        setEditingMonthlyReportEmail(false);
        setSendingByEmail(null);
      })
  }
  
  const triggerPDFDailyReport = (event, id) => {
    preventEventPropagation(event);
    setSendingByEmail(id);
    setEditingDailyReportEmail(true);
  }
  
  const _sendPDFDailyReport = (email, account) => {
    emailReport(restaurantId, sendingByEmail, email, account?.id ?? null)
      .then(() => {
        try {
          posthog.capture('daily_report.emailed', {
            report_id: anchorDaily.id,
            emailed_at: new Date().toString(),
            user_id: accountId
          });
        } catch (error) {
          console.error(error);
        }
      })
      .catch((e) => setError(e?.response?.data))
      .finally(() => {
        setEditingDailyReportEmail(false);
        setSendingByEmail(null);
      })
  };
  
  const updateReportingView = (value, params) => () => dispatch(reportingActions.setView(value, params));
  
  const showAdminAction = accountEmail === "<EMAIL>";

  const onPrint = (e, reportId) => {
    preventEventPropagation(e);
    if (hasFullReportingPermission) {
      setDailyReportModalIsOpen(true)
    } else {
      printDailyReport(reportId);
    }
  }

  return (
    <div className={classes.content} data-testid="reporting-exports">
      <div className={classes.header}>
        <Typography className={classes.headerTitle}>{t('download-center')}</Typography>
        <div className={classes.spacing} />
        <div className={classes.actions}>
          {!(selectedReportType === reportingExportTypes.DAILY_REPORT.value) && (
            <YearSelector year={year} onChange={changeYear}/>
          )}
        </div>
      </div>
  
      <TableContainer>
        <Table stickyHeader className={classes.table} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell colSpan={showAdminAction ? 14 : 12} className={tabClasses.tabTableCell}>
                <div className={tabClasses.container}>
                  <Button
                    disableRipple
                    className={clsx(tabClasses.tab, { [tabClasses.tabSelected]: selectedReportType === reportingExportTypes.DAILY_REPORT.value })}
                    onClick={() => updateSelectedReportType(reportingExportTypes.DAILY_REPORT.value)}
                  >
                    <span className={clsx(tabClasses.tabContent, { [tabClasses.tabContentSelected]: selectedReportType === reportingExportTypes.DAILY_REPORT.value })}>{t(reportingExportTypes.DAILY_REPORT.i18nKey)}</span>
                    <span className={clsx(tabClasses.tabCount, { [tabClasses.tabCountSelected]: selectedReportType === reportingExportTypes.DAILY_REPORT.value })}>{reports.total}</span>
                  </Button>
                  <Button
                    disableRipple
                    className={clsx(tabClasses.tab, { [tabClasses.tabSelected]: selectedReportType === reportingExportTypes.MONTHLY_REPORT.value })}
                    onClick={() => updateSelectedReportType(reportingExportTypes.MONTHLY_REPORT.value)}
                    disabled={!hasFullReportingPermission}
                  >
                    <span className={clsx(tabClasses.tabContent, { [tabClasses.tabContentSelected]: selectedReportType === reportingExportTypes.MONTHLY_REPORT.value })}>{t(reportingExportTypes.MONTHLY_REPORT.i18nKey)}</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
            {selectedReportType === reportingExportTypes.DAILY_REPORT.value && (
              <TableRow>
                <TableCell align="left">{t('report-history-report-id')}</TableCell>
                <TableCell align="left">{t('report-history-report-start-date-and-start-time')}</TableCell>
                <TableCell align="left">{t('report-history-report-end-date-and-end-time')}</TableCell>
                {hasFullReportingPermission && <TableCell align="right">{t('report-history-dishes-total')}</TableCell>}
                {hasFullReportingPermission && <TableCell align="right">{t('report-history-beverages-total')}</TableCell>}
                {hasFullReportingPermission && <TableCell align="right">{t('cash-register-tax-rate-none')}</TableCell>}
                {hasFullReportingPermission && <TableCell align="right">{t('report-history-total')}</TableCell>}
                <TableCell align="left">{t('report-history-report-user')}</TableCell>
                {showAdminAction && (
                  <TableCell align="left" />
                )}
                {showAdminAction && (
                  <TableCell align="left" />
                )}
              </TableRow>
            )}
            {selectedReportType === reportingExportTypes.MONTHLY_REPORT.value && (
              <TableRow>
                <TableCell align="left">{t('report-history-report-month')}</TableCell>
                <TableCell align="right">{t('report-history-dishes-total')}</TableCell>
                <TableCell align="right">{t('report-history-beverages-total')}</TableCell>
                <TableCell align="right">{t('report-history-report-total')}</TableCell>
                <TableCell align="right">{t('tips')}</TableCell>
                <TableCell align="right">{t('total-with-tips')}</TableCell>
                <TableCell align="left" />
                <TableCell align="left" />
              </TableRow>
            )}
          </TableHead>
          {selectedReportType === reportingExportTypes.DAILY_REPORT.value && (
            <TableBody>
              {reports && reports.items && reports.items.filter(i => !!i.closed).map(rp => {
                const { id, identifier, creationTime, closed, closedByAccountId, closeTime, printingRecord } = rp
                const { total = 0, orderItemsTotal = 0, tipTotal = 0, orderItemsBeveragesTotal, orderItemsDishesTotal  } = printingRecord ?? {};
                const formattedCreationTime = <Moment format={'DD.MM.YYYY'}>{creationTime}</Moment>;
                const formattedStartTime = <Moment format={'HH:mm:ss'}>{creationTime}</Moment>;
                const formattedEndDate = <Moment format={'DD.MM.YYYY'}>{closeTime}</Moment>;
                const formattedEndTime = <Moment format={'HH:mm:ss'}>{closeTime}</Moment>;
                const user = usersById[closedByAccountId] || {};
                let formatterUser = `${user.firstName || ""} ${user.lastName || ""}`;
                if (closedByAccountId === 'service-account-gluttony-api') {
                  formatterUser = 'System'
                }
      
                const finalizedTotal = total ? total : (orderItemsTotal + tipTotal)
                const fixedOrderItemsDishesTotal = ((orderItemsDishesTotal ?? 0).toFixed(2))
                const fixedOrderItemsBeveragesTotal = ((orderItemsBeveragesTotal ?? 0).toFixed(2))
                const fixedTipTotal = ((tipTotal ?? 0).toFixed(2))
                const fixedFinalizedTotal = ((finalizedTotal ?? 0).toFixed(2))
                return (
                  <TableRow key={id} onClick={updateReportingView(reportingViews.DAILY_REPORT.value, { id })} className={classes.tableRow}>
                    <TableCell align="left">{identifier}</TableCell>
                    <TableCell align="left">
                      {formattedCreationTime} {t("common-at")} {formattedStartTime}
                    </TableCell>
                    <TableCell align="left">
                      {closed ? formattedEndDate : '--'} {t("common-at")} {closed ? formattedEndTime : '--'}
                    </TableCell>
                    {hasFullReportingPermission && <TableCell align="right">{closed ? <CurrencyBadge amount={fixedOrderItemsDishesTotal}/> : '--'}</TableCell>}
                    {hasFullReportingPermission && <TableCell align="right">{closed ? <CurrencyBadge amount={fixedOrderItemsBeveragesTotal}/> : '--'}</TableCell>}
                    {hasFullReportingPermission && <TableCell align="right">{closed ? <CurrencyBadge amount={fixedTipTotal}/> : '--'}</TableCell>}
                    {hasFullReportingPermission && <TableCell align="right">{closed ? <CurrencyBadge amount={fixedFinalizedTotal}/> : '--'}</TableCell>}
                    <TableCell align="left">{closedByAccountId ? formatterUser : '--'}</TableCell>
                    <TableCell align="left">
                      <div className={classes.loadingBtnWrapper}>
                        <Button disabled={printing} onClick={(e) => onPrint(e, id)} disableElevation disableRipple disableFocusRipple disableTouchRipple classes={{ root: classes.downloadButtonRoot }}>{t('report-history-print-report-btn')}</Button>
                        {printing && (printing === id) && <CircularProgress size={20} className={classes.loadingBtnProgress} />}
                      </div>
                    </TableCell>
                    <TableCell align="left" onClick={(e)=> openDailyMenu(e, id, identifier)}>
                      <MoreOptionsButton/>
                    </TableCell>
                  </TableRow>
                )
              })}
              <CustomMenu
                id="more-menu-download-center-daily"
                anchorEl={anchorDaily.anchor}
                keepMounted
                open={Boolean(anchorDaily.anchor)}
                onClose={closeDailyMenu}
                classes={{
                  paper: classes.menu
                }}
              >
                <MenuItem classes={{ root: menuClasses.menuItem }} onClick={(e) => {
                  closeDailyMenu()
                  triggerPDFDailyReport(e, anchorDaily.id)
                }}>
                  <Typography style={{ ...typography.body.regular }}>{t('by-email')}</Typography>
                </MenuItem>
                {showAdminAction && (
                  <MenuItem classes={{ root: menuClasses.menuItem }} onClick={(e) => {
                    closeDailyMenu()
                    regenerateDailyReport(e, anchorDaily.id)
                  }}>
                    <Typography style={{ ...typography.body.regular }}>{t('regenerate-report')}</Typography>
                  </MenuItem>
                )}
                {showAdminAction && (
                  <MenuItem classes={{ root: menuClasses.menuItem }} onClick={(e) => {
                    closeDailyMenu()
                    createFiskalDailyClosing(e, anchorDaily.id)
                  }}>
                    <Typography style={{ ...typography.body.regular }}>{t('generate-fiskal-closing')}</Typography>
                  </MenuItem>
                )}
                {showAdminAction && (
                  <MenuItem classes={{ root: menuClasses.menuItem }} onClick={(e) => {
                    closeDailyMenu()
                    deleteDailyClosing(e, anchorDaily.id)
                  }}>
                    <Typography style={{ ...typography.body.regular }}>{t('delete-fiskal-closing')}</Typography>
                  </MenuItem>
                )}
              </CustomMenu>
            </TableBody>
          )}
          {selectedReportType === reportingExportTypes.MONTHLY_REPORT.value && (
            <TableBody>
              {!isEmpty(monthlyReports) && monthlyReports.map(({ firstDate, dishesTotal, beveragesTotal, total, tips, totalWithTips }) => {
                const monthNumber = moment(firstDate).month() + 1;
                const formattedDishesTotal = formatNumber(dishesTotal)
                const formattedBeveragesTotal = formatNumber(beveragesTotal)
                const formattedTips = formatNumber(tips)
                const formattedTotal = formatNumber(total)
                const formattedTotalWithTips = formatNumber(totalWithTips)
                return (
                  <TableRow key={firstDate}>
                    <TableCell align="left">{moment(firstDate).format("MMMM")}</TableCell>
                    <TableCell align="right">
                      <CurrencyBadge amount={formattedDishesTotal}/>
                    </TableCell>
                    <TableCell align="right">
                      <CurrencyBadge amount={formattedBeveragesTotal}/>
                    </TableCell>
                    <TableCell align="right">
                      <CurrencyBadge amount={formattedTotal}/>
                    </TableCell>
                    <TableCell align="right">
                      <CurrencyBadge amount={formattedTips}/>
                    </TableCell>
                    <TableCell align="right">
                      <CurrencyBadge amount={formattedTotalWithTips}/>
                    </TableCell>
                    <TableCell align="left">
                      <div className={classes.loadingBtnWrapper}>
                        <Button disabled={printing} onClick={() => _printMonthlyReport(monthNumber)} disableElevation disableRipple disableFocusRipple disableTouchRipple classes={{ root: classes.downloadButtonRoot }}>{t('report-history-print-report-btn')}</Button>
                        {printing && (printing === monthNumber) && <CircularProgress size={20} className={classes.loadingBtnProgress} />}
                      </div>
                    </TableCell>
                    <TableCell align="left" onClick={(e) => openMonthlyMenu(e, monthNumber)}>
                      <MoreOptionsButton/>
                    </TableCell>
                  </TableRow>
                )
              })}
              <CustomMenu
                id="more-menu-download-center-monthly"
                anchorEl={anchorMonthly.anchor}
                keepMounted
                open={Boolean(anchorMonthly.anchor)}
                onClose={closeMonthlyMenu}
                classes={{
                  paper: classes.menu
                }}
              >
                <MenuItem classes={{ root: menuClasses.menuItem }} onClick={(e) => {
                  closeMonthlyMenu()
                  triggerPDFMonthlyReport(e, anchorMonthly.monthNumber)
                }}>
                  <Typography style={{ ...typography.body.regular }}>{t('by-email')}</Typography>
                </MenuItem>
              </CustomMenu>
            </TableBody>
          )}
        </Table>
      </TableContainer>
      {(selectedReportType === reportingExportTypes.DAILY_REPORT.value) && reports && reports.pages > 1 && (
        <div className={classes.pagination}>
          <Pagination count={reports.pages} variant="text" shape="rounded" onChange={(e, v) => setPageOffset((v - 1) * pageSize)}/>
        </div>
      )}
      {editingDailyReportEmail && (
        <DailyReportModal
          titleI18n={"daily-report"}
          open={editingDailyReportEmail}
          onClose={() => setEditingDailyReportEmail(false)}
          dailyReportEmail={reportEmail}
          restaurantId={restaurantId}
          hasFullReportingPermission={hasFullReportingPermission}
          setValue={_sendPDFDailyReport}
        />
      )}
      {dailyReportModalIsOpen && hasFullReportingPermission && (
        <DailyReportModal
          titleI18n={"daily-report"}
          open={dailyReportModalIsOpen}
          onClose={() => setDailyReportModalIsOpen(false)}
          restaurantId={restaurantId}
          hasFullReportingPermission={hasFullReportingPermission}
          setValue={printDailyReport}
          isPrinting
        />
      )}
      {editingMonthlyReportEmail && (
        <TextUpdateModal
          titleI18n={"email"}
          type='email'
          open={editingMonthlyReportEmail}
          onClose={() => {
            setSendingByEmail(false);
            setEditingMonthlyReportEmail(false);
          }}
          value={reportEmail}
          setValue={_sendPDFMonthlyReport}
        />
      )}
    </div>
  );
});
