import React, {useEffect, useState} from "react";
import {useSelector, useDispatch} from "react-redux";
import Typography from '@material-ui/core/Typography';
import {withTranslation} from '../../../../i18n';
import {
  getRestaurantCustomerReport, getRestaurantReservationsReport,
  getRestaurantRevenueSummaryReport,
  getRestaurantTeamReport,
  getRestaurantTopProductsReport
} from '../../../api';
import {accountSelectors, reportingSelectors, restaurantSelectors} from "../../../../redux/selectors";
import { dateTimePickerRanges, noop, permissionIdentifiers, reportingViews } from "../../../utils/const";
import {formatNumber} from "../../../utils/formatNumber";
import DateTimePicker from "../../DateTimePicker";
import useStyles, {useMenuItemStyles} from "./styles";
import clsx from "clsx";
import {
  ArrowLeftIcon,
  BadgeIconGreen16,
  ReservationsIcon24,
  TeamIcon24
} from "../../../utils/icons";
import ButtonBase from "@material-ui/core/ButtonBase";
import isEmpty from "../../../utils/isEmpty";
import {reportingActions} from "../../../../redux/actions";
import {controlStringLength} from "../../../utils/sliceString";
import ReportBlockingErrorModal from "../../_popup/ReportBlockingErrorModal";
import moment from "moment/moment";

export const Overview = withTranslation('common')(({ t }) => {
  const classes = useStyles();
  const menuItemClasses = useMenuItemStyles();
  const dispatch = useDispatch();
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  
  const { dateTimePicker = {} } = useSelector(reportingSelectors.getDateTimePicker);
  const { dateType, dateRange = {} } = dateTimePicker;
  const { id: dateRangeId, start, end } = dateRange;
  
  const [revenueSummaryReport, setRevenueSummaryReport] = useState({});
  const { finished: finishedRevenue, running: runningRevenue, expected: expectedRevenue } = revenueSummaryReport;
  
  const [topProductsReport, setTopProductsReport] = useState({});
  const { beveragesSold, dishesSold, items: topProducts = [] } = topProductsReport;
  
  const [teamReport, setTeamReport] = useState({});
  const { totalAccounts: totalTeamAccounts, activeAccounts: activeTeamAccounts, plannedShifts: plannedTeamShifts, shiftHours: teamShiftHours } = teamReport;
  
  const [customerReport, setCustomerReport] = useState({});
  const { customersInPeriod, totalNumberOfCustomers, topCustomer = {} } = customerReport;
  
  const [reservationReport, setReservationReport] = useState({});
  const { reservationStatuses = [] } = reservationReport;
  
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const hasPermission = permissionIds.some(permissionId => reportingViews.OVERVIEW.permissions.includes(permissionId))
  
  const [showingTimeBlockerError, setShowingTimeBlockerError] = useState(false)
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    if (!start && !end) {
      return
    }
    //dispatch(reportingActions.setReportRange(null, null))
    getRestaurantRevenueSummaryReport(restaurantId, start, end, dateType)
      .then(({ data: revenueSummaryReportData = {} }) => {
        setRevenueSummaryReport(revenueSummaryReportData)
        const { startingReportNumber, endingReportNumber } = revenueSummaryReportData;
        dispatch(reportingActions.setReportRange(startingReportNumber, endingReportNumber))
      })
      .catch(({ response = {} }) => {
        const { status, data = {} } = (response || {});
        const { title } = (data || {});
        if (status === 422) {
          setShowingTimeBlockerError(true)
          dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.TODAY.value, start: moment().startOf('day').add(2, 'hour').format(), end: moment().endOf('day').format(), }));
        }
      })
    getRestaurantTopProductsReport(restaurantId, start, end, dateType)
      .then(({ data: topProductsReportData }) => setTopProductsReport(topProductsReportData))
      .catch(({ response = {} }) => {
        const { status, data = {} } = (response || {});
        const { title } = (data || {});
        if (status === 422) {
          setShowingTimeBlockerError(true)
          dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.TODAY.value, start: moment().startOf('day').add(2, 'hour').format(), end: moment().endOf('day').format(), }));
        }
      })
    getRestaurantTeamReport(restaurantId, start, end, dateType)
      .then(({ data: teamReportData }) => setTeamReport(teamReportData))
      .catch(({ response = {} }) => {
        const { status, data = {} } = (response || {});
        const { title } = (data || {});
        if (status === 422) {
          setShowingTimeBlockerError(true)
          dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.TODAY.value, start: moment().startOf('day').add(2, 'hour').format(), end: moment().endOf('day').format(), }));
        }
      })
    getRestaurantCustomerReport(restaurantId, start, end, dateType)
      .then(({ data: customerReportData }) => setCustomerReport(customerReportData))
      .catch(({ response = {} }) => {
        const { status, data = {} } = (response || {});
        const { title } = (data || {});
        if (status === 422) {
          setShowingTimeBlockerError(true)
          dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.TODAY.value, start: moment().startOf('day').add(2, 'hour').format(), end: moment().endOf('day').format(), }));
        }
      })
    getRestaurantReservationsReport(restaurantId, start, end, dateType)
      .then(({ data: reservationReportData }) => setReservationReport(reservationReportData))
      .catch(({ response = {} }) => {
        const { status, data = {} } = (response || {});
        const { title } = (data || {});
        if (status === 422) {
          setShowingTimeBlockerError(true)
          dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.TODAY.value, start: moment().startOf('day').add(2, 'hour').format(), end: moment().endOf('day').format(), }));
        }
      })
    // searchPayments(restaurantId, start, end, dateType)
    //   .then(({ data: paymentResultsData }) => setReservationReport(paymentResultsData))
    //   .catch(noop)
  }, [dateType, dateRangeId, start, end]);
  
  const updateReportingView = (value) => () => dispatch(reportingActions.setView(value));
  
  if (!hasPermission) {
    return null;
  }
  
  const renderTopProducts = () => {
    if (isEmpty(topProducts)) {
      return null;
    }
    
    return topProducts.map(({ itemsSold = 0, revenue = 0, menuItem = {} }, index) => {
      const { id = "", name = "", internalName = "", thumbnailUrl = "", description = ""  } = menuItem;
      return (
        <div className={classes.verticalContent} key={id}>
          <div className={menuItemClasses.container}>
            <div className={classes.left}>
              <div className={menuItemClasses.container}>
                <Typography className={clsx(menuItemClasses.rank, { [menuItemClasses.rankWithConnector]: index < 3, [menuItemClasses.rankWithShortConnector]: (topProducts.length < 4 && (index + 1) === topProducts.length) })}>{index + 1}</Typography>
                <img src={thumbnailUrl || "/documents/admin/menu-item-image.png"} className={menuItemClasses.image}
                     alt={internalName || name}/>
                <div className={menuItemClasses.itemInformation}>
                  <div className={menuItemClasses.itemTitle}>
                    {controlStringLength(internalName || name, 20)}
                  </div>
                  <div className={clsx(menuItemClasses.itemTitle, menuItemClasses.itemDescription)}>
                    {controlStringLength(description, 20)}
                  </div>
                </div>
              </div>
            </div>
            <div className={clsx(menuItemClasses.container, menuItemClasses.contentRight)}>
              <div className={classes.column}>
                <div className={menuItemClasses.itemInformation}>
                  <div className={menuItemClasses.itemTitle}>
                    {t('{{count}} sold', { count: itemsSold })}
                  </div>
                  <div className={clsx(menuItemClasses.itemTitle, menuItemClasses.itemDescription)}>
                    {formatNumber(revenue)} €
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    })
  }
  
  const renderMostOrderedProduct = () => {
    if (isEmpty(topProducts)) {
      return null;
    }
  
    const { menuItem = {} } = topProducts[0];
    const { id = "", name = "", internalName = "", thumbnailUrl = "", description = ""  } = menuItem;
    return (
      <div className={classes.verticalContent} key={id}>
        <div className={menuItemClasses.container}>
          <div className={menuItemClasses.container}>
            <img src={thumbnailUrl || "/documents/admin/menu-item-image.png"} className={menuItemClasses.image}
                 alt={internalName || name}/>
            <div className={menuItemClasses.itemInformation}>
              <div className={menuItemClasses.itemTitle}>
                {controlStringLength(internalName || name, 20)}
              </div>
              <div className={clsx(menuItemClasses.itemTitle, menuItemClasses.itemDescription)}>
                {controlStringLength(description, 15)}
              </div>
            </div>
            <div className={menuItemClasses.tag}>
              <div className={menuItemClasses.tagIcon}>
                <BadgeIconGreen16 />
              </div>
              {t('most-ordered')}
            </div>
          </div>
        </div>
      </div>
    )
  }
  
  const renderMostOrderingCustomer = () => {
    if (isEmpty(topCustomer)) {
      return null;
    }
    const { id = "", firstName = "", lastName = "" } = topCustomer;
    return (
      <div className={classes.verticalContent} key={id}>
        <div className={menuItemClasses.container}>
          <div className={menuItemClasses.container}>
            <img src={"/documents/admin/menu-item-image.png"} className={menuItemClasses.image}
                 alt={firstName}/>
            <div className={menuItemClasses.itemInformation}>
              <div className={menuItemClasses.itemTitle}>
                {firstName} {lastName}
              </div>
            </div>
            <div className={menuItemClasses.tag}>
              <div className={menuItemClasses.tagIcon}>
                <BadgeIconGreen16 />
              </div>
              {t('most-ordered')}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={classes.wrapper} data-testid="reporting-overview">
      <div className={classes.header}>
        <Typography className={classes.headerTitle}>{t('overview')}</Typography>
        <div className={classes.spacing} />
        <div className={classes.actions}>
          <DateTimePicker error={showingTimeBlockerError} />
        </div>
      </div>
      <div className={classes.container}>
        <div className={classes.topBar}>
          <div className={classes.containerTitle}>
            {t('revenue')}
          </div>
          <div>
            <ButtonBase disableRipple onClick={updateReportingView(reportingViews.REVENUE.value)}>
              <Typography className={classes.sourceBtnText}>
                {t('see-details')}
              </Typography>
              <div className={classes.sourceBtnIcon}>
                <ArrowLeftIcon />
              </div>
            </ButtonBase>
          </div>
        </div>
        <div className={classes.content}>
          <div className={classes.row}>
            <div className={clsx(classes.column, classes.revenueColumn)}>
              <Typography className={classes.cardTitle}>{formatNumber(finishedRevenue)}€</Typography>
              <Typography className={classes.cardSubtitle}>{t('finished')}</Typography>
            </div>
            <div className={clsx(classes.column, classes.revenueColumn)}>
              <Typography className={classes.cardTitle}>{formatNumber(runningRevenue)}€</Typography>
              <Typography className={classes.cardSubtitle}>{t('running')}</Typography>
            </div>
            <div className={clsx(classes.column, classes.revenueColumn)}>
              <Typography className={classes.cardTitle}>{formatNumber(expectedRevenue)}€</Typography>
              <Typography className={classes.cardSubtitle}>{t('expected')}</Typography>
            </div>
          </div>
        </div>
      </div>
      <div className={classes.columns}>
        <div className={classes.left}>
          <div className={classes.container}>
            <div className={classes.topBar}>
              <div className={classes.containerTitle}>
                {t('top-products')}
              </div>
              <div>
                <ButtonBase disableRipple onClick={updateReportingView(reportingViews.PRODUCTS.value)}>
                  <Typography className={classes.sourceBtnText}>
                    {t('see-details')}
                  </Typography>
                  <div className={classes.sourceBtnIcon}>
                    <ArrowLeftIcon />
                  </div>
                </ButtonBase>
              </div>
            </div>
            {renderTopProducts()}
          </div>
          <div className={classes.container}>
            <div className={classes.topBar}>
              <div className={classes.containerTitle}>
                {t('inventory')}
              </div>
            </div>
            {renderMostOrderedProduct()}
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>0</span> {t('items-expiring-soon')}
                </Typography>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>0</span> {t('items-low-on-stock')}
                </Typography>
              </div>
            </div>
          </div>
        </div>
        <div className={classes.right}>
          <div className={classes.container}>
            <div className={classes.topBar}>
              <div className={classes.containerTitle}>
                {t('team')}
              </div>
            </div>
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <div className={clsx(classes.column, classes.summaryIcon)}>
                  <TeamIcon24 />
                </div>
                <div className={classes.column}>
                  <Typography className={classes.cardTitle}>{totalTeamAccounts}</Typography>
                  <Typography className={classes.cardSubtitle}>{t('employees')}</Typography>
                </div>
              </div>
            </div>
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>{activeTeamAccounts}</span> {t('active')}
                </Typography>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>{plannedTeamShifts}</span> {t('shifts-planned')}
                </Typography>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>{teamShiftHours}</span> {t('shift-hours')}
                </Typography>
              </div>
            </div>
          </div>
          <div className={classes.container}>
            <div className={classes.topBar}>
              <div className={classes.containerTitle}>
                {t('reservations')}
              </div>
            </div>
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <div className={clsx(classes.column, classes.summaryIcon)}>
                  <ReservationsIcon24 />
                </div>
                <div className={classes.column}>
                  <Typography className={classes.cardTitle}>0</Typography>
                  <Typography className={classes.cardSubtitle}>{t('reservations-booked')}</Typography>
                </div>
              </div>
            </div>
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>0</span> {t('wait-list')}
                </Typography>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>2h</span> {t('expected-duration')}
                </Typography>
              </div>
            </div>
          </div>
          <div className={classes.container}>
            <div className={classes.topBar}>
              <div className={classes.containerTitle}>
                {t('administration-layout-customers-nav-label')}
              </div>
            </div>
            {renderMostOrderingCustomer()}
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>{customersInPeriod}</span> {t('new-customers')}
                </Typography>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>{totalNumberOfCustomers}</span> {t('all-customers')}
                </Typography>
              </div>
            </div>
          </div>
        </div>
      </div>
      {showingTimeBlockerError && <ReportBlockingErrorModal open={showingTimeBlockerError} onClose={() => setShowingTimeBlockerError(false)} />}
    </div>
  );
});
