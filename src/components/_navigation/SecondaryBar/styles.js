import { makeStyles } from '@material-ui/core/styles';
import { appBarHeight } from "../../../../styles/theme";
import palette from "../../../../styles/palette";

const useStyles = makeStyles(() => ({
  content: {
    marginTop: 8,
    paddingBottom: 12,
    minHeight: appBarHeight + 1,
    paddingLeft: 12,
    paddingRight: 12,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: `1px solid ${palette.grayscale.divider}`
  },
  left: {
    display: 'flex',
    alignItems: "center",
  },
  right: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexWrap: 'wrap'
  },
}));

export default useStyles;
