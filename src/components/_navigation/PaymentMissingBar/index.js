import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { withTranslation } from '../../../../i18n';
import { accountSelectors, configurationSelectors, restaurantSelectors } from "../../../../redux/selectors";
import { ButtonBase, Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import useStyles from './styles';
import { getBillingPaymentMethods } from "../../../api";
import isEmpty from "../../../utils/isEmpty";
import { operationModes } from "../../../utils/const";
import { views } from "../../../utils/administrationRoutes";

let timer;

const PaymentMissingBar = ({ t, setView }) => {
  const classes = useStyles();
  const [missingPaymentMethod, setMissingPaymentMethod] = useState(false);
  
  const mode = useSelector(restaurantSelectors.getRestaurantMode);
  const accountMode = useSelector(accountSelectors.getAccountModeMemo);
  
  const isExplorationMode = mode === operationModes.EXPLORATION
  const isTrainingMode = accountMode === operationModes.EXPLORATION
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const configuration = useSelector(configurationSelectors.getConfigurationMemo);
  const { showMissingPaymentMethodBanner } = configuration;
  
  const fetchPaymentMethods = () => {
    getBillingPaymentMethods(restaurantId)
      .then(({ data = {} }) => {
        const { items = [] } = data;
        if (isEmpty(items)) {
          setMissingPaymentMethod(true)
        } else {
          setMissingPaymentMethod(false);
          clearInterval(timer);
        }
      })
      .catch(() => {
        setMissingPaymentMethod(false);
      })
  }
  
  useEffect(() => {
    if (showMissingPaymentMethodBanner) {
      timer = setInterval(fetchPaymentMethods, 6000);
      return () => clearInterval(timer)
    }
  }, [showMissingPaymentMethodBanner])
  
  if (isExplorationMode || isTrainingMode) {
    return null;
  }
  
  if (showMissingPaymentMethodBanner && missingPaymentMethod) {
    return (
      <ButtonBase className={classes.bar} onClick={() => setView(views.BILLING)}>
        <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
          {t('please-add-payment-operation-interruption')}
        </Typography>
      </ButtonBase>
    );
  }
  
  return null;
};

export default withTranslation('common')(PaymentMissingBar);
