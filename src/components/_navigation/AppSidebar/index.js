import React from "react";
import clsx from "clsx";
import { useSelector } from "react-redux";
import getConfig from "next/config";
import Drawer from "@material-ui/core/Drawer";
import List from "@material-ui/core/List";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { withTranslation } from "../../../../i18n";
import OrganizationSwitcher from "../../OrganizationSwitcher";
import { views } from "../../../utils/administrationRoutes";
import {
  AppStoreIcon, BoxIcon,
  DineIn,
  DiscountIcon20, Lightning, ListIcon20, MobilePhoneIcon,
  ReceiptsIcon,
  ReportingIcon,
  ReservationsIcon,
  ShoppingBag, TeamIcon
} from "../../../utils/icons";
import { permissionIdentifiers } from "../../../utils/const";
import isEmpty from "../../../utils/isEmpty";
import {
  accountSelectors,
  applicationSelectors,
  configurationSelectors
} from "../../../../redux/selectors";
import SidebarItem from "../SidebarItem";
import ManagementSidebarItem from "../ManagementSidebarItem";
import AccountSidebarItem from "../AccountSidebarItem";
import useStyles from "./styles";
import MyAppsSidebarItem from "../MyAppsSidebarItem";
import HelpCenterSidebarItem from "../HelpCenterSidebarItem";
import AdvancedConfigSideBarItem from "../AdvancedConfigSideBarItem";
import ProductBetaSideBarItem from "../ProductBetaSidebarItem";

const AppSidebar = ({ t, open, onClose, view, setView }) => {
  const classes = useStyles();

  const isTablet = useMediaQuery('(min-width:800px)');

  const showInventory = useSelector(configurationSelectors.getConfigurationShowInventory)
  
  const { publicRuntimeConfig } = getConfig();
  const isProd = publicRuntimeConfig.APPLICATION_ENV === 'production';
  const capabilities = useSelector(configurationSelectors.getCapabilities)
  const embedded = useSelector(applicationSelectors.getIsEmbedded);
  const isStripeDevice = useSelector(applicationSelectors.getIsStripeDevice)
  const account = useSelector(accountSelectors.getAccountMemo);
  const { permissions = [] } = (account || {});
  const { email, emailConfirmed, isManaged } = (account || {});
  const admins =  [
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>"
  ];

  const isAdmin = (admins.includes(email) || (email && email.includes("@significa.co") && !isProd)) && emailConfirmed

  const getViews = (viewsWithPermission) => {
    let filteredViews = viewsWithPermission;
    const permissionKeys = (permissions || []).map(permission => permission.permissionIdentifier);
    const capabilityKeys = isEmpty(capabilities) ? [] : capabilities.map(it => it.id)

    if (capabilityKeys.indexOf(views.RESERVATION.toUpperCase()) === -1) {
      filteredViews = filteredViews.filter(vw => vw.value !== views.RESERVATION)
    }
    if (capabilityKeys.indexOf("TAKEAWAY") === -1) {
      filteredViews = filteredViews.filter(vw => vw.value !== views.PICKUP)
    }
    if (capabilityKeys.indexOf(views.DINE_IN.toUpperCase()) === -1) {
      filteredViews = filteredViews.filter(vw => vw.value !== views.DINE_IN)
    }
    if (capabilityKeys.indexOf(views.EXPRESS.toUpperCase()) === -1) {
      filteredViews = filteredViews.filter(vw => vw.value !== views.EXPRESS)
    }

    filteredViews = filteredViews.filter(view => isEmpty(view.permissions) || view.permissions.some(viewPermission => permissionKeys.includes(viewPermission)))

    return filteredViews;
  }

  const updateView = (view) => () => setView(view);

  const openStripeDeviceSettings = () => {
    try { JSBridge.openSettings() } catch (e) {console.log(e)}
  }


  return (
    <Drawer
      data-testid="main-navigation-drawer"
      variant="temporary"
      open={open}
      onClose={onClose}
      className={classes.drawer}
      style={{ position: "relative", height: "100%", overflow: "hidden" }}
      classes={{
        paper: clsx({
          [classes.drawerOpen]: open,
          [classes.drawerClose]: !open
        })
      }}
    >
      <div className={classes.container} style={{ height: "100%", overflow: "auto", paddingBottom: embedded ? 64 + 24 : 64 }}>
        <OrganizationSwitcher setView={setView} />
        <List className={classes.ul} data-testid="menu-item-list-1">
          {getViews([
            {
              label: t('administration-layout-terminal-nav-label'),
              value: views.DINE_IN,
              icon: <DineIn />,
              permissions: [
                permissionIdentifiers.CAN_ORDER.value
              ],
              hidden: isTablet
            },
            {
              label: t('express'),
              value: views.EXPRESS,
              icon: <Lightning />,
              permissions: [
                permissionIdentifiers.CAN_ORDER.value
              ],
              hidden: isTablet
            },
            {
              label: t('administration-layout-pickup-nav-label'),
              value: views.PICKUP,
              icon: <ShoppingBag />,
              permissions: [
                permissionIdentifiers.CAN_MANAGE_TAKEAWAYS.value
              ],
              hidden: isTablet
            },
            {
              label: t('administration-layout-reservation-nav-label'),
              value: views.RESERVATION,
              icon: <ReservationsIcon />,
              permissions: [
                permissionIdentifiers.CAN_MANAGE_RESERVATIONS.value,
                permissionIdentifiers.CAN_VIEW_RESERVATIONS.value
              ],
              hidden: isTablet
            },
            {
              label: t('administration-layout-receipts-nav-label'),
              value: views.RECEIPTS,
              icon: <ReceiptsIcon />,
              permissions: [
                permissionIdentifiers.CAN_VIEW_HISTORICAL_ORDERS.value,
                permissionIdentifiers.CAN_VIEW_OWN_HISTORICAL_ORDERS.value,
              ]
            },
            {
              label: t('open-invoices'),
              value: views.OPEN_INVOICES_MANAGER,
              icon: <ListIcon20 />,
              permissions: [
                permissionIdentifiers.CAN_VIEW_HISTORICAL_ORDERS.value,
                permissionIdentifiers.CAN_VIEW_OWN_HISTORICAL_ORDERS.value,
              ]
            }
          ]).filter(it => !it.hidden)
            .map(({ label, value = "", icon, rightIcon = null }) =>
              <SidebarItem key={value} value={value} label={label} icon={icon} active={view === value.toLowerCase()} onClick={updateView(value)} />)}
        </List>
        <List className={classes.ul}>
          {getViews(
            [
              {
                label: t('administration-layout-reporting-nav-label'),
                value: views.REPORTING,
                icon: <ReportingIcon />,
                permissions: [
                  permissionIdentifiers.CAN_VIEW_REPORTS.value,
                  permissionIdentifiers.CAN_VIEW_OWN_REPORTS.value
                ]
              }
            ]
          ).filter(it => !it.hidden)
            .map(({ label, value = "", icon, rightIcon = null }) =>
              <SidebarItem key={value} value={value} label={label} icon={icon} active={view === value.toLowerCase()} onClick={updateView(value)} />)
          }
          <ManagementSidebarItem view={view} setView={setView} />
          {isStripeDevice && (
            <SidebarItem key={'device-settings'} value={"device_settings"} label={t('device-settings')} icon={<MobilePhoneIcon />} onClick={openStripeDeviceSettings} />
          )}
          {getViews(
            [
              {
                label: t('administration-layout-marketing-nav-label'),
                value: views.MARKETING,
                icon: <DiscountIcon20 />,
                permissions: [
                  permissionIdentifiers.CAN_VIEW_MARKETING.value
                ]
              },
              {
                label: t('inventory'),
                value: views.INVENTORY,
                icon: <BoxIcon />,
                hidden: !showInventory,
                permissions: [
                  permissionIdentifiers.CAN_UPDATE_GENERAL_SETTINGS.value
                ]
              },
              // {
              //   label: t('my-work'),
              //   value: views.MY_WORK,
              //   icon: <TeamIcon />,
              //   permissions: []
              // }
            ]
          ).filter(it => !it.hidden)
            .map(({ label, value = "", icon, rightIcon = null }) =>
              <SidebarItem key={value} value={value} label={label} icon={icon} active={view === value.toLowerCase()} onClick={updateView(value)} />)
          }
        </List>
        <List className={classes.ul}>
          {!isStripeDevice && (
            <MyAppsSidebarItem close={onClose} />
          )}
          {getViews([
            {
              label: t('administration-layout-marketplace-nav-label'),
              value: views.APPS,
              icon: <AppStoreIcon />,
              permissions: [
                permissionIdentifiers.CAN_MANAGE_APPS.value
              ]
            },
            {
              label: t('administration-layout-allo-shop-nav-label'),
              value: views.SHOP,
              icon: <ShoppingBag />,
              permissions: [
                permissionIdentifiers.CAN_USE_ALLO_SHOP.value
              ]
            }
          ]).filter(it => !it.hidden)
            .map(({ label, value = "", icon, rightIcon = null }) =>
              <SidebarItem key={value} value={value} label={label} icon={icon} active={view === value.toLowerCase()} onClick={updateView(value)} />)

          }
        </List>
        {/*<List className={classes.ul}>*/}
        {/*  <HelpCenterSidebarItem view={view} setView={setView} />*/}
        {/*</List>*/}
        {isAdmin && (
          <List className={classes.ul}>
            <AdvancedConfigSideBarItem view={view} setView={setView} />
          </List>
        )}
        {(isManaged || isAdmin) && (
          <List className={classes.ul}>
            <ProductBetaSideBarItem view={view} setView={setView} />
          </List>
        )}
       {/* <List className={classes.ul}>
          {getViews([{
            label: t('help-and-feedback'),
            value: views.HELP_CENTER,
            icon: <HelpAndSupportIcon />,
            permissions: []
          }])
            .map(({ label, value = "", icon, rightIcon = null }) =>
              //<SidebarItem key={value} value={value} label={label} icon={icon} active={view === value.toLowerCase()} onClick={updateView(value)} />)
          }
          <SupportSidebarItem close={onClose} />
        </List>*/}
        {/*<List style={{ paddingTop: 12 }}>*/}
        {/*  <PromotionCard*/}
        {/*    promotionText={t("promotion-card-text-new-menu-editor")}*/}
        {/*    startButtonLabel={t("open-menu-editor")}*/}
        {/*    onStartBtn={updateView(views.MENU_EDITOR_NEW)}*/}
        {/*  />*/}
        {/*</List>*/}
      </div>
      {!isEmpty(account) && (
        <List className={classes.bottomUl} style={{ position: "absolute", bottom: 0, width: "100%", paddingBottom: embedded ? 24 : 0 }}>
          <AccountSidebarItem view={view} setView={updateView}/>
        </List>
      )}
    </Drawer>
  )
}

export default withTranslation('common')(AppSidebar);
