import React, { useState } from "react";
import { withTranslation } from "../../../../i18n";
import clsx from "clsx";
import ListItem from "@material-ui/core/ListItem";
import ListItemText from "@material-ui/core/ListItemText";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import { views } from "../../../utils/administrationRoutes";
import { ChevronDown20, ChevronUp20, HelpAndSupportIcon } from "../../../utils/icons";
import useStyles from "./styles";
import SidebarItem from "../SidebarItem";
import { adminViewsOptions, helpCenterViewOptions } from "../../../utils/const";
import { useSelector } from "react-redux";
import { accountSelectors } from "../../../../redux/selectors";

const HelpCenterSidebarItem = ({ t, view, setView }) => {
  const classes = useStyles();
  
  const isActiveView = helpCenterViewOptions.map(i => i.key).indexOf(view) > -1
  const [active, setActive] = useState(isActiveView);
  const toggleActive = () => setActive(!active);
  
  return (
    <>
      <ListItem
        data-testid="list-item-help-center"
        button
        disableRipple
        disableTouchRipple
        key={views.HELP_CENTER}
        className={clsx({
          [classes.listItemSelected]: active
        })}
        classes={{
          root: classes.listItemRoot
        }}
        onClick={toggleActive}
      >
        <HelpAndSupportIcon />
        <ListItemText disableTypography classes={{ root: classes.listItemTextRoot }}>
          <Typography style={{ ...typography.body.regular }}>{t('account-help-center-title')}</Typography>
        </ListItemText>
        {active ? <ChevronUp20 /> : <ChevronDown20 />}
      </ListItem>
      {active && helpCenterViewOptions
        .map(({ key, i18nKey }) => (
          <SidebarItem key={key} onClick={() => setView(key)} secondary value={key} label={t(i18nKey)} />
        ))}
    </>
  )
};
export default withTranslation('common')(HelpCenterSidebarItem);
