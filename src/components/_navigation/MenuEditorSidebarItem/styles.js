import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../../styles/palette";

const useStyles = makeStyles(theme => ({
  listItemRoot: {
    paddingTop: 12,
    paddingBottom: 12,
    borderRadius: 12,
    width: "auto",
    "&+&": {
      marginTop: 6
    }
  },
  listItemTextRoot: {
    marginLeft: 0,
    marginTop: 0,
    marginBottom: 0,
    marginRight: 0
  },
  listItemTextSecondary: {
    marginLeft: 5,
  },
  listItemSelected: {
    background: "#E8E7E6 !important"
  },
  listItemSecondary: {
    marginLeft: 24, // 12 normal margin + 10 half of icon size
    // borderLeft: `1px dashed ${palette.grayscale["400"]}`,
    '&:before': {
      position: "absolute",
      width: 1,
      height: 32,
      left: -8,
      borderLeft: `1px dashed ${palette.grayscale["400"]}`,
      content: '""',
    },
  },
}));

export default useStyles;
