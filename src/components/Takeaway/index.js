import React, { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../i18n";
import clsx from "clsx";
import moment from "moment";
import Moment from "react-moment";
import Typography from "@material-ui/core/Typography";
import TableContainer from "@material-ui/core/TableContainer";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import Table from "@material-ui/core/Table";
import { CloseIcon24, InfoIcon, LightningWhite, NoTakeaway120x90 } from "../../utils/icons";
import Button from "@material-ui/core/Button";
import { ButtonBase, TableFooter } from "@material-ui/core";
import SecondaryBar from "../_navigation/SecondaryBar";
import isEmpty from "../../utils/isEmpty";
import typography from "../../../styles/typography";
import Badge from "../_tags/Badge";
import { toI18nKey } from "../../utils/toI18nKey";
import { operationViews, takeawayPartners } from "../../../redux/constants";
import {
  configurationSelectors,
  takeawaySelectors,
  terminalSelectors,
  reportingSelectors, restaurantSelectors
} from "../../../redux/selectors";
import { takeawayActions, terminalActions, reportingActions } from "../../../redux/actions";
import OperationViewDropdown from "../_buttons/OperationViewDropdown";
import DishesDrinksBadge from "../_tags/DishesDrinksBadge";
import Timer from "../_progress/Timer";
import { formatNumber } from "../../utils/formatNumber";
import EmptyScreen from "../_placeholder/EmptyScreen";
import RevenueBadge from "../_tags/RevenueBadge";
import PaymentStatusBadge from "../_tags/PaymentStatusBadge";
import TakeawayTerminalModal from "../_popup/TakeawayTerminalModal";
import orderStatus from "../../utils/orderStatus";
import { dateTimePickerRanges, defaultLogoUrl, noop, onboardingTutorialSteps } from "../../utils/const";
import { zeroPadNumber } from "../../utils/zeroPadNumber";
import TakeawayReceipt from "../TakeawayReceipt";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import useStyles, { useBoardStyles, useTabsStyles } from "./styles";
import ButtonTooltip from "../_notification/ButtonTooltip";
import OrderStatusTakeawayBadge from "../_tags/OrderStatusTakeawayBadge";
import CurrencyBadge from "../_tags/CurrencyBadge";
import TakeAwaySearchModal from "../_popup/TakeAwaySearchModal";
import DatePickerWithDailyReport from "../DatePickerWithDailyReport";
import { useAddQueryParam } from "../../utils/useAddQueryParameter";
import palette from "../../../styles/palette";
import shadows from "../../../styles/shadows";
import Modal from "../_popup/Modal";
import PickupTimeBadge from "../_tags/PickupTimeBadge";
import TakeawayMapView from "../TakeawayMapView";
import Avatar from "@material-ui/core/Avatar";

let timer;

const Takeaway = ({ t }) => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const boardClasses = useBoardStyles();
  const tabsClasses = useTabsStyles();
  const isMobile = useMediaQuery("(max-width:960px)");

  const hasFlashTakeaway = useSelector(configurationSelectors.getConfigurationHasFlashTakeaway)
  const frontEndSettings = useSelector(configurationSelectors.getFrontEndSettings)
  const { showMapInTakeaway } = (frontEndSettings || {})
  const creatingTakeaway = useSelector(terminalSelectors.getCreatingTakeaway);

  const restaurantBranchIds = useSelector(restaurantSelectors.getRestaurantsBranchRestaurantIds)
  const isParent = !isEmpty(restaurantBranchIds);

  const view = useSelector(takeawaySelectors.getView);
  const updateView = (updatedView) => dispatch(takeawayActions.setView(updatedView));
  useAddQueryParam(view, "sv")

  const total = useSelector(takeawaySelectors.getTotal);
  const empty = !total;
  const { items = [] } = useSelector(takeawaySelectors.getList);
  const { lanes = [], lanesActiveGroup = [] } = useSelector(takeawaySelectors.getBoard);

  const { dateTimePicker = {} } = useSelector(reportingSelectors.getDateTimePicker);
  const { dateType, dateRange = {} } = dateTimePicker;
  const { id: dateRangeId } = dateRange;
  const orderStatusFilter = view === operationViews.MAP.key
    ? Object.values(orderStatus)
      .filter(it => ![orderStatus.CLOSED, orderStatus.CANCELLED].includes(it))
    : undefined;

  const [selectedDateRange, handleDateRangeChange] = React.useState([]);
  const [orderType, setOrderType] = useState(null);
  const [ordering, setOrdering] = useState(false);
  const [paying, setPaying] = useState(false);
  const [orderId, setOrderId] = useState(null);
  const startCreating = () => setOrdering(true);
  const startOrdering = (id) => {
    setOrdering(true);
    if (id) {
      setOrderId(id);
    }
  };
  const startPayment = (id) => {
    setPaying(true);
    setOrderId(id);
  };
  const stopOrdering = () => {
    setOrdering(false);
    setPaying(false);
    setOrderId(null);
    dispatch(terminalActions.setCreatingTakeaway(false))
    dispatch(takeawayActions.get(view, selectedDateRange, isMobile, orderStatusFilter));
  };
  const [updating, setUpdating] = useState(false);
  const [updatingStatus, setUpdatingStatus] = useState(null);
  const startUpdating = (id, orderType, status, paymentStatus, isWaiter) => {
    setOrderId(id);
    setOrderType(orderType);
    setUpdating(true);
    setUpdatingStatus(status);
  };
  const stopUpdating = () => {
    setUpdating(false);
    setOrderId(null);
    setOrderType(null);
    dispatch(takeawayActions.get(view, selectedDateRange, isMobile, orderStatusFilter));
  };

  const isListView = view === operationViews.LIST.key;

  useEffect(() => {
    dispatch(takeawayActions.get(view, selectedDateRange, isMobile, orderStatusFilter));
  }, [view, isMobile]);

  useEffect(() => {
    const now = moment();
    const isCurrentPeriod = moment(selectedDateRange[1]).add(0.5, "hour").isSameOrAfter(now);
    const isListViewPollingAllowed = isCurrentPeriod && !isEmpty(selectedDateRange)

    if (!ordering && (!isListView || isListViewPollingAllowed)) {
      timer = setInterval(() => dispatch(takeawayActions.get(
        view,
        isListViewPollingAllowed ? selectedDateRange : undefined,
        isMobile,
        orderStatusFilter
      )), 3000);
    }

    return function cleanup() {
      clearInterval(timer);
    };
  }, [view, ordering, isListView, dateRangeId, selectedDateRange, isMobile]);

  useEffect(() => {
    if (isListView) {
      dispatch(reportingActions.getDateTimePicker());

      if (!selectedDateRange.length) {
        dispatch(
          reportingActions.setDateRange({
            id: dateTimePickerRanges.TODAY.value,
            start: moment().startOf("day").add(2, "hour").format(),
            end: moment().endOf("day").format(),
          })
        );
      }
    }
  }, [view, selectedDateRange]);

  useEffect(() => {
    if (!isEmpty(selectedDateRange)) {
      dispatch(takeawayActions.get(view, selectedDateRange, isMobile, orderStatusFilter));
    }
  }, [dateType, dateRangeId, selectedDateRange, isMobile]);

  useEffect(() => {
    if (creatingTakeaway) {
      startCreating()
    }
  }, [creatingTakeaway]);

  const [tab, setTab] = useState(0);
  const [notificationOrder, setNotificationOrder] = useState(null);

  const setBoardActiveGroup = (laneIndex, groupId) => () => {
    dispatch(takeawayActions.setBoardActiveGroup(laneIndex, groupId));
  };

  const [anchor, setAnchor] = useState(null);
  const openMenu = (e) => {
    e.stopPropagation()
    setAnchor(e.currentTarget)
  };
  const closeMenu = () => setAnchor(null);

  const renderBoardLane = (groups, lanesActiveGroup, index) => (
    <div className={boardClasses.lane} key={`lane-${index}`}>
      {groups.map(({ id: groupId, numberOfOrders, orders = [] }) => {
        const groupI18nKey = toI18nKey(groupId);
        const expanded = !isEmpty(lanesActiveGroup) && lanesActiveGroup[index] ? lanesActiveGroup[index].groupId === groupId : false;

        const isCancelled = groupI18nKey === 'cancelled'

        return (
          <div className={boardClasses.group} style={expanded ? { flex: 1 } : null} key={groupId}>
            <div className={boardClasses.groupHeader} onClick={setBoardActiveGroup(index, groupId)}>
              <div style={{ display: "flex", alignItems: "center", }}>
                <Typography style={{ ...typography.body.medium }}>{t(groupI18nKey)}</Typography>
                <span style={{ marginLeft: 4, display: "inline-flex" }}>
                  <Badge color={isCancelled && !isMobile ? "NEGATIVE" : undefined} quantity={numberOfOrders} />
                </span>
              </div>
              {isCancelled &&
                <div onClick={openMenu} >
                  <InfoIcon color={palette.grayscale["500"]} />
                </div>}
            </div>
            {!isEmpty(orders) && expanded && (
              <div className={boardClasses.cards}>
                {orders.map(
                  ({
                     id,
                     number,
                     pickupTime,
                     takeawayDate,
                     numberOfDishes,
                     isNew,
                     numberOfDrinks,
                     customer = {},
                     total,
                     status,
                     type,
                     paymentStatus,
                     partnerId = takeawayPartners.ALLO.key,
                     partnerOrderInfo = {},
                     estimatedPreparationTime,
                     duration = 0,
                     remainingDuration = 0,
                     isWaiter,
                     restaurant = {}
                   }) => {
                    const { name, logoUrl } = restaurant;
                    const hasTimer = [orderStatus.OPEN, orderStatus.PREPARING].indexOf(status) > -1 && estimatedPreparationTime;
                    const isPreparing = status === orderStatus.PREPARING;

                    const partnerOrderNumber = !isEmpty(partnerOrderInfo) && partnerOrderInfo.orderNumber;
                    const partnerName = partnerId ? takeawayPartners[partnerId || takeawayPartners.ALLO.key].i18nKey : "";
                    return (
                      <div key={id} className={boardClasses.updatedCard} style={{  background: palette.grayscale["100"], ...shadows.base, borderRadius: 12 }} onClick={() => startUpdating(id, type, status, paymentStatus, isWaiter)}>
                        <div style={{ display: "flex", alignItems: "center", flexDirection: "row", justifyContent: "space-between", padding: 12, }}>
                          <div>
                            <div style={{ display: "flex", alignItems: "flex-start" }}>
                              <div style={{ display: "inline-flex", marginRight: 4 }}>
                                {takeawayPartners[partnerId || takeawayPartners.ALLO.key].icon}
                              </div>
                              <Typography style={{ ...typography.body.medium }}>
                                #{number}
                                {partnerOrderNumber && partnerName ? ` (${t(partnerName)} #${partnerOrderNumber})` : ""}{" "}
                                {customer && customer.fullName}
                              </Typography>
                              {!isWaiter && isNew && (
                                <div style={{ marginLeft: 4 }}>
                                  <Badge label={t("newly-added")} color="ACTION" />
                                </div>
                              )}
                            </div>
                            <div style={{ display: "flex", alignItems: "center", marginTop: 6, marginLeft: -1 }}>
                              <DishesDrinksBadge dishes={numberOfDishes} drinks={numberOfDrinks} />
                              <div style={{ marginLeft: 4 }}>
                                <RevenueBadge revenue={total} />
                              </div>
                              <div style={{ marginLeft: 4 }}>
                                <PaymentStatusBadge status={paymentStatus} />
                              </div>
                            </div>
                            <div style={{ display: "flex", alignItems: "center", flexWrap: "wrap" }}>
                              <div style={{ marginTop: 6 }}>
                                <PickupTimeBadge orderType={type} date={takeawayDate} time={pickupTime} partnerId={partnerId} status={status} />
                              </div>
                            </div>
                          </div>
                          {!!hasTimer && (
                            <div>
                              <Timer
                                minutes={estimatedPreparationTime}
                                remaining={isPreparing ? remainingDuration : estimatedPreparationTime}
                                duration={isPreparing ? duration : estimatedPreparationTime}
                              />
                            </div>
                          )}
                        </div>
                        {isParent && (
                          <div style={{ display: "flex", alignItems: "center", borderTop: `1px solid ${palette.grayscale.border}`, padding: 12, marginTop: 12  }}>
                            <div style={{ display: "flex", alignItems: "center", gap: 4 }}>
                              <Avatar
                                alt={name}
                                src={logoUrl || defaultLogoUrl}
                                classes={{ root: classes.avatar }}
                                defaultValue="allO"
                                style={{ width: "24px", height: "24px", borderRadius: 8 }}
                              >
							                {name}
						                  </Avatar>

                              <Typography style={{ ...typography.body.medium }}>{name}</Typography>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  }
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  const renderBoard = () => {
    if (!total || isEmpty(lanes)) {
      return null;
    }

    return (
      <div className={boardClasses.container}>
        {isMobile && (
          <div className={tabsClasses.categories}>
            {lanes.map(({ groups = [] }, index) => {
              if (isEmpty(groups)) {
                return null;
              }

              const laneTotal = groups.map((it) => it.numberOfOrders ?? 0).reduce((partialSum, a) => partialSum + a, 0);
              const firstGroup = groups[0];
              const laneI18nKey = toI18nKey(firstGroup.id || "");

              const isCancelled = laneI18nKey === "cancelled";

              return (
                <Button
                  key={`lane-${index}`}
                  disableRipple
                  disableTouchRipple
                  className={clsx(tabsClasses.category, { [tabsClasses.categorySelected]: tab === index })}
                  onClick={() => setTab(index)}
                >
                  <span className={clsx(tabsClasses.categoryContent, { [tabsClasses.categoryContentSelected]: tab === index })}>
                    {t(laneI18nKey)}
                  </span>
                  <span className={clsx(!isCancelled ? tabsClasses.count : tabsClasses.countNegative, { [tabsClasses.countSelected]: tab === index })}>{laneTotal}</span>
                </Button>
              );
            })}
          </div>
        )}
        <div className={boardClasses.content}>
          {!isMobile && lanes.map(({ groups = [] }, index) => renderBoardLane(groups, lanesActiveGroup, index))}
          {isMobile && !isEmpty(lanes[tab]) && !isEmpty(lanes[tab].groups) && renderBoardLane(lanes[tab].groups, (lanesActiveGroup || []).concat([{
            groupId: "CANCELLED"
          }]), tab)}
        </div>
      </div>
    );
  };

  const renderList = () => {
    const { [operationViews.LIST.key]: list = {} } = useSelector(takeawaySelectors.getList);

    if (isEmpty(list) || isEmpty(list.items)) {
      return null;
    }

    const { count, totalAmount } = list;

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          height: "100%",
          paddingTop: 16,
          paddingLeft: 16,
          paddingRight: 16,
          paddingBottom: 12,
        }}
      >
        <TableContainer>
          <Table stickyHeader aria-label="takeaway-table">
            <TableHead>
              <TableRow>
                <TableCell align="left">{t("order-history-table-header-order-number")}</TableCell>
                <TableCell align="left">{t("order-history-table-header-order-type")}</TableCell>
                <TableCell align="left">{t("order-history-table-header-table-number")}</TableCell>
                <TableCell align="left">{t("order-history-closed-by")}</TableCell>
                <TableCell align="left">{t("receipt-payment-method-label")}</TableCell>
                <TableCell align="right">{t("amount")}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {list.items
                .concat(list.items)
                .concat(list.items)
                .map(({ id, number, type, total, tableCode, payments }) => {
                  return (
                    <TableRow key={id} onClick={noop} className={classes.clickable}>
                      <TableCell>{number}</TableCell>
                      <TableCell>{t(`menu-editor-form-order-type-field-option-${type.toLowerCase().replace("_", "-")}`)}</TableCell>
                      <TableCell>{tableCode}</TableCell>
                      <TableCell align="right">{formatNumber(total)} €</TableCell>
                    </TableRow>
                  );
                })}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell>{t("table-count", { count })}</TableCell>
                <TableCell>{t("table-count", { count })}</TableCell>
                <TableCell>{t("table-count", { count })}</TableCell>
                <TableCell>{t("table-count", { count })}</TableCell>
                <TableCell>{t("table-count", { count })}</TableCell>
                <TableCell align="right">{t("table-total", { total: formatNumber(totalAmount) })}</TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      </div>
    );
  };

  const renderTable = (orders = [], isPending = false) => {
    const count = orders.length;

    return (
      <TableContainer>
        <Table stickyHeader aria-label="takeaway table">
          <TableHead>
            <TableRow>
              <TableCell align="left" />
              <TableCell align="left">{t("order-history-table-header-order-started")}</TableCell>
              {isParent && (
                <TableCell align="left">{t("restaurant-name")}</TableCell>
              )}
              <TableCell align="left">{t("order-history-table-header-order-type")}</TableCell>
              <TableCell align="left">{t("order-update-current-status-label")}</TableCell>
              <TableCell align="left">{t("order-history-table-header-order-number")}</TableCell>
              {/*<TableCell align="left">{t('order-history-table-header-order-id')}</TableCell>*/}
              <TableCell align="left">{t("order-history-table-header-order-customer-name")}</TableCell>
              {/*<TableCell align="left">{t('order-history-table-header-order-customer-phone')}</TableCell>*/}
              <TableCell align="left">{t("order-history-table-header-order-takeawayDate")}</TableCell>
              <TableCell align="left">{t("order-history-table-header-order-pickupTime")}</TableCell>
              {/*<TableCell>{t('order-history-table-header-order-notes')}</TableCell>*/}
              {/*<TableCell align="center">{t('order-history-table-header-order-total')}</TableCell>*/}
              {/*<TableCell align="center">{t('order-history-table-header-table-tax')}</TableCell>*/}
              <TableCell align="right">{t("order-history-table-header-table-total")}</TableCell>
              <TableCell align="left">{t("payment-status")}</TableCell>
              {/*<TableCell align="center">{t('order-history-table-header-current-status-action-label')}</TableCell>*/}
              {/*<TableCell align="center">{t('send-update-btn-label')}</TableCell>*/}
              {/*{isPending && <TableCell align="center" />}*/}
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => {
              const {
                id,
                type,
                number,
                identifier,
                customer,
                customerId,
                tableId,
                items = [],
                creationTime,
                modificationTime,
                status,
                paymentOption,
                total,
                isWaiter,
                payments = [],
                pickupTime,
                takeawayDate,
                notes,
                notificationSent,
                partnerId = takeawayPartners.ALLO.key,
                paymentStatus,
                restaurant = {}
              } = order;

              const { name } = restaurant;

              const formattedStartTime = <Moment format={"DD.MM.YYYY h:mm a"}>{creationTime}</Moment>;
              const customerFullname = customer ? `${customer.firstName} ${customer.lastName}` : "";
              const customerPhone = customer ? customer.phone : "";
              const formattedPickupTime = pickupTime
                ? `${moment.utc(pickupTime, "HH:mm:ss").format("HH:mm")}`
                : t("order-item-pickupTime-asap-label");
              const formattedTakeawayDate = takeawayDate ? <Moment format={"DD.MM.yyyy"}>{takeawayDate}</Moment> : "-";

              let formattedStatus = t(`status-${(status || "").toLowerCase().replace("_", "-")}`);
              if (status === "OPEN") {
                const isToday = !takeawayDate || moment(takeawayDate).isSameOrBefore(new Date(), "day");
                if (isToday) {
                  formattedStatus = t("incoming");
                } else {
                  formattedStatus = t("scheduled");
                }
              }
              if (status === "CLOSED") {
                formattedStatus = t("completed");
              }

              return (
                <TableRow key={id} className={classes.dataRowRoot} onClick={() => startUpdating(id, type, status, paymentStatus, isWaiter)} hover>
                  {/*<TableCell align="center">{identifier}</TableCell>*/}
                  <TableCell align="left">{takeawayPartners[partnerId || takeawayPartners.ALLO.key].icon}</TableCell>
                  <TableCell align="left">{formattedStartTime}</TableCell>
                  {isParent && (
                    <TableCell align="left">{name}</TableCell>
                  )}
                  <TableCell align="left">{type}</TableCell>
                  <TableCell align="left">
                    <OrderStatusTakeawayBadge status={status} formattedStatus={formattedStatus} />
                  </TableCell>
                  <TableCell align="left">{number ? zeroPadNumber(number, 3) : "-"}</TableCell>
                  {/*<TableCell align="left">{id.substr(id.length - 6)}</TableCell>*/}
                  <TableCell align="left">{customerFullname}</TableCell>
                  {/*<TableCell align="left">{customerPhone}</TableCell>*/}
                  <TableCell align="left">{formattedTakeawayDate}</TableCell>
                  <TableCell align="left">{formattedPickupTime}</TableCell>
                  {/*<TableCell style={{ whiteSpace: "normal" }}>{notes}</TableCell>*/}
                  {/*<TableCell align="center">{orderTotalFormatted}</TableCell>*/}
                  {/*<TableCell align="center">{totalTaxFormatted}</TableCell>*/}
                  <TableCell align="right">
                    <CurrencyBadge amount={total} />
                  </TableCell>
                  <TableCell>
                    <PaymentStatusBadge status={paymentStatus} />
                  </TableCell>
                  {/*<TableCell align="center">*/}
                  {/*  <IconButton classes={{ root: classes.iconButtonRoot }} onClick={() => startUpdating(id, type, status, paymentStatus, isWaiter)}>*/}
                  {/*    <EyeIcon />*/}
                  {/*  </IconButton>*/}
                  {/*</TableCell>*/}
                  {/*{!isPending && (*/}
                  {/*  <TableCell align="center">*/}
                  {/*    <IconButton classes={{ root: classes.iconButtonRoot }} onClick={(e) => {*/}
                  {/*      preventEventPropagation(e)*/}
                  {/*      setNotificationOrder(order)*/}
                  {/*    }} disabled={isWaiter}>*/}
                  {/*      {notificationSent ? <DoneAll /> : <EmailRounded />}*/}
                  {/*    </IconButton>*/}
                  {/*  </TableCell>*/}
                  {/*)}*/}
                  {/*{isPending && (*/}
                  {/*  <TableCell align="center">*/}
                  {/*    <Confirm*/}
                  {/*      title={t('delete-pickup-order-dialog-title')}*/}
                  {/*      body={(*/}
                  {/*        <Typography color="textSecondary" variant="body2">*/}
                  {/*          {t('delete-pickup-order-dialog-description')}*/}
                  {/*        </Typography>*/}
                  {/*      )}*/}
                  {/*    >*/}
                  {/*      {confirm => (*/}
                  {/*        <IconButton classes={{ root: classes.iconButtonRoot }} onClick={confirm(() => onDelete(id))}>*/}
                  {/*          <DeleteOutlineIcon />*/}
                  {/*        </IconButton>*/}
                  {/*      )}*/}
                  {/*    </Confirm>*/}
                  {/*  </TableCell>*/}
                  {/*)}*/}
                </TableRow>
              );
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell />
              <TableCell>{t("table-count", { count: count })}</TableCell>
              <TableCell>{t("table-count", { count: count })}</TableCell>
              <TableCell>{t("table-count", { count: count })}</TableCell>
              <TableCell />
              <TableCell />
              <TableCell>{t("table-count", { count: count })}</TableCell>
              <TableCell>{t("table-count", { count: count })}</TableCell>
              <TableCell />
              <TableCell />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    );
  };

  const EmptyComponent = () => {
    return <div className={classes.content}>
      <EmptyScreen
        icon={<NoTakeaway120x90 />}
        titleI18nKey="no-orders"
        descriptionI18nKey="click-the-button-below-to-create-an-order"
        action={{ i18nKey: hasFlashTakeaway ? "create-quick-takeaway" : "create-takeaway", onClick: startCreating }}
        tutorial={{
          url: "https://www.youtube.com/watch?v=NcRifDitRnX",
        }}
      />
    </div>
  }

  const renderLegacy = () => {
    return (
      <>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            height: "100%",
            paddingTop: isMobile ? 8 : 16,
            paddingLeft: 16,
            paddingRight: 16,
            paddingBottom: 12,
          }}
        >
          {isMobile && <div style={{ display: "flex", alignItems: "center", marginLeft: 8 }}>
            {view === operationViews.LIST.key && (
              <div style={{ display: "flex", justifyContent: "flex-end", flex: 1, marginBottom: 8 }}>
                <DatePickerWithDailyReport
                  range={selectedDateRange}
                  setRange={handleDateRangeChange}
                  allowedRanges={["TODAY", "YESTERDAY", "CUSTOM"]}
                />
              </div>
            )}
          </div>}
          {!isEmpty(items)
            ? renderTable(items)
            : <EmptyComponent />
          }
        </div>
      </>
    );
  };

  const renderMap = () => {
    if (isMobile) {
      return null
    }

    return <TakeawayMapView onClickOrder={startUpdating} />
  }

  const getContent = () => {
    if (empty && view === operationViews.BOARD.key) {
      return (
        <EmptyComponent />
      );
    }

    return (
      <div className={classes.content}>
        {view === operationViews.BOARD.key && renderBoard()}
        {view === operationViews.LIST.key && renderLegacy()}
        {view === operationViews.MAP.key && renderMap()}
        <Modal
          open={Boolean(anchor)}
          onClose={closeMenu}
          PaperProps={{ style: { background: "transparent",  ...shadows.large, zIndex: 9999, maxWidth: "95%" } }}
        >
          <ButtonBase style={{ padding: 8, position: "absolute", right: 0 }} onClick={closeMenu}>
            <CloseIcon24 />
          </ButtonBase>
          <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderRadius: 20, padding: 20 }}>
            <strong>{t("cancelled-orders")}</strong>
            <br/>
            <br/>
            <div dangerouslySetInnerHTML={{__html: t("cancelled-orders-description") }} />
          </div>
        </Modal>
      </div>
    );
  };

  const enabledViews = [operationViews.BOARD.key, operationViews.LIST.key].concat(showMapInTakeaway && !isMobile ? [operationViews.MAP.key] : [])

  const getSecondaryBarActions = () => {
    return (
      <>
        <div style={{ display: "flex", alignItems: "center", marginBottom: isMobile ? 5 : 0}}>
          <OperationViewDropdown
            style={{ marginRight: 8 }}
            view={view}
            setView={updateView}
            views={enabledViews}
          />

          <TakeAwaySearchModal />
          {view === operationViews.LIST.key && !isMobile && (
            <div style={{ display: "flex", justifyContent: "flex-end", flex: 1, marginRight: 8 }}>
              <DatePickerWithDailyReport
                range={selectedDateRange}
                setRange={handleDateRangeChange}
                allowedRanges={["TODAY", "YESTERDAY", "CUSTOM"]}
              />
            </div>
          )}
          <ButtonTooltip stepId={onboardingTutorialSteps.TAKEAWAY_ORDER_CREATION.value}>
            <Button
              className={classes.createBtn}
              onClick={startCreating}
              disableRipple
              style={{ padding: hasFlashTakeaway ? "6px 16px 6px 7px" : "6px 16px" }}
            >
              {hasFlashTakeaway && (
                <span style={{ marginRight: 4, display: "inline-flex" }}>
                  <LightningWhite />
                </span>
              )}
              {t("new")}
            </Button>
          </ButtonTooltip>
        </div>
      </>
    )
  };

  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        <SecondaryBar title={t("administration-layout-pickup-nav-label")} right={getSecondaryBarActions()} />
        {getContent()}
      </div>
      {(ordering || paying) && (
        <TakeawayTerminalModal
          open={ordering || paying}
          onClose={stopOrdering}
          orderType={orderType}
          orderId={orderId}
          checkout={!!paying}
          hasFlashTakeaway={hasFlashTakeaway}
        />
      )}
      {orderId && updating && updatingStatus && (
        <TakeawayReceipt orderId={orderId} isOpen={!!orderId} close={stopUpdating} startPayment={startPayment} startEditing={startOrdering} />
      )}
    </div>
  );
};

Takeaway.whyDidYouUpdate = true;

export default withTranslation("common")(memo(Takeaway));
