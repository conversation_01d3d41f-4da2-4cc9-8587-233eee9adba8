import React, { useEffect } from "react";
import clsx from "clsx";
import { useSelector } from "react-redux";
import { Button } from "@material-ui/core";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { withTranslation } from "../../../i18n";
import { configurationSelectors, restaurantSelectors } from "../../../redux/selectors";
import { operationModes, quickDialNavigations } from "../../utils/const";
import isEmpty from "../../utils/isEmpty";
import Typography from "@material-ui/core/Typography";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import Badge from "../_tags/Badge";
import useStyles from "./styles";
import { getView, views } from "../../utils/administrationRoutes";
import { useRouter } from "next/router";

const QuickDialNavigation = ({ t }) => {
	const classes = useStyles();
	const isTablet = useMediaQuery('(min-width:800px)', { noSsr: true });
	const router = useRouter();
	
	const { v } = router.query;
	const currentView = getView(v)
	
	const capabilities = useSelector(configurationSelectors.getCapabilities);
	const mode = useSelector(restaurantSelectors.getRestaurantMode);
	
	const setView = (updatedView) => {
		const { asPath } = router;
		const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "");
		router
			.push(`${resolvedAsPath}?v=${updatedView}`, undefined, { shallow: true })
			.catch(() => {});
	}
	
	useEffect(() => {
		if (currentView === null && !isEmpty(capabilities) && !isEmpty(mode)) {
			const isExplorationMode = mode === operationModes.EXPLORATION

			if (isExplorationMode) {
				setView(views.EXPLORATION_OVERVIEW)
			}

			const initialCapability = capabilities[0];
			if (initialCapability && !isExplorationMode) {
				const capability = quickDialNavigations[initialCapability.id];
				if (!isEmpty(capability)) {
					setView(capability.view)
					// re-render dine in due to issue with table rendering
					if (capability.view === quickDialNavigations.DINE_IN.view) {
						setTimeout(() => setView(capability.view), 300)
					}
				}
			}
		}
	}, [capabilities, mode])
	
	if (!isTablet) {
		return null;
	}
	
	if (isEmpty(capabilities)) {
		return null
	}
	
	return (
		<div>
			<div>
				<div className={classes.content}>
					{capabilities.map(({ id, ordersCount }) => {
						const { view, i18nKey, icon } = quickDialNavigations[id] ?? {};
						return (
							<Button
								data-testid={`quick-dial-navigation-${view}`}
								disableRipple
								disableElevation
								disableFocusRipple
								disableTouchRipple
								key={view}
								className={clsx(classes.button, { [classes.selected]: currentView === view })}
								onClick={() => setView(view)}
							>
								{icon}
								<Typography style={{ ...typography.body.medium, marginLeft: 2, color: currentView === view ? palette.grayscale.black : palette.grayscale["600"], marginRight: !!ordersCount ? 6 : 0 }}>
									{t(i18nKey)}
								</Typography>
								{!!ordersCount && (
									<Badge quantity={ordersCount} color="ACTION" />
								)}
							</Button>
						)
					})}
				</div>
			</div>
		</div>
	)
}

export default withTranslation('common')(QuickDialNavigation);
