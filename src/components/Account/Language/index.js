import React, { useEffect, useState } from "react";
import { i18n, withTranslation } from "../../../../i18n";
import useStyles from "./styles";
import Field from "../../form/Field";
import {useSelector} from "react-redux";
import {accountSelectors} from "../../../../redux/selectors";
import { getSystemLanguages, updateAccountPreferences } from "../../../api";
import { accountViews, noop } from "../../../utils/const";
import SecondaryBar from "../../_navigation/SecondaryBar";
import { ButtonBase, NativeSelect, Typography, useMediaQuery } from "@material-ui/core";
import typography from "../../../../styles/typography";
import Loader from "../../_progress/Loader";
import palette from "../../../../styles/palette";

const data = {
  language: "en"
}

export const Language = withTranslation('common')(({ t }) => {
  const { language } = i18n;
  const classes = useStyles();
  const isMobile = useMediaQuery('(max-width:900px)');

  const { preferences = {} } = useSelector(accountSelectors.getPreferences)

  const [form, setForm] = useState({ ...data, ...preferences, language })
  const [touched, setTouched] = useState(false)
  const [saving, setSaving] = useState(false)

  const [languageOptions, setLanguageOptions] = useState([])

  const onChange = (e) => {
    setTouched(true);
    setForm({ ...form, [e.target.name]: e.target.value })
  }

  const isValid = form.language && touched

  useEffect(() => {
    fetchSystemLanguages()
  },[])

  const fetchSystemLanguages = () => {
    getSystemLanguages()
      .then(({ data }) => {
        const { items = [] } = (data || {})
        setLanguageOptions(items)
      })
      .catch(() => {})
  };

  const onSubmit = () => {
    setSaving(true)
    updateAccountPreferences(form)
      .then(() => {
        setSaving(false)
        setTouched(false)
        i18n.changeLanguage(form.language, () => {
        location.reload();
        return false;
      });
    }).catch(noop)
      .finally(() => setSaving(false));
  }

  const getContent = () => (
    <div className={classes.content} data-testid="account-language">
      <div style={{ display: "flex", flexDirection: isMobile ? "column" : "row", alignItems: "top" }}>
        <div style={{ marginRight: isMobile ? 0 : 42, marginBottom: isMobile ? 32 : 0, width: isMobile ? " 100%" : 240 }}>
          <Typography style={{  ...typography.body.medium }}>{t('select-content-language')}</Typography>
          <Typography style={{  ...typography.body.regular, marginTop: 4 }}>{t('set-the-language-for-the-terminal-and-content')}</Typography>
        </div>
        <div style={{ flex: 1 }}>
          <div>
            <NativeSelect
              labelId="language-label"
              id="language-select"
              name="language"
              value={form.language}
              onChange={onChange}
              style={{ maxWidth: 520 }}
              InputLabelProps={{
                shrink: true,
              }}
              inputProps={{
                name: 'language',
                id: 'language-selector'
              }}
              input={<Field label={t('language')} />}
            >
              {languageOptions.map(language => {
                return(
                  <option value={language.code}>{language.label}</option>
                )
              })}
            </NativeSelect>
          </div>
        </div>
      </div>
    </div>
  )

  const getSecondaryBarActions = () => (
    <div style={{ display: "flex", alignItems: "center" }}>
      <ButtonBase
        style={{
          background: !isValid ? palette.grayscale["350"] : "#FF7C5C",
          fontStyle: "normal",
          fontWeight: "500",
          fontSize: "14px",
          lineHeight: "20px",
          letterSpacing: "-0.0014em",
          color: "#F9F9F9",
          borderRadius: 10,
          textTransform: "capitalize",
          padding: '6px 16px',
          minWidth: 64
        }}
        onClick={onSubmit}
        disabled={!isValid}
        disableRipple
        disableTouchRipple
      >
        {saving ? <Loader /> : (
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"], textTransform: "capitalize" }}>
            {t('common-save')}
          </Typography>
        )}
      </ButtonBase>
    </div>
  )

  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        <SecondaryBar title={t(accountViews.LANGUAGE.i18nKey)} right={getSecondaryBarActions()} contentStyle={{
          paddingLeft: 0,
          paddingRight: 0,
          marginTop: 0
        }}/>
        {getContent()}
      </div>
    </div>
  );
});
