import React, {useState} from "react";
import {withTranslation} from '../../../i18n';
import clsx from "clsx";
import { ClickAwayListener, InputBase } from "@material-ui/core";
import Button from "@material-ui/core/Button";
import Keyboard from "../Keyboard";
import {MagnifierIcon} from "../../utils/icons";
import shadows from "../../../styles/shadows";
import useStyles from "./styles";
import Modal from "../_popup/Modal";

const SearchBar = withTranslation('common')(({ t, query, setQuery, keyboardPosition = "right" }) => {
  const classes = useStyles();
  const [focused, setFocused] = useState(Boolean(query));
  
  const inputRef = React.useRef();
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [showNativeKeyboard, setShowNativeKeyboard] = useState(false);
  
  const toggleFocused = () => {
    if (focused && !query) {
      setFocused(false);
      setShowKeyboard(false);
      setShowNativeKeyboard(false);
    } else {
      setFocused(true);
      setShowKeyboard(true);
      setShowNativeKeyboard(false);
    }
  }
  
  const onDelete = () => {
    if (query) {
      if (inputRef) {
        inputRef.current.value = (inputRef.current.value ?? "").slice(0, -1);
      }
      setQuery(query.slice(0, -1))
    }
  }
  
  const onClick = (char) => {
    if (inputRef) {
      inputRef.current.value = inputRef.current.value + char;
    }
    setQuery(query + char)
  }
  
  const onShowKeyboard = () => {
    if (inputRef) {
      inputRef.current.focus();
      setShowKeyboard(false);
      setShowNativeKeyboard(true);
    }
  }
  
  const keyboardPositionStyle = keyboardPosition === "right" ? { right: 24 } : { left: 24 }
  
  return (
    <div>
      <div className={classes.searchBar}>
        <div>
          <Button variant="outlined" className={classes.button} onClick={toggleFocused} disableRipple>
            <MagnifierIcon />
          </Button>
        </div>
        <InputBase
          inputRef={inputRef}
          placeholder={t('common-search')}
          onChange={e => setQuery(e.target.value)}
          onFocus={() => setShowKeyboard(true)}
          classes={{
            input: clsx(classes.input, { [classes.inputActive]: focused }),
          }}
          inputProps={{ 'aria-label': 'search' }}
        />
      </div>
      {showKeyboard && !showNativeKeyboard && (
        <ClickAwayListener onClickAway={() => setShowKeyboard(false)}>
          <div style={{ position: "absolute", bottom: 24, ...keyboardPositionStyle, zIndex: 99999 }}>
            <Keyboard style={{ ...shadows.large }} extended specialChar="-" onDelete={onDelete} onClick={onClick} onShowKeyboard={onShowKeyboard} />
          </div>
        </ClickAwayListener>
      )}
    </div>
  )
});

export default SearchBar;
