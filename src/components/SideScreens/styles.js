import { makeStyles } from '@material-ui/core/styles';

const drawerWidth = 240;

const useStyles = makeStyles(theme => ({
  wrapper: {
    height: "100%"
  },
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100%"
  },
  content: {
    margin: 12,
    minHeight: `calc(100vh - 49px)`
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  buttonRoot: {
    fontSize: 14,
    padding: '4px 6px',
    borderRadius: theme.spacing(1)
  },
  icon: {
    color: 'rgba(0, 0, 0, 0.54)'
  },
  user: {
    display: 'flex',
    alignItems: 'center'
  },
  avatarRoot: {
    width: 16,
    height: 16
  },
  username: {
    marginLeft: theme.spacing(1)
  },
  tabs: {
    marginBottom: theme.spacing(2)
  },
  itemImg: {
    height: 24,
    width: 24,
    marginRight: 6,
    marginLeft: 6,
    objectFit: 'cover',
    borderRadius: 4
  },
  itemTitle: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: 12
  },
  clickable: {
    cursor: 'pointer'
  },
  actions: {
    '& > button:not(:first-child)': {
      marginLeft: 8
    }
  },
  numeration: {
    fontWeight: 500,
    marginRight: 8,
    marginLeft: 8,
  },
  createBtn: {
    background: "#FF7C5C",
    fontStyle: "normal",
    fontWeight: "500",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#F9F9F9",
    borderRadius: 10,
    '&:hover': {
      background: "#FF7C5C",
    },
    textTransform: "capitalize",
    padding: '6px 16px'
  },
}));

export default useStyles;
