import React, { useState } from "react";
import { withTranslation } from '../../../../i18n';
import { ButtonBase, Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import { Play20 } from "../../../utils/icons";
import isEmpty from "../../../utils/isEmpty";
import VideoTutorialModal from "../../_popup/VideoTutorialModal";
import Loader from "../../_progress/Loader";


const EmptyScreen = ({ t, icon, titleI18nKey, descriptionI18nKey, descriptionI18nVars, action = {}, tutorial = {} }) => {
  const [videoTutorial, setVideoTutorial] = useState(false);
  const openTutorial = () => setVideoTutorial(true);
  const closeTutorial = () => setVideoTutorial(false);
  
  const { url: tutorialUrl, title: tutorialTitle, description: tutorialDescription } = tutorial;
  
  return (
    <div style={{ display: "flex", alignItems: "center", justifyContent: "center", height: "100%" }}>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", maxWidth: 350, textAlign: "center" }}>
        {icon && (
          <div style={{ marginBottom: 20 }}>
            {icon}
          </div>
        )}
        <Typography style={{ ...typography.body.medium }}>{t(titleI18nKey)}</Typography>
        {descriptionI18nKey && (
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 4 }}>
            {t(descriptionI18nKey, descriptionI18nVars)}
          </Typography>
        )}
        {!isEmpty(action) && (
          <ButtonBase
            disableRipple
            disableTouchRipple
            onClick={action.onClick}
            disabled={action.disabled || action.loading}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              borderRadius: 12,
              background: action.disabled ? palette.grayscale["400"] : palette.primary["500"],
              marginTop: 24,
              minWidth: 100
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {action.loading ? <Loader /> : t(action.i18nKey)}
            </Typography>
          </ButtonBase>
        )}
        {tutorialUrl && (
          <ButtonBase
            disableRipple
            disableTouchRipple
            onClick={openTutorial}
            style={{
              padding: 0,
              marginTop: 16,
              display: "flex",
              alignItems: "center"
            }}
          >
            <Play20 />
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], marginLeft: 2 }}>
              {t('watch-tutorial')}
            </Typography>
          </ButtonBase>
        )}
      </div>
      {tutorial && <VideoTutorialModal open={videoTutorial} onClose={closeTutorial} url={tutorialUrl} title={tutorialTitle} description={tutorialDescription} />}
    </div>
  )
}

export default withTranslation('common')(EmptyScreen);
