import React from 'react';
import FormControl from "@material-ui/core/FormControl";
import InputBase from "@material-ui/core/InputBase";
import InputLabel from "@material-ui/core/InputLabel";
import {generate} from "shortid";
import useStyles from "./styles";
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";
import Typography from "@material-ui/core/Typography";
import clsx from "clsx";

const Field = React.forwardRef((props, ref) => {
	const classes = useStyles();
	const { id: componentId, label, required, adornEnd, adornStart, iconAdornEnd, disabled, noFocus, navigatorCheck, error, errorMessage, adornEndColor, slimField, ...otherProps } = props;
	const { type, select, multiline } = otherProps;
	const id = componentId || `field-${generate()}`;
	
	let is_safari = navigatorCheck && navigator ? navigator.userAgent.indexOf('Safari') > -1 : false;
	const is_chrome = navigatorCheck && navigator ? navigator.userAgent.indexOf('Chrome') > -1 : false;
	if ((is_chrome) && (is_safari)) {
		is_safari = false;
	}
	
	return (
		<>
			<FormControl classes={{ root: classes.formControlRoot }}>
				{label && (
					<InputLabel shrink htmlFor={id} className={classes.inputLabel}>
						{label}
						{required && <span style={{ color: palette.primary["500"] }}>*</span>}
					</InputLabel>
				)}
				{adornStart && (
					<div style={{ position: "absolute", left: 12, top: label ? 36 : 12, zIndex: 1 }}>
						<Typography style={{ ...typography.body.regular, fontSize: 16, display: "flex" }}>
							{adornStart}
						</Typography>
					</div>
				)}
				<InputBase id={id} classes={{
					root: clsx(classes.inputBaseRoot, { [classes.inputBaseRootError]: error || errorMessage }, { [classes.inputBaseRootMultiline]: multiline }),
					input: clsx(classes.inputBaseInput, { [classes.noFocusInputBaseInput]: noFocus },
						{ [classes.dateBaseInput]: type === "date" && is_safari },
						{ [classes.timeBaseInput]: type === "time" && is_safari },
						{ [classes.adornStartInput]: !!adornStart },
						{ [classes.selectInputBaseInput]: !!select }
					),
					multiline: classes.inputBaseMultiline
				}} ref={ref} required={required} disabled={disabled || noFocus} {...otherProps} />
				{adornEnd && (
					<div style={{ position: "absolute", right: 12, top: label ? 40 : 12 }}>
						<Typography style={{ ...typography.body.regular, fontSize: 16, color: adornEndColor ? adornEndColor : null }}>
							{adornEnd}
						</Typography>
					</div>
				)}
				{iconAdornEnd && (
					<div style={{ position: "absolute", right: slimField ? 9.5 : 16.5, top: label ? 40 : slimField ? 7 : 13}}>
							{iconAdornEnd}
					</div>
				)}
				{errorMessage && (
					<Typography style={{ ...typography.body.regular, color: palette.primary["500"] }}>{errorMessage}</Typography>
				)}
			</FormControl>
		</>
	)
});

export default Field;
