import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";

const useStyles = makeStyles(theme => ({
  wrapper: {
    height: "100%"
  },
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    paddingBottom: 40,
    paddingTop: 11
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: 12,
    // height: 32
  },
  createBtn: {
    background: "#FF7C5C",
    fontStyle: "normal",
    fontWeight: "500",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#F9F9F9",
    borderRadius: 10,
    '&:hover': {
      background: "#FF7C5C",
    },
    textTransform: "capitalize",
    padding: '6px 16px'
  },
  disabledBtn: {
    backgroundColor: palette.grayscale["350"],
  },
  content: {
    flex: 1,
    // "-webkit-font-smoothing": "subpixel-antialiased",
    // paddingTop: 11,
    // minHeight: `calc(100vh - 49px)`
    overflow: "auto",
    display: "flex",
    flexDirection: "column",
    height: "100%"
  },
  avatar: {
    width: 80,
    height: 80,
    backgroundColor: "#757574",
    color: theme.palette.common.white,
    fontSize: 20,
    lineHeight: '24px',
    fontWeight: 500,
    textTransform: 'uppercase',
    letterSpacing: '0.02em'
  },
  actions: {
    textAlign: 'right',
    '& > button': {
      marginLeft: 8
    }
  },
  pagination: {
    marginTop: theme.spacing(2),
    display: 'flex',
    justifyContent: 'flex-end'
  },
}));

export const useHeaderStyles = makeStyles(() => ({
  container: {
    background: "#F9F9F9",
    padding: 16,
    borderRadius: 12,
    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.02)",
    width: "100%",
    "&+&": {
      marginTop: 16,
    }
  },
  topBar: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    paddingBottom: 16,
    marginBottom: 16,
    borderBottom: "1px dashed #D8D7D6"
  },
  bottomBar: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    paddingTop: 16,
    marginBottom: 16,
    borderTop: "1px dashed #D8D7D6"
  },
  containerTitle: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#333332"
  },
  sourceBtnText: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
    marginRight: 2
  },
  sourceBtnIcon: {
    transform: "rotate(180deg)",
    display: "flex"
  },
  row: {
    display: "flex",
  },
  column: {
    display: "flex",
    flexDirection: "column",
    "&+&": {
      marginLeft: 32
    }
  },
  cardTitle: {
    ...typography.body.medium
  },
  cardSubtitle: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "11px",
    lineHeight: "16px",
    letterSpacing: "0.02em",
    textTransform: "uppercase",
    color: "#737372",
    marginTop: 2
  }
}));

export default useStyles;
