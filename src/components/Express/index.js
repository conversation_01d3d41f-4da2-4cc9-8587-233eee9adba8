import React, { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from '../../../i18n';
import Moment from "react-moment";
import Button from "@material-ui/core/Button";
import SecondaryBar from "../_navigation/SecondaryBar";
import { operationViews, paymentStatuses } from "../../../redux/constants";
import { accountSelectors, configurationSelectors, expressSelectors, floorsSelectors } from "../../../redux/selectors";
import { expressActions, floorsActions, terminalActions } from "../../../redux/actions";
import isEmpty from "../../utils/isEmpty";
import { toI18nKey } from "../../utils/toI18nKey";
import Typography from "@material-ui/core/Typography";
import typography from "../../../styles/typography";
import Badge from "../_tags/Badge";
import DishesDrinksBadge from "../_tags/DishesDrinksBadge";
import { NoTakeaway120x90 } from "../../utils/icons";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import { formatNumber } from "../../utils/formatNumber";
import { ButtonBase, TableFooter } from "@material-ui/core";
import OperationViewDropdown from "../_buttons/OperationViewDropdown";
import EmptyScreen from "../_placeholder/EmptyScreen";
import RevenueBadge from "../_tags/RevenueBadge";
import PaymentStatusBadge from "../_tags/PaymentStatusBadge";
import ExpressTerminalModal from "../_popup/ExpressTerminalModal";
import ReceiptInformation from "../Administration/ReceiptInformation";
import palette from "../../../styles/palette";
import orderStatus from "../../utils/orderStatus";
import { noop, onboardingTutorialSteps } from "../../utils/const";
import { updateOrderStatus } from "../../api";
import useStyles, { useBoardStyles, useTabsStyles } from "./styles";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import clsx from "clsx";
import ButtonTooltip from "../_notification/ButtonTooltip";
import ExpressModeModal from "../_popup/ExpressModeModal";
import PagerBadge from "../_tags/PagerBadge";

let timer;

const Express = ({ t, restaurantId }) => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const boardClasses = useBoardStyles();
  const tabsClasses = useTabsStyles();
  const isMobile = useMediaQuery('(max-width:960px)');

  const view = useSelector(expressSelectors.getView);
  const myAccountId = useSelector(accountSelectors.getAccountId);
  const updateView = (updatedView) => dispatch(expressActions.setView(updatedView))

  const total = useSelector(expressSelectors.getTotal);
  const empty = !total;
  const { lanes = [] } = useSelector(expressSelectors.getBoard);
  const { items = [], totalAmount } = useSelector(expressSelectors.getList);

  const [ordering, setOrdering] = useState(false);
  const [orderId, setOrderId] = useState(null);

  const staticExpressTerminal = useSelector(configurationSelectors.getExpressTerminalConfiguration);
  const { tablesById = {} } = useSelector(floorsSelectors.getTablesById);

  const [openExpressModeModal, setOpenExpressModeModal] = useState(false)
  const [isToGo, setIsToGo] = useState(false)

  const startOrdering = (toGo) => {
    setIsToGo(toGo)
    setOrdering(true);
    setOpenExpressModeModal(false)
  };
  const stopOrdering = () => {
    setOpenExpressModeModal(false)
    setOrdering(false);
    setOrderId(null);
    dispatch(expressActions.get(view));
  };
  const resetOrdering = () => {
    if (staticExpressTerminal) {
      setOrdering(true);
      setOrderId(null);
      dispatch(terminalActions.reset());
    } else {
      stopOrdering();
    }
  }
  
  const [updating, setUpdating] = useState(false);
  const [updatingStatus, setUpdatingStatus] = useState(null);

  /**
   * Checks if the account matches the user one
   * @param {string} accountId
   * @returns {boolean}
   */
  const isTheOrderMine = (accountId) => {
    // If there is no accountId assume that is mine - backward compatibility
    if (!accountId) return true;
    // If its not the same account as mine check
    return (accountId === myAccountId)
  }

  // accountId here is the account that created the order.
  const startUpdating = (id, status, paymentStatus, accountId, toGo) => {
    // TODO: Check if the order account id is the same as ours and do different things.
    // If the order is mine do what is supposed to be done.
    setIsToGo(toGo)
    if (isTheOrderMine(accountId)) {
      setOrderId(id);
      if (paymentStatus === paymentStatuses.PAID.key) {
        setUpdating(true);
        setUpdatingStatus(status);
      } else {
        setOrdering(true);
      }
    } else {
      // If the order is not mine open the status modal without a change button.
      setOrderId(id);
      setUpdating(true);
      setUpdatingStatus(status);
    }

  }
  const stopUpdating = () => {
    setUpdating(false);
    setOrderId(null);
    dispatch(expressActions.get(view));
  }
  const updateStatus = () => {
    if (updatingStatus === orderStatus.PREPARING) {
      updateOrderStatus(restaurantId, orderId, orderStatus.READY).then(() => { stopUpdating(); }).catch(noop);
    }

    if (updatingStatus === orderStatus.READY) {
      updateOrderStatus(restaurantId, orderId, orderStatus.CLOSED).then(() => { stopUpdating(); }).catch(noop);
    }
  }

  useEffect(() => {
    dispatch(expressActions.get(view));
    if (!ordering) {
      timer = setInterval(() => dispatch(expressActions.get(view)), 4000);
    }
    return function cleanup() {
      clearInterval(timer)
    }
  }, [view, ordering])

  useEffect(() => {
    dispatch(floorsActions.getTables());
  }, [])

  const [tab, setTab] = useState(0);

  const setBoardActiveGroup = (laneIndex, groupId) => () => {
    dispatch(expressActions.setBoardActiveGroup(laneIndex, groupId))
  }

  const renderBoardLane = (activeGroupId, groups, index) => (
    <div className={boardClasses.lane} key={`lane-${index}`}>
      {groups.map(({ id: groupId, numberOfOrders, orders = [] }) => {
        const groupI18nKey = toI18nKey(groupId);
        const expanded = activeGroupId === groupId;

        return (
          <div className={boardClasses.group} style={{ flex: expanded ? 1 : 0 }} key={groupId}>
            <div className={boardClasses.groupHeader} onClick={setBoardActiveGroup(index, groupId)}>
              <Typography style={{ ...typography.body.medium }}>{t(groupI18nKey)}</Typography>
              <span style={{ marginLeft: 4, display: "inline-flex" }}>
                        <Badge quantity={numberOfOrders}/>
                      </span>
            </div>
            {!isEmpty(orders) && expanded && (
              <div className={boardClasses.cards}>
                {orders.map(({ id, number, numberOfDishes, numberOfDrinks, customer = {}, total, status, paymentStatus, accountId, toGo, pagerIdentifier }) => (
                  <div key={id} className={boardClasses.card} onClick={() => startUpdating(id, status, paymentStatus, accountId, toGo)}>
                    <div style={{ display: "flex", alignItems: "center", flexDirection: "row", justifyContent: "space-between" }}>
                      <div>
                        <Typography style={{ ...typography.body.medium }}>#{number} {customer && customer.fullName}</Typography>
                        <div style={{ display: "flex", alignItems: "center", marginTop: 6, marginLeft: -1 }}>
                          <DishesDrinksBadge dishes={numberOfDishes} drinks={numberOfDrinks} />
                          <div style={{ marginLeft: 4 }}>
                            <RevenueBadge revenue={total} />
                          </div>
                          {pagerIdentifier && (
                            <div style={{ marginLeft: 4 }}>
                              <PagerBadge pagerIdentifier={pagerIdentifier} />
                            </div>
                          )}
                        </div>
                        <div style={{ display: "flex", alignItems: "center", marginTop: 6 }}>
                          {/*<AllOProviderLogo20 />*/}
                          <PaymentStatusBadge status={paymentStatus} />
                        </div>
                      </div>
                      {/*<div>*/}
                      {/*  <Timer />*/}
                      {/*</div>*/}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )
      })}
    </div>
  )

  const renderBoard = () => {
    if (!total || isEmpty(lanes)) {
      return null;
    }

    return (
      <div className={boardClasses.container}>
        {isMobile && (
          <div className={tabsClasses.categories}>
            {lanes.map(({ groups = [] }, index) => {
              if (isEmpty(groups)) {
                return null;
              }

              const laneTotal = groups.map(it => it.numberOfOrders ?? 0).reduce((partialSum, a) => partialSum + a, 0);
              const firstGroup = groups[0]
              const laneI18nKey = toI18nKey(firstGroup.id || "")
              return (
                <Button
                  key={`lane-${index}`}
                  disableRipple
                  disableTouchRipple
                  className={clsx(tabsClasses.category, { [tabsClasses.categorySelected]: tab === index })}
                  onClick={() => setTab(index)}
                >
                  <span className={clsx(tabsClasses.categoryContent, { [tabsClasses.categoryContentSelected]: tab === index })}>
                    {t(laneI18nKey)}
                  </span>
                  <span className={clsx(tabsClasses.count, { [tabsClasses.countSelected]: tab === index })}>
                    {laneTotal}
                  </span>
                </Button>
              );
            })}
          </div>
        )}

        <div className={boardClasses.content}>
          {!isMobile && lanes.map(({ groupId: activeGroupId, groups = [] }, index) => renderBoardLane(activeGroupId, groups, index))}
          {isMobile && !isEmpty(lanes[tab]) && !isEmpty(lanes[tab].groups) && renderBoardLane(lanes[tab].groupId, lanes[tab].groups, tab)}
        </div>
      </div>
    )
  }

  const renderList = () => {
    if (!total) {
      return null;
    }

    const hasPager = items.some(item => item.pagerIdentifier);

    return (
      <div style={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        paddingTop: 16,
        paddingLeft: 16,
        paddingRight: 16,
        paddingBottom: 12
      }}>
        <TableContainer>
          <Table stickyHeader aria-label="express-table">
            <TableHead>
              <TableRow>
                <TableCell align="left">{t('order-history-table-header-order-number')}</TableCell>
                <TableCell align="left">{t('order-update-current-status-label')}</TableCell>
                <TableCell align="left">{t('order-history-table-header-order-started')}</TableCell>
                <TableCell align="left">{t('common-pager')}</TableCell>
                <TableCell align="right">{t('amount')}</TableCell>
                <TableCell align="left">{t('payment-status')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {items.map(({ id, number, status, total, paymentStatus, creationTime, pagerIdentifier }) => {
                return (
                  <TableRow key={id} onClick={() => startUpdating(id, status, paymentStatus)} className={classes.clickable}>
                    <TableCell>
                      <Typography style={{ ...typography.body.regular }}>
                        {number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography style={{ ...typography.body.regular }}>
                        {status}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography style={{ ...typography.body.regular }}>
                        <Moment format={'DD.MM.YYYY HH:mm'}>{creationTime}</Moment>
                      </Typography>
                    </TableCell>
                    {hasPager && (
                      <TableCell>
                        <Typography style={{ ...typography.body.regular }}>
                          {pagerIdentifier}
                        </Typography>
                      </TableCell>
                    )}
                    <TableCell align="right">
                      <Typography style={{ ...typography.body.regular }}>
                        {formatNumber(total)} €
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <PaymentStatusBadge status={paymentStatus} />
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell>{t('table-count', { count: total })}</TableCell>
                <TableCell>{t('table-count', { count: total })}</TableCell>
                <TableCell>{t('table-count', { count: total })}</TableCell>
                <TableCell align="right">{t('table-total', { total: formatNumber(totalAmount) })}</TableCell>
                <TableCell>{t('table-count', { count: total })}</TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      </div>
    )
  }

  const getContent = () => {
    if (empty) {
      return (
        <div className={classes.content}>
          <EmptyScreen
            icon={<NoTakeaway120x90 />}
            titleI18nKey="no-orders"
            descriptionI18nKey="click-the-button-below-to-create-an-order"
            action={{ i18nKey: "create-express", onClick: () => setOpenExpressModeModal(true) }}
            tutorial={{
              url: "https://www.youtube.com/watch?v=NcRifDitRnX"
            }}
          />
        </div>
      )
    }

    return (
      <div className={classes.content}>
        {view === operationViews.BOARD.key && renderBoard()}
        {view === operationViews.LIST.key && renderList()}
      </div>
    )
  }

  const getSecondaryBarActions = () => (
    <div style={{ display: "flex", alignItems: "center" }}>
      <OperationViewDropdown style={{ marginRight: 8 }} view={view} setView={updateView} views={[operationViews.BOARD.key, operationViews.LIST.key]} />
      <ButtonTooltip stepId={onboardingTutorialSteps.EXPRESS_ORDER_CREATION.value}>
        <Button className={classes.createBtn} onClick={() => setOpenExpressModeModal(true)} disableRipple>{t('new')}</Button>
      </ButtonTooltip>
    </div>
  )

  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        <SecondaryBar title={t('express')} right={getSecondaryBarActions()} />
        {getContent()}
      </div>
      <ExpressModeModal
        open={openExpressModeModal}
        onClose={() => {setOpenExpressModeModal(false)}}
        submit={startOrdering}
      />
      <ExpressTerminalModal open={ordering} onClose={stopOrdering} onReset={resetOrdering} orderId={orderId} isToGo={isToGo} />
      {orderId && updating && updatingStatus && (
        <ReceiptInformation
          orderId={orderId}
          isOpen={!!orderId}
          close={stopUpdating}
          tablesById={tablesById}
          bottomActions={(updatingStatus === orderStatus.OPEN) ? null : (
            <div style={{
              paddingLeft: 16,
              paddingRight: 16,
              paddingBottom: 12,
              paddingTop: 12,
              background: palette.grayscale["100"],
              borderTop: `1px solid ${palette.grayscale.divider}`
            }}>
              <ButtonBase
                key={"update-status"}
                disableRipple
                disableTouchRipple
                onClick={updateStatus}
                style={{
                  paddingTop: 12,
                  paddingBottom: 12,
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                  background: palette.primary["500"],
                  borderRadius: 12
                }}
              >
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                  {t(updatingStatus === orderStatus.PREPARING ? 'mark-as-ready' : 'common-finish')}
                </Typography>
              </ButtonBase>
            </div>
          )}
        />
      )}
    </div>
  )
};

export default withTranslation('common')(memo(Express));
