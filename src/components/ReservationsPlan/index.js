import React, { memo, useEffect, useState } from "react";
import isEmpty from "../../utils/isEmpty";
import { useDispatch, useSelector } from "react-redux";
import { reservationsSelectors} from "../../../redux/selectors";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";
import useStyles from "../DineIn/styles";
import { reservationsActions } from "../../../redux/actions";
import { operationViews } from "../../../redux/constants";
import TablesPlan from "../floorPlan/TablesPlan";
import { NoTables100 } from "../../utils/icons";
import EmptyScreen from "../_placeholder/EmptyScreen";


const ReservationsPlan = ({ onClick, onCreate }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const { tables = [], items = [], floors = [] } = useSelector(reservationsSelectors.getPlan);
  const date = useSelector(reservationsSelectors.getDate);
  const floorId = useSelector(reservationsSelectors.getFloorId);

  const [key, setKey] = useState(1);
  useEffect(() => {
    setKey(key + 1);
  }, [date]);

  useEffect(() => {
    dispatch(reservationsActions.get(operationViews.PLAN.key));
  }, [floorId])

  const setFloor = (id) => {
    dispatch(reservationsActions.setFloor(id))
  }

  return (
    <div key={key} style={{ overflow: "auto", height: "100%" }}>
      {!isEmpty(tables) ? (
        <TablesPlan
          tables={tables}
          items={items}
          onClick={(reservation) => onClick(reservation.id)}
          onCreate={(table) => onCreate(table)}
        />
      ) : (
        <EmptyScreen
          icon={<NoTables100 />}
          titleI18nKey="no-tables"
          tutorial={{
            url: "https://www.youtube.com/watch?v=NcRifDitRnX"
          }}
        />
      )}
      {!(isEmpty(floors)) && (floors.length > 1) && (
        <div className={classes.bottomTabs}>
          <div className={classes.tabsWrapper}>
            <Tabs
              data-transform="translate"
              value={floorId || floors[0].id}
              onChange={(_, newValue) => setFloor(newValue)}
              aria-label="Vertical tabs example"
              variant="scrollable"
              scrollButtons="off"
              TabIndicatorProps={{ children: <div /> }}
              classes={{
                root: classes.tabsRoot
              }}
            >
              {(floors.length > 1) && floors.map(({ id, name }, index) => (
                <Tab disableRipple data-transform="translate" value={id} key={id || 'other'} label={name} />
              ))}
            </Tabs>
          </div>
        </div>
      )}
    </div>
  );
}

export default memo(ReservationsPlan)
