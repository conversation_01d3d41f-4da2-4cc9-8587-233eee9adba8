import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import clsx from 'clsx'
import Typography from '@material-ui/core/Typography'
import List from '@material-ui/core/List'
import { useMediaQuery } from '@material-ui/core'
import AppBar from '@material-ui/core/AppBar'
import Button from '@material-ui/core/Button'
import { withTranslation } from '../../../i18n'
import { menusActions } from '../../../redux/actions'
import { menusSelectors } from '../../../redux/selectors'
import { menuEditorViewOptions, menuEditorViews } from '../../utils/const'
import { CaretRightIcon } from '../../utils/icons'
import isEmpty from '../../utils/isEmpty'
import useStyles, { useAppBarStyles, useBackBtnStyles } from './styles'
import { CategoriesEditor } from './CategoriesEditor'
import {
  getOrderModeTags,
  getPredefinedNotes, getRestaurantConfiguration,
  getTags,
  resolveExtras,
  resolveMenuGroups,
  resolveMenus,
  resolveOptions,
  resolvePrinters,
  resolveRemarks
} from "../../api";
import { NotesEditor } from './NotesEditor'
import { RemarksEditor } from './RemarksEditor'
import { ExtraEditor } from './ExtraEditor'
import { OptionEditor } from './OptionEditor'
import MenuEditorSidebarItem from '../_navigation/MenuEditorSidebarItem'
import { views } from '../../utils/administrationRoutes'
import { useRouter } from 'next/router'
import GroupsEditor from './GroupsEditor'
import { OrderModes } from "./OrderModes";
import TemplateSetMenusEditor from "./TemplateSetMenusEditor";

const MenuEditor = ({ t, restaurantId }) => {
  const classes = useStyles();
  const appBarClasses = useAppBarStyles();
  const backBtnClasses = useBackBtnStyles();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery('(max-width:800px)');

  const router = useRouter();
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "");

  const { view, params } = useSelector(menusSelectors.getNavigation);
  const updateView = (value) => () => dispatch(menusActions.setView(value));


  const [menus, setMenus] = useState([]);
  const [remarks, setRemarks] = useState([]);
  const [options, setOptions] = useState([]);
  const [extras, setExtras] = useState([]);
  const [tags, setTags] = useState([]);
  const [predefinedNotes, setPredefinedNotes] = useState([]);
  const [printers, setPrinters] = useState([]);
  const [menuGroups, setMenuGroups] = useState([]);
  const [templateSetMenus, setTemplateSetMenus] = useState([]);
  const [orderModes, setOrderModes] = useState([]);
  const [orderModeTags, setOrderModeTags] = useState([]);
  const [configurationForm, setConfigurationForm] = useState({partnerConfigs:[]});

  const fetchConfig = () => {
    getRestaurantConfiguration(restaurantId)
      .then(({ data = {} }) => {
        setConfigurationForm(data ?? {});
      })
      .catch(() => {
      });
  }

  const fetchMenus = () => {
    resolveMenus(restaurantId)
      .then(({ data = [] }) => {
        setMenus(data);
      })
      .catch(() => {});
  };

  const fetchOptions = () => {
    resolveOptions(restaurantId)
      .then(({ data = [] }) => {
        setOptions(data);
      })
      .catch(() => {});
  };

  const fetchExtras = () => {
    resolveExtras(restaurantId)
      .then(({ data = [] }) => {
        setExtras(data);
      })
      .catch(() => {});
  };

  const fetchTags = () => {
    getTags()
      .then(({ data = [] }) => {
        setTags(data);
      })
      .catch(() => {});
  };

  const fetchRemarks = () => {
    resolveRemarks(restaurantId)
      .then(({ data = [] }) => {
        setRemarks(data);
      })
      .catch(() => {});
  };

  const fetchPredefinedNotes = () => {
    getPredefinedNotes(restaurantId)
      .then(({ data = [] }) => {
        setPredefinedNotes(data);
      })
      .catch(() => {});
  };

  const fetchPrinters = () => {
    resolvePrinters(restaurantId)
      .then(({ data = [] }) => {
        setPrinters(data);
      })
      .catch(() => {});
  };

  const fetchMenuGroups = () => {
    resolveMenuGroups(restaurantId)
      .then(({ data }) => {
        setMenuGroups(data.items);
      })
      .catch(() => {});
  }

  useEffect(() => {
    fetchPrinters();
  }, []);

  useEffect(() => {
    fetchTags();
  }, [])

  useEffect(() => {
    if (view === menuEditorViews.MENU_CATEGORIES.value) {
      fetchMenus();
      fetchMenuGroups();
      fetchOptions();
      fetchExtras();
      fetchRemarks();
      fetchPredefinedNotes();
      fetchConfig()
    }
  }, [view]);

  useEffect(() => {
    if(!view){
      dispatch(menusActions.setView(isEmpty(menuEditorViewOptions) ? null : menuEditorViewOptions[0].value));
    }
  }, [!isMobile])

  const fetchRestaurantTags = () => {
    getOrderModeTags(restaurantId)
      .then(({ data }) => {
        const { total, pages, items } = data
        setOrderModeTags(items)
      })
  };

  useEffect(() => {
    fetchRestaurantTags();
  },[]);

  const renderBackButton = () => {
    if (!view || !menuEditorViews[view]) {
      return null;
    }
    return (
      <Button variant="text" onClick={updateView(null)} className={backBtnClasses.container} disableRipple>
        <Typography className={backBtnClasses.content}>{t('menu-editor')}</Typography>
        <CaretRightIcon />
      </Button>
    )
  }

  const renderViewName = () => {
    if (!view || !menuEditorViews[view]) {
      return null;
    }
    return (
      <div className={appBarClasses.orderInfo}>
        <Typography className={appBarClasses.orderInfoContent}>{t(menuEditorViews[view].i18nKey)}</Typography>
      </div>
    )
  }

  const getCount = (value) => {
    switch (value) {
      case menuEditorViews.MENU_CATEGORIES.value: {
        return isEmpty(menus) ? 0 : menus.length;
      }
      case menuEditorViews.MENU_ITEMS.value: {
        let count = 0
        if (!isEmpty(menus)) {
          menus.forEach(({ items }) => {
            if (!isEmpty(items)) {
              count += items.length
            }
          })
        }
        return count;
      }
      case menuEditorViews.OPTION_CATEGORIES.value: {
        return isEmpty(options) ? 0 : options.length;
      }
      case menuEditorViews.EXTRA_CATEGORIES.value: {
        return isEmpty(extras) ? 0 : extras.length;
      }
      case menuEditorViews.ALLERGENS_ADDITIVES.value: {
        return isEmpty(remarks) ? 0 : remarks.length;
      }
      case menuEditorViews.PREDEFINED_ITEM_NOTES.value: {
        return isEmpty(predefinedNotes) ? 0 : predefinedNotes.length;
      }
      case menuEditorViews.MENU_GROUPS.value: {
        return isEmpty(menuGroups) ? 0 : menuGroups.length;
      }
      case menuEditorViews.SET_MENUS.value: {
        return isEmpty(templateSetMenus) ? 0 : templateSetMenus.length;
      }
      case menuEditorViews.TABLET_ORDERING_MODES.value: {
        return isEmpty(orderModes) ? 0 : orderModes.length;
      }
      case menuEditorViews.TABLET_ORDERING_TAGS.value: {
        return isEmpty(orderModeTags) ? 0 : orderModeTags.length;
      }
      case menuEditorViews.TABLET_ORDERING_ACTIONS.value: {
        const { orderActions = [] } = configurationForm;
        return orderActions.length;
      }
      default:
        return 0
    }
  }

  const renderNavigation = () => {
    return (
      <List className={classes.navigationList}>
        {menuEditorViewOptions.map(({ i18nKey, value, icon, disabled, secondary }) => (
          <MenuEditorSidebarItem
            button
            disableRipple
            key={value}
            classes={{ root: clsx(classes.listItemRoot, { [classes.listItemSelected]: value === view }) }}
            onClick={updateView(value)}
            disabled={disabled}
            icon={icon}
            value={value}
            label={t(i18nKey)}
            secondary={secondary}
            active={view === value}
            count={getCount(value)}
         />
        ))}
      </List>
    )
  }

  const renderView = () => {
    switch (view) {
      case menuEditorViews.MENU_GROUPS.value:
        return <GroupsEditor restaurantId={restaurantId} menuGroups={menuGroups} setMenuGroups={setMenuGroups}/>
      case menuEditorViews.SET_MENUS.value:
        return <TemplateSetMenusEditor restaurantId={restaurantId} templateSetMenus={templateSetMenus} setTemplateSetMenus={setTemplateSetMenus} orderModeTags={orderModeTags} courses={configurationForm?.courseConfig?.courses || []}/>
      case menuEditorViews.MENU_CATEGORIES.value:
        return <CategoriesEditor restaurantId={restaurantId} menus={menus} setMenus={setMenus}/>
      case menuEditorViews.MENU_ITEMS.value:
        return <CategoriesEditor restaurantId={restaurantId} menus={menus} setMenus={setMenus} itemsOnly/>
      case menuEditorViews.OPTION_CATEGORIES.value:
        return <OptionEditor restaurantId={restaurantId} tags={tags} options={options} setOptions={setOptions} />
      case menuEditorViews.EXTRA_CATEGORIES.value:
        return <ExtraEditor restaurantId={restaurantId} tags={tags} extras={extras} setExtras={setExtras}/>
      case menuEditorViews.ALLERGENS_ADDITIVES.value:
        return <RemarksEditor restaurantId={restaurantId} remarks={remarks} setRemarks={setRemarks}/>
      case menuEditorViews.PREDEFINED_ITEM_NOTES.value:
        return <NotesEditor restaurantId={restaurantId} predefinedNotes={predefinedNotes} setPredefinedNotes={setPredefinedNotes} />
      case menuEditorViews.TABLET_ORDERING_MODES.value:
        return <OrderModes restaurantId={restaurantId} orderModes={orderModes} setOrderModes={setOrderModes} setOrderModeTags={setOrderModeTags} orderModeTags={orderModeTags} fetchRestaurantTags={fetchRestaurantTags} menus={menus} />
      case menuEditorViews.TABLET_ORDERING_TAGS.value:
        return <OrderModes restaurantId={restaurantId} orderModes={orderModes} setOrderModes={setOrderModes} setOrderModeTags={setOrderModeTags} orderModeTags={orderModeTags} fetchRestaurantTags={fetchRestaurantTags} tagsOnly/>
      case menuEditorViews.TABLET_ORDERING_ACTIONS.value:
        return <OrderModes restaurantId={restaurantId} orderModes={orderModes} setOrderModes={setOrderModes} setOrderModeTags={setOrderModeTags} orderModeTags={orderModeTags} fetchRestaurantTags={fetchRestaurantTags} actionsOnly/>
      default:
        return null
    }
  }

  return (
    <>
      {isMobile && view && (
        <div>
          <AppBar
            position="fixed"
            elevation={0}
            color="inherit"
            data-transform="translate"
          >
            <div className={appBarClasses.content}>
              <div className={appBarClasses.left}>
                {renderBackButton()}
                {renderViewName()}
              </div>
            </div>
          </AppBar>
        </div>
      )}
      <div className={classes.container}>
        <div className={classes.content}>
          {(!isMobile || !view) && (
            <div className={clsx(classes.left, { [classes.leftMobile]: isMobile })} style={{ position: "relative"}}>
              <div className={classes.header}>
                <Typography className={classes.headerTitle}>{t('menu-editor')}</Typography>
              </div>
              <div className={classes.navigation}>
                {renderNavigation()}
              </div>
            </div>
          )}
          {view && (
            <div className={clsx(classes.right, { [classes.rightMobile]: isMobile })}>
              {renderView()}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default withTranslation('common')(MenuEditor);
