import React, { useEffect, useState } from "react";
import TextField from '@material-ui/core/TextField';
import { withTranslation } from '../../../../../i18n';
import Checkbox from "../../../_toggles/Checkbox";
import isEmpty from "../../../../utils/isEmpty";
import Grid from '@material-ui/core/Grid';
import Select from '@material-ui/core/Select';
import MenuItem from '@material-ui/core/MenuItem';
import Dropzone from 'react-dropzone';
import {uploadMenuImage} from '../../../../api';
import Typography from '@material-ui/core/Typography';
import {
  ChevronDown20new,
  CollapseIcon20, EuroSymbolIcon20Light, ExpandIcon20,
  MinusIcon20,
  PlusIcon20, TrashIcon20White,
  UploadIcon
} from "../../../../utils/icons";
import Compressor from "compressorjs";
import { ButtonBase, useMediaQuery } from "@material-ui/core";
import Chip from '@material-ui/core/Chip';
import useStyles from './styles';
import shadows from "../../../../../styles/shadows";
import ModalBar from "../../../_navigation/ModalBar";
import Modal from "../../../_popup/Modal"
import palette from "../../../../../styles/palette";
import typography from "../../../../../styles/typography";
import clsx from "clsx";
import Field from "../../../form/Field"
import InputAdornment from "@material-ui/core/InputAdornment";
import { IntegrationWarningConfirm } from "../../../IntegrationWarningConfirm";
import FieldWithTranslation from "../../../form/FieldWithTranslation";
import { measurementUnits, MenuProps } from "../../../../utils/const";
import SetupMenuItemIngredientsModal from "../../../_popup/SetupMenuItemIngredientsModal";

const taxCategories = [{
  value: "NORMAL",
  i18nKey: "menu-editor-form-tax-category-field-option-normal"
}, {
  value: "REDUCED",
  i18nKey: "menu-editor-form-tax-category-field-option-reduced"
}];

const itemCategories = [{
  value: "DISH",
  i18nKey: "menu-editor-form-category-field-option-dish"
}, {
  value: "BEVERAGE",
  i18nKey: "menu-editor-form-category-field-option-beverage"
}];


const data = {
  name: "",
  numeration: "",
  nameI18n: {},
  description: "",
  descriptionI18n: {},
  internalName: "",
  internalNameI18n: {},
  unitPrice: 0,
  category: "DISH",
  printerCategory: "KITCHEN",
  volume: "",
  thumbnailUrl: "",
  restricted: false,
  disabled: false,
  hidden: false,
  remarkAnnotations: [],
  optionId: "",
  min: 1,
  max: 1,
  dineTaxCategory: "NORMAL",
  takeawayTaxCategory: "REDUCED",
  tagIds: [],
  order: null,
  partnerPrices: null,
  alcoholPercentage: 0,
  metadata: {
    consumptions: []
  }
};

const OptionItemPanel = ({ t, open, close, _delete, values, restaurantCode, options = [], tags = [], remarks = [], submit, hasWolt, hasLieferando, hasUberEats }) => {
  const classes = useStyles();
  const isMobile = useMediaQuery("(max-width:900px)");

  const selectedOption = isEmpty(options) ? "" : options[0].id;
  const consolidatedData = isEmpty(values) ? data : { ...data, ...values, optionId: values.optionId || selectedOption };
  const [form, setForm] = useState(consolidatedData);

  const [isShowingAdvancedOptions, setIsShowingAdvancedOptions] = useState(false)

  const [titleTranslated, setTitleTranslated] = useState(false);
  const [internalNameTranslated, setInternalNameTranslated] = useState(false);
  const [descriptionTranslated, setDescriptionTranslated] = useState(false);

  const { consumptions = [] } = (form.metadata || {})

  useEffect(() => {
    if (!isEmpty(values) && !isEmpty(values.internalNameI18n)) {
      setCustomizeInternal(true);
    }
  }, [])

  const onChange = (e) => {
    let takeawayTaxCategory = form.takeawayTaxCategory
    if (e.target.name === "category") {
      const newTypeIsDish = e.target.value === "DISH";
      takeawayTaxCategory = newTypeIsDish ? "REDUCED" : "NORMAL";
    }
    setForm({ ...form, [e.target.name]: e.target.value, takeawayTaxCategory });
  };

  const onTitleTranslationChange = (e) => {
    setTitleTranslated(!titleTranslated);
  };
  const onDescriptionTranslationChange = (e) => {
    setDescriptionTranslated(!descriptionTranslated);
  };

  const onInternalNameTranslationChange = () => {
    setInternalNameTranslated(!internalNameTranslated);
  };
  
  const onNumberInputChange = (e) => {
    let validNumberInput = e.target.value.replace(/[^\d.]/g, '').trim()
    let val = validNumberInput
    if( val === ""){
      val = null;
    }
    if (e.target.name === "WEBSHOP") {
      setForm({ ...form, prices: { WEBSHOP: val }});
    } else {
      setForm({ ...form, [e.target.name]: val });
    }
  }

  const onPartnerPriceChange = (e) => {
    let validNumberInput = e.target.value.replace(/[^\d.]/g, '').trim()
    let val = validNumberInput
    if (val === "") {
      val = null
    }
    const partnerPrices = { ...(form.partnerPrices ?? {}), [e.target.name]: val }
    setForm({ ...form, partnerPrices })
  };

  const onChangeWithLimit = (e, min = -99, max = 999) => {
    let value = e.target.value;
    if (value < min) value = min;
    if (value > max) value = max;
    setForm({ ...form, [e.target.name]: value });
  };

  const onNameI18nChange = (e) => {
    const nameI18n = { ...form.nameI18n, [e.target.name]: e.target.value };
    setForm({ ...form, nameI18n })
  };

  const onInternalNameI18nChange = (e) => {
    const internalNameI18n = { ...form.internalNameI18n, [e.target.name]: e.target.value };
    setForm({ ...form, internalNameI18n })
  };

  const onResetInternalName = () => {
    setCustomizeInternal(!customizeInternal)
    if(customizeInternal){
      const internalNameI18n = {};
      setForm({ ...form, internalNameI18n })
    }
  }

  const onDescriptionI18nChange = (e) => {
    const descriptionI18n = { ...form.descriptionI18n, [e.target.name]: e.target.value };
    setForm({ ...form, descriptionI18n })
  };

  const onRemarkAnnotationsChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value })
  };

  const onCheckboxUpdate = (name) => {
    setForm({ ...form, [name]: !form[name] });
  }

  const onSubmit = () => {
    form.name = form.nameI18n.de;
    form.internalName = form.internalNameI18n.de;
    form.description = form.descriptionI18n.de;
    form.unitPrice = form.unitPrice ?? 0
    submit(form)
  };

  const handleDelete = () => {
    _delete(form)
  }

  const duplicate = () => {
    delete form.id
    delete form.code
    delete form.creationTime
    delete form.modificationTime
    delete form.version
    onSubmit()
  }

  const isValid = !!form.nameI18n.de;


  const sliceText = (val = '', max) => val ? `${val.substring(0, max-1)}${val.length > max ? '...' : ''}` : '';

  const onUpload = (file) => {
    new Compressor(file, {
      quality: 0.6,
      success(result) {
        uploadMenuImage(restaurantCode, result).then(({ data }) => setForm({ ...form, thumbnailUrl: data })).catch(() => {})
      },
      error(err) {}
    });

  };

  const decrementMinQuantity = (e) => {
    if (form.min === 1) {
      return;
    }
    const updatedForm = { ...form, min: Math.max(1, form.min - 1) };
    setForm(updatedForm);
  }

  const incrementMinQuantity = (e) => {
    const updatedForm = { ...form, min: form.min + 1 };
    setForm(updatedForm)

  }

  const decrementMaxQuantity = (e) => {
    if (form.max === 1) {
      return;
    }
    const updatedForm = { ...form, max: Math.max(1, form.max - 1) };
    setForm(updatedForm);
  }

  const incrementMaxQuantity = (e) => {
    const updatedForm = { ...form, max: form.max + 1 };
    setForm(updatedForm)
  }

  const [customizeInternal, setCustomizeInternal] = useState(false);

  const getThumbnailUploader = () => {
    return (
      <>
        <Dropzone onDrop={acceptedFiles => !isEmpty(acceptedFiles) && onUpload(acceptedFiles[0])} accept={"image/jpeg, image/png, image/jpg"} maxSize={5000000} >
          {({ getRootProps, getInputProps }) => (
            <section className={classes.uploaderWrapper} style={{ border: `1px dashed ${palette.grayscale["350"]}`, borderRadius: 12,}}>
              <div {...getRootProps()} className={classes.uploaderContainer} style={form.thumbnailUrl ? { backgroundImage: `url('${form.thumbnailUrl}')` } : null }>
                <input {...getInputProps()} />
                {!form.thumbnailUrl &&
                  <>
                    <div style={{ marginTop: isMobile ? 10 : 30.5 }}>
                      <UploadIcon />
                    </div>
                    <div>
                      <Typography style={{ ...typography.body.medium, whiteSpace: "break-spaces" }}>{t("menu-editor-form-thumbnail-field-placeholder")}</Typography>
                      <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], whiteSpace: "break-spaces" }}>{t("menu-editor-items-thumbnail-field-description")}</Typography>
                    </div>
                  </>
                }
              </div>
            </section>
          )}
        </Dropzone>
        {form.thumbnailUrl && (
          <div style={{ display: "flex", justifyContent: "flex-start", marginTop: 8 }}>
            <ButtonBase style={{ borderRadius: 12, padding: '6px 12px', background: palette.negative["500"] }} onClick={() => setForm({ ...form, thumbnailUrl: "" })} disableRipple disableTouchRipple>
              <TrashIcon20White/>
              <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"], marginLeft: 4 }}>
                {t("menu-editor-form-thumbnail-field-clear-image-label")}
              </Typography>
            </ButtonBase>
          </div>
        )}
      </>
    );
  };

  const getOptionField = () => {
    return (
      <div>
        <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("menu-editor-form-option-field-label")}</Typography>
        <div className={classes.selectWrapper}>
          <Select
            name="optionId"
            id={"select"}
            variant={"outlined"}
            style={{ borderRadius: 12, backgroundColor: palette.grayscale["100"], height: 44 }}
            value={form.optionId || selectedOption}
            onChange={onChange}
            IconComponent={ChevronDown20new}
            MenuProps={MenuProps}
          >
            {options.map(({ id, name }) => (
              <MenuItem key={id} value={id}>{name}</MenuItem>
            ))}
          </Select>
        </div>
      </div>
    );
  };

  const getNumerationField = () => {
    return (
      <div>
        <Field
          label={t("menu-editor-form-numeration-field-label")}
          variant={"outlined"}
          className={classes.field}
          placeholder={"000"}
          value={form.numeration}
          name="numeration"
          onChange={onChange}
          style={{ borderRadius: 12, marginBottom: 8 }}
        />
      </div>
    )
  }

  const getNameField = () => {
    return (
      <FieldWithTranslation
        label={t("menu-editor-form-title-field-label")}
        required
        placeholder={t('menu-editor-name-field-placeholder')}
        checked={titleTranslated}
        onCheckboxClick={onTitleTranslationChange}
        value={form.nameI18n}
        onChange={onNameI18nChange}
      />
    );
  };

  const getInternalNameField = () => {
    return (
      <div>
        <div className={classes.checkboxContainer} style={{ marginBottom: customizeInternal ? 24 : null, display: "flex", width: "fit-content" }}>
          <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "flex-end" }} onClick={() => onResetInternalName()} disableRipple disableTouchRipple>
            <Typography style={{ ...typography.body.regular, marginRight: 6 }}>
              {t('menu-editor-form-customize-internal-field-label')}
            </Typography>
            <Checkbox checked={customizeInternal} />
          </ButtonBase>
        </div>
        {customizeInternal && (
          <FieldWithTranslation
            label={t("menu-editor-form-internal-name-field-label")}
            placeholder={t('menu-editor-name-field-placeholder')}
            checked={internalNameTranslated}
            onCheckboxClick={onInternalNameTranslationChange}
            value={form.internalNameI18n}
            onChange={onInternalNameI18nChange}
          />
        )}
      </div>
    )
  }

  const getDescriptionField = () => {
    return (
      <FieldWithTranslation
        label={t("menu-editor-form-description-field-label")}
        multiline
        placeholder={t("menu-editor-description-field-placeholder")}
        checked={descriptionTranslated}
        onCheckboxClick={onDescriptionTranslationChange}
        value={form.descriptionI18n}
        onChange={onDescriptionI18nChange}
      />
    );
  };

  const getPricesField = () => {
    return (
      <div>
        <div>
          <div style={{ display: "flex", justifyContent: "space-evenly", gap: 12 }}>
            <Field
              className={classes.field}
              variant="outlined"
              label={t("menu-editor-form-price-field-label")}
              name="unitPrice"
              type="text"
              value={form.unitPrice}
              onChange={onNumberInputChange}
              iconAdornEnd={< EuroSymbolIcon20Light/>}
            />
            <Field
              label={t("allo-webshop-price")}
              variant={"outlined"}
              name="WEBSHOP"
              value={form.prices?.WEBSHOP}
              type={"text"}
              onChange={onNumberInputChange}
              className={classes.withAdornment}
              style={{ borderRadius: 12, marginBottom: 8 }}
              iconAdornEnd={<EuroSymbolIcon20Light />}
            />
          </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("allo-webshop-price-hint")}</Typography>
        </div>
        <div style={{ display: "flex", justifyContent: "space-evenly", gap: 12, marginTop: 24 , marginBottom: 24 }}>
          <Field
            className={classes.field}
            label={`${t("menu-editor-form-price-field-label")} Wolt`}
            name="WOLT"
            type="text"
            value={form.partnerPrices && form.partnerPrices.WOLT ? form.partnerPrices.WOLT : ""}
            onChange={onPartnerPriceChange}
            iconAdornEnd={< EuroSymbolIcon20Light/>}
          />
          <Field
            className={classes.field}
            label={`${t("menu-editor-form-price-field-label")} UberEats`}
            name="UBER_EATS"
            type="text"
            value={form.partnerPrices && form.partnerPrices.UBER_EATS ? form.partnerPrices.UBER_EATS : ""}
            onChange={onPartnerPriceChange}
            iconAdornEnd={< EuroSymbolIcon20Light/>}
          />
          <Field
            className={classes.field}
            label={`${t("menu-editor-form-price-field-label")} Lieferando`}
            name="LIEFERANDO"
            type="text"
            value={form.partnerPrices && form.partnerPrices.LIEFERANDO ? form.partnerPrices.LIEFERANDO : ""}
            onChange={onPartnerPriceChange}
            iconAdornEnd={< EuroSymbolIcon20Light/>}
          />
        </div>
      </div>
    );
  };

  const getQuantityField = () => {
    return (
      <div style={{ display: "flex", gap: 16, marginBottom: 24 }}>
        <div style={ {flex: 1 }}>
          <Typography
            style={{ ...typography.body.medium, marginBottom: 8 }}>{t("menu-editor-form-min-field-label")}</Typography>
          <div style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            height: 44,
            paddingRight: 12,
            paddingLeft: 12,
            borderRadius: 15,
            border: `1px solid ${palette.grayscale["350"]}`,
            backgroundColor: palette.grayscale["100"]
          }}>

            <ButtonBase disableRipple disableTouchRipple onClick={decrementMinQuantity}>
              <MinusIcon20 />
            </ButtonBase>
            <TextField
              inputProps={{
                style: { textAlign: "center", color: palette.grayscale["800"] }
              }}
              className={classes.field}
              value={form.min}
              name="min"
              disabled
            />
            <ButtonBase disableRipple disableTouchRipple onClick={incrementMinQuantity}>
              <PlusIcon20 />
            </ButtonBase>
          </div>
        </div>
        <div style={{flex: 1}}>
          <Typography
            style={{ ...typography.body.medium, marginBottom: 8 }}>{t("menu-editor-form-max-field-label")}</Typography>
          <div style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            height: 44,
            paddingRight: 12,
            paddingLeft: 12,
            borderRadius: 15,
            border: `1px solid ${palette.grayscale["350"]}`,
            backgroundColor: palette.grayscale["100"]
          }}>

            <ButtonBase disableRipple disableTouchRipple onClick={decrementMaxQuantity}>
              <MinusIcon20 />
            </ButtonBase>
            <TextField
              inputProps={{
                style: { textAlign: "center", color: palette.grayscale["800"] }
              }}
              className={classes.field}
              value={form.max}
              name="max"
              type="number"
              disabled
            />
            <ButtonBase disableRipple disableTouchRipple onClick={incrementMaxQuantity}>
              <PlusIcon20 />
            </ButtonBase>
          </div>
        </div>
      </div>
    );
  };

  const getOrderField = () => {
    return (
      <div>
        <Field
          variant={"outlined"}
          style={{ borderRadius: 12, marginBottom: 8 }}
          className={classes.field}
          label={t("menu-editor-form-order-field-label")}
          value={form.order}
          name="order"
          onChange={onChange}
          type="number"
        />
      </div>
    );
  };

  const getCheckboxes = () => {
    return (
      <div>
        <div>
          <Typography style={{ ...typography.body.medium , marginBottom: 8 }}>{t("menu-editor-checkbox-section")}</Typography>
          <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
            <div style={{ display: "flex", width: "100%" }}>
              <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("restricted")} disableRipple disableTouchRipple>
                <Typography style={{ ...typography.body.medium }}>
                  {t('menu-editor-form-restricted-field-label')}
                </Typography>
                <Checkbox checked={form.restricted} />
              </ButtonBase>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("restricted-field-menu-editor-explanation")}</Typography>
          </div>

          <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
            <div style={{ display: "flex", width: "100%" }}>
              <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("hidden")} disableRipple disableTouchRipple>
                <Typography style={{ ...typography.body.medium }}>
                  {t('menu-editor-form-hidden-field-label')}
                </Typography>
                <Checkbox checked={form.hidden} />
              </ButtonBase>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("hidden-field-menu-editor-explanation")}</Typography>
          </div>

          <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
            <div style={{ display: "flex", width: "100%" }}>
              <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("disabled")} disableRipple disableTouchRipple>
                <Typography style={{ ...typography.body.medium }}>
                  {t('menu-editor-form-disabled-field-label')}
                </Typography>
                <Checkbox checked={form.disabled} />
              </ButtonBase>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("disabled-field-menu-editor-explanation")}</Typography>
          </div>

          <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
            <div style={{ display: "flex", width: "100%" }}>
              <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("collapsed")} disableRipple disableTouchRipple>
                <Typography style={{ ...typography.body.medium }}>
                  {t('menu-editor-form-collapsed-field-label')}
                </Typography>
                <Checkbox checked={form.collapsed} />
              </ButtonBase>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("collapsed-field-menu-editor-explanation")}</Typography>
          </div>
        </div>
      </div>
    );
  };

  const getServicesField = () => {
    return (
      <div>
        <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("menu-editor-service-section")}</Typography>
        <Grid className={clsx(classes.container, isMobile && classes.smallContainer)} style={{ marginBottom: 16 }}>
          <div style={{
            flex: 1,
            width: isMobile ? "130px" : null,
            backgroundColor: palette.grayscale["100"],
            borderRadius: 12,
            display: "flex",
            alignItems: "center",
            paddingLeft: 14,
            ...shadows.base,
            height: 44,
          }}>
            <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "flex-end" }}  disableRipple disableTouchRipple>
              <Typography style={{ ...typography.body.regular }}>
                {t("dine-in-setting")}
              </Typography>
            </ButtonBase>
          </div>
          <div className={classes.selectWrapper} style={{ marginLeft: isMobile ? 8 : 16,  ...shadows.base, flex: 1, borderRadius: 12, backgroundColor: palette.grayscale["100"] }}>
            <Select
              name="dineTaxCategory"
              id="select"
              disableUnderline
              classes={isMobile ? {root: classes.TaxSelectSmall } : {root: classes.TaxSelect }}
              value={form.dineTaxCategory}
              onChange={onChange}
              IconComponent={ChevronDown20new}
              MenuProps={MenuProps}
              startAdornment = {<InputAdornment className={classes.selectAdornment} position="start" variant={"outlined"}>TAX</InputAdornment>}
            >
              {taxCategories.map(category => (
                <MenuItem value={category.value}>
                  <Typography style={{ ...typography.body.regular }}>{t(category.i18nKey)}</Typography>
                </MenuItem>
              ))}
            </Select>
          </div>
        </Grid>
        <Grid className={clsx(classes.container, isMobile && classes.smallContainer)} >
          <div style={{
            flex: 1,
            width: isMobile ? "130px" : null,
            backgroundColor: palette.grayscale["100"],
            borderRadius: 12,
            display: "flex",
            alignItems: "center",
            paddingLeft: 14,
            ...shadows.base,
            height: 44,
          }}>
            <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "flex-end" }} disableRipple disableTouchRipple>
              <Typography style={{ ...typography.body.regular }}>{t("takeaway-setting")}</Typography>
            </ButtonBase>
          </div>
          <div style={{ marginLeft: isMobile ? 8 : 16, flex: 1, ...shadows.base, borderRadius: 12, backgroundColor: palette.grayscale["100"] }} className={classes.selectWrapper}>
            <Select
              name="takeawayTaxCategory"
              id="select"
              disableUnderline
              classes={isMobile ? {root: classes.TaxSelectSmall } : {root: classes.TaxSelect }}
              value={form.takeawayTaxCategory}
              onChange={onChange}
              IconComponent={ChevronDown20new}
              MenuProps={MenuProps}
              startAdornment = {<InputAdornment className={classes.selectAdornment} position="start" variant={"outlined"}>TAX</InputAdornment>}
            >
              >
              {taxCategories.map(category => (
                <MenuItem value={category.value}>
                  <Typography style={{ ...typography.body.regular }}>{t(category.i18nKey)}</Typography>
                </MenuItem>
              ))}
            </Select>
          </div>
        </Grid>
      </div>
    );
  };
  

  const getTagsField = () => {
    return (
      <div style={{ width: "100%" }} className={classes.chipSelectWrapper}>
        <Select
          name="tagIds"
          value={form.tagIds || []}
          onChange={onChange}
          disableUnderline
          IconComponent={ChevronDown20new}
          multiple
          input={<Field id="select-multiple-chip" label={t("menu-editor-form-tags-field-label")} select />}
          MenuProps={MenuProps}
          renderValue={(selected) => (
            <div className={classes.chips} style={{ display: "flex", flexWrap: "wrap" }}>
              {selected.map((value) => {
                const tag = tags.find(t => t.id === value) || {};
                return (
                  <Chip
                    key={value}
                    label={tag.label}
                    icon={<img className={classes.tagImg} src={`/icons/tags/${tag.identifier}.svg`}
                               alt={tag.identifier} />}
                    className={classes.chip}
                  />
                );
              })}
            </div>
          )}
        >
          {!isEmpty(tags) && tags.map(tag => (
            <MenuItem value={tag.id} key={tag.id}>
              <div className={classes.tag}>
                <img className={classes.tagImg} src={`/icons/tags/${tag.identifier}.svg`} alt={tag.identifier}/>
                <Typography style={{ ...typography.medium.regular }}>{tag.label}</Typography>
              </div>
            </MenuItem>
          ))}
        </Select>
      </div>
    );
  };

  const getCategoryField = () => {
    return (
      <div>
        <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("menu-editor-form-category-field-label")}</Typography>
        <div className={classes.selectWrapper}>
          <Select
            id={"select"}
            variant={"outlined"}
            name="category"
            value={form.category}
            onChange={onChange}
            style={{ borderRadius: 12, backgroundColor: palette.grayscale["100"], height: 44 }}
            IconComponent={ChevronDown20new}
            MenuProps={MenuProps}
            required
          >
            {itemCategories.map(category => (
              <MenuItem value={category.value}>
                {t(category.i18nKey)}
              </MenuItem>
            ))}
          </Select>
        </div>
      </div>
    );
  };

  const getItemBeverageFields = () => {
    return (
      <div style={{ display: "flex", maxWidth: 520, width: "100%", marginRight: 8, marginTop: 16 }}>
        <div style={{ flex: 1 }}>
          <Field
            className={classes.field}
            label={t("menu-editor-form-volume-field-label")}
            name="volume"
            value={form.volume}
            onChange={onChange}
          />
        </div>
        <div style={{ marginLeft: 16, flex: 1 }}>
          <Field
            className={classes.field}
            label={t("alcohol-percentage")}
            name="alcoholPercentage"
            value={form.alcoholPercentage}
            onChange={onChange}
            type="number"
          />
        </div>
      </div>
    );
  };

  const getRemarkField = () => {
    return (
      <div style={{ width: "100%" }}>
        <div className={classes.chipSelectWrapper} >
          <Select
            name="remarkAnnotations"
            value={form.remarkAnnotations || []}
            onChange={onRemarkAnnotationsChange}
            disableUnderline
            multiple
            IconComponent={ChevronDown20new}
            MenuProps={MenuProps}
            input={<Field id="select-multiple-chip" label={t("order-item-details-remarks-section-title")} select />}
            renderValue={(selected) => (
              <div className={classes.chips} style={{ display: "flex", flexWrap: "wrap" }}>
                {selected.map((value) => {
                  const remark = remarks.find(t => t.annotation === value) || {};
                  return (
                    <Chip
                      key={value}
                      label={`${remark.annotation}. ${t(remark.description)}`}
                      className={classes.chip}
                    />
                  );
                })}
              </div>
            )}
          >
            {remarks.map(remark => (
              <MenuItem value={remark.annotation} key={remark.annotation}>
                <div className={classes.tag}>
                  <Typography style={{ ...typography.medium.regular }}>{`${remark.annotation}. ${t(remark.description)}`}</Typography>
                </div>
              </MenuItem>
            ))}
          </Select>
        </div>
      </div>
    );
  };


  const [editingIngredients, setEditingIngredients] = useState(false);
  const openIngredientsModal = () => setEditingIngredients(true)
  const closeIngredientsModal = () => setEditingIngredients(false)
  const setIngredients = (value) => {
    setForm({ ...form, metadata: { consumptions: value } })
  }

  const getActions = () => (
    <div style={{ marginBottom: 20, background: palette.grayscale["300"], borderRadius: 12, padding: "16px", marginTop: 24 }}>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", borderBottom: `1px dashed ${palette.grayscale.border}`, paddingBottom: 12 }}>
        <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("duplicate-section-header-item")}</Typography>
        <div>
          <ButtonBase style={{ border: `1px solid ${palette.grayscale.border}`, padding: "6px 12px", borderRadius: 10 }}
                      onClick={duplicate} >
            <Typography style={{ ...typography.body.medium }}>{t("common-duplicate")}</Typography>
          </ButtonBase>
        </div>
      </div>
      <Typography style={{ ...typography.body.medium, marginBottom: 8, paddingTop: 12 }}>{t("delete-section-header-item")}</Typography>
      <div style={{ marginBottom: 12, display: "flex", justifyContent: "space-between", alignItems: "center", gap: 12 }} >
        <Typography style={{ ...typography.body.regular, marginRight: 2 }}>
          {t("menu-editor-this-action-deletes-item", {title: form.name})}
        </Typography>
        <IntegrationWarningConfirm
          title={t("menu-editor-warning")}
          hasWolt={hasWolt}
          hasUberEats={hasUberEats}
          hasLieferando={hasLieferando}
        >
          {IntegrationWarningConfirm => (
            <ButtonBase style={{ borderRadius: 10, padding: '6px 12px', background: palette.negative["500"] }} disableRipple disableTouchRipple onClick={IntegrationWarningConfirm(handleDelete)}>
              <TrashIcon20White/>
              <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"], marginLeft: 4 }}>
                {t('common-delete')}
              </Typography>
            </ButtonBase>
          )}
        </IntegrationWarningConfirm>
      </div>
    </div>
  )

  return (
    <Modal open={open}
           onClose={close}
           sticky
           variant="temporary"
           fullScreen
           style={{ marginTop: 16}}
           PaperProps={{ style: { ...shadows.large, zIndex: 1200, borderRadius: 20 } }}
    >
      {isValid ? (
        <IntegrationWarningConfirm
          title={t("menu-editor-warning")}
          hasWolt={hasWolt}
          hasUberEats={hasUberEats}
          hasLieferando={hasLieferando}
        >
          {IntegrationWarningConfirm => (
            <ModalBar title={form.id ? sliceText(form.name, 16) : t('menu-editor-actions-create-option-item')} onClose={close} onDone={IntegrationWarningConfirm(isValid && onSubmit)}/>
          )}
        </IntegrationWarningConfirm>
      ) : (<ModalBar title={form.id ? sliceText(form.name, 16) : t('menu-editor-actions-create-option-item')} onClose={close}/>
      )}
      <div style={{ display: "flex", flexDirection: "column", width: "100%", height: "100%", overflow: "hidden" }}>
        <div style={{ height: "100%", overflow: "auto" }}>
          <div style={{ display: "flex", flexDirection: isMobile ? "column" : "row", marginTop: 32, alignItems: "center", justifyContent: "center", marginLeft: 16, marginRight: 16 }}>
            <div className={clsx(classes.fieldGroup, isMobile && classes.smallFieldGroup)} style={{ borderBottom: `1px solid ${palette.grayscale["300"]}` }}>
              <div style={{ marginBottom: 24}}>
                <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("menu-editor-form-thumbnail-field-label")}</Typography>
                {getThumbnailUploader()}
              </div>
              <div style={{ marginBottom: 24 }}>
                {getOptionField()}
              </div>
              <div style={{ marginBottom: 24 }}>
                {getNumerationField()}
              </div>
              <div style={{ marginBottom: 8 }}>
                {getNameField()}
              </div>
              <div style={{ marginBottom: 24 }}>
                {getInternalNameField()}
              </div>
              <div style={{ marginBottom: 24 }}>
                {getDescriptionField()}
              </div>
              <div>
                {getPricesField()}
              </div>
              <div>
                {getQuantityField()}
              </div>
              <div style={{ display: "flex",flexDirection: isMobile ? "column" : "row", marginTop: 24, paddingBottom: 24, alignItems: "flex-start", justifyContent: "flex-start", borderBottom: isShowingAdvancedOptions ? `1px dashed ${palette.grayscale["400"]}`: null }}>
                <div>
                  <ButtonBase onClick={() => setIsShowingAdvancedOptions(!isShowingAdvancedOptions)}>
                    {isShowingAdvancedOptions ? ( <CollapseIcon20 style={{ marginRight: 5 }}/>) : (<ExpandIcon20 style={{ marginRight: 5 }}/> )}
                    <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"] }}>{ isShowingAdvancedOptions ? t("menu-editor-hide-advanced-options"): t("menu-editor-show-advanced-options")}</Typography>
                  </ButtonBase>
                </div>
              </div>
              {isShowingAdvancedOptions ? (
                <>
                  <div>
                    <div style={{ marginBottom: 24, marginTop: 24 }}>
                      {getCategoryField()}
                    </div>
                    {form.category === "BEVERAGE" && (
                      <div style={{ marginBottom: 24 }}>
                        {getItemBeverageFields()}
                      </div>
                    )}
                    <div style={{ marginBottom: 24 }}>
                      {getOrderField()}
                    </div>
                    <div style={{ marginBottom: 24 }}>
                      {getRemarkField()}
                    </div>
                    <div style={{ marginBottom: 24 }}>
                      {getCheckboxes()}
                    </div>
                    <div style={{ marginBottom: 24 }}>
                      {getServicesField()}
                    </div>
                    <div style={{ marginBottom: 24 }}>
                      {getTagsField()}
                    </div>
                    <div style={{
                      display: "flex",
                      flexDirection: isMobile ? "column" : "row",
                      marginTop: 24,
                      alignItems: isMobile ? "center" : "flex-start",
                      justifyContent: isMobile ? "center" : "flex-start",
                      borderBottom: `1px solid ${palette.grayscale["300"]}`
                    }}>
                      <div style={{
                        marginRight: isMobile ? null : 42,
                        marginBottom: isMobile ? 16 : 0,
                        width: isMobile ? "90%" : 241
                      }}>
                        <Typography style={{ ...typography.body.medium }}>{t("consumption")}</Typography>
                        <Typography style={{
                          ...typography.body.regular,
                          marginTop: 4
                        }}>{t("setup-the-ingredients-and-amounts-this-item-consumes")}</Typography>
                      </div>
                      <div className={clsx(classes.fieldGroup, isMobile && classes.smallFieldGroup)}>
                        <div style={{ marginBottom: 26 }}>
                          {!isEmpty(consumptions) && (
                            <div style={{
                              padding: 12,
                              borderRadius: 12,
                              width: "100%",
                              background: palette.grayscale["100"], ...shadows.base
                            }}>
                              {consumptions.map(it => {
                                const resolvedMeasurementUnit = measurementUnits[it.unit] ? t(measurementUnits[it.unit].i18nKey) : '-'
                                return (
                                  <div key={it.categoryId} style={{ display: "flex", justifyContent: "space-between" }}>
                                    <Typography>{it.name}</Typography>
                                    <Typography
                                      style={{ marginLeft: 4 }}>{it.amount} {resolvedMeasurementUnit}</Typography>
                                  </div>
                                )
                              })}
                            </div>
                          )}
                          <div style={{ marginTop: 24 }}>
                            <ButtonBase
                              style={{ padding: 11, border: `1px solid ${palette.grayscale.border}`, borderRadius: 10 }}
                              onClick={openIngredientsModal}>
                              <Typography style={{ ...typography.body.medium }}>
                                {t(isEmpty(consumptions) ? "setup-ingredients" : "update-ingredients")}
                              </Typography>
                            </ButtonBase>
                          </div>
                          {editingIngredients && (
                            <SetupMenuItemIngredientsModal
                              open={editingIngredients}
                              onClose={closeIngredientsModal}
                              ingredients={form?.metadata?.consumptions} setValue={setIngredients} />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  {values.id ? getActions() : null}
                </>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
};

export default withTranslation('common')(OptionItemPanel);
