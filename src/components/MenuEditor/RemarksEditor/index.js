import React, {useEffect, useState} from "react";
import TableContainer from '@material-ui/core/TableContainer';
import TableRow from '@material-ui/core/TableRow';
import TableCell from '@material-ui/core/TableCell';
import TableBody from '@material-ui/core/TableBody';
import Table from '@material-ui/core/Table';
import {withTranslation} from '../../../../i18n';
import useStyles, { useMenuStyles } from "./styles";
import { createRemark, deleteRemark, getRestaurantApps, resolveRemarks, updateRemark } from "../../../api";
import Button from '@material-ui/core/Button';
import RemarkPanel from './RemarkPanel';
import SecondaryBar from "../../_navigation/SecondaryBar";
import { menuEditorViews } from "../../../utils/const";
import isEmpty from "../../../utils/isEmpty";
import { NoItems80 } from "../../../utils/icons";
import EmptyScreen from "../../_placeholder/EmptyScreen";
import { Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import MoreOptionsButton from "../../_buttons/MoreOptionsButton";
import NotEditableBadge from "../../_tags/NotEditableBadge";
import { Confirm } from "../../Confirmation";
import MenuItem from "@material-ui/core/MenuItem";
import CustomMenu from "../../_popup/CustomMenu";

export const RemarksEditor = withTranslation('common')(({ t, restaurantId, remarks, setRemarks }) => {
  const classes = useStyles();
  const menuClasses = useMenuStyles();
  
  const [remark, setRemark] = useState(null);
  const [remarkMenu, setRemarkMenu] = useState({ anchor: null, remark: null });
  const openRemarkMenu = (e, remark) => setRemarkMenu({ anchor: e.currentTarget, remark })
  const closeRemarkMenu = () => setRemarkMenu({ anchor: null, remark: null });
  
  const [hasLieferando, setHasLieferando] = useState(false);
  const [hasUberEats, setHasUberEats] = useState(false);
  const [hasWolt, setHasWolt] = useState(false);
  
  useEffect(()=> {
    getRestaurantApps(restaurantId, "TAKEAWAY", true, ["lieferando", "wolt", "ubereats"])
      .then((response)=> {
        const hasLieferando = response.data.items.some(deliveryPartner => deliveryPartner.identifier === "lieferando" )
        const hasUberEats = response.data.items.some(deliveryPartner => deliveryPartner.identifier === "ubereats" )
        const hasWolt = response.data.items.some(deliveryPartner => deliveryPartner.identifier === "wolt" )
        
        setHasLieferando(hasLieferando);
        setHasUberEats(hasUberEats);
        setHasWolt(hasWolt);
      })
      .catch(()=>{})
  },[])
  
  const fetchRemarks = () => {
    resolveRemarks(restaurantId)
      .then(({ data = [] }) => {
        setRemarks(data);
      })
      .catch(() => {});
  };

  useEffect(() => {
    fetchRemarks();
  }, []);

  const submitRemark = (form) => {
    const call = form.id ? updateRemark : createRemark;
    if (form.id === "") {
      delete form.id;
    }
    call(restaurantId, form)
      .then(() => setRemark(null))
      .then(() => {
        fetchRemarks();
      })
      .catch(() => {});
  };

  const _deleteRemark = () => {
    if(remarkMenu && remarkMenu.remark && remarkMenu.remark.id){
      deleteRemark(restaurantId, remarkMenu.remark.id)
        .then(() => setRemark(null))
        .then(() => {
          fetchRemarks();
        })
        .catch(() => {});
    }
  }

  const isRemarkSelected = id => remark && remark.id === id;
  
  const startCreation = () => setRemark({ id: "" });
  
  const getContent = () => {
    if (isEmpty(remarks)) {
      return (
        <EmptyScreen
          icon={<NoItems80 />}
          titleI18nKey="no-allergens-and-additives"
          descriptionI18nKey="click-the-button-below-to-create"
          action={{ i18nKey: "common-create", onClick: startCreation }}
          tutorial={{
            url: "https://www.youtube.com/watch?v=NcRifDitRnX"
          }}
        />
      )
    }
    
    return (
      <div className={classes.content}>
        <TableContainer>
          <Table stickyHeader className={classes.table} aria-label="remarks editor table">
            <TableBody>
              <TableRow hover={false} className={classes.clickable} />
              {remarks.map(r => {
                const { id, annotation, description,type, global, shortText } = r;
                const hasShortText = !!shortText
                return (
                  <>
                    <TableRow key={id} hover selected={isRemarkSelected(id)} className={classes.clickable}>
                      <TableCell onClick={() => {setRemark(r)}}>
                        <div>
                          <Typography style={{...typography.body.medium}}>{description}</Typography>
                          {hasShortText ? (
                            <Typography style={{...typography.body.regular, color: palette.grayscale["600"]}}>{annotation} ∙ {type.toLowerCase()} ∙ {shortText}</Typography>
                          ) : (
                            <Typography style={{...typography.body.regular, color: palette.grayscale["600"]}}>{annotation} ∙ {type.toLowerCase()}</Typography>
                          )}
                        </div>
                       </TableCell>
                      <TableCell style={{ width: "1%", whiteSpace: "nowrap" }}>
                        <div style={{display: "flex", justifyContent: "flex-end"}}>
                          {!global && <MoreOptionsButton onClick={(e) => openRemarkMenu(e, r)}/>}
                          {global && <NotEditableBadge />}
                        </div>
                      </TableCell>
                    </TableRow>
                  </>
                )
              })}
            </TableBody>
          </Table>
        </TableContainer>
        <CustomMenu
          anchorEl={remarkMenu.anchor}
          keepMounted
          open={Boolean(remarkMenu.anchor)}
          onClose={closeRemarkMenu}
          classes={{
            paper: menuClasses.menu
          }}
        >
          <Confirm
            closeMenu={closeRemarkMenu}
            title={t("delete-remark-title")}
            body={(
              <Typography color="textSecondary" variant="body2">
                <br />
                {t("delete-table-description")}
                <br />
                {t("are-you-sure-description-to-delete-remark")}
              </Typography>
            )}
          >
            {confirm => (
              <MenuItem onClick={confirm(_deleteRemark)} classes={{ root: menuClasses.menuItem }}>
                <div className={menuClasses.content}>
                  <div className={menuClasses.left}>
                    <Typography style={{ ...typography.body.regular, color: palette.negative["500"] }}>
                      {t('common-delete')}
                    </Typography>
                  </div>
                </div>
              </MenuItem>
            )}
          </Confirm>
        </CustomMenu>
      </div>
        )
      };
  
  const getSecondaryBarActions = () => (
    <div style={{ display: "flex", alignItems: "center" }}>
      {!isEmpty(remarks) && (
        <Button className={classes.createBtn} onClick={startCreation} disableRipple>{t('new')}</Button>
      )}
    </div>
  )
  
  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        <SecondaryBar title={t(menuEditorViews.ALLERGENS_ADDITIVES.i18nKey)} right={getSecondaryBarActions()} contentStyle={{
          paddingLeft: 0,
          paddingRight: 0,
          marginTop: 0,
          borderBottom: 'none'
        }}/>
        {getContent()}
        {remark && <RemarkPanel open={remark} close={() => setRemark(null)} values={remark} _delete={_deleteRemark} submit={submitRemark} hasWolt={hasWolt} hasLieferando={hasLieferando} hasUberEats={hasUberEats}/>}
      </div>
    </div>
  )
});
