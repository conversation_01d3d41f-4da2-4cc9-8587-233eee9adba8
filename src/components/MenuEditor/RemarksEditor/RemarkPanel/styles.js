import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../../../styles/palette";

const drawerWidth = 240;

const useStyles = makeStyles(theme => ({
  content: {
    padding: theme.spacing(2)
  },
  actions: {
    textAlign: 'right',
    '& > button': {
      marginLeft: 8
    }
  },
  page: {
    padding: theme.spacing(2),
    minHeight: `calc(100vh - 49px)`
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    justifyContent: 'space-between'
  },
  toolbox: {
    marginLeft: theme.spacing(2)
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  spacing: {
    flex: 1
  },
  box: {
    padding: theme.spacing(1)
  },
  info: {
    marginTop: theme.spacing(2),
    overflow: 'hidden'
  },
  cursor: {
    cursor: 'pointer'
  },
  taken: {
    '&&': {
      background: '#FFBCBC'
    }
  },
  selected: {
    '&&': {
      border: '2px solid #dbdede'
    }
  },
  requestingPayment: {
    '&&': {
      background: '#FEF7EB',
      border: '2px solid #fde7a1'
    }
  },
  infoHeader: {
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
  },
  data: {
    padding: theme.spacing(2),
    display: 'flex',
    flexDirection: 'column'
  },
  field: {
    width: '100%'
  },
  capitalize: {
    textTransform: 'capitalize'
  },
  fieldGroup: {
    paddingBottom: theme.spacing(4),
    width: "520px"
  },
  smallFieldGroup: {
    paddingBottom: theme.spacing(4),
    width: "90%"
  },
  selectWrapper: {
    ["& .MuiSelect-select.MuiSelect-select"]: {paddingRight: '19px', borderColor: palette.grayscale["350"] },
    ["& .MuiSelect-select.MuiSelect-select:focus"]: {borderRadius:12, padding: "14px 19px 14px 12px"},
    ["& svg"]: {width:'12px', height: '7px', color: palette.grayscale["400"], position: 'absolute',
      right: 19,
      top: 'calc(50% - 2px)',
      pointerEvents: 'none',
    },
    ["& fieldset"]: { borderColor: palette.grayscale["350"] },
    iconOpen: {
      transform: 'rotate(180deg)',
    },
  },
}));

export default useStyles;
