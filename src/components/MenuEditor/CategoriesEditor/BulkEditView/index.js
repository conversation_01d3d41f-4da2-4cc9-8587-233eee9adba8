import React, { useState } from 'react'
import { withTranslation } from '../../../../../i18n'
import TableContainer from '@material-ui/core/TableContainer'
import Table from '@material-ui/core/Table'
import TableHead from '@material-ui/core/TableHead'
import useStyles from './styles'
import TableRow from '@material-ui/core/TableRow'
import TableCell from '@material-ui/core/TableCell'
import TableBody from '@material-ui/core/TableBody'
import Checkbox from '../../../_toggles/Checkbox'

const taxCategories = {
  'NORMAL': 'menu-editor-form-tax-category-field-option-normal',
  'REDUCED': 'menu-editor-form-tax-category-field-option-reduced'
}

const category = {
  'DISH': 'common-dish',
  'BEVERAGE': 'common-beverage'
}

const BulkEditView = ({
  t,
  items,
  toggleAllMenuItems,
  onSelectClickHandler,
  selectedMenuItems,
  onMenuItemClick
}) => {
  const classes = useStyles()

  const allMenusSelected = selectedMenuItems && items && selectedMenuItems.length >= items.length

  return (
    <React.Fragment>
      <TableContainer>
        <Table stickyHeader className={classes.table}>
          <TableHead>
            <TableRow>
              <TableCell style={{ width: 40 }}>
                <Checkbox onClick={toggleAllMenuItems} checked={allMenusSelected} />
              </TableCell>
              <TableCell>{t('name')}</TableCell>
              <TableCell>{t('category')}</TableCell>
              <TableCell>{t('menu-editor-form-dine-tax-category-field-label')}</TableCell>
              <TableCell>{t('menu-editor-form-takeaway-tax-category-field-label')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {items.map(item => (
              <TableRow hover key={`bulk_edit_${item.id}`} onClick={() => onMenuItemClick(item)}
                        style={{ cursor: 'pointer' }}>
                <TableCell>
                  <Checkbox
                    onClick={e => onSelectClickHandler(e, item.id)}
                    checked={selectedMenuItems.includes(item.id)}
                  />
                </TableCell>
                <TableCell>
                  {item.name}
                </TableCell>
                <TableCell>
                  {t([item.category])}
                </TableCell>
                <TableCell>
                  {t(taxCategories[item.dineTaxCategory])}
                </TableCell>
                <TableCell>
                  {t(taxCategories[item.takeawayTaxCategory])}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </React.Fragment>
  )
}

export default withTranslation('common')(BulkEditView)
