import React, { useCallback, useEffect, useState } from 'react'
import useStyles from './styles'
import { withTranslation } from '../../../../i18n'
import SecondaryBar from '../../_navigation/SecondaryBar'
import { resolveMenuGroups } from '../../../api'
import TableContainer from '@material-ui/core/TableContainer'
import Table from '@material-ui/core/Table'
import TableBody from '@material-ui/core/TableBody'
import TableRow from '@material-ui/core/TableRow'
import TableCell from '@material-ui/core/TableCell'
import Typography from '@material-ui/core/Typography'
import typography from '../../../../styles/typography'
import palette from '../../../../styles/palette'
import Button from '@material-ui/core/Button'
import GroupsPanel from './GroupsPanel'
import MoreOptionsButton from "../../_buttons/MoreOptionsButton";
import isEmpty from "../../../utils/isEmpty";
import { NoItems80 } from "../../../utils/icons";
import EmptyScreen from "../../_placeholder/EmptyScreen";

const GroupsEditorTable = ({
  t,
  menuGroups,
  onCreateHandler,
  onClickHandler
}) => {
  const classes = useStyles()
  
  if (isEmpty(menuGroups)) {
    return (
      <EmptyScreen
        icon={<NoItems80 />}
        titleI18nKey="no-menu-items"
        descriptionI18nKey="click-the-button-below-to-create"
        action={{ i18nKey: "common-create", onClick: onCreateHandler }}
        tutorial={{
          url: "https://www.youtube.com/watch?v=NcRifDitRnX"
        }}
      />
    )
  }
  
  return (
    <TableContainer>
      <Table
        stickyHeader
        className={classes.table}
        aria-label="menugroups editor table"
      >
        <TableBody>
          <TableRow hover={false} className={classes.clickable} />
          {
            menuGroups.map(menuGroup => {
              const {
                id,
                createdBy,
                internalNameI18n = {},
                nameI18n = {},
                menuIds
              } = menuGroup
              return (
                <TableRow
                  key={id}
                  hover
                  className={classes.clickable}
                  onClick={() => onClickHandler(menuGroup)}
                >
                  <TableCell>
                    <div style={{display: 'flex', flexDirection:'row', alignItems:'center', justifyContent: "space-between"}}>
                      <div>
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <Typography
                            style={{ ...typography.body.medium }}>
                            {internalNameI18n?.de || nameI18n?.de}
                          </Typography>
                          <div style={{ marginLeft: 6, borderRadius: 12, background: palette.grayscale["250"], minWidth: 20, height: 20, display: "flex", justifyContent: "center" }}>
                            <Typography style={{ ...typography.small.medium, color: palette.grayscale["800"], lineHeight: '20px', paddingLeft: 4, paddingRight: 4 }}>
                              {menuIds ? menuIds.length : 0}
                            </Typography>
                          </div>
                        </div>
                        {internalNameI18n?.de && (internalNameI18n?.de !== nameI18n?.de) && (
                          <Typography
                            style={{
                              ...typography.body.regular,
                              color: palette.grayscale['600']
                            }}>
                            {nameI18n.de}
                          </Typography>
                        )}
                      </div>
                      <div>
                        <MoreOptionsButton style={{ marginLeft: 12 }}/>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )
            })
          }
        </TableBody>
      </Table>
    </TableContainer>
  )
}

const GroupsEditor = ({
  t,
  restaurantId,
  menuGroups,
  setMenuGroups
}) => {
  const classes = useStyles()
  const [menuGroupPanel, setMenuGroupPanel] = useState(null);
  const fetchMenuGroups = () => {
    resolveMenuGroups(restaurantId)
      .then(({ data }) => {
        setMenuGroups(data.items)
      })
      .catch(() => {
      })
  }

  useEffect(() => {
    fetchMenuGroups()
  }, [])

  useEffect(() => {
    fetchMenuGroups()
  }, [menuGroupPanel])

  const onTableRowClickHandler = useCallback((data) => {
    setMenuGroupPanel(data)
  }, [])

  const getValues = () => {
    const data = {
      nameI18n: {},
      menuIds: [],
      internalNameI18n: {},
      id: null,
      imageUrl: "",
      showImage: false,
      widthSize: 1
    }
    if (isEmpty(menuGroupPanel) || menuGroupPanel.id === null) {
      return data;
    }
    data.nameI18n = menuGroupPanel?.nameI18n;
    data.internalNameI18n = menuGroupPanel?.internalNameI18n;
    data.menuIds = menuGroupPanel?.menuIds;
    data.id = menuGroupPanel?.id;
    data.imageUrl = menuGroupPanel?.imageUrl;
    data.showImage = menuGroupPanel?.showImage;
    data.widthSize = menuGroupPanel?.widthSize;
    return data;
  }

  const getSecondaryBarActions = () => {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center'
      }}>
        <Button
          className={classes.createBtn}
          disableRipple
          onClick={() => setMenuGroupPanel({})}
        >
          {t('new')}
        </Button>
      </div>
    )
  }

  return (
    <React.Fragment>
      <div className={classes.wrapper}>
        <div className={classes.container}>
          <SecondaryBar
            right={getSecondaryBarActions()}
            title={t('groups')}
            contentStyle={{
              paddingLeft: 0,
              paddingRight: 0,
              marginTop: 0,
              borderBottom: 'none'
            }}
          />
          <GroupsEditorTable onClickHandler={onTableRowClickHandler} t={t} menuGroups={menuGroups} onCreateHandler={() => setMenuGroupPanel({})}/>
        </div>
      </div>
      {menuGroupPanel && (
        <GroupsPanel
          restaurantId={restaurantId}
          open={menuGroupPanel}
          close={() => setMenuGroupPanel(null)}
          values={getValues()}
        />
      )}
    </React.Fragment>
  )
}

export default withTranslation('common')(GroupsEditor)
