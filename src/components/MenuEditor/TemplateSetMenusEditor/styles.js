import { makeStyles } from '@material-ui/core/styles'

const useStyles = makeStyles(theme => ({
  wrapper: {
    height: "100%"
  },
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    // paddingTop: 16,
    overflow: "hidden",
    paddingTop: 11
  },
  clickable: {
    cursor: 'pointer'
  },
  createBtn: {
    background: "#FF7C5C",
    fontStyle: "normal",
    fontWeight: "500",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#F9F9F9",
    borderRadius: 10,
    '&:hover': {
      background: "#FF7C5C",
    },
    textTransform: "capitalize",
    padding: '6px 16px'
  },
}))

export default useStyles;
