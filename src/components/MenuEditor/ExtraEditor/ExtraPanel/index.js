import React, { useEffect, useState } from "react";
import TextField from '@material-ui/core/TextField';
import { withTranslation } from '../../../../../i18n';
import Grid from '@material-ui/core/Grid';
import useStyles from './styles';
import Typography from '@material-ui/core/Typography';
import {
  CollapseIcon20,
  ExpandIcon20,
  MinusIcon20,
  PlusIcon20, TrashIcon20White
} from "../../../../utils/icons";
import { ButtonBase, useMediaQuery } from "@material-ui/core";
import { useSelector } from "react-redux";
import { configurationSelectors } from "../../../../../redux/selectors";
import shadows from "../../../../../styles/shadows";
import ModalBar from "../../../_navigation/ModalBar";
import palette from "../../../../../styles/palette";
import clsx from "clsx";
import typography from "../../../../../styles/typography";
import Field from "../../../form/Field";
import Modal from "../../../_popup/Modal"
import isEmpty from "../../../../utils/isEmpty";
import FieldWithTranslation from "../../../form/FieldWithTranslation";
import Checkbox from "../../../_toggles/Checkbox";
import { IntegrationWarningConfirm } from "../../../IntegrationWarningConfirm";


const data = {
  name: "",
  nameI18n: {},
  description: "",
  descriptionI18n: {},
  internalName: "",
  internalNameI18n: {},
  restricted: false,
  disabled: false,
  hidden: false,
  collapsed: false,
  max: 99,
  order: null,
  rounds: 0,
  printerIds: [],
  // base applies only for ongoing case, where base items should not be calculated in the qtd weather
  // user selected anything or not
  base: false
};

const ExtraPanel = ({ t, open, close, _delete, printers = [], values, submit, hasWolt, hasLieferando, hasUberEats }) => {
  const classes = useStyles();
  const isMobile = useMediaQuery("(max-width:900px)");

  const consolidatedData = isEmpty(values) ? data : { ...data, ...values };
  const [form, setForm] = useState(consolidatedData);
  
  const {configuration = {} } = useSelector(configurationSelectors.getConfiguration)
  const { printByAddition: printByAddition } = configuration
  const { hasOngoingItems} = configuration
  
  const [isShowingAdvancedOptions, setIsShowingAdvancedOptions] = useState(false)
  
  const [titleTranslated, setTitleTranslated] = useState(false);
  const [internalNameTranslated, setInternalNameTranslated] = useState(false);
  const [descriptionTranslated, setDescriptionTranslated] = useState(false);
  
  
  useEffect(() => {
    if (!isEmpty(values) && !isEmpty(values.internalNameI18n)) {
      setCustomizeInternal(true);
    }
  }, [])
  
  const onChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };
  const onTitleTranslationChange = () => {
    setTitleTranslated(!titleTranslated);
  };
  const onDescriptionTranslationChange = () => {
    setDescriptionTranslated(!descriptionTranslated);
  };
  const onInternalNameTranslationChange = () => {
    setInternalNameTranslated(!internalNameTranslated);
  };
  
  const onNameI18nChange = (e) => {
    const nameI18n = { ...form.nameI18n, [e.target.name]: e.target.value };
    setForm({ ...form, nameI18n })
  };

  const onInternalNameI18nChange = (e) => {
    const internalNameI18n = { ...form.internalNameI18n, [e.target.name]: e.target.value };
    setForm({ ...form, internalNameI18n })
  };
  
  const onResetInternalName = () => {
    setCustomizeInternal(!customizeInternal)
    if(customizeInternal){
      const internalNameI18n = {};
      setForm({ ...form, internalNameI18n })
    }
  }

  const onDescriptionI18nChange = (e) => {
    const descriptionI18n = { ...form.descriptionI18n, [e.target.name]: e.target.value };
    setForm({ ...form, descriptionI18n })
  };
  
  const onCheckboxUpdate = (name) => {
    setForm({ ...form, [name]: !form[name] });
  }

  const onSubmit = () => {
    form.name = form.nameI18n.de;
    form.internalName = form.internalNameI18n.de;
    form.description = form.descriptionI18n.de;
    submit(form)
  };
  
  const handleDelete = () => {
    _delete(form)
  }
  
  const isValid = !!form.nameI18n.de;
  
  const decrementMaxQuantity = (e) => {
    if (form.max === 1) {
      setForm({ ...form, max: 99 });
      return;
    }
    const updatedForm = { ...form, max: Math.max(1, form.max - 1) };
    setForm(updatedForm);
  }
  
  const incrementMaxQuantity = (e) => {
    const updatedForm = { ...form, max: form.max === 99 ? 1 : form.max + 1 };
    setForm(updatedForm)
  }

  const sliceText = (val = '', max) => val ? `${val.substring(0, max-1)}${val.length > max ? '...' : ''}` : '';

  const [customizeInternal, setCustomizeInternal] = useState(false);

  const togglePrinterSelection = (printerId) => {
      let updatedPrinterIds = [...form.printerIds]
      const idIncluded = updatedPrinterIds.includes(printerId)
      if (!idIncluded) {
        updatedPrinterIds.push(printerId)
      } else {
        updatedPrinterIds = updatedPrinterIds.filter(x => x !== printerId)
      }
      setForm({
        ...form,
        printerIds: updatedPrinterIds
      })
  }
  
  const getNameField = () => {
    return (
      <FieldWithTranslation
        label={t("menu-editor-form-title-field-label")}
        required
        placeholder={t('menu-editor-name-field-placeholder')}
        checked={titleTranslated}
        onCheckboxClick={onTitleTranslationChange}
        value={form.nameI18n}
        onChange={onNameI18nChange}
      />
    );
  };
  
  const getInternalNameField = () => {
    return (
      <div>
        <div className={classes.checkboxContainer} style={{ marginBottom: customizeInternal ? 24 : null, display: "flex", width: "fit-content" }}>
          <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "flex-end" }} onClick={() => onResetInternalName()} disableRipple disableTouchRipple>
            <Typography style={{ ...typography.body.regular, marginRight: 6 }}>
              {t('menu-editor-form-customize-internal-field-label')}
            </Typography>
            <Checkbox checked={customizeInternal} />
          </ButtonBase>
        </div>
        {customizeInternal && (
          <FieldWithTranslation
            label={t("menu-editor-form-internal-name-field-label")}
            placeholder={t('menu-editor-name-field-placeholder')}
            checked={internalNameTranslated}
            onCheckboxClick={onInternalNameTranslationChange}
            value={form.internalNameI18n}
            onChange={onInternalNameI18nChange}
          />
        )}
      </div>
    )
  }
  
  const getDescriptionField = () => {
    return (
      <FieldWithTranslation
        label={t("menu-editor-form-description-field-label")}
        multiline
        placeholder={t("menu-editor-description-field-placeholder")}
        checked={descriptionTranslated}
        onCheckboxClick={onDescriptionTranslationChange}
        value={form.descriptionI18n}
        onChange={onDescriptionI18nChange}
      />
    );
  };
  
  const getMaxQuantityField = () => {
    return (
      <div style={{ marginBottom: 22 }}>
        <div>
          <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("menu-editor-form-max-field-label")}</Typography>
          <div style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            height: 44,
            paddingRight: 12,
            paddingLeft: 12,
            borderRadius: 15,
            border: `1px solid ${palette.grayscale["350"]}`,
            backgroundColor: palette.grayscale["100"]
          }}>
            
            <ButtonBase disableRipple disableTouchRipple onClick={decrementMaxQuantity}>
              <MinusIcon20 />
            </ButtonBase>
            <TextField
              inputProps={{
                style: { textAlign: "center", color: palette.grayscale["800"] }
              }}
              className={classes.field}
              value={form.max}
              name="max"
              type="number"
              disabled
            />
            <ButtonBase disableRipple disableTouchRipple onClick={incrementMaxQuantity}>
              <PlusIcon20 />
            </ButtonBase>
          </div>
        </div>
      </div>
    );
  };
  
  const getCheckboxes = () => {
    return (
      <div>
        <div>
          <Typography style={{ ...typography.body.medium , marginBottom: 8 }}>{t("menu-editor-checkbox-section")}</Typography>
          <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
            <div style={{ display: "flex", width: "100%" }}>
              <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("restricted")} disableRipple disableTouchRipple>
                <Typography style={{ ...typography.body.medium }}>
                  {t('menu-editor-form-restricted-field-label')}
                </Typography>
                <Checkbox checked={form.restricted} />
              </ButtonBase>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], maxWidth: 400 }}>{t("restricted-field-menu-editor-explanation")}</Typography>
          </div>
          
          <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
            <div style={{ display: "flex", width: "100%" }}>
              <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("hidden")} disableRipple disableTouchRipple>
                <Typography style={{ ...typography.body.medium }}>
                  {t('menu-editor-form-hidden-field-label')}
                </Typography>
                <Checkbox checked={form.hidden} />
              </ButtonBase>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], maxWidth: 400 }}>{t("hidden-field-menu-editor-explanation")}</Typography>
          </div>
          
          <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
            <div style={{ display: "flex", width: "100%" }}>
              <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("disabled")} disableRipple disableTouchRipple>
                <Typography style={{ ...typography.body.medium }}>
                  {t('menu-editor-form-disabled-field-label')}
                </Typography>
                <Checkbox checked={form.disabled} />
              </ButtonBase>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], maxWidth: 400 }}>{t("disabled-field-menu-editor-explanation")}</Typography>
          </div>
          
          <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
            <div style={{ display: "flex", width: "100%" }}>
              <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("collapsed")} disableRipple disableTouchRipple>
                <Typography style={{ ...typography.body.medium }}>
                  {t('menu-editor-form-collapsed-field-label')}
                </Typography>
                <Checkbox checked={form.collapsed} />
              </ButtonBase>
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], maxWidth: 400 }}>{t("collapsed-field-menu-editor-explanation")}</Typography>
          </div>
        </div>
      </div>
    );
  };
  
  const getOrderField = () => {
    return (
      <div>
        <Field
          variant={"outlined"}
          tyle={{ borderRadius: 12, marginBottom: 8 }}
          className={classes.field}
          label={t("menu-editor-form-order-field-label")}
          value={form.order}
          name="order"
          onChange={onChange}
          type="number"
        />
      </div>
    );
  };
  
  const getRoundsField = () => {
    return (
      <div>
        <Field
          variant={"outlined"}
          style={{ borderRadius: 12, marginBottom: 8 }}
          className={classes.field}
          label={t("menu-editor-form-rounds-field-label")}
          value={form.rounds}
          name="rounds"
          onChange={onChange}
          type="number"
        />
      </div>
    );
  };
  
  const getOngoingItemField = () => {
    return (
      <div>
        <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
          <div style={{ display: "flex", width: "100%" }}>
            <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => onCheckboxUpdate("base")} disableRipple disableTouchRipple>
              <Typography style={{ ...typography.body.medium }}>
                {t('ongoing-item-base')}
              </Typography>
            </ButtonBase>
            <Checkbox checked={form.base} />
          </div>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], maxWidth: 400 }}>{t("ongoing-field-menu-editor-explanation")}</Typography>
        </div>
      </div>
    );
  };
  
  const getPrinterField = () => {
    return (
      <div>
        <Typography style={{ ...typography.body.medium, marginBottom: 8 }}> {t("menu-editor-form-printer-field-label")} </Typography>
        {(printers ?? []).filter(p => ["KITCHEN", "BAR"].indexOf(p.printerCategory) > -1).map(p => {
          const checked = (form.printerIds || []).indexOf(p.id) > -1;
          return (
            <div className={classes.checkboxContainer} style={{ marginBottom: 8, display: "flex", width: "100%", flexDirection: "column", justifyContent: "flex-start", alignItems: "flex-start" }}>
              <div style={{ display: "flex", width: "100%" }}>
                <ButtonBase style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }} onClick={() => togglePrinterSelection(p.id)} disableRipple disableTouchRipple>
                  <Typography style={{ ...typography.body.regular }}>
                    {p.label}
                  </Typography>
                  <Checkbox checked={checked} />
                </ButtonBase>
              </div>
            </div>
          );
        })}
      </div>
    );
  };
  
  
  const getActions = () => (
    <div style={{ paddingTop: 24, borderTop: `1px solid ${palette.grayscale.border}` }}>
      <div style={{ marginBottom: 20, background: palette.grayscale["300"], borderRadius: 12, padding: "16px" }}>
        <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t("delete-section-header-extra")}</Typography>
        <div style={{ marginBottom: 12 }} >
          <Typography style={{ ...typography.body.regular, flexGrow: 1 }}>
            {t("menu-editor-this-action-deletes-extra", {title: form.name})}
          </Typography>
        </div>
        <div>
          <IntegrationWarningConfirm
            title={t("menu-editor-warning")}
            hasWolt={hasWolt}
            hasUberEats={hasUberEats}
            hasLieferando={hasLieferando}
          >
            {IntegrationWarningConfirm => (
              <ButtonBase style={{ borderRadius: 12, padding: '6px 12px', background: palette.negative["500"] }} disableRipple disableTouchRipple onClick={IntegrationWarningConfirm(handleDelete)}>
                <TrashIcon20White/>
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"], marginLeft: 4 }}>
                  {t('common-delete')}
                </Typography>
              </ButtonBase>
            )}
          </IntegrationWarningConfirm>
        </div>
      </div>
    </div>
  );

  return (
    <Modal open={open}
           onClose={close}
           variant="temporary"
           fullScreen
           style={{ marginTop: 16 }}
           PaperProps={{ style: { ...shadows.large, zIndex: 1200, borderRadius: 20} }}
    >
      {isValid ? (
        <IntegrationWarningConfirm
          title={t("menu-editor-warning")}
          hasWolt={hasWolt}
          hasUberEats={hasUberEats}
          hasLieferando={hasLieferando}
        >
          {IntegrationWarningConfirm => (
            <ModalBar title={form.id ? form.name : t('menu-editor-actions-create-extra')} onClose={close} onDone={IntegrationWarningConfirm(isValid && onSubmit)} />
          )}
        </IntegrationWarningConfirm>
      ) : (<ModalBar title={form.id ? form.name : t('menu-editor-actions-create-extra')} onClose={close}/>
      )}
      <div style={{ display: "flex", flexDirection: "column", width: "100%", height: "100%", overflow: "hidden" }}>
        <div style={{ height: "100%", overflow: "auto" }}>
          <div style={{ display: "flex", flexDirection: "row", margin: isMobile ? "32px 16px 0px" : '32px auto 0px', alignItems: "flex-start", justifyContent: "center" }}>
            <div className={clsx(classes.fieldGroup, isMobile && classes.smallFieldGroup)}>
              <div style={{ marginBottom: 8 }}>
                {getNameField()}
              </div>
              <div style={{ marginBottom: 24 }}>
                {getInternalNameField()}
              </div>
              <div style={{ marginBottom: 24 }}>
                {getDescriptionField()}
              </div>
              <div style={{ marginBottom: 24 }}>
                {getMaxQuantityField()}
              </div>
              <div style={{ marginBottom: 24 }}>
                {getCheckboxes()}
                {hasOngoingItems &&
                  getOngoingItemField()
                }
              </div>
              <div style={{ display: "flex",flexDirection: isMobile ? "column" : "row", marginTop: 24, paddingBottom: 24, alignItems: "flex-start", justifyContent: "flex-start", borderBottom: isShowingAdvancedOptions ? `1px dashed ${palette.grayscale["400"]}` : null }}>
                <div>
                  <ButtonBase onClick={() => setIsShowingAdvancedOptions(!isShowingAdvancedOptions)}>
                    {isShowingAdvancedOptions ? ( <CollapseIcon20 style={{marginRight: 5}}/>) : (<ExpandIcon20 style={{marginRight: 5}}/> )}
                    <Typography style={{...typography.body.medium, color: palette.grayscale["600"]}}>{ isShowingAdvancedOptions ? t("menu-editor-hide-advanced-options"): t("menu-editor-show-advanced-options")}</Typography>
                  </ButtonBase>
                </div>
              </div>
              {isShowingAdvancedOptions ? (
                <>
                  <div style={{ marginTop: 24, marginBottom: 24 }}>
                    {getOrderField()}
                  </div>
                  <div style={{ marginBottom: 24 }}>
                    {getRoundsField()}
                  </div>
                  {printByAddition ? (
                    <div style={{ marginBottom: 24 }}>
                      {getPrinterField()}
                    </div> ): null }
                  {values.id ? getActions() : null}
                </>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
};

export default withTranslation('common')(ExtraPanel);
