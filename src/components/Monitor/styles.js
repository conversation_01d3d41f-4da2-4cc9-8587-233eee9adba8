import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../styles/palette";
import shadows from "../../../styles/shadows";

export const useBoardStyles = makeStyles(theme => ({
	container: {
		padding: 16,
		height: "100%",
		display: "flex",
		flexDirection: "column"
	},
	content: {
		display: "flex",
		flexDirection: "row",
		flex: 1,
		overflow: "hidden"
	},
	lane: {
		flex: 1,
		display: "flex",
		flexDirection: "column",
		"&+&": {
			marginLeft: 16
		}
	},
	group: {
		background: palette.grayscale["300"],
		borderRadius: 16,
		"&+&": {
			marginTop: 16
		},
		overflow: "hidden"
	},
	groupHeader: {
		display: "flex",
		alignItems: "center",
		padding: 16,
		cursor: "pointer"
	},
	cards: {
		paddingLeft: 12,
		paddingRight: 12,
		paddingBottom: 32 + 32,
		overflow: "auto",
		height: "100%",
	},
	card: {
		background: palette.grayscale["100"],
		padding: 12,
		...shadows.base,
		borderRadius: 12,
		'&+&': {
			marginTop: 16,
		},
		cursor: "pointer"
	}
}))

const useStyles = makeStyles(theme => ({
	wrapper: {
		height: "100%"
	},
	container: {
		display: "flex",
		flexDirection: "column",
		height: "100%"
	},
	content: {
		flex: 1,
		overflow: "hidden",
	},
	createBtn: {
		background: "#FF7C5C",
		fontStyle: "normal",
		fontWeight: "500",
		fontSize: "14px",
		lineHeight: "20px",
		letterSpacing: "-0.0014em",
		color: "#F9F9F9",
		borderRadius: 10,
		'&:hover': {
			background: "#FF7C5C",
		},
		textTransform: "capitalize",
		padding: '6px 16px'
	},
	clickable: {
		cursor: "pointer"
	}
}));

export default useStyles;
