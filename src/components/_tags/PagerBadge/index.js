import React from 'react';
import { Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import { InfoIcon, RevenueIcon16 } from "../../../utils/icons";
import { formatNumber } from "../../../utils/formatNumber";

const PagerBadge = ({ pagerIdentifier }) => {
  return (
    <div style={{
      border: `1px solid ${palette.grayscale.divider}`,
      borderRadius: 12,
      paddingTop: 1,
      paddingBottom: 1,
      paddingLeft: 4,
      paddingRight: 6,
      display: "flex",
      alignItems: "center"
    }}>
      <div style={{
        display: "flex",
        alignItems: "center",
      }}>
        <InfoIcon size={12} color={'#BAB9B8'} />
        <Typography style={{
          ...typography.small.medium,
          marginLeft: 4
        }}>{pagerIdentifier}</Typography>
      </div>
    </div>
  )
}

export default PagerBadge;
