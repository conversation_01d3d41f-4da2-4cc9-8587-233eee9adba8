import React from 'react';
import { Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import { RevenueIcon16 } from "../../../utils/icons";
import { formatNumber } from "../../../utils/formatNumber";

const RevenueBadge = ({ revenue }) => {
  
  return (
    <div style={{
      border: `1px solid ${palette.grayscale.divider}`,
      borderRadius: 12,
      paddingTop: 1,
      paddingBottom: 1,
      paddingLeft: 4,
      paddingRight: 6,
      display: "flex",
      alignItems: "center"
    }}>
      <div style={{
        display: "flex",
        alignItems: "center",
      }}>
        <RevenueIcon16 />
        <Typography style={{
          ...typography.small.medium,
          marginLeft: 2
        }}>{formatNumber(revenue)}€</Typography>
      </div>
    </div>
  )
}

export default RevenueBadge;
