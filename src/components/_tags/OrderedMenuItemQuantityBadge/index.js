import React from 'react';
import { Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import { withTranslation } from "../../../../i18n";

const OrderedMenuItemQuantityBadge = ({ t, icon, totalQuantity, pendingQuantity, color = "GREY_300_BG" }) => {
  const background = {
    GREY_100_BG: palette.grayscale["250"],
    GREY_200_BG: palette.grayscale["400"],
    GREY_300_BG: palette.grayscale["500"],
    GREY_LIGHT_BG: palette.grayscale["300"],
    ACTION: palette.primary["500"],
    WARNING: palette.secondary.yellow["100"],
    DEFAULT: palette.primary["200"]
  }
  
  const labelColor = {
    GREY_100_BG: palette.grayscale["800"],
    GREY_300_BG: palette.grayscale["100"],
    GREY_LIGHT_BG: palette.grayscale["800"],
    ACTION: palette.grayscale["100"],
    WARNING: palette.grayscale["800"]
  }
  
  const hasOnlyPendingQuantity = pendingQuantity > 0 && totalQuantity === 0
  const hasOnlyOrderedQuantity = totalQuantity > 0 && pendingQuantity === 0
  const hasBothPendingAndOrdered = (totalQuantity > 0 && pendingQuantity > 0)
  const rootPaddingLeft = hasBothPendingAndOrdered || hasOnlyOrderedQuantity ? 5 : 0;
  const rootPaddingRight = pendingQuantity > 0 ? 0 : 5;
  
  
  return (
    <div style={{
      paddingLeft: rootPaddingLeft,
      paddingRight: rootPaddingRight,
      background: background.GREY_300_BG,
      display: hasOnlyOrderedQuantity? "inline-block" : "flex",
      borderRadius: 12,
      minWidth: 17
    }}>
      {totalQuantity > 0 && (
        <Typography style={{
          ...typography.extraSmall.medium,
          color: labelColor[color],
          lineHeight: "16px",
          textAlign: "center",
          whiteSpace: "nowrap",
          paddingRight: hasOnlyOrderedQuantity? 0 : 1,
        }}>
          <span style={{ marginLeft: !!icon ? 4 : 0, marginRight: hasBothPendingAndOrdered? 4 : 0 }}>{totalQuantity}</span>
        </Typography>
      )}
      {pendingQuantity > 0 && (
        <div style={{
          paddingRight: 5,
          paddingLeft: 5,
          background: background[color],
          display: "inline-block",
          borderRadius: 12,
          minWidth: 17
        }}>
          <Typography style={{
            ...typography.extraSmall.medium,
            color:labelColor[color],
            lineHeight: "16px",
            textAlign: "center",
            whiteSpace: "nowrap",
          }}>
            <span>+{pendingQuantity}</span>
          </Typography>
        </div>
      )}
    </div>
  )
}

export default withTranslation("common")(OrderedMenuItemQuantityBadge);
