import React from 'react';
import { withTranslation } from '../../../../i18n';
import { Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";

const DisabledBadge = ({ t, title }) => {
  
  return (
    <div style={{
      background: palette.negative["100"],
      borderRadius: 12,
      paddingTop: 2,
      paddingBottom: 2,
      paddingLeft: 4,
      paddingRight: 6,
      display: "inherit",
      alignItems: "center"
    }}>
      <div style={{
        display: "flex",
        alignItems: "center",
      }}>
        <Typography style={{
          ...typography.small.medium,
          marginLeft: 2,
          whiteSpace: "nowrap"
        }}>{title}</Typography>
      </div>
    </div>
  )
}

export default withTranslation('common')(DisabledBadge);
