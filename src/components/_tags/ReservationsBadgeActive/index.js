import React from 'react';
import { Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import { ReservationsIconActive } from "../../../utils/icons";

const ReservationsBadgeActive = ({ reservations }) => {
  
  return (
    <div style={{
      border: `1px solid rgba(249, 249, 249, 0.2)`,
      borderRadius: 12,
      paddingTop: 1,
      paddingBottom: 1,
      paddingLeft: 4,
      paddingRight: 6,
      display: "flex",
      alignItems: "center"
    }}>
      <div style={{
        display: "flex",
        alignItems: "center",
      }}>
        <ReservationsIconActive />
        <Typography style={{
          ...typography.small.medium,
          marginLeft: 2,
          color: palette.grayscale["100"]
        }}>{reservations}</Typography>
      </div>
    </div>
  )
}

export default ReservationsBadgeActive;
