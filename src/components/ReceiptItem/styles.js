import { makeStyles } from '@material-ui/core/styles';
import { colors, drawer } from "../../../styles/theme";
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";

const useStyles = makeStyles(theme => ({
  name: {
    fontWeight: '500',
    lineHeight: '2',
    marginRight: theme.spacing(1)
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'baseline'
  },
  price: {
    whiteSpace: 'nowrap',
    fontWeight: '500',
  },
  actions: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
    display: 'flex',
    justifyContent: 'space-between'
  },
  statusChip: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1)
  },
  notes: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
    '& > div': {
      width: '100%'
    }
  },
  lightBold: {
    fontWeight: 400
  },
  addNoteBtn: {
    marginLeft: theme.spacing(2)
  },
  strikethrough: {
    textDecoration: 'line-through'
  },
  counterBtn: {
    '&&': {
      border: '1px solid rgba(0, 0, 0, 0.23)',
      float: 'right'
    }
  },
  quantityBtn: {
    float: 'right'
  },
  iconButton: {
    minWidth: 0,
    padding: 5,
    border: "1px solid #D9D9D8",
    borderRadius: "10px",
    "&:hover": {
      background: "transparent"
    },
    "&+&": {
      marginLeft: 8
    }
  }
}));

const newStyles = makeStyles((theme) => ({
  container: {
    overflow: 'auto',
    width: '100%',
    background: palette.grayscale["100"],
  },
  nestedContainerStyling: {
    marginTop: 12
  },
  containerStyling: {
    paddingTop: 12,
    paddingLeft: 12,
    paddingRight: 12,
  },
  borderBottom: {
    paddingBottom: 12,
    borderBottom: "1px solid #EFEFEE"
  },
  groupedContainerStyling: {
    borderRadius: 12,
    padding: 12,
    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.02)",
    '& ~ &': {
      marginTop: 12
    },
  },
  paid: {
    opacity: .5
  },
  layout: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'top',
    justifyContent: 'space-between',
    verticalAlign: 'middle'
  },
  text: {
    display: 'flex',
    flex: 1
  },
  body: {
    flex: 1
  },
  count: {
    minWidth: 20,
    marginRight: 8,
    paddingTop: 2,
  },
  countTableMove: {
    minWidth: 20,
    marginRight: 8,
    paddingTop: 0
  },
  countBadge: {
    ...typography.extraSmall.medium,
    background: "#E8E7E6",
    height: 16,
    paddingRight: 6,
    paddingLeft: 6,
    display: "flex",
    justifyContent: "center",
    borderRadius: 12
  },
  additionCount: {
    minWidth: 20,
    marginRight: 8
  },
  bodyGroup: {
    // paddingTop: theme.spacing(1),
    // paddingBottom: 6
  },
  nestedBodyGroup: {
    paddingTop: 0
  },
  itemGroup: {
    display: 'flex',
    alignItems: 'baseline',
    flexWrap: "wrap"
  },
  itemTextGroup: {
    flex: 1
  },
  name: {
    flex: 1
  },
  creationTime: {
    marginLeft: theme.spacing(2),
    fontSize: 12
  },
  notes: {
    color: colors.leviee.greyscale.midGray
  },
  actions: {
    marginTop: 8,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  },
  action: {
    '&+&': {
      marginLeft: 8
    }
  },
  price: {
    "& > p": {
      fontStyle: "normal",
      fontWeight: "normal",
      fontSize: "14px",
      lineHeight: "20px",
      color: "#333332"
    }
  },
  additionPrice: {
    color: colors.leviee.greyscale.midGray
  },
  strike: {
    textDecoration: 'line-through'
  },
  additionalGroup: {
    display: 'flex',
    flex: 1
  },
  additionTag: {
    "&+&": {
      marginLeft: 4
    }
  }
}));

export default useStyles;

export const useMenuStyles = makeStyles(theme => ({
  container: {},
  content: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    cursor: "pointer"
  },
  badge: {
    '& > span': {
      background: colors.leviee.main.dark
    }
  },
  avatarBtn: {
    padding: 0
  },
  avatar: {
    width: 24,
    height: 24,
    backgroundColor: "#929191",
    color: theme.palette.common.white,
    fontSize: 12,
    lineHeight: '12px',
    fontWeight: 500,
    textTransform: 'uppercase'
  },
  name: {
    marginLeft: 8,
    fontStyle: 'normal',
    fontWeight: 'normal',
    fontSize: '14px',
    lineHeight: '20px',
    letterSpacing: "-0.0014em"
  },
  left: {
    display: "flex",
    alignItems: "center",
  },
  right: {
    display: "flex",
    justifyContent: "flex-end"
  },
  menu: {
    backgroundColor: theme.palette.common.white,
    borderRadius: 12,
    width: `${drawer - 32}px`,
    maxWidth: "100%",
    boxShadow: "0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04)",
    maxHeight: 425 // shows half of the next menu item
  },
  menuItem: {
    padding: "12px 16px",
    "&:focus": {
      background: "transparent"
    }
  }
}));

export const useExpendableCardStyles = makeStyles((theme) => ({
  tags: {
    display: "flex",
    flexWrap: "wrap",
    gap: 4
  },
  tag: {
    background: "#EFEFEE",
    borderRadius: 12,
    fontWeight: 500,
    fontSize: 12,
    letterSpacing: "-1.2%",
    padding: "2px 6px",
    lineHeight: "16px",
    display: "flex"
  }
}));

export {
  newStyles
}
