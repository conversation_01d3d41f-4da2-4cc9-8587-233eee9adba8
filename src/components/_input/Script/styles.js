import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";

const useStyles = makeStyles(theme => ({
  preStyling: {
    fontFamily: 'Inter',
    ...typography.body.medium,
    color: palette.grayscale["700"],
    marginTop: 0,
    marginBottom: 0
  },
  preContainer: {
    backgroundColor: palette.grayscale["250"],
    padding: 11,
    borderRadius: 12,
    border: "1px solid #D8D7D6",
    overflow: "auto",
    minHeight: "42px"
  }
}));

export default useStyles;
