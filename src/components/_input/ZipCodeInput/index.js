import React, { useEffect, useState } from "react";
import { withTranslation } from "../../../../i18n";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import { ButtonBase } from "@material-ui/core";
import { CloseIcon20Gray } from "../../../utils/icons";
import { isEqual } from "lodash";

const ZipCodeInput = ({ t, values=[] , label = "", required, _delete }) => {
  
  const [zipCodes, setZipCodes] = useState(values);
  
  useEffect(() => {
    if(isEqual(zipCodes, values)){
      return
    }
    setZipCodes(values);
  }, [values])
  
  const onZipCodeDelete = (zipCode) => {
   _delete(zipCode)
  }
  
  const ZipCodeChip = ({ t, zipCode }) => {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent:"flex-end",
        borderRadius: 12,
        backgroundColor: palette.grayscale["250"],
        padding: '2px 2px 2px 6px',
        height: "fit-content"
      }}>
        <Typography style={{ ...typography.small.medium }}>{zipCode}</Typography>
        <ButtonBase style={{ marginLeft: 2 }} onClick={() => onZipCodeDelete(zipCode)}>
          <CloseIcon20Gray/>
        </ButtonBase>
      </div>
    )
  }
  
  return (
    <div>
      <div style={{ display: "flex" }}>
        {label && <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{label}</Typography>}
        {required && <Typography style={{ ...typography.body.medium, color: palette.primary["500"], marginBottom: 8 }}>*</Typography>}
      </div>
      <div style={{
        backgroundColor: palette.grayscale.white,
        borderRadius: 12,
        border: `1px solid ${palette.grayscale["350"]}`,
        display: "flex",
        alignItems: "center",
        minHeight: 44,
        padding: "8px 14px",
        gap: 6,
        flexWrap:"wrap"
      }}>
        {zipCodes.map(area => {
          const { zipCode } = area;
          if (zipCode === "") {
          } else
            return (
              <ZipCodeChip zipCode={zipCode} />
            );
        })}
      </div>
    </div>
  );
}

export default withTranslation('common')(ZipCodeInput);