import React, { useState } from "react";
import { i18n } from "../../../i18n";
import Typography from "@material-ui/core/Typography";
import { ChevronDown20, GlobeSimpleIcon20 } from "../../utils/icons";
import palette from "../../../styles/palette";
import FlexDiv from "../_div/FlexDiv";
import typography from "../../../styles/typography";
import { noop, systemLanguages } from "../../utils/const";
import { ButtonBase } from "@material-ui/core";
import useStyles from "./styles";
import { useRouter } from "next/router";
import shadows from "../../../styles/shadows";
import LanguageSelectorModal from "../_popup/LanguageSelectorModal";

const LanguageSelector = (props) => {
  const classes = useStyles();
  const router = useRouter();

  const [anchor, setAnchor] = useState(null);
  const openMenu = (e) => setAnchor(e.currentTarget);
  const closeMenu = () => setAnchor(null);

  const updateLanguage = (value) => () => {
    closeMenu();
    i18n.changeLanguage(value).then(noop).catch(noop);
  };

  return (
    <div>
      <ButtonBase
        data-testid={'button-language-selector'}
        style={{
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 8,
          paddingRight: 8,
          backgroundColor: palette.grayscale["100"],
          borderRadius: 10,
          ...shadows.base,
        }}
        onClick={openMenu}
        {...props}
      >
        <FlexDiv>
          <GlobeSimpleIcon20 />
          <Typography
            style={{
              ...typography.body.medium,
              color: palette.grayscale["800"],
              marginLeft: 4,
              marginRight: 6,
            }}
          >
            {systemLanguages?.[i18n?.language]?.label}
          </Typography>
          <ChevronDown20 />
        </FlexDiv>
      </ButtonBase>
      {anchor && <LanguageSelectorModal open={anchor} onClose={closeMenu} setValue={updateLanguage} t />}
    </div>
  );
};

export default LanguageSelector;
