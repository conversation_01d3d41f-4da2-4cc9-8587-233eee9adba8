import React, { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../../i18n";
import Typography from "@material-ui/core/Typography";
import {
  getRestaurantConfiguration,
  updateRestaurantConfigurationById,
  uploadImage
} from "../../../api";
import { configurationSelectors, restaurantSelectors } from "../../../../redux/selectors";
import Loading from "../../Loading";
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";
import useStyles from "./styles";
import Field from "../../form/Field";
import { ChevronDown20new, TrashIcon20White, UploadIcon } from "../../../utils/icons";
import Select from "@material-ui/core/Select";
import { MenuProps } from "../../../utils/const";
import MenuItem from "@material-ui/core/MenuItem";
import { ButtonBase, useMediaQuery } from "@material-ui/core";
import { isEmpty } from "lodash";
import { kioskPaymentOptions } from "../../../../redux/constants";
import Chip from "@material-ui/core/Chip";
import Dropzone from "react-dropzone";
import Compressor from "compressorjs";
import Switch from "../../_toggles/Switch";
import { systemLanguageOptions} from "../../../utils/const";

const configData = {
  paymentMethods: [],
  heroImageUrl: null,
  topImageUrl: null,
  languages: [],
  allowPromotions: false,
  allowGiftCards: false,
  enableBypassSinglePaymentMethod: false,
  enablePager: false,
  diningOptions: []
};

const kioskDiningOptions = [
  {
    value: "IN_HOUSE",
    i18nKey: "in-house-express-mode"
  }, {
    value: "TO_GO",
    i18nKey: "to-go"
 }
];

const KioskAppSettings = ({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  const [fetching, setFetching] = useState(false);
  const isMobile = useMediaQuery("(max-width:900px)");

  const { configuration = {} } = useSelector(configurationSelectors.getConfiguration);
  const { kioskConfig = {} } = configuration;

  const [configurationFrom, setConfigurationForm] = useState(configuration);
  const [form, setForm] = useState(configData);

  useEffect(() => {
    fetchConfiguration();
  }, [JSON.stringify(configuration)]);

  const fetchConfiguration = () => {
    getRestaurantConfiguration(restaurantId)
      .then(({ data = {} }) => {
        setForm(data?.kioskConfig ?? configData);
        setConfigurationForm(data);
      })
      .catch(() => {});
  };

  const updateConfiguration = (updatedConfiguration) => {
    const fullConfigForm = { ...configurationFrom, kioskConfig: updatedConfiguration };
    updateRestaurantConfigurationById(restaurantId, fullConfigForm)
      .then(() => fetchConfiguration())
      .catch(() => {});
  };

  const onChange = (key, value) => {
    const updatedForm = { ...form, [key]: value };
    updateConfiguration(updatedForm);
  };

  const onUpload = (key, file) => {
    new Compressor(file, {
      quality: 0.6,
      success(result) {
        uploadImage(restaurantId, result)
          .then(({ data }) => onChange(key, data))
          .catch(() => {});
      },
      error(err) {}
    });
  };

  const removeImage = (key) => {
    const updatedForm = { ...form, [key]: "" };
    updateConfiguration(updatedForm);
  };
  
  const getImageUploader = (labelKey, configKey) => {
    const imageStyles = {
      heroImageUrl: { width: "324px", height: "417.6px"  }, // Proportionally scaled from 1080x1392
      topImageUrl: { width: "414px", height: "216px" }, // Proportionally scaled from 920x480
    };

    return (
      <div>
        <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>{t(labelKey)}</Typography>
        <Dropzone
          onDrop={(acceptedFiles) =>
            !isEmpty(acceptedFiles) && onUpload(configKey, acceptedFiles[0])
          }
          accept={"image/jpeg, image/png, image/jpg, image/gif"}
          maxSize={5000000}
          style={{
            ...imageStyles[configKey],
            border: form[configKey] ? `1px dashed ${palette.grayscale["350"]}` : null,
            borderRadius: 12,
          }}
        >
          {({ getRootProps, getInputProps }) => (
            <section
              className={classes.uploaderWrapper}
              style={{
                ...imageStyles[configKey],
                border: form[configKey] ? `1px dashed ${palette.grayscale["350"]}` : null,
                borderRadius: 12,
              }}
            >
              <div
                {...getRootProps()}
                className={classes.uploaderContainer}
                style={
                  kioskConfig?.[configKey]
                    ? {
                      backgroundImage: `url('${kioskConfig[configKey]}')`,
                      ...imageStyles[configKey],
                      backgroundSize: "cover",
                    }
                    : null
                }
              >
                <input {...getInputProps()} />
                {!kioskConfig?.[configKey] && (
                  <>
                    <div style={{ marginTop: isMobile ? 10 : 30.5 }}>
                      <UploadIcon />
                    </div>
                    <div>
                      <Typography
                        style={{
                          ...typography.body.medium,
                          whiteSpace: "break-spaces",
                        }}
                      >
                        {t("menu-editor-form-thumbnail-field-placeholder")}
                      </Typography>
                      <Typography
                        style={{
                          ...typography.body.medium,
                          color: palette.grayscale["600"],
                          whiteSpace: "break-spaces",
                        }}
                      >
                        {t("menu-editor-items-thumbnail-field-description")}
                      </Typography>
                    </div>
                  </>
                )}
              </div>
            </section>
          )}
        </Dropzone>
        {kioskConfig?.[configKey] && (
          <div style={{ display: "flex", justifyContent: "flex-start", marginTop: 12 }}>
            <ButtonBase
              style={{
                borderRadius: 12,
                padding: "6px 12px",
                background: palette.negative["500"],
              }}
              onClick={() => removeImage(configKey)}
              disableRipple
              disableTouchRipple
            >
              <TrashIcon20White />
              <Typography
                style={{
                  ...typography.body.medium,
                  color: palette.grayscale["100"],
                  marginLeft: 4,
                }}
              >
                {t("menu-editor-form-thumbnail-field-clear-image-label")}
              </Typography>
            </ButtonBase>
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div>
      {fetching && <Loading />}
      <div className={classes.settings}>
        <div className={classes.settingHeader}>
          <Typography style={{ ...typography.body.medium }}>{t("kiosk-settings")}</Typography>
          <Typography
            style={{ ...typography.body.regular, color: palette.grayscale["600"] }}
          >
            {t("kiosk-settings-description")}
          </Typography>
        </div>
        {getImageUploader("heroImage", "heroImageUrl")}
        {getImageUploader("topImage", "topImageUrl")}
        <div className={classes.selectWrapper}>
          <Select
            value={kioskConfig?.paymentMethods ?? []}
            name={"paymentMethods"}
            onChange={(e) => {
              onChange("paymentMethods", e.target.value);
            }}
            MenuProps={MenuProps}
            IconComponent={ChevronDown20new}
            multiple
            input={<Field id="select-chip" label={t("payments")} select />}
            renderValue={(selected) => (
              <div style={{ display: "flex", flexWrap: "wrap" }}>
                {selected.map((value) => {
                  const paymentMethod = kioskPaymentOptions.find(t => t.key === value) || {};
                  return (
                    <Chip
                      key={value}
                      label={t(paymentMethod.i18nKey)}
                      style={{ margin: 2 }}
                    />
                  );
                })}
              </div>
            )}
          >
            {kioskPaymentOptions.map(option => (
              <MenuItem value={option.key} key={option.key}><Typography>{t(option.i18nKey)}</Typography></MenuItem>
            ))}
          </Select>
        </div>
        <div className={classes.selectWrapper}>
          <Select
            multiple
            value={form.languages ?? []}
            name="languages"
            onChange={(e) => {
              const selectedValues = e.target.value;
              if (selectedValues.length <= 4) {
                onChange(e.target.name, selectedValues);
              }
            }}
            MenuProps={MenuProps}
            IconComponent={ChevronDown20new}
            input={<Field id="select-language" label={t("select-language")} select />}
            renderValue={(selected) => (
              <div style={{ display: "flex", flexWrap: "wrap" }}>
                {selected.map((value) => {
                  const language = systemLanguageOptions.find((lang) => lang.value === value);
                  return (
                    <Chip key={value} label={language.label} style={{ margin: 2 }} />
                  );
                })}
              </div>
            )}
          >
            {systemLanguageOptions.map((option) => (
              <MenuItem value={option.value} key={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </div>
        {/* dining options kiosk */}
        <div className={classes.selectWrapper}>
          <Select
            value={kioskConfig?.diningOptions ?? []}
            name={"diningOptions"}
            onChange={(e) => {
              onChange("diningOptions", e.target.value);
            }}
            MenuProps={MenuProps}
            IconComponent={ChevronDown20new}
            multiple
            input={<Field id="select-chip" label={t("dining-options")} select />}
            renderValue={(selected) => (
              <div style={{ display: "flex", flexWrap: "wrap" }}>
                {selected.map((value) => {
                  const diningOption = kioskDiningOptions.find(t => t.value === value) || {};
                  return (
                    <Chip
                      key={value}
                      label={t(diningOption.i18nKey)}
                      style={{ margin: 2 }}
                    />
                  );
                })}
              </div>
            )}
          >
            {kioskDiningOptions.map(option => (
              <MenuItem value={option.value} key={option.value}><Typography>{t(option.i18nKey)}</Typography></MenuItem>
            ))}
          </Select>
        </div>
        
        {/*/!* enable promotions *!/*/}
        {/*<div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0 }}>*/}
        {/*  <div className={classes.flex}>*/}
        {/*    <div style={{ marginRight: 8 }}>*/}
        {/*      <Typography style={{ ...typography.body.medium }}>*/}
        {/*        {t("allow-promotions")}*/}
        {/*      </Typography>*/}
        {/*      <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>*/}
        {/*        {t("enable-promotions-kiosk")}*/}
        {/*      </Typography>*/}
        {/*    </div>*/}
        {/*    <Switch*/}
        {/*      checked={form.allowPromotions ?? false}*/}
        {/*      onClick={() =>*/}
        {/*        onChange("allowPromotions", !(form.allowPromotions ?? false))*/}
        {/*      }*/}
        {/*    />*/}
        {/*  </div>*/}
        {/*</div>*/}
        {/*/!* enable gift cards *!/*/}
        {/*<div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0 }}>*/}
        {/*  <div className={classes.flex}>*/}
        {/*    <div style={{ marginRight: 8 }}>*/}
        {/*      <Typography style={{ ...typography.body.medium }}>*/}
        {/*        {t("allow-gift-cards")}*/}
        {/*      </Typography>*/}
        {/*      <Typography*/}
        {/*        style={{*/}
        {/*          ...typography.body.regular,*/}
        {/*          color: palette.grayscale["600"],*/}
        {/*        }}*/}
        {/*      >*/}
        {/*        {t("enable-gift-cards-kiosk")}*/}
        {/*      </Typography>*/}
        {/*    </div>*/}
        {/*    <Switch*/}
        {/*      checked={form.allowGiftCards ?? false}*/}
        {/*      onClick={() =>*/}
        {/*        onChange("allowGiftCards", !(form.allowGiftCards ?? false))*/}
        {/*      }*/}
        {/*    />*/}
        {/*  </div>*/}
        {/*</div>*/}
        {/* enable tips */}
        <div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0 }}>
          <div className={classes.flex}>
            <div style={{ marginRight: 8 }}>
              <Typography style={{ ...typography.body.medium }}>
                {t("allow-tips-toggle-kiosk")}
              </Typography>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                {t("allow-tips-toggle-description-kiosk")}
              </Typography>
            </div>
            <Switch
              checked={form.tipEnabled ?? false}
              onClick={() =>
                onChange("tipEnabled", !(form.tipEnabled ?? false))
              }
            />
          </div>
        </div>
        
        {/*/!* enableBypassSinglePaymentMethod *!/*/}
        {/*<div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0 }}>*/}
        {/*  <div className={classes.flex}>*/}
        {/*    <div style={{ marginRight: 8 }}>*/}
        {/*      <Typography style={{ ...typography.body.medium }}>*/}
        {/*        {t("allow-bypassing-single-payment-method-toggle-kiosk")}*/}
        {/*      </Typography>*/}
        {/*      <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>*/}
        {/*        {t("allow-bypassing-single-payment-method-toggle-description-kiosk")}*/}
        {/*      </Typography>*/}
        {/*    </div>*/}
        {/*    <Switch*/}
        {/*      checked={form.enableBypassSinglePaymentMethod ?? false}*/}
        {/*      onClick={() =>*/}
        {/*        onChange("enableBypassSinglePaymentMethod", !(form.enableBypassSinglePaymentMethod ?? false))*/}
        {/*      }*/}
        {/*    />*/}
        {/*  </div>*/}
        {/*</div>*/}

        {/* enablePager */}
        <div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0 }}>
          <div className={classes.flex}>
            <div style={{ marginRight: 8 }}>
              <Typography style={{ ...typography.body.medium }}>
                {t("enable-pager")}
              </Typography>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                {t("allow-guests-to-assign-pager-to-order")}
              </Typography>
            </div>
            <Switch
              checked={form.enablePager ?? false}
              onClick={() =>
                onChange("enablePager", !(form.enablePager ?? false))
              }
            />
          </div>
        </div>



      </div>
    </div>
  );
};

export default withTranslation("common")(KioskAppSettings);

