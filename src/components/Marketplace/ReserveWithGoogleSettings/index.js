import React, { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../../i18n";
import { ButtonBase} from "@material-ui/core";
import Typography from "@material-ui/core/Typography";
import {
  updateRestaurantById
} from "../../../api";
import { restaurantSelectors } from "../../../../redux/selectors";
import Switch from "../../_toggles/Switch";
import Loading from "../../Loading";
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";
import { ArrowRight20, EditIcon20 } from "../../../utils/icons";
import isEmpty from "../../../utils/isEmpty";
import { restaurantActions } from "../../../../redux/actions";
import useStyles from "./styles";
import Field from "../../form/Field";
import TextUpdateModal from "../../_popup/TextUpdateModal";

const data = {
  googlePlaceId: "",
  googleReservationEnabled: false
};

const ReserveWithGoogleSettings = ({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  const [fetching, setFetching] = useState(false);
  
  const [form, setForm] = useState(data);
  
  useEffect(() => {
    updateForm(restaurant);
  }, [JSON.stringify(restaurant)]);
  
  const updateForm = (restaurant = {}) => {
    if (isEmpty(restaurant)) {
      return;
    }
    
    setForm({ ...form, ...restaurant });
  };
  
  const updateRestaurantSettings = (updatedForm) => {
    updateRestaurantById(restaurantId, updatedForm)
      .then(({ data: restaurantResponse }) => {
        updateForm(restaurantResponse);
        dispatch(restaurantActions.getRestaurant(restaurantId));
      })
      //dispatch --> updates store
      .catch((e) => {
        console.log(e);
      });
  };
  
  const handleToggle = (stateKey, value) => {
    setForm({ ...form, [stateKey]: value });
    updateRestaurantSettings({ ...form, [stateKey]: value });
  };
  
  const updateGooglePlaceId = (value) => {
    const updatedForm = { ...form, googlePlaceId: value };
    setForm(updatedForm);
    updateRestaurantSettings(updatedForm);
  };
  
  const [updatingGooglePlaceIdField, setUpdatingGooglePlaceIdField] = useState(false);
  
  const getGooglePlaceIdField = () => (
    <Fragment>
      <Field
        value={form.googlePlaceId}
        readOnly
      />
      <TextUpdateModal
        titleI18n={"google-place-id"}
        open={updatingGooglePlaceIdField}
        onClose={() => setUpdatingGooglePlaceIdField(false)}
        value={form.googlePlaceId}
        setValue={updateGooglePlaceId}
      />
    </Fragment>
  );
  
  const TableUpdateBtn = () => (
    <div style={{ display: "flex", alignItems: "center", paddingLeft: 10, paddingRight: 10, minWidth: 100 }}>
      <EditIcon20 />
      <Typography style={{
        ...typography.body.medium,
        color: palette.grayscale["600"],
        marginLeft: 2
      }}>{t("common-update")}</Typography>
    </div>
  );
  
  
  return (
    <div className={classes.settings}>
      {fetching && <Loading />}
      <div className={classes.settingsForm}>
        
        <div className={classes.settingHeader}>
          <Typography style={{ ...typography.body.medium }}>
            {t("reserve-with-google-settings")}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
            {t("control-settings-for-reserve-with-google-integration")}
          </Typography>
        </div>
        <div className={classes.settingBody}>
          {/*google place id*/}
          <div>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("google-place-id")}
                </Typography>
              </div>
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                          style={{ padding: 0 }} onClick={() => setUpdatingGooglePlaceIdField(true)}>
                <TableUpdateBtn />
              </ButtonBase>
            </div>
            <div style={{ marginTop: 12 }}>
              {getGooglePlaceIdField()}
            </div>
            <div style={{ marginTop: 8 }}>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], display: "flex", alignItems: "center" }}>
                {t("set-google-place-id-here")}
                <ArrowRight20 />
                <a href={'https://developers.google.com/maps/documentation/places/web-service/place-id'} target="_blank">
                  {t("google-place-id")}
                </a>
              </Typography>
            </div>
          </div>
          {/*enable reserve with google*/}
          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("enable-reservations")}
                </Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                  {t("allow-google-to-send-reservations-to-your-allo-app")}
                </Typography>
              </div>
              <Switch checked={restaurant.googleReservationEnabled ?? false}
                      onClick={() => handleToggle("googleReservationEnabled", !form.googleReservationEnabled)} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default withTranslation("common")(ReserveWithGoogleSettings);
