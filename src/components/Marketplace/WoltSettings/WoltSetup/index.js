import React, { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../../../i18n";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../styles/typography";
import palette from "../../../../../styles/palette";
import {
  deleteWoltStore, getRestaurantConfiguration,
  getWoltStore,
  registerWoltStore, updateRestaurantConfigurationById
} from "../../../../api";
import { restaurantSelectors } from "../../../../../redux/selectors";
import Loading from "../../../Loading";
import useStyles from "./styles";
import { ButtonBase } from "@material-ui/core";
import Field from "../../../form/Field";
import { ChevronDown20new, EditIcon20 } from "../../../../utils/icons";
import { Confirm } from "../../../Confirmation";
import WoltSetupUpdateModal from "../../../_popup/WoltSetupUpdateModal";
import { appActions, configurationActions } from "../../../../../redux/actions";
import Select from "@material-ui/core/Select";
import MenuItem from "@material-ui/core/MenuItem";
import TimeInSecondsUpdateModal from "../../../_popup/TimeInSecondsUpdateModal";

const data = {
  venueId: null,
  apiKey: null,
  basicUsername: null,
  basicPassword: null
}

const WoltSetup = ({ t, isManaged }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const [store, setStore] = useState(data);
  const { venueId, apiKey, basicUsername, basicPassword } = (store || {})
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const [fetching, setFetching] = useState(false);
  const [updating, setUpdating] = useState(false);
  
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);
  
  const [configurationForm, setConfigurationForm] = useState({partnerConfigs:[]});
  
  const { partnerConfigs } = configurationForm
  const dpIndex = (partnerConfigs ?? []).findIndex(config => config.partnerId === "WOLT");
  const hasPartnerConfig = partnerConfigs?.length && dpIndex !== -1
  
  const [updatingAutoRejectAcceptTime, setUpdatingAutoRejectAcceptTime] = useState(false)
  
  const fetchConfiguration = () => {
    getRestaurantConfiguration(restaurantId)
      .then(({ data = {} }) => {
        setConfigurationForm(data ?? {});
      })
      .catch(() => {
      });
    dispatch(configurationActions.getConfiguration(restaurantId));
  };
  
  useEffect(() => {
    fetchConfiguration();
  }, []);
  
  const handleCustomizedOrderHandling = (e) => {
    const deliveryPartner = e.target.name
    const action = e.target.value
    const dpIndex = (configurationForm.partnerConfigs ?? []).findIndex(config => config.partnerId === deliveryPartner);
    
    if (!configurationForm.partnerConfigs) {
      const updatedConfiguration = { ...configurationForm, partnerConfigs: [{ partnerId: deliveryPartner, unseenOrder: { action: e.target.value, waitingTimeInSeconds: 60 } }] };
      setConfigurationForm(updatedConfiguration);
      updateConfiguration(updatedConfiguration);
    }
    if (configurationForm.partnerConfigs) {
      if (dpIndex !== -1 && action === "NONE") {
        const filteredConfig = {...configurationForm, partnerConfigs: [...configurationForm.partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner)] }
        setConfigurationForm(filteredConfig);
        updateConfiguration(filteredConfig);
      }
      if (dpIndex !== -1 && action !== "NONE") {
        const partnerObject = configurationForm.partnerConfigs[dpIndex]
        const updatedPartnerObject = { ...configurationForm.partnerConfigs[dpIndex], unseenOrder: { ...partnerObject?.unseenOrder ?? {}, action : action, waitingTimeInSeconds: partnerObject.unseenOrder.waitingTimeInSeconds ?? 60 }}
        const updatedPartnerConfigs =[ ...configurationForm.partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner), updatedPartnerObject]
        const updatedConfiguration = { ...configurationForm, partnerConfigs: updatedPartnerConfigs }
        setConfigurationForm(updatedConfiguration);
        updateConfiguration(updatedConfiguration);
      }
      if (dpIndex === -1){
        // partnerId NOT in partnerConfig
        const updatedConfiguration = { ...configurationForm, partnerConfigs: [...configurationForm.partnerConfigs, { partnerId: e.target.name, unseenOrder: { action: action, waitingTimeInSeconds: 60 }}] };
        setConfigurationForm(updatedConfiguration);
        updateConfiguration(updatedConfiguration);
      }
    }
  }
  
  const updateTimeForCustomizeOrderHandling = (value, deliveryPartner) => {
    const dpIndex = (configurationForm.partnerConfigs ?? []).findIndex((dp => dp.partnerId === deliveryPartner))
    const partnerObject = configurationForm.partnerConfigs[dpIndex]
    const updatedPartnerObject = { ...configurationForm.partnerConfigs[dpIndex], unseenOrder: { ...partnerObject?.unseenOrder ?? {}, waitingTimeInSeconds : value  }}
    const updatedPartnerConfigs =[ ...configurationForm.partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner), updatedPartnerObject]
    const updatedConfiguration = { ...configurationForm, partnerConfigs: updatedPartnerConfigs }
    setConfigurationForm(updatedConfiguration)
    updateConfiguration(updatedConfiguration)
  }
  
  const updateConfiguration = (updatedConfiguration) => {
    updateRestaurantConfigurationById(restaurantId, updatedConfiguration)
      .then(() => {
        fetchConfiguration();
      })
      .catch(() => {
      })
  };
  
  const fetchWoltStore = () => {
    setFetching(true);
    getWoltStore(restaurantId)
      .then(({ data }) => {
        const { total, items } = (data || {})
        if (total && items) {
          setStore(items[0]);
        } else {
          setStore(data)
        }
      })
      .catch(() => {})
      .finally(() => setFetching(false))
  }
  
  const removeWoltStore = () => {
    deleteWoltStore(restaurantId).then(() => {
      setStore({});
      fetchWoltStore();
      dispatch(appActions.setNotification('integration-removed'))
    }).catch(() => {
      dispatch(appActions.setNotification('removing-integration-failed', 'error'))
    }).finally()
  }
  
  useEffect(() => {
    fetchWoltStore();
  }, [])
  
  const TableUpdateBtn = () => (
    <div style={{ display: "flex", alignItems: "center", paddingLeft: 10, paddingRight: 10, minWidth: 100 }}>
      <EditIcon20 />
      <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], marginLeft: 2}}>{t('common-update')}</Typography>
    </div>
  )
  
  const updateStore = (value) => {
    setUpdating(true);
    if (!value) {
      return
    }
    
    const { venueId: storeVenueId, apiKey: storeApiKey, basicUsername: storeBasicUsername, basicPassword: storeBasicPassword } = (value || {})
    
    registerWoltStore(restaurantId, storeVenueId, storeApiKey, storeBasicUsername, storeBasicPassword)
      .then(() => {
        fetchWoltStore();
        dispatch(appActions.setNotification('integration-setup-success'))
      })
      .catch(() => {
        dispatch(appActions.setNotification('integration-setup-failed', 'error'))
      })
      .finally(() => {
        setSettingsModalOpen(false);
        setUpdating(false);
      });
  }
  
  const getStoreFields = () => {
    const { venueId: storeVenueId, apiKey: storeApiKey, basicUsername: storeUsername, basicPassword: storePassword } = (store || {})
    
    return (
      <Fragment>
        <Field
          value={storeVenueId}
          disabled
          placeholder={t('venue-id')}
        />
        <Field
          style={{ marginTop: 12 }}
          value={storeApiKey}
          disabled
          placeholder={t('api-key')}
        />
        <Field
          style={{ marginTop: 12 }}
          value={storeUsername}
          disabled
          placeholder={t('username')}
        />
        <Field
          style={{ marginTop: 12 }}
          value={storePassword}
          disabled
          placeholder={t('password')}
        />
        <WoltSetupUpdateModal
          open={settingsModalOpen}
          onClose={() => setSettingsModalOpen(false)}
          value={{ venueId: storeVenueId, apiKey: storeApiKey, basicPassword: storePassword, basicUsername: storeUsername }}
          setValue={updateStore}
        />
      </Fragment>
    )
  }
  
  if (fetching || updating) {
    return <Loading />;
  }
  
  return (
    <div className={classes.settings}>
      <div style={{ marginTop: 16 }}>
        <div className={classes.settingHeader}>
          <Typography style={{ ...typography.body.medium }}>
            {t('wolt-setup')}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
            {t('setup-wolt-integration-using-venue-id-and-api-key')}
          </Typography>
        </div>
        <div className={classes.settingBody}>
          <div style={{ marginTop: 24 }}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <Typography style={{ ...typography.body.medium }}>
                {t('integration-info')}
              </Typography>
              {(!venueId || isManaged) && (
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setSettingsModalOpen(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              )}
            </div>
            <div style={{ marginTop: 12 }}>
              {getStoreFields()}
            </div>
          </div>
          {venueId && isManaged && (
            <div style={{ display: "flex", flexDirection: "column", alignItems: "top", marginTop: 32, paddingTop: 32, borderTop: '1px solid #E8E7E6' }}>
              <div style={{ marginRight: 0, marginBottom: 16, width: "100%" }}>
                <Typography style={{  ...typography.body.medium }}>{t('delete-integration')}</Typography>
                <Typography style={{  ...typography.body.regular, marginTop: 4 }}>{t('this-actions-is-not-reversible')}</Typography>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ display: "flex", width: "100%" }}>
                  <Confirm
                    title={t("delete-integration")}
                    description={t("are-you-sure-description-to-delete-item")}
                  >
                    {confirm => (
                      <ButtonBase style={{ padding: '12px 24px', width: "100%", background: palette.negative["500"], borderRadius: 12 }} disableRipple disableTouchRipple onClick={confirm(() => removeWoltStore())}>
                        <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                          {t('common-delete')}
                        </Typography>
                      </ButtonBase>
                    )}
                  </Confirm>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className={classes.settingsForm} style={{ flex: 1}}>
        <div className={classes.settingHeader}>
          <Typography style={{ ...typography.body.medium }}>
            {t('unseen-webshop-order-handling-label')}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
            {t('control-takeaway-settings')}
          </Typography>
        </div>
        <div className={classes.settingBody}>
          <div>
            <Typography style={{ ...typography.body.medium , marginBottom: 8 }}>{t("action-unseen-order")}</Typography>
          </div>
          <div className={classes.selectWrapper}>
            <Select
              id="customAction-select"
              name="WOLT"
              value={ hasPartnerConfig ? partnerConfigs[dpIndex]?.unseenOrder?.action : "NONE"}
              onChange={handleCustomizedOrderHandling}
              disableUnderline
              variant="outlined"
              classes={{ root: classes.select }}
              style={{borderRadius: 12, backgroundColor: palette.grayscale["100"], height: 44, borderColor: palette.grayscale["350"] }}
              IconComponent={ChevronDown20new}
              required
            >
              <MenuItem value="NONE">{t('none')}</MenuItem>
              <MenuItem value="ACCEPT">{t('accept')}</MenuItem>
              <MenuItem value="REJECT">{t('reject')}</MenuItem>
            </Select>
          </div>
          {dpIndex !== -1 &&
            <>
              <div className={classes.flex} style={{ marginTop: 8 }}>
                <div style={{ marginRight: 8, marginTop: 12 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("time-in-s-till-action")}</Typography>
                </div>
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingAutoRejectAcceptTime(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              </div>
              <Field
                value={ hasPartnerConfig ? partnerConfigs[dpIndex]?.unseenOrder?.waitingTimeInSeconds : 60}
                readOnly
              />
              <TimeInSecondsUpdateModal
                titleI18n={"configuration-editor-auto-reject-accept-time-field-label"}
                open={updatingAutoRejectAcceptTime}
                deliveryPartner={"WOLT"}
                onClose={() => setUpdatingAutoRejectAcceptTime(false)}
                value={hasPartnerConfig ? partnerConfigs[dpIndex]?.unseenOrder?.waitingTimeInSeconds : 60}
                setValue={updateTimeForCustomizeOrderHandling}
                type={"number"}
                fieldProps={{
                  type: "number"
                }}
              />
            </>
          }
        </div>
      </div>
    </div>
  )
}

export default withTranslation("common")(WoltSetup);
