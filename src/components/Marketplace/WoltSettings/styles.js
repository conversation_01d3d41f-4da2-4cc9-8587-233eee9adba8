import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../../styles/palette";
import shadows from "../../../../styles/shadows";
import typography from "../../../../styles/typography";


const useStyles = makeStyles(() => ({
  container:{
    //borderTop: "1px solid #E8E7E6",
    //paddingLeft: 12,
    //paddingRight: 12,
    height: "100vh"
  },
  content: {
    display: "flex",
    flexDirection: "row",
    height: "100%"
  },
  header: {
    display: 'flex',
    alignItems: 'center',
  },
  headerTitle: {
    ...typography.medium.semiBold,
    whiteSpace: "nowrap"
  },
  right: {
    paddingLeft: 12,
    overflow: "auto",
    width: "100%"
  },
  listItemRoot: {
    paddingTop: 12,
    paddingBottom: 12,
    borderRadius: 12
  },
  listItemText: {
    color: "#333332",
    fontSize: 14,
    lineHeight: "20px",
    fontWeight: "normal",
    '& > span': {
      fontSize: 14,
      lineHeight: "20px",
      fontWeight: "normal",
      color: "#333332",
    },
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 8
  },
    listItemSelected: {
      background: "#E8E7E6 !important"
    },
  left: {
    overflow: "auto",
    paddingRight: 12,
    borderRight: "1px solid #E8E7E6",
    flexBasis: "28%",
    maxWidth: 250,
    minWidth: 240,
  },
}))


export default useStyles;