import React, { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../../i18n";
import { ButtonBase, NativeSelect, TableHead, useMediaQuery } from "@material-ui/core";
import Typography from "@material-ui/core/Typography";
import {
  getAccounts,
  updateRestaurantById, updateRestaurantConfigurationById
} from "../../../api";
import { configurationSelectors, restaurantSelectors } from "../../../../redux/selectors";
import Switch from "../../_toggles/Switch";
import Loading from "../../Loading";
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";
import {
  ChevronDown20new,
  ClockIcon20,
  EditIcon20,
  EuroSymbolIcon20Light
} from "../../../utils/icons";
import isEmpty from "../../../utils/isEmpty";
import { configurationActions, restaurantActions } from "../../../../redux/actions";
import useStyles from "./styles";
import Field from "../../form/Field";
import TextUpdateModal from "../../_popup/TextUpdateModal";
import ZipCodeInput from "../../_input/ZipCodeInput";
import ZipCodeUpdateModal from "../../_popup/ZipCodeUpdateModal";
import { uniqBy } from "lodash/array";
import ZipCodeFieldsUpdateModal from "../../_popup/ZipCodeFieldsUpdateModal";
import PrepTimeFieldsUpdateModal from "../../_popup/PrepTimeFieldsUpdateModal";
import FormControl from "@material-ui/core/FormControl";
import Select from "@material-ui/core/Select";
import Chip from "@material-ui/core/Chip";
import MenuItem from "@material-ui/core/MenuItem";
import { Paragraph } from "../../Text";
import byId from "../../../utils/byId";
import TimeInSecondsUpdateModal from "../../_popup/TimeInSecondsUpdateModal";
import { webshopPaymentOptions } from "../../../../redux/constants";
import { MenuProps } from "../../../utils/const";
import GoogleMapsWrapper from "../../Map";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import calculateOpeningHours from "../../../utils/calculateOpeningHours";
import DayPeriodsUpdateModal from "../../_popup/DayPeriodsUpdateModal";
import removeUndefined from "../../../utils/removeUndefined";


const data = {
  hasScan: "",
  hasPickup: "",
  hasDelivery: "",
  takeawayThreshold: "",
  deliveryFee: "",
  hasDifferentWebshopHours: ""
};

const configurationData = {
  webshopConfig: {
    printWebshopUrlQRCodeOnFinalReceipt: false,
    printAddressQrCodeForDriver: false,
    incomingOrderSound: null,
    pollSoundNotificationAccountIds: [],
    promotionText: "",
    unseenOrder: {
      disabled: true,
      action: "ACCEPT",
      waitingTimeInSeconds: 60
    },
    disableTakeawayEmailCopy: true,
    disableNewOrderReceipt: false,
    pickupPaymentChannels: [],
    deliveryPaymentChannels:[],
    deliveryAreas: [],
    deliveryRadius: [],
    deliveryFeeMode: "KM_RADIUS",
    allowGiftCards: false,
    allowPromoCodes: false
  },
  preparationTimeInMinutes: {
    PICKUP: 45,
    DELIVERY: 45,
    DINE_IN: 45,
    EXPRESS: 45,
    WOLT: 10,
    UBER_EATS: 10,
    LIEFERANDO: 10
  }
}

const partners = [{
  value: "UBER_EATS",
  i18nKey: "UberEats",
}, {
  value: "WOLT",
  i18nKey: "Wolt"
}, {
  value: "LIEFERANDO",
  i18nKey: "Lieferando"
}];

const deliveryFeeModeOptions = [{
  value: "ZIP_CODES",
  i18nKey: "address-zip-code",
}, {
  value: "KM_RADIUS",
    i18nKey: "km-radius"
}];

const WebshopSettings = ({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery("(max-width:900px)");
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  const { configuration = {} } = useSelector(configurationSelectors.getConfiguration);
  const { webshopConfig = {} } = (configuration || {})
  
  const { printWebshopUrlQRCodeOnFinalReceipt, printAddressQrCodeForDriver, incomingOrderSound, pollSoundNotificationAccountIds, disableTakeawayEmailCopy, disableNewOrderReceipt } = (webshopConfig || {})
  const { deliveryAreas = [], deliveryRadius = [], pickupDiscountPercentage, unseenOrder = {}, pickupPaymentChannels = [], deliveryPaymentChannels = [], deliveryFeeMode, webshopHours = [], hasDifferentWebshopHours, allowGiftCards, allowPromoCodes } = (webshopConfig || {})
  const [pickupDiscountEnabled, setPickupDiscountEnabled] = useState(Boolean(pickupDiscountPercentage));
  
  useEffect(() => {
    setPickupDiscountEnabled(Boolean(pickupDiscountPercentage))
  }, [pickupDiscountPercentage])
  
  const [accounts, setAccounts] = useState([]);
  const [accountsById, setAccountsById] = useState({});
  
  const [fetching, setFetching] = useState(false);
  
  const [form, setForm] = useState(data);
  const [configurationForm, setConfigurationForm] = useState(configurationData);
  
  useEffect(() => {
    getAccounts(restaurantId, 100, 0).then(({ data }) => {
      setAccounts(data.items)
      setAccountsById(byId(data.items))
    }).catch(() => {});
  }, [])
  
  useEffect(() => {
    updateForm(restaurant);
  }, [JSON.stringify(restaurant)]);

  useEffect(() => {
    updateConfigurationForm(configuration);
  }, [JSON.stringify(configuration)]);
  
  const updateForm = (restaurant = {}) => {
    if (isEmpty(restaurant)) {
      return;
    }
    setForm({ ...form, ...restaurant });
  };
  
  const updateConfigurationForm = (config = {}) => {
    if (isEmpty(config)) {
      return;
    }
    setConfigurationForm({ ...configurationForm, ...config });
  };
  
  const updateWebshopSettings = (updatedForm) => {
    updateRestaurantById(restaurantId, updatedForm)
      .then(({ data: restaurantResponse }) => {
        updateForm(restaurantResponse);
        dispatch(restaurantActions.getRestaurant(restaurantId));
      })
      //dispatch --> updates store
      .catch((e) => {
        console.log(e);
      });
  };
  
  const updateWebshopConfigurationSettings = (updatedForm) => {
    updateRestaurantConfigurationById(restaurantId, updatedForm)
      .then(({ data: restaurantResponse }) => {
        updateConfigurationForm(restaurantResponse);
        dispatch(configurationActions.getConfiguration(restaurantId));
      })
      //dispatch --> updates store
      .catch((e) => {
        console.log(e);
      });
  };
  
  const handleToggle = (stateKey, value) => {
    setForm({ ...form, [stateKey]: value });
    updateWebshopSettings({ ...form, [stateKey]: value });
  };
  
  const handleWebshopConfigToggle = (stateKey, value) => {
    let configForm = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, [stateKey]: value } }
    if (stateKey === 'printWebshopUrlQRCodeOnFinalReceipt' && value) {
      configForm = { ...configForm, webshopConfig: { ...configForm?.webshopConfig, ['printAddressQrCodeForDriver']: false } }
    }
    if (stateKey === 'printAddressQrCodeForDriver' && value) {
      configForm = { ...configForm, webshopConfig: { ...configForm?.webshopConfig, ['printWebshopUrlQRCodeOnFinalReceipt']: false } }
    }
    setConfigurationForm(configForm);
    updateWebshopConfigurationSettings(configForm);
  };
  
  
  const updateCustomAction = (e) => {
    if (e.target.value === "NONE") {
      const filteredWebshopConfig = Object.fromEntries(
        Object.entries(webshopConfig).filter(([key]) => key !== "unseenOrder"))
      const updatedWebshopConfig = { ...configurationForm, webshopConfig: filteredWebshopConfig }
      setConfigurationForm(updatedWebshopConfig)
      updateWebshopConfigurationSettings(updatedWebshopConfig)
    } else {
      const updatedWebshopConfig = { ...configurationForm,
        webshopConfig: { ...configurationForm.webshopConfig,
          unseenOrder: { ...configurationForm.webshopConfig.unseenOrder,
            action: e.target.value,
            waitingTimeInSeconds: configurationForm.webshopConfig?.unseenOrder?.waitingTimeInSeconds ?? 60 }
        }
      }
      setConfigurationForm(updatedWebshopConfig)
      updateWebshopConfigurationSettings(updatedWebshopConfig)
    }
  };
  
  const updatePrepTimeFields = (prepTime) => {
    const configForm = { ...configurationForm, preparationTimeInMinutes: prepTime };
    setConfigurationForm(configForm);
    updateWebshopConfigurationSettings(configForm);
  }
  
  const updateWebshopConfig = (e) => {
    const updatedWebshopConfig = { ...configurationForm,
      webshopConfig: {...configurationForm.webshopConfig, [e.target.name]: e.target.value }
    };
    setConfigurationForm(updatedWebshopConfig)
    updateWebshopConfigurationSettings(updatedWebshopConfig);
  };
  
  const deleteZipCode = (zipCode) => {
    const filteredDeliveryAreas = configurationForm.webshopConfig.deliveryAreas.filter((area) => area.zipCode !== zipCode);
    const updatedConfigForm = { ...configurationForm, webshopConfig: { ...webshopConfig, deliveryAreas:  filteredDeliveryAreas } }
    setConfigurationForm(updatedConfigForm)
    updateWebshopConfigurationSettings(updatedConfigForm);
  }
  
  const updateDeliveryAreas = (zipCodes) => {
    const updatedDeliveryAreas = zipCodes.map(zip => ({ zipCode: zip, deliveryFee: 0, minOrderValue: 0 }))
    const updatedConfigForm = { ...configurationForm, webshopConfig: { ...webshopConfig, deliveryAreas: uniqBy([...deliveryAreas, ...updatedDeliveryAreas], "zipCode") } }
    setConfigurationForm(updatedConfigForm)
    updateWebshopConfigurationSettings(updatedConfigForm)
  }
  
  const updateDeliveryMinAndDeliveryFee = (key, deliveryZone) => {
    //deliveryZone is array with objects
    const updatedConfigForm = { ...configurationForm, webshopConfig: { ...webshopConfig, [key]: deliveryZone} }
    setConfigurationForm(updatedConfigForm)
    updateWebshopConfigurationSettings(updatedConfigForm)
  }
  
  const updatePickupDiscount = (discountValue) => {
    let value = discountValue
    if (value && value < 1) {
      value = null
    }
    if (value && value > 100) {
      value = 100
    }
    const updatedConfigForm = {...configurationForm, webshopConfig: {...webshopConfig, pickupDiscountPercentage: value}}
    setConfigurationForm(updatedConfigForm)
    if (!value) {
      setPickupDiscountEnabled(false)
    }
    updateWebshopConfigurationSettings(updatedConfigForm)
  }
  
  const resetPickupDiscount = () => {
    const updatedConfigForm = {...configurationForm, webshopConfig: {...webshopConfig, pickupDiscountPercentage: null}}
    setConfigurationForm(updatedConfigForm)
    updateWebshopConfigurationSettings(updatedConfigForm)
  }
  
  const updatePromotionText = (textValue) => {
    const updatedConfigForm = {...configurationForm, webshopConfig: {...webshopConfig, promotionText: textValue}}
    setConfigurationForm(updatedConfigForm)
    updateWebshopConfigurationSettings(updatedConfigForm)
  }
  
  const [updatingDeliveryAreaField, setUpdatingDeliveryAreaField] = useState(false);
  const [updatingDeliveryMinAndDeliveryFeePerArea, setUpdatingDeliveryMinAndDeliveryFeePerArea] = useState(false);
  const [updatingPrepTimeFields, setUpdatingPrepTimeFields] = useState(false);
  const [updatingAutoRejectAcceptTime, setUpdatingAutoRejectAcceptTime] = useState(false)
  const [updatingPickupDiscount, setUpdatingPickupDiscount] = useState(false);
  const [updatingPromotionText, setUpdatingPromotionText] = useState(false)
  const [updatingOpeningHoursDay, setUpdatingOpeningHoursDay] = useState(null);
  
  const updateSoundNotificationAccountIds = (e) => {
    let val = e.target.value;
    val = [...new Set(val ?? [])]
    
    const updatedWebshopConfig = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, pollSoundNotificationAccountIds: val }}
    setConfigurationForm(updatedWebshopConfig)
    updateWebshopConfigurationSettings(updatedWebshopConfig)
  };
  
  const updateTimeForCustomizeOrderHandling = (value) => {
    const updatedWebshopConfig = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, unseenOrder: { ...configurationForm.webshopConfig.unseenOrder, waitingTimeInSeconds: value } }}
    setConfigurationForm(updatedWebshopConfig)
    updateWebshopConfigurationSettings(updatedWebshopConfig)
  }

  const togglePickupDiscountEnabled = () => {
    if (pickupDiscountEnabled) {
      setPickupDiscountEnabled(false);
      resetPickupDiscount();
    } else {
      setPickupDiscountEnabled(true);
    }
  }
  
  const updateDeliveryMode = (e) => {
    const mode = e.target.value
    const isRadiusMode = mode === "KM_RADIUS"
    let updatedWebshopConfig
    
    if(isRadiusMode){
      delete configurationForm.webshopConfig.deliveryAreas
      updatedWebshopConfig = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, deliveryFeeMode: mode }}
      
    } else {
      delete configurationForm.webshopConfig.deliveryRadius
      updatedWebshopConfig = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, deliveryAreas: [], deliveryFeeMode: mode }}
      
    }
    
    setConfigurationForm(updatedWebshopConfig)
    updateWebshopConfigurationSettings(updatedWebshopConfig)
  };
  
  const updateOpeningHoursDayIntervals = (periods = []) => {
    const updatedOpeningHours = { ...webshopHours, [updatingOpeningHoursDay]: periods };
    const updatedConfig = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, webshopHours: removeUndefined(updatedOpeningHours)}};
    setConfigurationForm(updatedConfig)
    updateWebshopConfigurationSettings(updatedConfig)
  };
  
  const getDeliveryAreaField = () => (
    <Fragment>
      <ZipCodeInput
        _delete={deleteZipCode}
        values={configurationForm.webshopConfig.deliveryAreas}
      />
      <ZipCodeUpdateModal
        titleI18n={"zip-code-update-modal-title"}
        open={updatingDeliveryAreaField}
        onClose={() => setUpdatingDeliveryAreaField(false)}
        setValue={updateDeliveryAreas}
        type={"number"}
        fieldProps={{
          type: "number"
        }}
      />
    </Fragment>
  );
  
  const getDeliveryMinAndDeliveryFeePerArea = () => {
    return (
      <Fragment>
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr 1fr", alignItems: "center", justifyContent: "space-between" }} >
          <div style={{ display:"flex", alignItems:"center" }}>
            <Typography style={{ ...typography.body.medium }}>{t("pickup-order-creation-form-address-zipCode-field-label")}</Typography>
            <Typography style={{ color: palette.primary["500"] }}>*</Typography>
          </div>
          <div style={{ display:"flex", alignItems:"center", marginLeft: isMobile ? null : 3 }}>
            <Typography style={{ ...typography.body.medium }}>{t("webshop-settings-set-the-delivery-fee")}</Typography>
            <Typography style={{ color: palette.primary["500"] }}>*</Typography>
          </div>
          <div style={{ display:"flex", alignItems:"center", marginLeft: isMobile ? null : 5 }}>
            <Typography style={{ ...typography.body.medium }}>{t("webshop-settings-min-order-value-label")}</Typography>
            <Typography style={{ color: palette.primary["500"] }}>*</Typography>
          </div>
        </div>
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr 1fr", alignItems: "center", justifyContent: "space-between", gap: 8 }} >
          {!isEmpty(deliveryAreas) && (configurationForm.webshopConfig.deliveryAreas || []).map(area => {
            return (
             <>
                <Field
                  value={area.zipCode}
                  readOnly
                />
                <Field
                  value={area.deliveryFee}
                  readOnly
                  iconAdornEnd={< EuroSymbolIcon20Light />}
                />
                <Field
                  value={area.minOrderValue}
                  readOnly
                  iconAdornEnd={< EuroSymbolIcon20Light />}
                />
             </>
            );
          })}
        </div>
        <ZipCodeFieldsUpdateModal
          titleI18n={isMobile? "short-title-zipcode-fields-update-modal-title" : "zipcode-fields-update-modal-title"}
          open={updatingDeliveryMinAndDeliveryFeePerArea}
          onClose={() => setUpdatingDeliveryMinAndDeliveryFeePerArea(false)}
          values={configurationForm.webshopConfig.deliveryAreas || []}
          setValue={updateDeliveryMinAndDeliveryFee}
          type={"number"}
          fieldProps={{
            type: "number"
          }}
          deliveryFeeMode={deliveryFeeMode}
        />
      </Fragment>
    );
  };
  
  const getDeliveryMinAndDeliveryFeePerRadius = () => {
    return (
      <Fragment>
        <div style={{ marginTop: 24, maxWidth: 520 }}>
          {!isEmpty(deliveryRadius) && (
            <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr 1fr", alignItems: "center", justifyContent: "space-between" }} >
              <div style={{ display:"flex", alignItems:"center" }}>
                <Typography style={{ ...typography.body.medium }}>{t("km-radius")}</Typography>
                <Typography style={{ color: palette.primary["500"] }}>*</Typography>
              </div>
              <div style={{ display:"flex", alignItems:"center", marginLeft: isMobile ? null : 3 }}>
                <Typography style={{ ...typography.body.medium }}>{t("webshop-settings-set-the-delivery-fee")}</Typography>
                <Typography style={{ color: palette.primary["500"] }}>*</Typography>
              </div>
              <div style={{ display:"flex", alignItems:"center", marginLeft: isMobile ? null : 5 }}>
                <Typography style={{ ...typography.body.medium }}>{t("webshop-settings-min-order-value-label")}</Typography>
                <Typography style={{ color: palette.primary["500"] }}>*</Typography>
              </div>
            </div>
          )}
          <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr 1fr", alignItems: "center", justifyContent: "space-between", gap: 8 }} >
            {!isEmpty(deliveryRadius) && deliveryRadius.map((r, index) => {
              
              return (
                <>
                  <Field
                    value={deliveryRadius?.[index]?.radiusInKm ?? ""}
                    readOnly
                    name={"radiusInKm"}
                  />
                  <Field
                    value={deliveryRadius?.[index]?.deliveryFee ?? ""}
                    readOnly
                    name={"deliveryFee"}
                    iconAdornEnd={< EuroSymbolIcon20Light />}
                  />
                  <Field
                    value={deliveryRadius?.[index]?.minOrderValue ?? ""}
                    readOnly
                    name={"minOrderValue"}
                    iconAdornEnd={< EuroSymbolIcon20Light />}
                  />
                </>
              )
            })}
          </div>
        </div>
        <ZipCodeFieldsUpdateModal
          titleI18n={"km-radius"}
          open={updatingDeliveryMinAndDeliveryFeePerArea}
          onClose={() => setUpdatingDeliveryMinAndDeliveryFeePerArea(false)}
          values={configurationForm.webshopConfig.deliveryRadius || []}
          setValue={updateDeliveryMinAndDeliveryFee}
          type={"number"}
          fieldProps={{
            type: "number"
          }}
          deliveryFeeMode={deliveryFeeMode}
        />
      </Fragment>
    );
  };
  
  const getPrepTimeFields = () => (
    <Fragment>
      <div style={{display: "flex", flexDirection: "row", justifyContent:"space-between", gap: 12}}>
        <Field
          label={t("pickup-order-creation-form-type-field-pickup-option-label")}
          value={configurationForm.preparationTimeInMinutes.PICKUP}
          readOnly
          iconAdornEnd={< ClockIcon20 />}
        />
        <Field
          label={t("pickup-order-creation-form-type-field-delivery-option-label")}
          value={configurationForm.preparationTimeInMinutes.DELIVERY}
          readOnly
          iconAdornEnd={< ClockIcon20 />}
        />
      </div>
      <div style={{display: "flex", flexDirection: "row", justifyContent:"space-between", gap: 12,  marginTop: 8}}>
        {partners.map((partner)=> {
          return(
            <Field
              label={partner.i18nKey}
              value={configurationForm.preparationTimeInMinutes[partner.value]}
              readOnly
              iconAdornEnd={< ClockIcon20 />}
              type={"number"}
              fieldProps={{
                type: "number"
              }}
            />
          )
        })}
      </div>
      <PrepTimeFieldsUpdateModal
        titleI18n={"default-prep-time-header"}
        open={updatingPrepTimeFields}
        onClose={() => setUpdatingPrepTimeFields(false)}
        deliveryPartners={partners}
        values={configurationForm.preparationTimeInMinutes}
        setValue={updatePrepTimeFields}
      />
    </Fragment>
  );
  
  const getAutoRejectAcceptTakeawayOrderField = () => {
    return (
      <Fragment>
        <div style={{ marginTop: 16 }} className={classes.chipSelectWrapper}>
          <NativeSelect
            labelId="customAction-label"
            id="customAction-select"
            name="action"
            IconComponent={ChevronDown20new}
            value={configurationForm.webshopConfig?.unseenOrder?.action ?? "NONE"}
            onChange={updateCustomAction}
            style={{ maxWidth: 520 }}
            InputLabelProps={{
              shrink: true,
            }}
            inputProps={{
              name: 'action',
              id: 'customAction-selector'
            }}
            input={<Field label={t('unseen-webshop-order-handling-label')} />}
          >
            <option value="NONE">{t('none')}</option>
            <option value="ACCEPT">{t('accept')}</option>
            <option value="REJECT">{t('reject')}</option>
          </NativeSelect>
        </div>
        {configurationForm.webshopConfig?.unseenOrder &&
          <>
            <div className={classes.flex} style={{ maxWidth: 520 }}>
              <div style={{ marginRight: 8, marginTop: 20 }}>
                <Typography style={{ ...typography.body.medium }}>{t("time-in-s-till-action")}</Typography>
              </div>
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                          style={{ padding: 0 }} onClick={() => setUpdatingAutoRejectAcceptTime(true)}>
                <TableUpdateBtn />
              </ButtonBase>
            </div>
            <Field
              value={webshopConfig?.unseenOrder?.waitingTimeInSeconds ?? null }
              readOnly
              style={{ maxWidth: 520 }}
            />
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] , marginTop: 8, maxWidth: 520 }}>{t("set-time-for-auto-accept-or-reject-for-takeaway-order")}</Typography>
            <TimeInSecondsUpdateModal
              titleI18n={"configuration-editor-auto-reject-accept-time-field-label"}
              open={updatingAutoRejectAcceptTime}
              onClose={() => setUpdatingAutoRejectAcceptTime(false)}
              value={webshopConfig?.unseenOrder?.waitingTimeInSeconds ?? null }
              setValue={updateTimeForCustomizeOrderHandling}
              type={"number"}
              fieldProps={{
                type: "number"
              }}
            />
          </>
        }
      </Fragment>
    )
  };
  
  const getPickupDiscountField = () => (
    <Fragment>
      <div>
        <Field
          value={configurationForm.webshopConfig?.pickupDiscountPercentage}
          readOnly
          iconAdornEnd={"%"}
        />
        <TextUpdateModal
          titleI18n={"pickup-discount-field-label"}
          open={updatingPickupDiscount}
          onClose={() => setUpdatingPickupDiscount(false)}
          value={configurationForm.webshopConfig?.pickupDiscountPercentage}
          setValue={updatePickupDiscount}
          iconAdornEnd={"%"}
          type={"number"}
          fieldProps={{
            type: "number",
            min: 1
          }}
        />
      </div>
    </Fragment>
  )
  
  const getPromotionTextField = () => (
    <Fragment>
      <div>
        <Field
          value={configurationForm.webshopConfig.promotionText}
          readOnly
          multiline
        />
        <TextUpdateModal
          titleI18n={"promotion-text-field-label"}
          open={updatingPromotionText}
          onClose={() => setUpdatingPromotionText(false)}
          value={configurationForm.webshopConfig.promotionText}
          setValue={updatePromotionText}
          type={"text"}
          multiline
          fieldProps={{
            type: "text"
          }}
        />
      </div>
    </Fragment>
  )
  
  const getOpeningHoursTable = () => {
    const {
      MONDAY = [],
      TUESDAY = [],
      WEDNESDAY = [],
      THURSDAY = [],
      FRIDAY = [],
      SATURDAY = [],
      SUNDAY = []
    } = (webshopHours || {});
    
    const resolvedOpeningHours = webshopHours ?? {}
    
    return (
      <Fragment>
        <TableContainer stickyHeader>
          <Table style={{width: 520}}>
            <TableHead>
              <TableRow>
                <TableCell>{t("day")}</TableCell>
                <TableCell>{t("time-period")}</TableCell>
                <TableCell />
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow onClick={() => setUpdatingOpeningHoursDay("MONDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-monday")}</TableCell>
                <TableCell>{calculateOpeningHours(MONDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingOpeningHoursDay("TUESDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-tuesday")}</TableCell>
                <TableCell>{calculateOpeningHours(TUESDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingOpeningHoursDay("WEDNESDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-wednesday")}</TableCell>
                <TableCell>{calculateOpeningHours(WEDNESDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingOpeningHoursDay("THURSDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-thursday")}</TableCell>
                <TableCell>{calculateOpeningHours(THURSDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingOpeningHoursDay("FRIDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-friday")}</TableCell>
                <TableCell>{calculateOpeningHours(FRIDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingOpeningHoursDay("SATURDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-saturday")}</TableCell>
                <TableCell>{calculateOpeningHours(SATURDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingOpeningHoursDay("SUNDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-sunday")}</TableCell>
                <TableCell>{calculateOpeningHours(SUNDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn />
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
        {!!updatingOpeningHoursDay && (
          <DayPeriodsUpdateModal
            open={updatingOpeningHoursDay}
            onClose={() => setUpdatingOpeningHoursDay(null)}
            value={resolvedOpeningHours[updatingOpeningHoursDay]}
            setValue={updateOpeningHoursDayIntervals}
          />
        )}
      </Fragment>
    );
  };
  
  const TableUpdateBtn = () => (
    <div style={{ display: "flex", alignItems: "center", paddingLeft: 10, paddingRight: 10, minWidth: 100 }}>
      <EditIcon20 />
      <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], marginLeft: 2 }}>{t("common-update")}</Typography>
    </div>
  );
  
  return (
    <div className={classes.settings}>
      {fetching && <Loading />}
      <div style={{ display: "flex", flexDirection: "column", alignItems: "top", maxWidth: 520 }}>
        <div style={{ marginRight: 0 , marginBottom: 32, width: " 100%"  }}>
          <Typography style={{ ...typography.body.medium }}>{t("webshop-settings")}</Typography>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("control-settings-for-webshop")}</Typography>
        </div>
        <div style={{ flex: 1 }}>
          {/* webshop opening hours */}
            <div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0, marginBottom: 24 }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("webshop-opening-hours")}</Typography>
                  <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("enable-to-set-different-opening-hours-for-webshop")}</Typography>
                </div>
                <Switch checked={hasDifferentWebshopHours ?? false} onClick={() => handleWebshopConfigToggle("hasDifferentWebshopHours", !hasDifferentWebshopHours)} />
              </div>
            </div>
          {hasDifferentWebshopHours && (
            <div style={{ marginTop: 12,  maxWidth: 520, marginBottom: 24 }}>
              <Typography style={{ ...typography.body.medium }}>
                {t('webshop-opening-hours')}
              </Typography>
              <div style={{marginTop:16}}>
                {getOpeningHoursTable()}
              </div>
            </div>
          )}
          {/*set takeaway default prep time*/}
          <div style={{ marginTop: 0 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("default-prep-time-header")}
                </Typography>
              </div>
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                          style={{ padding: 0 }} onClick={() => setUpdatingPrepTimeFields(true)}>
                <TableUpdateBtn />
              </ButtonBase>
            </div>
            <div style={{ marginTop: 12 }}>
              {getPrepTimeFields()}
            </div>
            <div style={{ marginTop: 8 }}>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                {t("set-default-prep-time-description")}
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <div style={{ display: "flex", flexDirection:  "column" , alignItems: "top", marginTop: 32, paddingTop: 32, borderTop: '1px solid #E8E7E6', width: isMobile ? null : 520 }}>
        <div style={{ flex: 1, maxWidth: 520 }}>
          {/* gift cards in webshop */}
          <div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0, marginBottom: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("gift-cards-in-webshop")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("enable-the-use-of-gift-cards-in-webshop")}</Typography>
              </div>
              <Switch checked={allowGiftCards ?? false} disabled onClick={() => handleWebshopConfigToggle("allowGiftCards", !allowGiftCards)} />
            </div>
          </div>
          {/* promo codes in webshop */}
          <div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0, marginBottom: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("promo-codes-in-webshop")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("enable-the-use-of-promo-codes-in-webshop")}</Typography>
              </div>
              <Switch checked={allowPromoCodes ?? false} disabled onClick={() => handleWebshopConfigToggle("allowPromoCodes", !allowPromoCodes)} />
            </div>
          </div>
          {/*enable takeaway orders sound*/}
          <div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("new-order-sound")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("enable-sound-notification-for-any-incoming-takeaway-order")}</Typography>
              </div>
              <Switch checked={incomingOrderSound ?? false} onClick={() => handleWebshopConfigToggle("incomingOrderSound", configurationForm.webshopConfig.incomingOrderSound ? null : 1)} />
            </div>
          </div>
          {incomingOrderSound &&
          <div style={{ marginTop: 24 }}>
            <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper}>
              <Select
                name="pollSoundNotificationAccountIds"
                value={pollSoundNotificationAccountIds || []}
                onChange={updateSoundNotificationAccountIds}
                IconComponent={ChevronDown20new}
                multiple
                MenuProps={MenuProps}
                input={<Field id="select-multiple-chip"  label={t('sound-notification-polling-accounts')} select />}
                renderValue={(selected) => (
                  <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                    {selected.map((value) => {
                      const acc = accountsById[value] || {};
                      return (
                        <Chip
                          key={value}
                          label={`${acc.firstName || ""} ${acc.lastName || ""}`}
                          style={{ margin: 2 }}
                        />
                      )
                    })}
                  </div>
                )}
              >
                {accounts.map(account => (
                  <MenuItem value={account.id} key={account.id}>
                    <div className={classes.tag}>
                      <Paragraph>{`${account.firstName || ""} ${account.lastName || ""}`}</Paragraph>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <div style={{ marginTop: 8 }}>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                {t("set-sound-notification-accounts-description")}
              </Typography>
            </div>
          </div>
          }
          {/*enable custom action for incoming unseenOrder*/}
          {getAutoRejectAcceptTakeawayOrderField()}
          {/*enable print qr code in the receipt*/}
          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("enable-webshop-qr-in-receipt")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("print-qr-of-webshop-in-final-receipt")}</Typography>
              </div>
              <Switch checked={printWebshopUrlQRCodeOnFinalReceipt ?? false} onClick={() => handleWebshopConfigToggle("printWebshopUrlQRCodeOnFinalReceipt", !configurationForm.webshopConfig.printWebshopUrlQRCodeOnFinalReceipt)} />
            </div>
          </div>
          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("enable-address-qr-in-receipt")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("print-qr-of-google-maps-directions-in-receipt")}</Typography>
              </div>
              <Switch checked={printAddressQrCodeForDriver ?? false} onClick={() => handleWebshopConfigToggle("printAddressQrCodeForDriver", !configurationForm.webshopConfig.printAddressQrCodeForDriver)} />
            </div>
          </div>
          {/*enable new order notification receipt*/}
          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("enable-reminder-bon-printing")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("print-reminder-bon")}</Typography>
              </div>
              <Switch checked={disableNewOrderReceipt ?? false} onClick={() => handleWebshopConfigToggle("disableNewOrderReceipt", !configurationForm.webshopConfig.disableNewOrderReceipt)} />
            </div>
          </div>
          {/*enable takeaway email copy*/}
          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("takeaway-email-copy-toggle")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("takeaway-email-copy-toggle-description")}</Typography>
              </div>
              <Switch checked={disableTakeawayEmailCopy ?? true}
                      onClick={() => handleWebshopConfigToggle("disableTakeawayEmailCopy", !configurationForm.webshopConfig.disableTakeawayEmailCopy )} />
            </div>
          </div>
        </div>
      </div>
      <div style={{ display: "flex", flexDirection:  "column" , alignItems: "top", marginTop: 32, paddingTop: 32, borderTop: '1px solid #E8E7E6', width: isMobile ? null : 520 }}>
        <div style={{ flex: 1 }}>
          {/*enable pickup*/}
          <div className={classes.switchSetting} style={{ maxWidth: 520, marginTop: 0 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("pick-up-toggle-header")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("enable-pickup-toggle-description")}</Typography>
              </div>
              <Switch checked={restaurant.hasPickup ?? false} onClick={() => handleToggle("hasPickup", !form.hasPickup)} />
            </div>
          </div>
          {/*chose payment methods pickup*/}
          <div style={{ maxWidth: 520, marginTop: 12 }}>
            <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper} >
              <Select
                value={pickupPaymentChannels ?? []}
                name={"pickupPaymentChannels"}
                onChange={updateWebshopConfig}
                MenuProps={MenuProps}
                IconComponent={ChevronDown20new}
                multiple
                input={<Field id="select-chip" label={t("payment-option-pickup")} select />}
                renderValue={(selected) => (
                  <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                    {selected.map((value) => {
                      const paymentMethod = webshopPaymentOptions.find(t => t.key === value) || {}
                      return (
                        <Chip
                          key={value}
                          label={t(paymentMethod.i18nKey)}
                          style={{ margin: 2 }}
                        />
                      )
                    })}
                  </div>
                )}
              >
                {webshopPaymentOptions.map(paymentChannel => (
                  <MenuItem value={paymentChannel.key} key={paymentChannel.key}>
                    <div className={classes.tag}>
                      <Typography>{t(paymentChannel.i18nKey)}</Typography>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
          <div className={classes.switchSetting} style={{ maxWidth: 520 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("pickup-discount-toggle")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("enable-discount-on-pickup-orders")}</Typography>
              </div>
              <Switch checked={pickupDiscountEnabled} onClick={togglePickupDiscountEnabled} />
            </div>
          </div>
          {pickupDiscountEnabled && (
            <>
              <div className={classes.flex} style={{ marginTop: 24 }}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>
                    {t("pickup-discount-field-label")}
                  </Typography>
                </div>
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingPickupDiscount(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              </div>
              <div style={{ marginTop: 12, maxWidth: 520 }}>
                {getPickupDiscountField()}
              </div>
              {/*Promotion area*/}
              <div className={classes.flex} style={{ marginTop: 24 }}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>
                    {t("promotion-text-field-label")}
                  </Typography>
                </div>
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingPromotionText(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              </div>
              <div style={{ maxWidth: 520, marginTop: 12 }}>
                {getPromotionTextField()}
              </div>
              <div style={{ marginTop: 8 }}>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                  {t("set-promotion-text")}
                </Typography>
              </div>
            </>
          )}
        </div>
      </div>
      <div style={{ display: "flex", flexDirection:  "column" , alignItems: "top", marginTop: 32, paddingTop: 32, borderTop: '1px solid #E8E7E6', width: isMobile ? null : 520 }}>
        {/*<div style={{ marginRight: 0 , marginBottom: 32, width: " 100%" }}>*/}
        {/*  <Typography style={{ ...typography.body.medium }}>{t("delivery-settings")}</Typography>*/}
        {/*  <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("control-delivery-settings-for-webshop")}</Typography>*/}
        {/*</div>*/}
        <div style={{ flex: 1, maxWidth: 520 }}>
          {/*enable delivery*/}
          <div className={classes.switchSetting} style={{ marginTop: 0 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("pickup-order-creation-form-type-field-delivery-option-label")}</Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("enable-delivery-toggle-description")}</Typography>
              </div>
              <Switch checked={restaurant.hasDelivery ?? false} onClick={() => handleToggle("hasDelivery", !form.hasDelivery)} />
            </div>
          </div>
          {/*choose payment methods delivery*/}
          <div style={{ maxWidth: 520, marginTop: 12 }}>
            <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper} >
              <Select
                value={deliveryPaymentChannels ?? []}
                name={"deliveryPaymentChannels"}
                onChange={updateWebshopConfig}
                MenuProps={MenuProps}
                IconComponent={ChevronDown20new}
                multiple
                input={<Field id="select-chip" label={t("payment-option-delivery")} select />}
                renderValue={(selected) => (
                  <div style={{ display: 'flex', flexWrap: 'wrap', }}>
                    {selected.map((value) => {
                      const paymentMethod = webshopPaymentOptions.find(t => t.key === value) || {}
                      return (
                        <Chip
                          key={value}
                          label={t(paymentMethod.i18nKey)}
                          style={{ margin: 2 }}
                        />
                      )
                    })}
                  </div>
                )}
              >
                {webshopPaymentOptions.map(rOption => (
                  <MenuItem value={rOption.key} key={rOption.key}>
                    <div className={classes.tag}>
                      <Typography>{t(rOption.i18nKey)}</Typography>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
          {/*delivery fee mode*/}
          <div style={{ marginTop: 12, maxWidth: 520 }}>
            <FormControl style={{ width: "100%", marginBottom: 12 }} className={classes.chipSelectWrapper} >
              <Select
                value={deliveryFeeMode || "ZIP_CODES"}
                onChange={updateDeliveryMode}
                name={"deliveryFeeMode"}
                MenuProps={MenuProps}
                IconComponent={ChevronDown20new}
                input={<Field id="select-chip" label={t("delivery-fee-mode")} select />}
                renderValue={(selected) => {
                  const dFOption = deliveryFeeModeOptions.find(t => t.value === selected) || {};
                  return (
                    <div style={{ display: "flex", flexWrap: "wrap" }}>
            
                      <Chip
                        key={selected}
                        label={t(dFOption.i18nKey)}
                        style={{ margin: 2 }}
                      />
                    </div>
                  );
                }}
              >
                {deliveryFeeModeOptions.map(dFOption => (
                  <MenuItem value={dFOption.value} key={dFOption.value}>
                    <div className={classes.tag}>
                      <Typography>{t(dFOption.i18nKey)}</Typography>
                    </div>
                  </MenuItem>
                ))}
              </Select>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 8 }}>{t("delivery-fee-mode-hint")}</Typography>
            </FormControl>
            {deliveryFeeMode === "ZIP_CODES" && (
              <>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <div style={{ display: "flex" }}>
                    <Typography
                      style={{ ...typography.body.medium }}>{t("webshop-settings-select-delivery-area-label")}</Typography>
                    <Typography style={{ ...typography.body.medium, color: palette.primary["500"] }}>*</Typography>
                  </div>
                  <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple style={{ padding: 0 }}
                              onClick={() => setUpdatingDeliveryAreaField(true)}>
                    <TableUpdateBtn />
                  </ButtonBase>
                </div>
                <div style={{ marginTop: 12, maxWidth: 520 }}>
                  {getDeliveryAreaField()}
                </div>
              </>
            )}
           <div style={{ marginTop: 12, maxWidth: 520 }}>
              <GoogleMapsWrapper />
            </div>
          </div>
          {deliveryFeeMode === "KM_RADIUS" && (
            <div style={{ marginTop: 24, maxWidth: 520 }}>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "flex-end" }}>
              {isEmpty(deliveryRadius) ? (
                <ButtonBase style={{ border: `1px solid ${palette.grayscale.border}`, borderRadius: 10, padding: "6px 8px" }}
                            onClick={() => setUpdatingDeliveryMinAndDeliveryFeePerArea(true)}>
                  <Typography style={{ ...typography.body.regular }}>Setup Radius</Typography>
                </ButtonBase>
              ) : (
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple style={{ padding: 0 }}
                          onClick={() => setUpdatingDeliveryMinAndDeliveryFeePerArea(true)}>
                <TableUpdateBtn />
              </ButtonBase>
                )}
              </div>
              <div style={{ marginTop: 12, maxWidth: 520 }}>
                {getDeliveryMinAndDeliveryFeePerRadius()}
              </div>
            </div>
          )}
          {deliveryFeeMode === "ZIP_CODES" && (
            <div style={{ marginTop: 24, maxWidth: 520 }}>
              {!isEmpty(deliveryAreas) &&
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <div>
                  </div>
                  <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple style={{ padding: 0 }}
                              onClick={() => setUpdatingDeliveryMinAndDeliveryFeePerArea(true)}>
                    <TableUpdateBtn />
                  </ButtonBase>
                </div>
              }
              {!isEmpty(deliveryAreas) && (
                <div style={{ marginTop: 12, maxWidth: 520 }}>
                  {getDeliveryMinAndDeliveryFeePerArea()}
                </div>
              )}
            </div>
          )}
          
        </div>
      </div>
    </div>
  );
};

export default withTranslation("common")(WebshopSettings);
