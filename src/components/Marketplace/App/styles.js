import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../../styles/palette";
import shadows from "../../../../styles/shadows";

const useStyles = makeStyles(theme => ({
  content: {
    paddingTop: 16,
    paddingLeft: 12,
    paddingRight: 12,
    paddingBottom: 45 + 16,
    overflowY: "auto",
    overflowX: "hidden",
    display: "flex",
    flexDirection: "column",
    height: "100%",
    borderTop: "1px solid #E8E7E6"
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  buttonRoot: {
    fontSize: 14,
    padding: '4px 6px',
    borderRadius: theme.spacing(1)
  },
  spacing: {
    flex: 1
  },
  user: {
    display: 'flex',
    alignItems: 'center'
  },
  avatarRoot: {
    width: 16,
    height: 16
  },
  card: {
    border: '1px solid #f3f4f4',
    borderRadius: theme.spacing(1),
    padding: theme.spacing(2)
  },
  cardLayout: {
    display: 'flex',
    '& button + button': {
      marginLeft: 10
    }
  },
  img: {
    width: 32,
    height: 32,
    marginRight: theme.spacing(2),
    borderRadius: '20%'
  },
  secondaryText: {
    '& > p': {
      opacity: 0.6
    }
  },
  actionBtn: {
    marginTop: 8
  },
  section: {
    // maxWidth: theme.breakpoints.values.lg,
    // margin: '0 auto',
    '&+&': {
      marginTop: theme.spacing(6)
    }
  },
  sourceBtnText: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
    marginRight: 2
  },
  sourceBtnIcon: {
    transform: "rotate(180deg)",
    display: "flex"
  },
  galleryPadding: {
    paddingLeft: `calc(100vw / 2 - 260px - 178px)`,
    '@media (max-width: 885px)': {
      paddingLeft: 16
    }
  },
  iconButton: {
    minWidth: 0,
    padding: 5,
    border: "1px solid #D9D9D8",
    borderRadius: "10px",
    "&:hover": {
      background: "transparent"
    },
    "&+&": {
      marginLeft: 8
    },
  }
}));

export const useMenuStyles = makeStyles((theme) => ({
  list: {
    paddingTop: 0,
    paddingBottom: 0
  },
  paper: {
    backgroundColor: theme.palette.common.white,
    borderRadius: 12,
    minWidth: 340,
    maxWidth: "100%",
    boxShadow: "0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04)",
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 2,
    paddingBottom: 2,
    transition: "none"
  },
  menuItemRoot: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    "&:hover": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  },
  styleFreeMenuItem: {
    padding: 0,
    margin: 0,
    "&:hover": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  },
  borderTop: {
    borderTop: "1px solid #EFEFEE"
  },
  borderBottom: {
    borderBottom: "1px solid #EFEFEE"
  },
  content: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  },
  contentWithIcon: {
    display: "flex",
    alignItems: "center"
  },
  selection: {
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
    marginRight: 6
  },
  option: {
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#333332",
  },
  optionWithIcon: {
    marginLeft: 8
  },
  optionTitle: {
    fontWeight: 500
  },
  selected: {
    fontWeight: 500,
    color: "#FF7C5C"
  },
  property: {
    opacity: 0.6,
    borderRadius: 12,
    background: "#EDECEC",
    paddingLeft: 8, paddingRight: 10,
    paddingTop: 6,
    paddingBottom: 6,
    marginLeft: 8, marginRight: 8,
    marginBottom: 8
  },
  selectedProperty: {
    background: palette.grayscale["800"],
    color: palette.grayscale["100"],
    opacity: 1
  },
  right: {
    display: "flex",
    alignItems: "center"
  },
  customRangeRightContent: {
    display: "flex",
    alignItems: "center"
  },
  calendarSelectedRange: {
    marginRight: 2,
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
  },
  clearBtn: {
    fontWeight: 500,
    color: "#FF7C5C",
    fontSize: 14,
    lineHeight: "20px",
    padding: 0,
    minWidth: 0,
    '&:hover': {
      backgroundColor: 'transparent',
    },
  }
}));

export const useMenuItemStyles = makeStyles(() => ({
  menuList: {
    paddingTop: 0,
    paddingBottom: 0
  },
  menuPaper: {
    backgroundColor: palette.grayscale["100"],
    borderRadius: 12,
    minWidth: 250,
    maxWidth: "100%",
    ...shadows.large,
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 2,
    paddingBottom: 2,
    transition: "none"
  },
  menuItemRoot: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    "&:hover": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  },
  content: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  },
  contentWithIcon: {
    display: "flex",
    alignItems: "center"
  },
  optionWithIcon: {
    marginLeft: 8
  },
}));

export default useStyles;
