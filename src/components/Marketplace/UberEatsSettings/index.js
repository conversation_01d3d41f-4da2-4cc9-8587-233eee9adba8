import React, { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../../i18n";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import isEmpty from "../../../utils/isEmpty";
import {
  deleteUberEatsStore, getRestaurantConfiguration,
  getUberEatsStore,
  getUberEatsStoreStatus,
  registerUberEatsStore, updateRestaurantConfigurationById,
  updateUberEatsStoreStatus
} from "../../../api";
import { restaurantSelectors } from "../../../../redux/selectors";
import Loading from "../../Loading";
import useStyles from "./styles";
import { noop } from "../../../utils/const";
import { ButtonBase } from "@material-ui/core";
import Field from "../../form/Field";
import TextUpdateModal from "../../_popup/TextUpdateModal";
import { ChevronDown20new, CopyIcon20, EditIcon20 } from "../../../utils/icons";
import { Confirm } from "../../Confirmation";
import Select from "@material-ui/core/Select";
import MenuItem from "@material-ui/core/MenuItem";
import TimeInSecondsUpdateModal from "../../_popup/TimeInSecondsUpdateModal";
import { appActions, configurationActions } from "../../../../redux/actions";
import OnlineBadge from "../../_tags/OnlineBadge";
import Script from "../../_input/Script";

const UberEatsSettings = ({ t, isManaged }) => {
  const classes = useStyles();
  const dispatch = useDispatch()
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const [fetching, setFetching] = useState(false);
  const [store, setStore] = useState({});
  const { storeId, posData } = (store || {})
  const { integrationEnabled = false } = (posData || {})
  const isOperating = integrationEnabled === true;
  
  const [storeStatus, setStoreStatus] = useState({});
  const { status, offlineReason } = storeStatus;
  const offlineDueToMenuHours = offlineReason === "OUT_OF_MENU_HOURS";
  
  const [updating, setUpdating] = useState(false);
  const [configurationForm, setConfigurationForm] = useState({partnerConfigs:[]});
  
  const { partnerConfigs } = configurationForm
  const dpIndex = (partnerConfigs ?? []).findIndex(config => config.partnerId === "UBER_EATS");
  const hasPartnerConfig = partnerConfigs?.length && dpIndex !== -1
  
  const [updatingAutoRejectAcceptTime, setUpdatingAutoRejectAcceptTime] = useState(false)
  
  const readyToShowSettings = !isEmpty(store) && !isEmpty(storeStatus);
  
  const fetchConfiguration = () => {
    getRestaurantConfiguration(restaurantId)
      .then(({ data = {} }) => {
        setConfigurationForm(data ?? {});
      })
      .catch(() => {
      });
    dispatch(configurationActions.getConfiguration(restaurantId));
  };
  
  useEffect(() => {
    fetchConfiguration();
  }, []);
  
  const handleCustomizedOrderHandling = (e) => {
    const deliveryPartner = e.target.name
    const action = e.target.value
    const dpIndex = (configurationForm.partnerConfigs ?? []).findIndex(config => config.partnerId === deliveryPartner);
    
    if (!partnerConfigs) {
      const updatedConfiguration = { ...configurationForm, partnerConfigs: [{ partnerId: deliveryPartner, unseenOrder: { action: e.target.value, waitingTimeInSeconds: 60 } }] };
      setConfigurationForm(updatedConfiguration);
      updateConfiguration(updatedConfiguration);
    }
    if (configurationForm.partnerConfigs) {
      if (dpIndex !== -1 && action === "NONE") {
        const filteredConfig = {...configurationForm, partnerConfigs: [...partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner)] }
        setConfigurationForm(filteredConfig);
        updateConfiguration(filteredConfig);
      }
      if (dpIndex !== -1 && action !== "NONE") {
        const partnerObject = partnerConfigs[dpIndex]
        const updatedPartnerObject = { ...partnerConfigs[dpIndex], unseenOrder: { ...partnerObject?.unseenOrder ?? {}, action : action, waitingTimeInSeconds: partnerObject.unseenOrder.waitingTimeInSeconds ?? 60 }}
        const updatedPartnerConfigs =[ ...configurationForm.partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner), updatedPartnerObject]
        const updatedConfiguration = { ...configurationForm, partnerConfigs: updatedPartnerConfigs }
        setConfigurationForm(updatedConfiguration);
        updateConfiguration(updatedConfiguration);
      }
      if (dpIndex === -1){
        // partnerId NOT in partnerConfig
        const updatedConfiguration = { ...configurationForm, partnerConfigs: [...partnerConfigs, { partnerId: e.target.name, unseenOrder: { action: action, waitingTimeInSeconds: 60 }}] };
        setConfigurationForm(updatedConfiguration);
        updateConfiguration(updatedConfiguration);
      }
    }
  }
  
  const updateTimeForCustomizeOrderHandling = (value, deliveryPartner) => {
    const dpIndex = (partnerConfigs ?? []).findIndex((dp => dp.partnerId === deliveryPartner))
    const partnerObject = partnerConfigs[dpIndex]
    const updatedPartnerObject = { ...partnerConfigs[dpIndex], unseenOrder: { ...partnerObject?.unseenOrder ?? {}, waitingTimeInSeconds : value  }}
    const updatedPartnerConfigs =[ ...partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner), updatedPartnerObject]
    const updatedConfiguration = { ...configurationForm, partnerConfigs: updatedPartnerConfigs }
    setConfigurationForm(updatedConfiguration)
    updateConfiguration(updatedConfiguration)
  }
  
  const updateConfiguration = (updatedConfiguration) => {
    updateRestaurantConfigurationById(restaurantId, updatedConfiguration)
      .then(fetchConfiguration)
      .catch(() => {
      })
  };
  
  const fetchUberEatsStoreStatus = () => {
    getUberEatsStoreStatus(restaurantId)
      .then(({ data }) => setStoreStatus(data))
      .catch(() => {});
  }
  
  const toggleOperatingStatus = () => {
    const statusToSet = isOperating ? "PAUSED" : "ONLINE";
    updateUberEatsStoreStatus(restaurantId, statusToSet).then(fetchUberEatsStoreStatus).catch(noop)
  }
  
  const fetchUberEatsStore = () =>
    getUberEatsStore(restaurantId).then(({ data }) => setStore(data)).catch(() => {}).finally(() => setFetching(false))
  
  const removeUberEatsStore = () =>
    deleteUberEatsStore(restaurantId).then(fetchUberEatsStore).catch(() => {}).finally()
  
  useEffect(() => {
    setFetching(true);
    fetchUberEatsStoreStatus();
    fetchUberEatsStore();
  }, [])
  
  const onCopyLink = () => {
    navigator.clipboard.writeText(store?.webUrl ?? "")
      .then(() => {
        dispatch(appActions.setNotification(t('copy-success'), "success"))
      })
      .catch(() => {
        dispatch(appActions.setNotification(t('copy-error'), "success"))
      });
    
  }
  
  const TableUpdateBtn = () => (
    <div style={{ display: "flex", alignItems: "center", paddingLeft: 10, paddingRight: 10, minWidth: 100 }}>
      <EditIcon20 />
      <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], marginLeft: 2}}>{t('common-update')}</Typography>
    </div>
  )
  
  const [updatingApiKey, setUpdatingApiKey] = useState(false);
  
  const updateApiKey = (value) => {
    setUpdating(true);
    if (!value) {
      return
    }
    registerUberEatsStore(restaurantId, value)
      .then(() => fetchUberEatsStore())
      .catch(({ response = {} }) => {
        const { data = {} } = response;
        const { title } = data;
        if (title) {
          dispatch(appActions.setNotification(title, "error"))
        }
        
    })
      .finally(() => {
        setUpdatingApiKey(false);
        setUpdating(false);
      });
  }
  
  const getApiKeyField = () => {
    return (
      <Fragment>
        <Field
          value={storeId}
          disabled
        />
        <TextUpdateModal
          titleI18n={'set-api-key'}
          open={updatingApiKey}
          onClose={() => setUpdatingApiKey(false)}
          value={storeId}
          setValue={updateApiKey}
        />
      </Fragment>
    )
  }
  
  if (fetching || updating) {
    return <Loading />;
  }
  
  return (
    <div className={classes.settings}>
      <div style={{ width: 520, height: "100%", margin: "0 auto", display: 'flex', maxWidth: "100%", flexDirection: "column" }}>
        <div className={classes.settingBody}>
          <div style={{ marginBottom: 24 }}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <Typography style={{ ...typography.body.medium }}>
                {t("uber-eats-webshop-link")}
              </Typography>
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                          style={{ padding: 0 }} onClick={onCopyLink}>
                <div style={{
                  display: "flex",
                  alignItems: "center",
                  paddingLeft: 10,
                  paddingRight: 10,
                  minWidth: 60
                }}>
                  <CopyIcon20 />
                  <Typography style={{
                    ...typography.body.medium,
                    color: palette.grayscale["600"],
                    marginLeft: 2
                  }}>{t("copy")}</Typography>
                </div>
              </ButtonBase>
            </div>
            <div style={{ marginTop: 12 }}>
              <Script script={store?.webUrl ?? ""} />
            </div>
          </div>
        </div>
        <div style={{ marginBottom: 32 }}>
          <Typography style={{ ...typography.body.medium }}>
            {t('uber-eats-key')}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
            {t('setup-uber-eats-integration-by-using-store-id')}
          </Typography>
        </div>
        <div className={classes.settingBody}>
          <div>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <Typography style={{ ...typography.body.medium }}>
                {t('set-api-key')}
              </Typography>
              {(!storeId || isManaged) &&  (
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingApiKey(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              )}
            </div>
            <div style={{ marginTop: 12 }}>
              {getApiKeyField()}
            </div>
            <div className={classes.switchSetting} style={{ marginTop: 24 }}>
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%" }}>
                <Typography style={{ ...typography.body.medium }}>
                  {!isOperating ? t("uber-eats-app-not-active") : t("uber-eats-app-active")}
                </Typography>
                <OnlineBadge t={t} isActive={isOperating}/>
              </div>
            </div>
          </div>
          {storeId && isManaged && (
            <div style={{ display: "flex", flexDirection: "column", alignItems: "top", marginTop: 32, paddingTop: 32, borderTop: '1px solid #E8E7E6' }}>
              <div style={{ marginRight: 0, marginBottom: 16, width: "100%" }}>
                <Typography style={{  ...typography.body.medium }}>{t('delete-integration')}</Typography>
                <Typography style={{  ...typography.body.regular, marginTop: 4 }}>{t('this-actions-is-not-reversible')}</Typography>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ display: "flex", width: "100%" }}>
                  <Confirm
                    title={t("delete-integration")}
                    description={t("are-you-sure-description-to-delete-item")}
                  >
                    {confirm => (
                      <ButtonBase style={{ padding: '12px 24px', width: "100%", background: palette.negative["500"], borderRadius: 12 }} disableRipple disableTouchRipple onClick={confirm(() => removeUberEatsStore())}>
                        <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                          {t('common-delete')}
                        </Typography>
                      </ButtonBase>
                    )}
                  </Confirm>
                </div>
              </div>
            </div>
          )}
        </div>
        {/*{readyToShowSettings && (
          <div className={classes.settingsForm}>
            <div className={classes.settingHeader}>
              <Typography style={{ ...typography.body.medium }}>
                {t('operations')}
              </Typography>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                {t('update-your-restaurant-operating-settings')}
              </Typography>
            </div>
            <div className={classes.settingBody}>
              <div className={classes.switchSetting}>
                <div className={classes.flex}>
                  <div style={{ opacity: offlineDueToMenuHours ? .8 : 1 }}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t('operating')}
                    </Typography>
                    <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                      {t('accepting-orders-from-partner')}
                    </Typography>
                  </div>
                  <Switch checked={isOperating} onClick={toggleOperatingStatus} disabled={true}/>
                </div>
                {offlineDueToMenuHours && (
                  <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                    {t('offline-due-to-opening-hours')}
                  </Typography>
                )}
              </div>
            </div>
          </div>
        )}*/}
        <div className={classes.settingsForm}>
          <div className={classes.settingHeader}>
            <Typography style={{ ...typography.body.medium }}>
              {t('unseen-webshop-order-handling-label')}
            </Typography>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
              {t('control-takeaway-settings')}
            </Typography>
          </div>
          <div className={classes.settingBody}>
            <div>
              <Typography style={{ ...typography.body.medium , marginBottom: 8 }}>{t("action-unseen-order")}</Typography>
            </div>
            <div className={classes.selectWrapper}>
              <Select
                id="customAction-select"
                name="UBER_EATS"
                value={ hasPartnerConfig ? partnerConfigs[dpIndex]?.unseenOrder?.action : "NONE"}
                onChange={handleCustomizedOrderHandling}
                disableUnderline
                variant="outlined"
                classes={{ root: classes.select }}
                style={{borderRadius: 12, backgroundColor: palette.grayscale["100"], height: 44, borderColor: palette.grayscale["350"] }}
                IconComponent={ChevronDown20new}
                required
              >
                <MenuItem value="NONE">{t('none')}</MenuItem>
                <MenuItem value="ACCEPT">{t('accept')}</MenuItem>
                <MenuItem value="REJECT">{t('reject')}</MenuItem>
              </Select>
            </div>
            {dpIndex !== -1 &&
              <>
                <div className={classes.flex} style={{ marginTop: 8 }}>
                  <div style={{ marginRight: 8, marginTop: 12 }}>
                    <Typography style={{ ...typography.body.medium }}>{t("time-in-s-till-action")}</Typography>
                  </div>
                  <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                              style={{ padding: 0 }} onClick={() => setUpdatingAutoRejectAcceptTime(true)}>
                    <TableUpdateBtn />
                  </ButtonBase>
                </div>
                <Field
                  value={hasPartnerConfig ? partnerConfigs[dpIndex]?.unseenOrder?.waitingTimeInSeconds : 60}
                  readOnly
                />
                <TimeInSecondsUpdateModal
                  titleI18n={"configuration-editor-auto-reject-accept-time-field-label"}
                  open={updatingAutoRejectAcceptTime}
                  deliveryPartner={"UBER_EATS"}
                  onClose={() => setUpdatingAutoRejectAcceptTime(false)}
                  value={hasPartnerConfig ? partnerConfigs[dpIndex]?.unseenOrder?.waitingTimeInSeconds : 60}
                  setValue={updateTimeForCustomizeOrderHandling}
                  type={"number"}
                  fieldProps={{
                    type: "number"
                  }}
                />
              </>
            }
          </div>
        </div>
      </div>
    </div>
  )
}

export default withTranslation("common")(UberEatsSettings);
