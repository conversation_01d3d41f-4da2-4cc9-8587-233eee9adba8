import React, { Fragment, useEffect, useState } from "react";
import useStyles from "./styles";
import { withTranslation } from "../../../../../i18n";
import { ReservationsSettingsHeader } from "./components/ReservationsSettingsHeader";
import { ReservationSettingsAllowGuests } from "./components/ReservationSettingsAllowGuests";
import { ReservationSettingsReservationWindow } from "./components/ReservationSettingsReservationWindow";
import { ReservationsSettingsMaxOverlapMinutes } from "./components/ReservationsSettingsMaxOverlapMinutes";
import { ReservationSettingsShowDurationInEmail } from "./components/ReservationSettingsShowDurationInEmail";
import { ReservationSettingsCustomerMessage } from "./components/ReservationSettingsCustomerMessage";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../styles/typography";
import { ButtonBase, Table, TableBody, TableCell, TableHead, TableRow, useMediaQuery } from "@material-ui/core";
import Field from "../../../form/Field";
import TextUpdateModal from "../../../_popup/TextUpdateModal";
import {
  getReservationConfig,
  getReservationConfiguration,
  getReservationConfigV2,
  updateReservationConfig, updateReservationCoverImages, uploadMenuImage, uploadReservationCoverImage,
  updateReservationConfigV2
} from "../../../../api";
import { useSelector } from "react-redux";
import { restaurantSelectors } from "../../../../../redux/selectors";
import removeUndefined from "../../../../utils/removeUndefined";
import { CloseIcon24, EditIcon20, PlusIconFilled20, UploadIcon } from "../../../../utils/icons";
import palette from "../../../../../styles/palette";
import isEmpty from "../../../../utils/isEmpty";
import moment from "moment";
import ExcludedDatesReservationModal from "../../../_popup/ExcludedDatesReservationModal";
import TableContainer from "@material-ui/core/TableContainer";
import DinningEstimateUpdateModal from "../../../_popup/DinningEstimateUpdateModal";
import calculateReservationLimits from "../../../../utils/calculateReservationLimits";
import ReservationPeriodUpdateModal from "../../../_popup/ReservationPeriodUpdateModal";
import Switch from "../../../_toggles/Switch";
import isValidEmail from "../../../../utils/isValidEmail";
import shadows from "../../../../../styles/shadows";
import SetupReservationTermsModal from "../../../_popup/SetupReservationTermsModal";
import axios from "axios";
import clsx from "clsx";
import List from "@material-ui/core/List";
import { settingsAdministrationViewOptions, settingsConfigurationView } from "../../../../utils/const";
import ReservationSettingsSidebarItem from "./components/ReservationSettingsSidebarItem";
import { QuantityButton } from "../../../Buttons";
import { ReservationSettingsTableGroups } from "./components/ReservationSettingsTableGroups";
import Compressor from "compressorjs";
import Dropzone from "react-dropzone";
import { ReservationSettingsSpecialDays } from "./components/specialDays/ReservationSettingsSpecialDays";
import { ReservationSettingsLeadTime } from "./components/ReservationSettingsLeadTime";

const TableResetBtn = ({ t }) => (

  <div style={{
    display: "flex",
    alignItems: "center",
    paddingLeft: 10,
    paddingRight: 10,
    minWidth: 100
  }}>
    <EditIcon20 />
    <Typography style={{
      ...typography.body.medium,
      color: palette.grayscale["600"],
      marginLeft: 2
    }}>{t("common-reset")}</Typography>
  </div>

);

const TableUpdateBtn = ({ t }) => (
  <div style={{
    display: "flex",
    alignItems: "center",
    paddingLeft: 10,
    paddingRight: 10,
    minWidth: 100
  }}>
    <EditIcon20 />
    <Typography style={{
      ...typography.body.medium,
      color: palette.grayscale["600"],
      marginLeft: 2
    }}>{t("common-update")}</Typography>
  </div>
);

const TableAddBtn = ({ t }) => (
  <div style={{
    display: "flex",
    alignItems: "center",
    paddingLeft: 10,
    paddingRight: 10,
    minWidth: 100
  }}>
    <PlusIconFilled20 />
    <Typography style={{
      ...typography.body.medium,
      color: palette.grayscale["600"],
      marginLeft: 2
    }}>{t("common-add")}</Typography>
  </div>
);

let request = null;

const ReservationsSettingsV2 = ({ t }) => {
  const classes = useStyles();
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const {
    id: restaurantId,
    openingHours = {}
  } = restaurant;
  const [fetching, setFetching] = useState(false);
  const [configuration, setConfiguration] = useState({});
  const [saving, setSaving] = useState(false);
  const [openExcludedDatesModal, setOpenExcludedDatesModal] = useState(false);
  const {
    allowCustomerReservations,
    maxGuestsPerReservation,
    maxOverlapMinutes,
    requireNumberOfGuestsInput,
    estimatedDiningMinutes,
    reservationLimits = {},
    includeReservationDurationInEmail,
    includedGoodToKnowTextI18n = {},
    replyTo,
    displaySlotExplainButton,
    reservationTerms,
    blockedDates,
    allowMultipleTableSelection
  } = configuration;
  const { de: includedGoodToKnowTextDE = "" } = includedGoodToKnowTextI18n;
  const [reservationCoverImages, setReservationCoverImages] = useState([]);
  const [selectedCoverImage, setSelectedCoverImage] = useState({
    alt: "",
    src: null
  });
  const [updatingIncludedGoodToKnowText, setUpdatingIncludedGoodToKnowText] = useState(false);
  const [updatingEstimatedDinningTimeForGuests, setUpdatingEstimatedDinningTimeForGuests] = useState(null);
  const [addingEstimatedDinningTimeForGuests, setAddingEstimatedDinningTimeForGuests] = useState(false);
  const [updatingReservationLimitsDay, setUpdatingReservationLimitsDay] = useState(null);
  const [updatingReplyTo, setUpdatingReplyTo] = useState(false);
  const [updatingTerms, setUpdatingTerms] = useState(false);
  const [refreshReservationWindows, setRefreshReservationWindows] = useState(false);
  const [reservationWindows, setReservationWindows] = useState({});
  const [maximumTimeOverEndInMinutes, setMaximumTimeOverEndInMinutes] = useState(0);
  const [updatingMaximumTimeOverEndInMinutes, setUpdatingMaximumTimeOverEndInMinutes] = useState(false);
  const [allowSmsNotifications, setAllowSmsNotifications] = useState(false);
  const [minimumLeadTimeInMinutes, setMinimumLeadTimeInMinutes] = useState(0);
  const [specialDaysEnabled, setSpecialDaysEnabled] = useState(false);

  const [view, setView] = useState(settingsConfigurationView.GENERAL_SETTINGS.value);
  const isMobile = useMediaQuery("(max-width:800px)");

  const updateMaximumTimeOverEndInMinutes = (value) => {
    if (!value) {
      return;
    }
    const newValue = parseInt(value) || 0;
    setMaximumTimeOverEndInMinutes(newValue);
    updateReservationConfigV2(restaurantId, {
      maximumTimeOverEndInMinutes: newValue
    }).then(() => {
      setMaximumTimeOverEndInMinutes(newValue);
    }).catch(() => {})
  };

  const updateIncludedGoodToKnowTextDE = (value) => {
    const updatedIncludedGoodToKnowTextI18n = {
      ...includedGoodToKnowTextI18n,
      "de": value
    };
    setConfiguration({
      ...configuration,
      includedGoodToKnowTextI18n: removeUndefined(updatedIncludedGoodToKnowTextI18n)
    });
    setSaving(true);
    setUpdatingIncludedGoodToKnowText(false);
  };
  const updateExcludedDates = (dates) => {
    setConfiguration({
      ...configuration,
      blockedDates: dates
    });
    setSaving(true);
  };
  const toggleDisplaySlotExplainButton = () => {
    setConfiguration({
      ...configuration,
      displaySlotExplainButton: !displaySlotExplainButton
    });
    setSaving(true);
  };

  const updateEstimatedDinningTimeForGuests = (guests, estimation) => {
    const updatedEstimationDinningTimes = {
      ...estimatedDiningMinutes,
      [guests]: estimation
    };
    setConfiguration({
      ...configuration,
      estimatedDiningMinutes: removeUndefined(updatedEstimationDinningTimes)
    });
    setSaving(true);
    setUpdatingEstimatedDinningTimeForGuests(null);
  };
  // const updateMaximumTimeOverEndInMinutes = (value) => {
  //   const newValue = parseInt(value) || 0;
  //   if (newValue !== maximumTimeOverEndInMinutes) {
  //     setMaximumTimeOverEndInMinutes(newValue);
  //     updateReservationConfigV2(restaurantId, {
  //       maximumTimeOverEndInMinutes: newValue
  //     }).then(() => {}).catch(() => {})
  //   }
  // };
  const updateReservationLimitsDayIntervals = (periods = []) => {
    if (!isEmpty(periods)) {
      const updatedReservationLimits = {
        ...reservationLimits,
        [updatingReservationLimitsDay]: periods
      };
      setConfiguration({
        ...configuration,
        reservationLimits: removeUndefined(updatedReservationLimits)
      });
      setSaving(true);
    }
    setUpdatingReservationLimitsDay(null);
  };
  const updateReplyTo = (value) => {
    if (!value) {
      return;
    }
    let email = value.trim();
    if (!isValidEmail(email)) {
      return;
    }
    setConfiguration({
      ...configuration,
      replyTo: email
    });
    setSaving(true);
    setUpdatingReplyTo(false);
  };
  const updateTerms = (value) => {
    setConfiguration({
      ...configuration,
      reservationTerms: value
    });
    setSaving(true);
    setUpdatingTerms(false);
  };
  const toggleRequireNumberOfGuestsInput = () => {
    setConfiguration({
      ...configuration,
      requireNumberOfGuestsInput: !requireNumberOfGuestsInput
    });
    setSaving(true);
  };

  const fetchReservationSettings = () => {
    setFetching(true);
    getReservationConfig(restaurantId)
      .then(({ data }) => {
        setConfiguration(data)
      })
      .catch(() => {})
      .finally(() => setFetching(false));
  };
  const fetchReservationSettingsReservationWindows = () => {
    setFetching(true);
    getReservationConfigV2(restaurantId)
      .then(({ data }) => {
        setReservationWindows(data);
      })
      .catch(() => {})
      .finally(() => setFetching(false));
  };
  const onUpload = (file) => {
    new Compressor(file, {
      quality: 0.6,
      success (result) {
        uploadReservationCoverImage(restaurantId, result)
          .then(({ data }) => {
            // Get the whole array again
            fetchReservationConfiguration();
          })
          .catch(() => {});
      },
      error (err) {}
    });
  };
  const onDeleteCoverImageAtIndex = (index) => {
    const coverImages = reservationCoverImages.filter((it, i) => i !== index);
    updateReservationCoverImages(restaurantId, coverImages)
      .then(() => {
        fetchReservationConfiguration();
      })
      .catch(() => {});
  };
  const fetchReservationConfiguration = () => {
    setFetching(true);
    getReservationConfiguration(restaurantId)
      .then(({ data }) => {
        const { cover = [], maximumTimeOverEndInMinutes = 0, allowSmsNotifications = false, minimumLeadTimeInMinutes, specialDaysEnabled = false } = data;
        if (cover.length > 0) {
          setSelectedCoverImage(cover[0]);
        }
        setAllowSmsNotifications(allowSmsNotifications)
        setMaximumTimeOverEndInMinutes(maximumTimeOverEndInMinutes);
        setMinimumLeadTimeInMinutes(minimumLeadTimeInMinutes);
        setSpecialDaysEnabled(specialDaysEnabled)
        console.log(`cover images: ${cover}, length: ${cover.length}`);
        setReservationCoverImages(cover);
      })
      .catch(() => {})
      .finally(() => setFetching(false));
  };
  const updateReservationSettings = () => {
    // cancel previous ajax if exists
    if (request) {
      request.cancel();
    }

    request = axios.CancelToken.source();

    setFetching(true);
    updateReservationConfig(
      restaurantId,
      configuration,
      request.token
    )
      .then(fetchReservationSettings)
      .catch(() => setFetching(false))
      .finally(() => setSaving(false));
  };

  useEffect(() => {
    fetchReservationSettings();
    fetchReservationSettingsReservationWindows();
  }, []);
  useEffect(() => {
    if (!isEmpty(configuration) && saving) {
      updateReservationSettings();
    }
  }, [saving, JSON.stringify(configuration)]);
  useEffect(() => {
    if (!refreshReservationWindows) return;
    setRefreshReservationWindows(false);
    fetchReservationSettingsReservationWindows();
  }, [refreshReservationWindows]);

  useEffect(() => {
    fetchReservationConfiguration();
  }, [refreshReservationWindows]);

  const toggleAllowTableMerging = () => {
    const updatedConfig = { ...configuration, allowMultipleTableSelection: !allowMultipleTableSelection }
    //setReservationConfigurationForm(updatedConfig)
    updateReservationConfig(restaurantId, updatedConfig )
      .then(() => {
        fetchReservationSettings()
      })
      .catch(() => {})
  }

  const incrementMaxGuestsPerReservation = () => {
    setConfiguration({
      ...configuration,
      maxGuestsPerReservation: maxGuestsPerReservation + 1
    });
    setSaving(true);
  };

  //shouldn't this be maxGuestPerReservation rather than minutes ?
  const decrementMaxGuestsPerReservation = () => {
    if (maxOverlapMinutes === 1) {
      return;
    }
    setConfiguration({
      ...configuration,
      maxGuestsPerReservation: Math.max(1, maxGuestsPerReservation - 1)
    });
    setSaving(true);
  };

  const toggleAllowSmsNotification = (value) => {
    updateReservationConfigV2(restaurantId, {
      allowSmsNotifications: value
    }).then(fetchReservationConfiguration)
      .catch((err ) => {console.log(err)})
  };

  const updateMinimumLeadTimeInMinutes = (value) => {
    updateReservationConfigV2(restaurantId, {
      minimumLeadTimeInMinutes: value
    }).then(fetchReservationConfiguration)
      .catch((err ) => {console.log(err)})
  }

  const getIncludedGoodToKnowTextField = () => {
    return (
      <Fragment>
        <Field
          value={includedGoodToKnowTextDE}
          multiline
          disabled
        />
        <TextUpdateModal
          titleI18n={"add-message-in-customer-email"}
          open={updatingIncludedGoodToKnowText}
          onClose={() => setUpdatingIncludedGoodToKnowText(false)}
          value={includedGoodToKnowTextDE}
          setValue={updateIncludedGoodToKnowTextDE}
          multiline={true}
        />
      </Fragment>
    );
  };
  const getBlockedDaysField = () => {
    return (
      <Fragment>
        {!isEmpty(blockedDates) && blockedDates.map(d => {
          const {
            isStaffBlocked,
            date
          } = d;
          const formatedDate = moment(date).format("DD.MM.YYYY");
          return (
            <div style={{
              backgroundColor: palette.grayscale.white,
              borderRadius: 10,
              padding: 12,
              display: "flex",
              justifyContent: "space-between",
              marginBottom: 8
            }}>
              <Typography style={{ ...typography.body.medium }}>{formatedDate}</Typography>
              <Typography
                style={{ ...typography.body.regular }}>{isStaffBlocked ? t("customer-and-staff-blocked") : t("only-customer-blocked")}</Typography>
            </div>
          );
        })}
        <ExcludedDatesReservationModal
          titleI18n={"add-dates"}
          open={openExcludedDatesModal}
          onClose={() => setOpenExcludedDatesModal(false)}
          values={blockedDates}
          setValue={updateExcludedDates}
        />
      </Fragment>
    );
  };
  const getEstimatedDinningTable = (t) => {
    return (
      <Fragment>
        <TableContainer stickyHeader>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t("number-of-guests")}</TableCell>
                <TableCell>{t("estimated-dinning-minutes")}</TableCell>
                <TableCell />
              </TableRow>
            </TableHead>
            <TableBody>
              {!isEmpty(estimatedDiningMinutes) && Object.keys(estimatedDiningMinutes).map(key => (
                <TableRow onClick={() => setUpdatingEstimatedDinningTimeForGuests(key)} style={{ cursor: "pointer" }}>
                  <TableCell>{key}</TableCell>
                  <TableCell>{estimatedDiningMinutes[key]}</TableCell>
                  <TableCell>
                    <TableUpdateBtn t={t} />
                  </TableCell>
                </TableRow>
              ))}
              <TableRow onClick={() => setAddingEstimatedDinningTimeForGuests(true)} style={{ cursor: "pointer" }}>
                <TableCell colSpan={3}>
                  <TableAddBtn t={t} />
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
        {(!!updatingEstimatedDinningTimeForGuests || addingEstimatedDinningTimeForGuests) && (
          <DinningEstimateUpdateModal
            open={(!!updatingEstimatedDinningTimeForGuests || addingEstimatedDinningTimeForGuests)}
            onClose={() => {
              setUpdatingEstimatedDinningTimeForGuests(null);
              setAddingEstimatedDinningTimeForGuests(false);
            }}
            guests={addingEstimatedDinningTimeForGuests ? null : parseInt(updatingEstimatedDinningTimeForGuests + "")}
            estimation={addingEstimatedDinningTimeForGuests ? null : estimatedDiningMinutes[updatingEstimatedDinningTimeForGuests]}
            setValue={updateEstimatedDinningTimeForGuests}
          />
        )}
      </Fragment>
    );
  };
  const getReservationLimits = () => {
    const {
      MONDAY = [],
      TUESDAY = [],
      WEDNESDAY = [],
      THURSDAY = [],
      FRIDAY = [],
      SATURDAY = [],
      SUNDAY = []
    } = reservationLimits;

    return (
      <Fragment>
        <TableContainer stickyHeader>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t("day")}</TableCell>
                <TableCell>{t("number-of-guests-at")}</TableCell>
                <TableCell />
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow onClick={() => setUpdatingReservationLimitsDay("MONDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-monday")}</TableCell>
                <TableCell>{calculateReservationLimits(MONDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn t={t} />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingReservationLimitsDay("TUESDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-tuesday")}</TableCell>
                <TableCell>{calculateReservationLimits(TUESDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn t={t} />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingReservationLimitsDay("WEDNESDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-wednesday")}</TableCell>
                <TableCell>{calculateReservationLimits(WEDNESDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn t={t} />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingReservationLimitsDay("THURSDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-thursday")}</TableCell>
                <TableCell>{calculateReservationLimits(THURSDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn t={t} />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingReservationLimitsDay("FRIDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-friday")}</TableCell>
                <TableCell>{calculateReservationLimits(FRIDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn t={t} />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingReservationLimitsDay("SATURDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-saturday")}</TableCell>
                <TableCell>{calculateReservationLimits(SATURDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn t={t} />
                </TableCell>
              </TableRow>
              <TableRow onClick={() => setUpdatingReservationLimitsDay("SUNDAY")} style={{ cursor: "pointer" }}>
                <TableCell>{t("common-sunday")}</TableCell>
                <TableCell>{calculateReservationLimits(SUNDAY)}</TableCell>
                <TableCell>
                  <TableUpdateBtn t={t} />
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
        {!!updatingReservationLimitsDay && (
          <ReservationPeriodUpdateModal
            open={updatingReservationLimitsDay}
            onClose={() => setUpdatingReservationLimitsDay(null)}
            value={reservationLimits[updatingReservationLimitsDay]}
            setValue={updateReservationLimitsDayIntervals}
          />
        )}
      </Fragment>
    );
  };
  const getReplyToField = () => {
    return (
      <Fragment>
        <Field
          value={replyTo}
          disabled
        />
        <TextUpdateModal
          titleI18n={"set-reply-to-email"}
          open={updatingReplyTo}
          onClose={() => setUpdatingReplyTo(false)}
          value={replyTo}
          setValue={updateReplyTo}
        />
      </Fragment>
    );
  };
  const getSetupTermsField = () => {
    return (
      <div style={{
        padding: 12,
        borderRadius: 12,
        background: palette.grayscale["100"],
        ...shadows.base
      }}>
        {isEmpty(reservationTerms) && (
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: 12
          }}>
            <Typography style={{ ...typography.body.medium }}>
              {t("no-terms")}
            </Typography>
          </div>
        )}
        {!isEmpty(reservationTerms) && reservationTerms.map((term, index) => {
          const { textI18n } = (term || {});
          const {
            en,
            de,
            zh
          } = (textI18n || {});

          const firstChild = index === 0;
          return (
            <div className={classes.flex} style={{ marginTop: firstChild ? 0 : 16 }}>
              <div>
                <Typography style={{ ...typography.body.medium }}>
                  {`${de || ""} / ${en || ""} / ${zh || ""}`}
                </Typography>
              </div>
            </div>
          );
        })}
        {updatingTerms && (
          <SetupReservationTermsModal
            open={updatingTerms}
            onClose={() => setUpdatingTerms(false)}
            terms={reservationTerms}
            setValue={updateTerms}
            fieldProps={{
              type: "text"
            }}
          />
        )}
      </div>
    );
  };
  const updateView = (value) => setView(value);
  const renderNavigation = () => {
    return (
      <Fragment>
        <List className={classes.navigationList}>
          {settingsAdministrationViewOptions.map(({
            i18nKey,
            value
          }) => (
            <ReservationSettingsSidebarItem
              specialDaysEnabled={specialDaysEnabled}
              onClick={() => updateView(value)}
              value={value}
              label={t(i18nKey)}
              active={view === value}
            />
          ))}
        </List>
      </Fragment>
    );
  };

  const renderViewGeneralSettings = () => {
    return (
      <Fragment>
        <div className={classes.settings}>
          <div className={classes.settingsForm}>

            <ReservationSettingsReservationWindow
              refreshReservationWindows={() => setRefreshReservationWindows(true)}
              reservationWindows={reservationWindows.items}
              t={t}
              classes={classes}
            />

            <div>
              <ReservationsSettingsMaxOverlapMinutes
                setConfiguration={setConfiguration}
                setSaving={setSaving}
                configuration={configuration}
                maxOverlapMinutes={maxOverlapMinutes}
                classes={classes}
                t={t}
              />
            </div>
            <div style={{ marginTop: 24 }}>
              <div>
                <div style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}>
                  <div style={{ display: "flex" }}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t("max-reservation-overlap")}
                    </Typography>
                  </div>

                  <ButtonBase
                    disableRipple
                    disableTouchRipple
                    disableElevation
                    disableFocusRipple
                    style={{ padding: 0 }} onClick={() => setUpdatingMaximumTimeOverEndInMinutes(true)}>
                    <TableUpdateBtn t={t} />
                  </ButtonBase>
                </div>
                <div style={{ marginTop: 12 }}>
                <Fragment>
                  <Field
                    value={maximumTimeOverEndInMinutes}
                    disabled
                  />
                  <TextUpdateModal
                    fieldProps={{
                      type: "number"
                    }}
                    titleI18n={"max-reservation-overlap"}
                    open={updatingMaximumTimeOverEndInMinutes}
                    onClose={() => setUpdatingMaximumTimeOverEndInMinutes(false)}
                    value={maximumTimeOverEndInMinutes}
                    setValue={updateMaximumTimeOverEndInMinutes}
                  />
                </Fragment>
              </div>
              </div>
            </div>

            {/*reservation blocking on special days */}
            <div className={classes.counterSetting} style={{ marginTop: 24, display: specialDaysEnabled ? 'none' : 'inherit' }}>
              <div className={classes.flex}>
                <div style={{ marginRight: 8 }}>
                  <Typography style={{ ...typography.body.medium }}>
                    {t("special-days-for-blocking-reservations-header")}
                  </Typography>
                  <Typography style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"]
                  }}>
                    {t("set-special-days-for-blocking-reservations")}
                  </Typography>
                </div>
              </div>
              {isEmpty(blockedDates) && (
                <div style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: 12
                }}>
                  <ButtonBase style={{
                    border: `1px solid ${palette.grayscale.border}`,
                    borderRadius: 10,
                    padding: "6px 8px"
                  }}
                              onClick={() => setOpenExcludedDatesModal(true)}>
                    <Typography style={{ ...typography.body.regular }}>{t("add-dates")}</Typography>
                  </ButtonBase>
                </div>
              )}
              {!isEmpty(blockedDates) && (
                <div style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: 8
                }}>
                  <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                              style={{ padding: 0 }} onClick={() => setOpenExcludedDatesModal(true)}>
                    <TableUpdateBtn t={t} />
                  </ButtonBase>
                </div>
              )}
              <div style={{ marginTop: 12 }}>
                {getBlockedDaysField()}
              </div>
            </div>

          </div>

          <div className={classes.settingHeader} style={{
            display: "none",
            marginTop: 24,
            paddingTop: 24,
            borderTop: `1px dashed ${palette.grayscale["400"]}`
          }}>
            <Typography style={{ ...typography.body.medium }}>
              {t("reservation-rules")}
            </Typography>
            <Typography style={{
              ...typography.body.regular,
              color: palette.grayscale["600"]
            }}>
              {t("update-settings-for-all-reservations")}
            </Typography>
          </div>

          {/*estimatedDiningMinutes*/}
          <div className={classes.composedSetting} style={{ marginTop: 24 }}>
            <Typography style={{ ...typography.body.medium }}>
              {t("estimated-dinning-times")}
            </Typography>
            <Typography style={{
              ...typography.body.regular,
              color: palette.grayscale["600"]
            }}>
              {t("configure-estimations-for-dinning-duration-by-guests")}
            </Typography>
            <div style={{ marginTop: 12 }}>
              {getEstimatedDinningTable(t)}
            </div>
          </div>

          {/*requireNumberOfGuestsInput*/}
          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("require-number-of-guests-in-reservation")}
                </Typography>
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.grayscale["600"]
                }}>
                  {t("require-waiters-to-input-number-of-guests-in-table")}
                </Typography>
              </div>
              <Switch checked={requireNumberOfGuestsInput} onClick={toggleRequireNumberOfGuestsInput} />
            </div>
          </div>


          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("allow-table-merging-reservation")}
                </Typography>
                <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                  {t("this-will-allow-table-merging-for-reservations")}
                </Typography>
              </div>
              <Switch checked={allowMultipleTableSelection ?? false}
                      onClick={() => toggleAllowTableMerging("allowMultipleTableSelection", !allowMultipleTableSelection)} />
            </div>
          </div>

        </div>
      </Fragment>
    );
  };
  const getThumbnailUploader = () => {
    return (
      <>
        <Dropzone onDrop={acceptedFiles => !isEmpty(acceptedFiles) && onUpload(acceptedFiles[0])}
                  accept={"image/jpeg, image/png, image/jpg, image/gif"} maxSize={5000000}>
          {({
            getRootProps,
            getInputProps
          }) => (
            <section style={{
              border: `1px dashed ${palette.grayscale["350"]}`,
              borderRadius: 12
            }}>
              <div
                {...getRootProps()}
                className={classes.uploaderContainer}
                style={selectedCoverImage.src ? { backgroundImage: `url('${selectedCoverImage.src}')` } : null}
              >
                <input {...getInputProps()} />
                {
                  reservationCoverImages.length === 0 && (
                    <>
                      <div style={{ marginTop: isMobile ? 10 : 30.5 }}>
                        <UploadIcon />
                      </div>
                      <div>
                        <Typography style={{
                          ...typography.body.medium,
                          whiteSpace: "break-spaces"
                        }}>{t("menu-editor-form-thumbnail-field-placeholder")}</Typography>
                        <Typography style={{
                          ...typography.body.medium,
                          color: palette.grayscale["600"],
                          whiteSpace: "break-spaces"
                        }}>{t("menu-editor-items-thumbnail-field-description")}</Typography>
                      </div>
                    </>
                  )
                }
              </div>
            </section>
          )}
        </Dropzone>
        {reservationCoverImages.length > 1 && (
          <>
            <div style={{
              marginTop: 10,
              display: "flex",
              flexWrap: "wrap",
              gap: 10
            }}>
              {
                reservationCoverImages.map((it, index) => {
                  if (index === 0) {
                    return null;
                  }
                  return (
                    <div style={{
                      border: "1px dashed rgb(216, 215, 214)",
                      padding: 5,
                      position: "relative"
                    }}
                         onClick={() => onDeleteCoverImageAtIndex(index)}
                    >
                      <div style={{
                        position: "absolute",
                        top: 10,
                        right: 10,
                        cursor: "pointer"
                      }}>
                        <CloseIcon24 />
                      </div>
                      <img style={{
                        height: 100,
                        width: 100
                      }} key={index} src={it.src} alt={it.alt} />
                    </div>
                  );
                })
              }
            </div>
          </>
        )}
      </>
    );
  };
  const renderViewGuestCommunications = () => {
    return (
      <div className={classes.settings}>
        <div className={classes.settingsForm}>
          <ReservationsSettingsHeader classes={classes} t={t} />

          <ReservationSettingsAllowGuests
            configuration={configuration}
            setSaving={setSaving}
            setConfiguration={setConfiguration}
            allowCustomerReservations={allowCustomerReservations}
            classes={classes} t={t}
          />

          <div className={classes.counterSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("max-number-of-guests-allowed")}
                </Typography>
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.grayscale["600"]
                }}>
                  {t("set-the-max-amount-of-guests-a-reservation-can-have")}
                </Typography>
              </div>
              <QuantityButton counter={maxGuestsPerReservation} onMore={incrementMaxGuestsPerReservation}
                              onLess={decrementMaxGuestsPerReservation} />
            </div>
          </div>

          <ReservationSettingsLeadTime
            t={t}
            classes={classes}
            updateMinimumLeadTimeInMinutes={updateMinimumLeadTimeInMinutes}
            minimumLeadTimeInMinutes={minimumLeadTimeInMinutes}
          />

          {/* Cover image */}
          <div className={classes.composedSetting} style={{ marginTop: 24 }}>
            <Typography style={{
              ...typography.body.medium,
              marginBottom: 8
            }}>{t("menu-editor-form-thumbnail-field-label")}</Typography>
            {getThumbnailUploader()}
          </div>
          <ReservationSettingsCustomerMessage
            configuration={configuration}
            setConfiguration={setConfiguration}
            setSaving={setSaving}
            includedGoodToKnowTextI18n={includedGoodToKnowTextI18n}
            classes={classes}
            t={t}
          />

          {/*replyTo setting*/}
          <div className={classes.composedSetting} style={{ marginTop: 24 }}>
            <div style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between"
            }}>
              <Typography style={{ ...typography.body.medium }}>
                {t("set-reply-to-email")}
              </Typography>
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                          style={{ padding: 0 }} onClick={() => setUpdatingReplyTo(true)}>
                <TableUpdateBtn t={t} />
              </ButtonBase>
            </div>
            <div style={{ marginTop: 12 }}>
              {getReplyToField()}
            </div>
          </div>

          <ReservationSettingsShowDurationInEmail
            configuration={configuration}
            setSaving={setSaving}
            setConfiguration={setConfiguration}
            includeReservationDurationInEmail={includeReservationDurationInEmail}
            classes={classes}
            t={t}
          />

          {/*send sms for reservation creation/update*/}
          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("allow-sms-notification-reservation")}
                </Typography>
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.grayscale["600"]
                }}>
                  {t("this-will-allow-sms-notification-for-reservations-update-and-creation")}
                </Typography>
              </div>
              <Switch checked={allowSmsNotifications} onClick={() => toggleAllowSmsNotification(!allowSmsNotifications)} />
            </div>
          </div>


          {/*showCustomerSlotsExplanation*/}
          <div className={classes.switchSetting} style={{ marginTop: 24 }}>
            <div className={classes.flex}>
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("explain-slots-for-guests")}
                </Typography>
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.grayscale["600"]
                }}>
                  {t("allow-to-view-explanation-for-guest-reservation-slots")}
                </Typography>
              </div>
              <Switch checked={displaySlotExplainButton} onClick={toggleDisplaySlotExplainButton} />
            </div>
          </div>


          {/* show terms */}
          <div className={classes.composedSetting} style={{ marginTop: 24 }}>
            <div style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between"
            }}>
              <Typography style={{ ...typography.body.medium }}>
                {t("setup-terms")}
              </Typography>
              <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                          style={{ padding: 0 }} onClick={() => setUpdatingTerms(true)}>
                <TableUpdateBtn t={t} />
              </ButtonBase>
            </div>
            <div style={{ marginTop: 12 }}>
              {getSetupTermsField()}
            </div>
          </div>

        </div>
      </div>
    );
  };
  const renderViewTableGroups = () => {
    return (
      <Fragment>
        <ReservationSettingsTableGroups />
      </Fragment>
    );
  };
  const renderViewSpecialDays = () => {
    return (
      <Fragment>
        <ReservationSettingsSpecialDays />
      </Fragment>
    );
  }
  const renderView = () => {
    switch (view) {
      case settingsConfigurationView.GENERAL_SETTINGS.value: {
        return renderViewGeneralSettings();
      }
      case settingsConfigurationView.GUEST_COMMUNICATION.value: {
        return renderViewGuestCommunications();
      }
      case settingsConfigurationView.TABLE_GROUPS.value: {
        return renderViewTableGroups();
      }
      case settingsConfigurationView.SPECIAL_DAYS.value: {
        return renderViewSpecialDays();
      }
      default:
        return null;
    }
  };

  return (
    <Fragment>
      <div className={classes.container}>
        <div className={classes.content}>
        <div className={clsx(classes.left, { [classes.leftMobile]: isMobile })} style={{ position: "relative" }}>
            <div className={classes.header}>
              <Typography className={classes.headerTitle}>{t("reservations-sidebar-title")}</Typography>
            </div>
            <div className={classes.navigation}>
              {renderNavigation()}
            </div>
          </div>
          <div className={clsx(classes.right, { [classes.rightMobile]: isMobile })}>
            {renderView()}
          </div>
        </div>
      </div>

    </Fragment>
  );
};

export default withTranslation("common")(ReservationsSettingsV2);
