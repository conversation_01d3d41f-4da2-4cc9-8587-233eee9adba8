import React, { useEffect, useState } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { withTranslation } from "../../../../../../../i18n";
import Modal from "../../../../../_popup/Modal";
import shadows from "../../../../../../../styles/shadows";
import ModalBar from "../../../../../_navigation/ModalBar";
import { useDispatch } from "react-redux";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../../../styles/typography";
import palette from "../../../../../../../styles/palette";
import DateRangePicker from "../../../../../DateRangePicker";
import moment from "moment/moment";
import Field from "../../../../../form/Field";
import Switch from "../../../../../_toggles/Switch";
import { ReservationSettingsSpecialDaysReservationWindows } from "./ReservationSettingsSpecialDaysReservationWindows";
import { deleteSpecialDay, postSpecialDay, putSpecialDay } from "../../../../../../api";
import { appActions } from "../../../../../../../redux/actions";
import { isNull } from "lodash";
import Button from "@material-ui/core/Button";
import { Confirm } from "../../../../../Confirmation";

const useStyles = makeStyles(() => ({
  modalWrapper: {
    height: "100%",
    overflow: "auto",
    background: "#F2F2F2",
    paddingTop: 12,
    paddingLeft: 12,
    paddingRight: 16,
    paddingBottom: 24
  },
  modalContainer: {
    height: "100%",
    margin: "0 auto",
    display: "flex",
    maxWidth: "464px",
    flexDirection: "row"
  },
  modalContent: {
    width: "100%",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    marginBottom: 24,
    paddingBottom: 24
  },
  switchSetting: {
    padding: 12,
    borderRadius: 12,
    background: palette.grayscale["100"],
    ...shadows.base
  },
  flex: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  }
}));

export const ReservationSettingsSpecialDaysModal = withTranslation("common")(({
  t,
  open,
  handleClose,
  restaurantId,
  refreshSpecialDays,
  specialDayForEditing
}) => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const [isFormValid, setIsFormValid] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [form, setForm] = useState({
    id: null,
    name: "",
    waiterReservationsDisabledMessage: "",
    customerReservationsDisabledMessage: "",
    startDate: new Date(),
    endDate: new Date(),
    allowReservations: false,
    allowCustomerReservations: false,
    reservationWindows: []
  });

  useEffect(() => {
    if (!isNull(specialDayForEditing) && !isNull(specialDayForEditing.id)) {
      const deserializedForm = deserializeForm(specialDayForEditing);
      setForm({ ...form, ...deserializedForm });
      setIsEditing(true);
    } else {
      setForm({
        id: null,
        name: "",
        waiterReservationsDisabledMessage: "",
        customerReservationsDisabledMessage: "",
        startDate: new Date(),
        endDate: new Date(),
        allowReservations: false,
        allowCustomerReservations: false,
        reservationWindows: []
      });
    }
  }, [specialDayForEditing]);

  const serializeForm = () => {
    // Need to convert startDate and endDate to YYYY-MM-dd format for the server
    return {
      ...form,
      startDate: moment(form.startDate).format("YYYY-MM-DD"),
      endDate: moment(form.endDate).format("YYYY-MM-DD")
    };
  };
  const deserializeForm = (form) => {
    // Convert startDate and endDate to Date format for the UI
    return {
      ...form,
      startDate: moment(form.startDate).toDate(),
      endDate: moment(form.endDate).toDate()
    };
  };

  const handleDelete = () => {
    deleteSpecialDay(restaurantId, form.id)
      .then(() => {
        refreshSpecialDays();
        handleClose();
      })
      .catch(e => {
        const { response: { data: { title } } } = e;
        dispatch(appActions.setNotification(title || e, "error"));
      });
  };

  const onDoneHandler = () => {
    const serializedForm = serializeForm();
    if (isNull(serializedForm.id)) {
      postSpecialDay(restaurantId, serializedForm)
        .then(() => {
          refreshSpecialDays();
          handleClose();
        })
        .catch(e => {
          const { response: { data: { title } } } = e;
          dispatch(appActions.setNotification(title || e, "error"));
        });
    } else {
      putSpecialDay(restaurantId, serializedForm.id, serializedForm)
        .then(() => {
          refreshSpecialDays();
          handleClose();
        })
        .catch(e => {
          const { response: { data: { title } } } = e;
          dispatch(appActions.setNotification(title || e, "error"));
        });
    }

  };

  useEffect(() => {
    // Check if the form is valid
    if (form.name !== "") {
      setIsFormValid(true);
    } else {
      setIsFormValid(false);
    }
  }, [form]);

  const setFormHandle = (obj) => {
    setForm({ ...form, ...obj });
  };

  const handleDateRangeChange = (v) => {
    const startDate = v[0];
    const endDate = v[1];
    setFormHandle({
      startDate,
      endDate
    });
    //setSelectedDateRange(v)
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      fullScreen
      style={{ marginTop: 16 }}
      PaperProps={{
        style: {
          background: "transparent", ...shadows.large,
          zIndex: 1200
        }
      }}
    >
      <ModalBar title={t("new")} onClose={handleClose} onDone={isFormValid ? onDoneHandler : null} />
      {
        open && (
          <div style={{
            height: "100%",
            overflow: "auto"
          }}>
            <div className={classes.modalWrapper}>
              <div className={classes.modalContainer}>
                <div className={classes.modalContent}>
                  <div>
                    <div>
                      <div style={{ marginRight: 8 }}>

                      </div>
                      <div style={{ marginTop: 8 }}>
                        <Typography style={{ ...typography.body.medium }}>
                          {t("reservation-settings-special-days-modal-basic-information-name-title")}
                        </Typography>
                        <Field
                          variant={"outlined"}
                          style={{
                            borderRadius: 12,
                            marginBottom: 8,
                            marginTop: 12,
                            width: "100%"
                          }}
                          value={form.name}
                          name="name"
                          onChange={(x) => {
                            setFormHandle({ name: x.target.value });
                          }}
                        />
                      </div>
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Typography style={{ ...typography.body.medium }}>
                        {t("reservation-settings-special-days-modal-basic-information-date-selection")}
                      </Typography>
                      <DateRangePicker
                        disableFuture={false}
                        range={[form.startDate, form.endDate]}
                        setRange={handleDateRangeChange}
                      />
                      <Typography style={{ ...typography.body.regular }}>
                        {t("reservation-settings-special-days-modal-basic-information-date-selection-information")}
                      </Typography>
                    </div>
                  </div>
                  <div>
                    <ReservationSettingsSpecialDaysReservationWindows
                      setFormHandle={setFormHandle}
                      form={form}
                    />
                  </div>
                  {
                    isEditing && (
                      <div style={{ marginTop: 24 }}>
                        <Confirm
                          title={t("product-update-delete-entry")}
                          body={(
                            <Typography color="textSecondary" variant="body2">
                              <br />
                              {t("delete-table-description")}
                              <br />
                              {t("product-update-are-you-sure-confirm")}
                            </Typography>
                          )}>
                          {confirm => (
                            <Button style={{
                              width: 100,
                              borderRadius: 12,
                              padding: "12px 24px",
                              backgroundColor: palette.negative["500"]
                            }} onClick={confirm(handleDelete)}>
                              <Typography style={{
                                ...typography.body.medium,
                                color: palette.grayscale.white
                              }}>{t("common-delete")}</Typography>
                            </Button>
                          )}
                        </Confirm>
                      </div>
                    )
                  }

                </div>
              </div>
            </div>
          </div>
        )
      }
    </Modal>
  );
});
