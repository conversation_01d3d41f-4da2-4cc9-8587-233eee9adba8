import React, { Fragment, useEffect, useState } from "react";
import { withTranslation } from "../../../../../../../i18n";
import shadows from "../../../../../../../styles/shadows";
import Modal from "../../../../../_popup/Modal";
import ModalBar from "../../../../../_navigation/ModalBar";
import { makeStyles } from "@material-ui/core/styles";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../../../styles/typography";
import palette from "../../../../../../../styles/palette";
import { ButtonBase } from "@material-ui/core";
import Field from "../../../../../form/Field";
import TextUpdateModal from "../../../../../_popup/TextUpdateModal";
import { CollapseIcon20, Confirm20, EditIcon20, ExpandIcon20 } from "../../../../../../utils/icons";
import clsx from "clsx";
import TimeSelector from "../../../../../Administration/TimeSelector";
import moment from "moment";
import Switch from "../../../../../_toggles/Switch";
import isEmpty from "../../../../../../utils/isEmpty";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import { noop } from "lodash";
import TableBody from "@material-ui/core/TableBody";
import { Confirm } from "../../../../../Confirmation";
import Button from "@material-ui/core/Button";

const useStyles = makeStyles(() => ({
  modalWrapper: {
    height: "100%",
    overflow: "auto",
    background: "#F2F2F2",
    paddingTop: 12,
    paddingLeft: 12,
    paddingRight: 16,
    paddingBottom: 24
  },
  modalContainer: {
    height: "100%",
    margin: "0 auto",
    display: "flex",
    maxWidth: "464px",
    flexDirection: "row"
  },
  modalContent: {
    width: "100%",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    marginBottom: 24,
    paddingBottom: 24
  },
  switchSetting: {
    padding: 12,
    borderRadius: 12,
    background: palette.grayscale["100"],
    ...shadows.base
  },
  flex: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  }
}));

const TableUpdateBtn = ({ t }) => (
  <div style={{
    display: "flex",
    alignItems: "center",
    paddingLeft: 10,
    paddingRight: 10,
    minWidth: 100
  }}>
    <EditIcon20 />
    <Typography style={{
      ...typography.body.medium,
      color: palette.grayscale["600"],
      marginLeft: 2
    }}>{t("common-update")}</Typography>
  </div>
);

const NameField = ({
  classes,
  t,
  name,
  setForm
}) => {

  const [updatingName, setUpdatingName] = useState(false);

  const updateName = (value) => {
    if (!value) {
      return;
    }
    setForm({ name: value });
    setUpdatingName(false);
  };

  return (
    <div style={{ marginTop: 24 }}>
      <div style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between"
      }}>
        <div style={{ display: "flex" }}>
          <Typography style={{ ...typography.body.medium }}>
            {t("name")}
          </Typography>
          <Typography style={{
            ...typography.body.medium,
            color: palette.primary["500"],
          }}>*</Typography>
        </div>
      </div>
      <Fragment>
        <Field
          variant={"outlined"}
          style={{
            borderRadius: 12,
            marginBottom: 8,
            marginTop: 12,
            width: "100%"
          }}
          value={name}
          name="name"
          onChange={(x) => {
            setForm({ name: x.target.value });
          }}
        />
      </Fragment>

    </div>
  );
};

const ScheduleField = ({
  t,
  setForm,
  startTime = "06:00:00",
  endTime = "05:30:00"
}) => {
  const [scheduledFrom, setScheduledFrom] = useState(startTime + ":00");
  const [scheduledTo, setScheduledTo] = useState(endTime + ":00");

  useEffect(() => {
    setForm({
      startTime: scheduledFrom,
      endTime: scheduledTo
    });
  }, [scheduledFrom, scheduledTo]);

  return (
    <div style={{ marginTop: 24 }}>
      <div style={{
        display: "flex",
        alignItems: "flex-start",
        justifyContent: "flex-start",
        marginTop: 12,
        gap: 24
      }}>
        <div>
          <div style={{ display: "flex" }}>
            <Typography style={{
              ...typography.body.medium,
              marginBottom: 8
            }}>{t("menu-editor-form-schedule-from-field-label")}</Typography>
            <Typography style={{
              ...typography.body.medium,
              color: palette.primary["500"],
              marginBottom: 8
            }}>*</Typography>
          </div>
          <TimeSelector
            beginLimit={"06:00"} endLimit={"23:45"}
            name={"scheduledFrom"} value={scheduledFrom}
            onChange={(e) => setScheduledFrom(e.target.value)} />
        </div>
        <div>
          <div style={{ display: "flex" }}>
            <Typography style={{
              ...typography.body.medium,
              marginBottom: 8
            }}>{t("menu-editor-form-schedule-to-field-label")}</Typography>
            <Typography style={{
              ...typography.body.medium,
              color: palette.primary["500"],
              marginBottom: 8
            }}>*</Typography>
          </div>
          <TimeSelector
            beginLimit={"00:00"} endLimit={"23:45"}
            name={"scheduledTo"} value={scheduledTo}
            onChange={(e) => setScheduledTo(e.target.value)} />
        </div>
      </div>
    </div>
  );
};

export const AllowLimits = ({
  t,
  classes,
  allowLimits,
  setForm
}) => {

  const toggle = () => {
    setForm({
      hasLimits: !allowLimits
    });
  };

  return (
    <div className={classes.switchSetting} style={{ marginTop: 24 }}>
      <div className={classes.flex}>
        <div style={{ marginRight: 8 }}>
          <Typography style={{ ...typography.body.medium }}>
            {t("reservations-window-v2-modal-allow-limits-title")}
          </Typography>
          <Typography style={{
            ...typography.body.regular,
            color: palette.grayscale["600"]
          }}>
            {t("reservations-window-v2-modal-allow-limits-description")}
          </Typography>
        </div>
        <Switch
          checked={allowLimits}
          onClick={toggle}
        />
      </div>
    </div>
  );
};

const ReservationCapacityField = ({
  t,
  classes,
  setForm,
  capacity
}) => {
  return (
    <div style={{ marginTop: 24 }}>
      <div style={{}}>
        <Typography style={{ ...typography.body.medium }}>
          {t("reservations-settings-v2-modal-capacity-title")}
        </Typography>
        <Typography style={{
          ...typography.body.regular,
          color: palette.grayscale["600"]
        }}>
          {t("reservations-settings-v2-modal-capacity-description")}
        </Typography>
      </div>
      <div>
        <Field
          variant={"outlined"}
          style={{
            borderRadius: 12,
            marginBottom: 8,
            marginTop: 12
          }}
          className={classes.field}
          value={capacity}
          name="order"
          onChange={(e) => setForm({ seatingCapacity: e.target.value })}
          type="number"
        />
      </div>
    </div>
  );
};

const StartLimitField = ({
  t,
  classes,
  setForm,
  startLimit
}) => {
  return (
    <div className={classes.composedSetting} style={{ marginTop: 24 }}>
      <div style={{}}>
        <Typography style={{ ...typography.body.medium }}>
          {t("reservations-settings-v2-modal-start-limit-title")}
        </Typography>
        <Typography style={{
          ...typography.body.regular,
          color: palette.grayscale["600"]
        }}>
          {t("reservations-settings-v2-modal-start-limit-description")}
        </Typography>
      </div>
      <div>
        <Field
          variant={"outlined"}
          style={{
            borderRadius: 12,
            marginBottom: 8,
            marginTop: 12
          }}
          className={classes.field}
          value={startLimit}
          name="order"
          onChange={(e) => setForm({ startLimit: e.target.value })}
          type="number"
        />
      </div>
    </div>
  );
};

const TableResetBtn = ({ t }) => (

  <div style={{
    display: "flex",
    alignItems: "center",
    paddingLeft: 10,
    paddingRight: 10,
    minWidth: 100
  }}>
    <Confirm20 />
    <Typography style={{
      ...typography.body.medium,
      color: palette.grayscale["600"],
      marginLeft: 2
    }}>{t("common-reset")}</Typography>
  </div>

);


const ShiftsTableUpdateModal = ({
  open,
  onClose,
  titleI18n,
  onDone,
  t,
  selectedSlot,
  selectedSlotData
}) => {
  const valid = true;

  const [startTime, setStartTime] = useState(0);
  const [capacity, setCapacity] = useState(0);

  useEffect(() => {
    if (selectedSlotData?.startLimit) {
      setStartTime(selectedSlotData?.startLimit);
    }
  }, [selectedSlotData?.startLimit]);

  useEffect(() => {
    if (selectedSlotData?.seatingCapacity) {
      setCapacity(selectedSlotData?.seatingCapacity);
    }
  }, [selectedSlotData?.seatingCapacity]);

  const onDoneHandler = () => {
    onDone(selectedSlot, {
      startLimit: startTime,
      seatingCapacity: capacity
    });
    onClose();
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{
        style: {
          background: "transparent", ...shadows.large,
          width: 500,
          maxWidth: "90%"
        }
      }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} onDone={valid ? onDoneHandler : null} />
      <div style={{
        height: "100%",
        overflow: "auto",
        background: palette.grayscale["200"],
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20
      }}>
        <div style={{
          paddingLeft: 20,
          paddingRight: 20,
          paddingTop: 20,
          paddingBottom: 40
        }}>
          <Field
            label={t("reservations-settings-v2-modal-start-limit-title")}
            value={startTime}
            onChange={(e) => setStartTime(e.target.value)}
            error={!valid}
            type={"number"}
          />
        </div>
        <div style={{
          paddingLeft: 20,
          paddingRight: 20,
          paddingBottom: 40
        }}>
          <Field
            label={t("reservations-settings-v2-modal-capacity-title")}
            value={capacity}
            onChange={(e) => setCapacity(e.target.value)}
            error={!valid}
            type={"number"}
          />
        </div>
      </div>
    </Modal>
  );
};


const ShiftsTable = ({
  t,
  slots,
  startLimit,
  seatingCapacity,
  resetAllData,
  editSlot
}) => {
  const [open, setOpen] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [selectedSlotData, setSelectedSlotData] = useState(null);

  const onUpdateModalClose = () => {
    setSelectedSlot(null);
    setSelectedSlotData(null);
    setOpen(false);
  };

  const onUpdateModalOpen = (slotIndex, slot) => {
    setSelectedSlot(slotIndex);
    setSelectedSlotData(slot);
    setOpen(true);
  };

  const onUpdateModalDone = (slotIndex, slotData) => {
    editSlot(slotIndex, slotData);
    onUpdateModalClose();
  }

  const isEqual = (a, b) => a.toString() === b.toString();
  return (
    <Fragment>
      <TableContainer stickyHeader>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell
                style={{ padding: 13 }}>{t("reservations-settings-v2-modal-shift-table-column-slot")}</TableCell>
              <TableCell
                style={{ padding: 13 }}>{t("reservations-settings-v2-modal-shift-table-column-start")}</TableCell>
              <TableCell
                style={{ padding: 13 }}>{t("reservations-settings-v2-modal-shift-table-column-seating")}</TableCell>
              <TableCell style={{ padding: 13 }}>
                <ButtonBase
                  disableRipple
                  disableTouchRipple
                  disableElevation
                  disableFocusRipple
                  onClick={resetAllData}
                  style={{ padding: 0 }}>
                  <TableResetBtn t={t} />
                </ButtonBase>
              </TableCell>
              <TableCell style={{ padding: 13 }}></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {slots.map((slot, index) => (
              <TableRow key={index}>
                <TableCell style={{ padding: 13 }}>{slot.time}</TableCell>
                <TableCell style={{ padding: 13 }}>{slot.startLimit}</TableCell>
                <TableCell style={{ padding: 13 }}>{slot.seatingCapacity}</TableCell>
                <TableCell style={{ padding: 13 }}>
                  {(!isEqual(startLimit, slot.startLimit) || !isEqual(seatingCapacity, slot.seatingCapacity)) && (
                    <>
                      <ButtonBase
                        disableRipple
                        disableTouchRipple
                        disableElevation
                        disableFocusRipple
                        onClick={() => editSlot(index, {
                          startLimit: startLimit,
                          seatingCapacity: seatingCapacity
                        })}
                        style={{ padding: 0 }}>
                        <TableResetBtn t={t} />
                      </ButtonBase>
                    </>
                  )}
                </TableCell>
                <TableCell>
                  <ButtonBase
                    disableRipple
                    disableTouchRipple
                    disableElevation
                    disableFocusRipple
                    style={{ padding: 0 }}
                    onClick={() => onUpdateModalOpen(index, slot)}
                  >
                    <TableUpdateBtn t={t} />
                  </ButtonBase>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        {
          open && (
            <ShiftsTableUpdateModal
              t={t}
              open={open}
              onClose={onUpdateModalClose}
              onDone={onUpdateModalDone}
              titleI18n={"common-update"}
              selectedSlotData={selectedSlotData}
              selectedSlot={selectedSlot}
            />
          )
        }
      </TableContainer>
    </Fragment>
  );
};

export const ReservationSettingsSpecialDaysReservationWindowsModal = withTranslation("common")(({
  t,
  open,
  handleClose,
  restaurantId,
  refreshReservationWindows,
  addReservationWindow,
  updateReservationWindow,
  selectedReservationWindow,
  selectedReservationIndex = -1,
  handleDeleteReservationWindow
}) => {
  const classes = useStyles();

  const [isFormValid, setIsFormValid] = useState(false);
  const [isShowingAdvancedOptions, setIsShowingAdvancedOptions] = useState(false);
  const [form, setForm] = useState({
    id: null,
    startTime: "06:00",
    endTime: "05:30",
    hasLimits: false,
    seatingCapacity: 0,
    startLimit: 0,
    minimumLeadTimeInMinutes: 0,
    slots: []
  });

  useEffect(() => {
    if (selectedReservationIndex !== -1) {
      setForm(selectedReservationWindow);
    }
  }, [selectedReservationWindow]);

  const setFormHandle = (obj) => {
    setForm({ ...form, ...obj });
  };

  function calculateSlots (from, to) {

    const isEarlierThanEndLimit = (timeValue, lastValue, nextValue) => {
      let timeValueIsEarlier = moment(timeValue, "HH:mm").diff(moment(lastValue, "HH:mm")) < 0;
      let timeValueIsLaterThanLastValue = nextValue === undefined ? true : moment(nextValue, "HH:mm").diff(moment(timeValue, "HH:mm")) < 0;
      return timeValueIsEarlier && timeValueIsLaterThanLastValue;
    };

    const options = [];
    let timeValue = from || "00:00";
    let nextValue;
    let lastValue = to || "23:59";

    const first = moment(timeValue, "HH:mm").add(0, "minutes").format("HH:mm");
    options.push(first);
    while (isEarlierThanEndLimit(timeValue, lastValue, nextValue)) {
      nextValue = timeValue;
      timeValue = moment(timeValue, "HH:mm").add(15, "minutes").format("HH:mm");
      options.push(timeValue);
    }

    options.pop(); // Remove the last value.
    return options;
  }

  function calculateSlotsData (slots, startLimit, seatingCapacity, slotsData) {
    const localData = slotsData;
    const newSlotsObject = {};
    slots.forEach((slot) => {
      newSlotsObject[slot] = {};
    });
    Object.keys(newSlotsObject).forEach((key) => {
      if (!localData[key]) {
        newSlotsObject[key] = {
          startLimit: startLimit || 0,
          seatingCapacity: seatingCapacity || 0
        };
      } else {
        newSlotsObject[key] = localData[key];
      }
    });
    return newSlotsObject;
  }

  useEffect(() => {
    const {
      startTime,
      endTime
    } = form;
    if (startTime !== "" && endTime !== "") {
      const _slotsData = form.slots ? form.slots.reduce((acc, slot) => {
        acc[slot.time] = {
          startLimit: slot.startLimit,
          seatingCapacity: slot.seatingCapacity
        };
        return acc;
      }, {}) : {};
      const isOvernightSlots = moment(startTime, "HH:mm").diff(moment(endTime, "HH:mm")) > 0;
      let slots = [];
      if (isOvernightSlots) {
        slots = calculateSlots(startTime, "24:00");
        slots = slots.concat(calculateSlots("00:00", endTime));
      } else {
        slots = calculateSlots(startTime, endTime);
      }
      const data = calculateSlotsData(slots, form.startLimit, form.seatingCapacity, _slotsData);
      const formSlots = Object.keys(data).map(key => {
        return {
          time: key,
          startLimit: data[key].startLimit,
          seatingCapacity: data[key].seatingCapacity
        };
      });
      setForm({
        ...form,
        slots: formSlots
      });
    }
  }, [form.startTime, form.endTime]);

  useEffect(() => {
    if (!isEmpty(form.name) && !isEmpty(form.slots) && !isEmpty(form.startLimit) && !isEmpty(form.seatingCapacity)) {
      setIsFormValid(true);
    } else {
      setIsFormValid(false);
    }
  }, [form]);

  const onDoneHandler = () => {
    if (selectedReservationIndex === -1) {
      addReservationWindow(form);
      handleClose();
    } else {
      const localForm = {...form};
      updateReservationWindow(localForm, selectedReservationIndex);
      handleClose();
    }
  };

  const onClose = () => {
    setForm({
      startTime: "06:00",
      endTime: "05:30",
      hasLimits: false,
      seatingCapacity: 0,
      startLimit: 0,
      minimumLeadTimeInMinutes: 0,
      slots: []
    });
    handleClose();
  };

  const resetAllData = () => {
    const slots = form.slots.map(slot => {
      return {
        time: slot.time,
        startLimit: form.startLimit,
        seatingCapacity: form.seatingCapacity
      };
    });
    setForm({
      ...form,
      slots
    });
  };

  const handleDelete = () => {
    handleDeleteReservationWindow(selectedReservationIndex);
    handleClose();
  };

  const editSlot = (slotIndex, data) => {
    const slots = [...form.slots];
    const localSlotData = slots[slotIndex];
    localSlotData.startLimit = data.startLimit;
    localSlotData.seatingCapacity = data.seatingCapacity;
    slots[slotIndex] = localSlotData;
    setForm({
      ...form,
      slots
    });
  }

  return (
    <Modal
      open={open}
      onClose={onClose}
      fullScreen
      style={{ marginTop: 16 }}
      PaperProps={{
        style: {
          background: "transparent", ...shadows.large,
          zIndex: 1200
        }
      }}
    >
      <ModalBar title={t("new")} onClose={onClose} onDone={isFormValid ? onDoneHandler : null} />
      {
        open && (
          <div style={{
            height: "100%",
            overflow: "auto"
          }}>
            <div className={classes.modalWrapper}>
              <div className={classes.modalContainer}>
                <div className={classes.modalContent}>
                  {/* Name Field */}
                  <NameField name={form.name} setForm={setFormHandle} t={t} />
                  <ScheduleField startTime={form.startTime} endTime={form.endTime} setForm={setFormHandle} t={t} />
                  <AllowLimits allowLimits={form.hasLimits} classes={classes} t={t} setForm={setFormHandle} />
                  {
                    form.hasLimits &&
                    (
                      <>
                        <ReservationCapacityField capacity={form.seatingCapacity} setForm={setFormHandle} t={t}
                                                  classes={classes} />

                        <StartLimitField setForm={setFormHandle} startLimit={form.startLimit} t={t} classes={classes} />

                      </>
                    )
                  }
                  <div style={{ marginTop: 24 }}>
                    <div style={{
                      display: "flex",
                      //flexDirection: isMobile ? "column" : "row",
                      marginTop: 24,
                      paddingBottom: 24,
                      alignItems: "flex-start",
                      justifyContent: "flex-start",
                      borderTop: `1px solid ${palette.grayscale["400"]}`
                    }}>
                      <div>
                        <ButtonBase style={{ marginTop: 24 }}
                                    onClick={() => setIsShowingAdvancedOptions(!isShowingAdvancedOptions)}>
                          {isShowingAdvancedOptions ? (<CollapseIcon20 style={{ marginRight: 5 }} />) : (
                            <ExpandIcon20 style={{ marginRight: 5 }} />)}
                          <Typography style={{
                            ...typography.body.medium,
                            color: palette.grayscale["600"]
                          }}>{isShowingAdvancedOptions ? t("menu-editor-hide-advanced-options") : t("menu-editor-show-advanced-options")}</Typography>
                        </ButtonBase>
                      </div>
                    </div>
                    {
                      isShowingAdvancedOptions && form.hasLimits && (
                        <Fragment>
                          <ShiftsTable
                            t={t}
                            slots={form.slots}
                            startLimit={form.startLimit}
                            seatingCapacity={form.seatingCapacity}
                            resetAllData={resetAllData}
                            editSlot={editSlot}
                          />
                          <div style={{
                            marginTop: 24,
                            marginBottom: 12,
                            display: selectedReservationIndex === -1 ? "none" : "inherit"
                          }}>
                            <Confirm
                              title={t("product-update-delete-entry")}
                              body={(
                                <Typography color="textSecondary" variant="body2">
                                  <br />
                                  {t("delete-table-description")}
                                  <br />
                                  {t("product-update-are-you-sure-confirm")}
                                </Typography>
                              )}>
                              {confirm => (
                                <Button style={{
                                  width: 100,
                                  borderRadius: 12,
                                  padding: "12px 24px",
                                  backgroundColor: palette.negative["500"]
                                }} onClick={confirm(handleDelete)}>
                                  <Typography style={{
                                    ...typography.body.medium,
                                    color: palette.grayscale.white
                                  }}>{t("common-delete")}</Typography>
                                </Button>
                              )}
                            </Confirm>
                          </div>
                        </Fragment>
                      )
                    }
                    {
                      isShowingAdvancedOptions && !form.hasLimits && (
                        <Fragment>
                          <div style={{
                            marginTop: 24,
                            marginBottom: 12,
                            display: selectedReservationIndex === -1 ? "none" : "inherit"
                          }}>
                            <Confirm
                              title={t("product-update-delete-entry")}
                              body={(
                                <Typography color="textSecondary" variant="body2">
                                  <br />
                                  {t("delete-table-description")}
                                  <br />
                                  {t("product-update-are-you-sure-confirm")}
                                </Typography>
                              )}>
                              {confirm => (
                                <Button style={{
                                  width: 100,
                                  borderRadius: 12,
                                  padding: "12px 24px",
                                  backgroundColor: palette.negative["500"]
                                }} onClick={confirm(handleDelete)}>
                                  <Typography style={{
                                    ...typography.body.medium,
                                    color: palette.grayscale.white
                                  }}>{t("common-delete")}</Typography>
                                </Button>
                              )}
                            </Confirm>
                          </div>
                        </Fragment>
                      )
                    }
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      }
    </Modal>
  );
});
