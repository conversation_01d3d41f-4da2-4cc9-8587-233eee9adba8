import React, { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { withTranslation } from "../../../../i18n";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import Switch from "../../_toggles/Switch";
import {
  getLieferandoRestaurants, getRestaurantConfiguration,
  loginLieferandoRestaurant,
  logoutLieferandoRestaurant,
  setLieferandoActivationCode, updateRestaurantConfigurationById
} from "../../../api";
import { restaurantSelectors } from "../../../../redux/selectors";
import Loading from "../../Loading";
import useStyles from "./styles";
import { noop } from "../../../utils/const";
import isEmpty from "../../../utils/isEmpty";
import Field from "../../form/Field";
import TextUpdateModal from "../../_popup/TextUpdateModal";
import { ButtonBase } from "@material-ui/core";
import { ChevronDown20new, EditIcon20 } from "../../../utils/icons";
import Select from "@material-ui/core/Select";
import MenuItem from "@material-ui/core/MenuItem";
import { configurationActions } from "../../../../redux/actions";
import TimeInSecondsUpdateModal from "../../_popup/TimeInSecondsUpdateModal";

const LieferandoSettings = ({ t , isManaged}) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const [fetching, setFetching] = useState(false);
  const [store, setStore] = useState({});
  const { disabled, partnerRestaurantId } = (store || {});
  const isOperating = !isEmpty(store) && !disabled
  
  const [updating, setUpdating] = useState(false);
  const [updatingLieferandoCode, setUpdatingLieferandoCode] = useState(false);
  
  const [configurationForm, setConfigurationForm] = useState({partnerConfigs:[]});
  
  const { partnerConfigs } = configurationForm
  const dpIndex = (configurationForm.partnerConfigs ?? []).findIndex(config => config.partnerId === "LIEFERANDO");
  const hasPartnerConfig = partnerConfigs?.length && dpIndex !== -1
  
  const [updatingAutoRejectAcceptTime, setUpdatingAutoRejectAcceptTime] = useState(false)
  
  const fetchConfiguration = () => {
    getRestaurantConfiguration(restaurantId)
      .then(({ data = {} }) => {
        setConfigurationForm(data ?? {});
      })
      .catch(() => {
      });
    dispatch(configurationActions.getConfiguration(restaurantId));
  };
  
  useEffect(() => {
    fetchConfiguration();
  }, []);
  
  const handleCustomizedOrderHandling = (e) => {
    const deliveryPartner = e.target.name
    const action = e.target.value
    const dpIndex = (partnerConfigs ?? []).findIndex(config => config.partnerId === deliveryPartner);
    
    if (!partnerConfigs) {
      const updatedConfiguration = { ...configurationForm, partnerConfigs: [{ partnerId: deliveryPartner, unseenOrder: { action: e.target.value, waitingTimeInSeconds: 60 } }] };
      setConfigurationForm(updatedConfiguration);
      updateConfiguration(updatedConfiguration);
    }
    if (partnerConfigs) {
      if (dpIndex !== -1 && action === "NONE") {
        const filteredConfig = {...configurationForm, partnerConfigs: [...partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner)] }
        setConfigurationForm(filteredConfig);
        updateConfiguration(filteredConfig);
      }
      if (dpIndex !== -1 && action !== "NONE") {
        const partnerObject = partnerConfigs[dpIndex]
        const updatedPartnerObject = { ...partnerConfigs[dpIndex], unseenOrder: { ...partnerObject?.unseenOrder ?? {}, action : action, waitingTimeInSeconds: partnerObject.unseenOrder.waitingTimeInSeconds ?? 60 }}
        const updatedPartnerConfigs =[ ...partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner), updatedPartnerObject]
        const updatedConfiguration = { ...configurationForm, partnerConfigs: updatedPartnerConfigs }
        setConfigurationForm(updatedConfiguration);
        updateConfiguration(updatedConfiguration);
      }
      if (dpIndex === -1){
        // partnerId NOT in partnerConfig
        const updatedConfiguration = { ...configurationForm, partnerConfigs: [...partnerConfigs, { partnerId: e.target.name, unseenOrder: { action: action, waitingTimeInSeconds: 60 }}] };
        setConfigurationForm(updatedConfiguration);
        updateConfiguration(updatedConfiguration);
      }
    }
  }
  
  const updateTimeForCustomizeOrderHandling = (value, deliveryPartner) => {
    const dpIndex = partnerConfigs.findIndex((dp => dp.partnerId === deliveryPartner))
    const partnerObject = partnerConfigs[dpIndex]
    const updatedPartnerObject = { ...partnerConfigs[dpIndex], unseenOrder: { ...partnerObject?.unseenOrder ?? {}, waitingTimeInSeconds : value  }}
    const updatedPartnerConfigs =[ ...partnerConfigs.filter(dp => dp.partnerId !== deliveryPartner), updatedPartnerObject]
    const updatedConfiguration = { ...configurationForm, partnerConfigs: updatedPartnerConfigs }
    setConfigurationForm(updatedConfiguration)
    updateConfiguration(updatedConfiguration)
  }
  
  const updateConfiguration = (updatedConfiguration) => {
    updateRestaurantConfigurationById(restaurantId, updatedConfiguration)
      .then(() => {
        fetchConfiguration();
      })
      .catch(() => {
      })
  };
  
  const getLieferandoStore = () => {
    setFetching(true);
    getLieferandoRestaurants(restaurantId)
      .then(({ data: fetchedStores = {} }) => {
        if (fetchedStores) {
          const { items = [], pages = 0, total = 0 } = fetchedStores;
          if (!isEmpty(items)) {
            setStore(items[0])
          }
        }
      })
      .catch(() => {})
      .finally(() => setFetching(false))
  }
  
  useEffect(() => {
    getLieferandoStore();
  }, [])
  
  if (fetching || updating) {
    return <Loading />;
  }
  
  const switchOperating = () => {
    if (isEmpty(store)) {
      return;
    }
  
    if (disabled) {
      loginLieferandoRestaurant(restaurantId).then(getLieferandoStore).catch(noop);
    } else {
      logoutLieferandoRestaurant(restaurantId).then(getLieferandoStore).catch(noop);
    }
  }
  
  const readyToShowSettings = !isEmpty(store);
  const TableUpdateBtn = () => (
    <div style={{ display: "flex", alignItems: "center", paddingLeft: 10, paddingRight: 10, minWidth: 100 }}>
      <EditIcon20 />
      <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], marginLeft: 2}}>{t('common-update')}</Typography>
    </div>
  )
  
  const updateLieferandoCode = (value) => {
    setUpdating(true);
    if (!value) {
      return
    }
    setLieferandoActivationCode(restaurantId, value)
      .then(() => getLieferandoStore())
      .catch(() => {})
      .finally(() => {
        setUpdatingLieferandoCode(false);
        setUpdating(false);
      });
  }
  
  const getApiKeyField = () => {
    return (
      <Fragment>
        <Field
          value={partnerRestaurantId}
          disabled
        />
        <TextUpdateModal
          titleI18n={'set-api-key'}
          open={updatingLieferandoCode}
          onClose={() => setUpdatingLieferandoCode(false)}
          value={partnerRestaurantId}
          setValue={updateLieferandoCode}
        />
      </Fragment>
    )
  }
  
  return (
    <div className={classes.settings}>
      <div>
        <div className={classes.settingHeader}>
          <Typography style={{ ...typography.body.medium }}>
            {t('lieferando-key')}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
            {t('setup-lieferando-integration-by-using-id')}
          </Typography>
        </div>
        <div className={classes.settingBody}>
          <div>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <Typography style={{ ...typography.body.medium }}>
                {t('set-lieferando-id')}
              </Typography>
              {(!partnerRestaurantId || isManaged) && (
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingLieferandoCode(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              )}
            </div>
            <div style={{ marginTop: 12 }}>
              {getApiKeyField()}
            </div>
          </div>
        </div>
      </div>
      {/*{readyToShowSettings && (
        <div className={classes.settingsForm}>
          <div className={classes.settingHeader}>
            <Typography style={{ ...typography.body.medium }}>
              {t('operations')}
            </Typography>
            <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
              {t('update-your-restaurant-operating-settings')}
            </Typography>
          </div>
          <div className={classes.settingBody}>
            <div className={classes.switchSetting}>
              <div className={classes.flex}>
                <div >
                  <Typography style={{ ...typography.body.medium }}>
                    {t('operating')}
                  </Typography>
                  <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                    {t('accepting-orders-from-partner')}
                  </Typography>
                </div>
                <Switch checked={isOperating} onClick={switchOperating} />
              </div>
            </div>
          </div>
        </div>
      )}*/}
      <div className={classes.settingsForm}>
        <div className={classes.settingHeader}>
          <Typography style={{ ...typography.body.medium }}>
            {t('unseen-webshop-order-handling-label')}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
            {t('control-takeaway-settings')}
          </Typography>
        </div>
        <div className={classes.settingBody}>
          <div>
            <Typography style={{ ...typography.body.medium , marginBottom: 8 }}>{t("action-unseen-order")}</Typography>
          </div>
          <div className={classes.selectWrapper}>
            <Select
              id="customAction-select"
              name="LIEFERANDO"
              value={hasPartnerConfig ? partnerConfigs[dpIndex].unseenOrder.action : "NONE"}
              onChange={handleCustomizedOrderHandling}
              disableUnderline
              variant="outlined"
              classes={{ root: classes.select }}
              style={{borderRadius: 12, backgroundColor: palette.grayscale["100"], height: 44, borderColor: palette.grayscale["350"] }}
              IconComponent={ChevronDown20new}
              required
            >
              <MenuItem value="NONE">{t('none')}</MenuItem>
              <MenuItem value="ACCEPT">{t('accept')}</MenuItem>
            </Select>
          </div>
          {dpIndex !== -1 &&
            <>
              <div className={classes.flex} style={{ marginTop: 8 }}>
                <div style={{ marginRight: 8, marginTop: 12 }}>
                  <Typography style={{ ...typography.body.medium }}>{t("time-in-s-till-action")}</Typography>
                </div>
                <ButtonBase disableRipple disableTouchRipple disableElevation disableFocusRipple
                            style={{ padding: 0 }} onClick={() => setUpdatingAutoRejectAcceptTime(true)}>
                  <TableUpdateBtn />
                </ButtonBase>
              </div>
              <Field
                value={hasPartnerConfig ? partnerConfigs[dpIndex].unseenOrder.waitingTimeInSeconds : 60}
                readOnly
              />
              <TimeInSecondsUpdateModal
                titleI18n={"configuration-editor-auto-reject-accept-time-field-label"}
                open={updatingAutoRejectAcceptTime}
                deliveryPartner={"LIEFERANDO"}
                onClose={() => setUpdatingAutoRejectAcceptTime(false)}
                value={hasPartnerConfig ? partnerConfigs[dpIndex].unseenOrder.waitingTimeInSeconds : 60}
                setValue={updateTimeForCustomizeOrderHandling}
                type={"number"}
                fieldProps={{
                  type: "number"
                }}
              />
            </>
          }
        </div>
      </div>
    </div>
  )
}

export default withTranslation("common")(LieferandoSettings);
