import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { withTranslation } from "../../../../../i18n";
import {
  createPaypalConfiguration,
  disconnectPaypalAccount, getPaypalConfiguration,
} from "../../../../api";
import { restaurantSelectors } from "../../../../../redux/selectors";
import Loading from "../../../Loading";
import { CheersIllustration, NoReservation120 } from "../../../../utils/icons";
import useStyles from "./styles";
import { noop } from "../../../../utils/const";
import { useRouter } from "next/router";
import EmptyScreen from "../../../_placeholder/EmptyScreen";
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../styles/typography";
import palette from "../../../../../styles/palette";
import { ButtonBase, useMediaQuery } from "@material-ui/core";
import { Confirm } from "../../../Confirmation";


const PaypalSettings = ({ t, settingsOnly }) => {
  const classes = useStyles();
  const router = useRouter();
  const isMobile = useMediaQuery("(max-width:900px)");
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  const [fetching, setFetching] = useState(false);
  const [saving, setSaving] = useState(false);
 
  const [paypalConfiguration, setPaypalConfiguration] = useState(null);
  const { id, primaryEmailConfirmed, paymentsReceivable, accountLink, onboardingCompleted } = (paypalConfiguration || {})
 
  const paypalNotSetup = !onboardingCompleted && !primaryEmailConfirmed && !paymentsReceivable && !id
  const unconfirmedPaypalEmailAddress = !primaryEmailConfirmed
  const cantReceivePayments = !paymentsReceivable
  
  const fetchPaypalConfiguration = () => {
    setFetching(true);
    getPaypalConfiguration(restaurantId)
      .then(({ data }) => setPaypalConfiguration(data))
      .catch(noop)
      .finally(() => setFetching(false))
  }
  
  
  useEffect(() => {
    fetchPaypalConfiguration();
  }, [])
  
  const setupPaypalConfiguration = () => {
    setSaving(true);
    createPaypalConfiguration(restaurantId).then(({ data = {} }) => {
      const { onboardingLink } = data;
      if (onboardingLink) {
        router.push(onboardingLink).then(noop).catch(noop)
      }
    }).catch(() => setSaving(false))
  }
  
  const openPaypalDashboardLogin = () => {
   /* setSaving(true);
    getPaypalConfiguration(restaurantId).then(({ data = {} }) => {
      const { accountLink } = data;*/
      if (accountLink) {
        router.push(accountLink).then(noop).catch(noop)
      }
    /*}).catch(() => setSaving(false))*/
  };
  
  const onDisconnectPayPalAccount = () => {
    disconnectPaypalAccount(restaurantId)
      .then(() => {})
      .catch(() => {})
  }
  
  const redirectToPayPalLink = (link) => {
    router.push(link).then(noop).catch(noop)
  }
  
  const getPaypalSettings = () => {
    if(paypalNotSetup){
      return (
        <div style={{ margin: '0 auto', width: isMobile ? null : 464 }}>
          <Typography style={{ ...typography.body.medium }}>{t("paypal-settings")}</Typography>
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("paypal-not-setup")}</Typography>
        </div>
      )
    }
    return (
      <div style={{ margin: '0 auto', minWidth: 350 }}>
        <Typography style={{ ...typography.body.medium }}>{t("paypal-settings")}</Typography>
        <Typography  style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>{t("control-your-paypal-settings")}</Typography>
        <div style={{ display: "flex", flexDirection: "column", justifyContent: "flex-start", gap: 12, marginTop: 12 }}>
          <div>
            <Typography style={{ ...typography.body.medium }}>{t("disconnect-paypal-confirm-dialog-title")}</Typography>
          </div>
          <Confirm
            title={t("disconnect-paypal-confirm-dialog-title")}
            body={(
              <Typography color="textSecondary" variant="body2">
                {t("disconnect-paypal-confirm-dialog-description-line1")}
                <br />
                <br />
                {t("disconnect-paypal-confirm-dialog-description-line2")}
              </Typography>
            )}
          >
            {confirm => (
              <ButtonBase style={{ backgroundColor: palette.negative["500"], padding: "8px 12px", borderRadius: 12, width: "fit-content" }}
                checked={!paypalNotSetup} onClick={confirm(onDisconnectPayPalAccount)} >
                <Typography style={{ ...typography.body.regular, color: palette.grayscale.white }}>{t("disconnect-paypal-confirm-dialog-title")}</Typography>
              </ButtonBase>
            )}
          </Confirm>
        </div>
      </div>
    )
  }
  
  if (fetching) {
    return <Loading />
  }
  
  const getPaypalStatus = () => {
    if (paypalNotSetup) {
      return (
        <EmptyScreen
          icon={<NoReservation120 />}
          titleI18nKey="paypal-not-setup"
          descriptionI18nKey="click-the-button-below-to-setup-paypal"
          action={{ i18nKey: "setup-paypal-account", onClick: setupPaypalConfiguration, loading: saving }}
        />
      )
    }
    if (unconfirmedPaypalEmailAddress) {
      return (
        <EmptyScreen
          icon={<NoReservation120 />}
          titleI18nKey="paypal-unconfirmed-email"
          descriptionI18nKey="paypal-unconfirmed-email-message"
          action={{ i18nKey: "paypal-redirect-to-link-btn-label", onClick: () => redirectToPayPalLink("https://www.paypal.com/businessprofile/settings") }}
        />
      )
    }
  
    if (cantReceivePayments) {
      return (
        <EmptyScreen
          icon={<NoReservation120 />}
          titleI18nKey="paypal-can-not-receive-payments"
          descriptionI18nKey="paypal-payments-cant-be-received-message"
          action={{ i18nKey: "paypal-redirect-to-link-btn-label", onClick: () => redirectToPayPalLink("https://www.paypal.com") }}
        />
      )
    }
    
    return (
      <EmptyScreen
        icon={<CheersIllustration />}
        titleI18nKey="accepting-payments-with-paypal"
        descriptionI18nKey="your-paypal-setup-is-completed-and-active"
        action={{ i18nKey: "log-in-for-paypal", onClick: openPaypalDashboardLogin, loading: saving }}
      />
    )
  }
  
  return (
    <div className={classes.wrapper}>
      <div className={classes.container}>
        {settingsOnly ? getPaypalSettings() : getPaypalStatus()}
      </div>
    </div>
  )
}


export default withTranslation("common")(PaypalSettings);
