import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../../../styles/palette";
import shadows from "../../../../../styles/shadows";

const useStyles = makeStyles(() => ({
  wrapper: {
    height: "100%"
  },
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    // paddingTop: 16,
    overflow: "hidden",
    // paddingTop: 11
  },
  background: {
    background: palette.grayscale["200"],
    paddingTop: 12,
    paddingBottom: 24,
    paddingLeft: 12,
    paddingRight: 16,
    height: "100%",
    overflow: "auto"
  },
  layout: {
    display: "flex",
    flexDirection: "row",
    height: "100%",
    maxWidth: 464,
    margin: "0 auto"
  },
  settings: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    width: 520,
    maxWidth: "100%",
    margin: "0 auto",
    marginTop: 16
  },
  settingsForm: {
    flex: 1,
    // marginTop: 12
  },
  settingHeader: {
    marginBottom: 24
  },
  settingBody: {

  },
  switchSetting: {
    padding: 12,
    borderRadius: 12,
    background: palette.grayscale["100"],
    ...shadows.base,
  },
  counterSetting: {

  },
  composedSetting: {

  },
  flex: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  },
  setting:{
    "&+&": {
      marginTop: 12
    }
  },
  breadcrumbs: {
    display: "flex",
    alignItems: "center",
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
    flex: 1
  },
  people: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  partner: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  date: {
    marginTop: 16,
    paddingBottom: 16,
    // borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  time: {
    marginTop: 16,
    paddingBottom: 16,
    // borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  tables: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  notes: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  customer: {
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 12,
    border: `1px solid ${palette.grayscale["350"]}`,
    background: palette.grayscale["250"],
    padding: 16
  },
  field: {
    "&+&": {
      marginTop: 12
    }
  },
  options :{
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap"
  },
  scrollableOptions: {
    overflow: "auto",
    display: "flex",
    marginBottom: "-30px",
    paddingBottom: "30px"
  },
  cardOption: {
    marginRight: 12,
    marginTop: 16,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    background: palette.grayscale["100"],
    padding: 12,
    ...shadows.base,
    borderRadius: 12
  },
  circularCardOption: {
    marginTop: 16,
    marginRight: 12,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    background: palette.grayscale["100"],
    padding: 12,
    ...shadows.base,
    borderRadius: "100%"
  },
  dateCardOption: {
    marginTop: 12,
    width: 56,
    height: 62,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    background: palette.grayscale["100"],
    ...shadows.base,
    borderRadius: 12,
    "&+&": {
      marginLeft: 12,
    }
  },
  selectedDateCardOption: {
    background: palette.primary["500"],
  },
  giftCard: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
  },
  tip: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
  },
  promotion: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
  },
  printReceipt: {
    marginTop: 16
  },
  expendableSummary: {
    marginBottom: 16
  },
  summaryLine: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    "&+&": {
      marginTop: 4
    }
  },
}));

export default useStyles;
