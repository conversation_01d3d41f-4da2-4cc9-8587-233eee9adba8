import React from 'react';
import useStyles from './styles';
import { withTranslation } from '../../../../../../i18n'
import TableContainer from '@material-ui/core/TableContainer'
import Table from '@material-ui/core/Table'
import TableHead from '@material-ui/core/TableHead'
import TableRow from '@material-ui/core/TableRow'
import TableCell from '@material-ui/core/TableCell'
import TableBody from '@material-ui/core/TableBody'

import { Pagination } from '@material-ui/lab';
import PayoutTableRow from './PayoutTableRow';

const PayoutsTable = ({ t, payouts, setPageOffset, pageSize }) => {
  const classes = useStyles();
  if (!payouts || payouts.items.length === 0) return null;

  return (
    <React.Fragment>
      <TableContainer>
          <Table stickyHeader aria-label="payouts-table">
            <TableHead>
              <TableRow>
                <TableCell align="left">{t('account')}</TableCell>
                <TableCell align="left">{t('payouts-table-column-triggered-at')}</TableCell>
                <TableCell align="left">{t('amount')}</TableCell>
                <TableCell align="left">{t('payouts-table-column-method')}</TableCell>
                <TableCell align="left">{t('status')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {payouts.items.map(item => <PayoutTableRow t={t} key={item.id} payoutItem={item} />)}
            </TableBody>
          </Table>
      </TableContainer>
      <div className={classes.pagination}>
        <Pagination
          count={payouts.pages}
          variant='text'
          shape='rounded'
          onChange={(e, v) => setPageOffset((v - 1) * pageSize)}
        />
      </div>
    </React.Fragment>
  )
}

export default withTranslation('common')(PayoutsTable);
