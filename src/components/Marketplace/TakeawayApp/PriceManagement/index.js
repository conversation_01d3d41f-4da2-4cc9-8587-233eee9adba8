import { withTranslation } from "../../../../../i18n";
import {
  resolveExtras,
  resolveMenus,
  resolveOptions, updateExtraItem,
  updateMenuItem,
  updateOptionItem
} from "../../../../api";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { restaurantSelectors } from "../../../../../redux/selectors";
import isEmpty from "../../../../utils/isEmpty";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import palette from "../../../../../styles/palette";
import { ButtonBase, Typography } from "@material-ui/core";
import typography from "../../../../../styles/typography";
import {
  AllOProviderLogo20,
  CollapseIcon20Grayscale400,
  EditIcon20, EuroSymbolIcon20Light
} from "../../../../utils/icons";
import TableBody from "@material-ui/core/TableBody";
import { LieferandoIcon20, UberEatsIcon20, WoltIcon20 } from "../../../../utils/partnerIcons";
import SegmentedControlBaseTabs from "../../../_tabs/SegmentedControlBaseTabs";
import Field from "../../../form/Field";
import Checkbox from "../../../_toggles/Checkbox"

const defaultImg = "https://storage.googleapis.com/leviee_public/allO/allo-eat-menu-item-img-placeholder-dark.png"

const partners = [{
  value: "UBER_EATS",
  i18nKey: "UberEats",
  icon: <UberEatsIcon20 />
}, {
  value: "WOLT",
  i18nKey: "Wolt",
  icon: <WoltIcon20 />
}, {
  value: "LIEFERANDO",
  i18nKey: "Lieferando",
  icon: <LieferandoIcon20 />
}];

const PriceManagement = ({ t }) => {
  
  const { restaurant = {}} = useSelector(restaurantSelectors.getRestaurant)
  const { id: restaurantId } = restaurant
  
  const [menus, setMenus] = useState([]);
  const [options, setOptions] = useState([]);
  const [extras, setExtras] = useState([]);
  
  const [menuItems, setMenusItems] = useState([]);
  const [optionItems, setOptionItems] = useState([])
  const [extraItems, setExtraItems] = useState([])
  
  const [menuItem, setMenuItem] = useState({});
  const [optionItem, setOptionItem] = useState({});
  const [extraItem, setExtraItem] = useState({});
  
  const [tab, setTab] = useState("MENU");
  
  const [expandedMenuIds, setExpandedMenuIds] = useState([]);
  const [allMenuItemsIds, setAllMenuItemsIds] = useState([]);
  const [expandedOptionIds, setExpandedOptionIds] = useState([]);
  const [allOptionIds, setAllOptionIds] = useState([]);
  const [expandedExtraIds, setExpandedExtraIds] = useState([]);
  const [allExtraIds, setAllExtraIds] = useState([]);
  
  const fetchMenus = () => {
    resolveMenus(restaurantId)
      .then(({ data = [] }) => {
        setMenus(data);
        const itemsOnly = data.flatMap(menu => menu.items || [])
        const allItemIds = data.map(menu => menu.id);
        setMenusItems(itemsOnly)
        setAllMenuItemsIds(allItemIds)
      })
      .catch(() => {});
  };
  
  const fetchOptions = () => {
    resolveOptions(restaurantId)
      .then(({ data = [] }) => {
        setOptions(data);
        const itemsOnly = data.flatMap(option => option.items || [])
        const allItemIds = data.map(option => option.id);
        setOptionItems(itemsOnly)
        setAllOptionIds(allItemIds)
      })
      .catch(() => {});
  };
  
  const fetchExtras = () => {
    resolveExtras(restaurantId)
      .then(({ data = [] }) => {
        setExtras(data);
        const itemsOnly = data.flatMap(extra => extra.items || [])
        const allItemIds = data.map(extra => extra.id);
        setExtraItems(itemsOnly)
        setAllExtraIds(allItemIds)
      })
      .catch(() => {});
  };
  
  useEffect(() => {
    fetchMenus();
    fetchOptions();
    fetchExtras();
  }, []);
  
  const submitMenuItem = (updatedMenuItem) => {
    updateMenuItem(restaurantId, updatedMenuItem)
      .then(() => setMenuItem(null))
      .then(() => {
        fetchMenus();
      })
      .catch(() => {});
  };
  
  const submitOptionItem = (updatedOptionItem) => {
    updateOptionItem(restaurantId, updatedOptionItem)
      .then(() => setOptionItem(null))
      .then(() => {
        fetchOptions();
      })
      .catch(() => {});
  };
  
  const submitExtraItem = (updatedExtraItem) => {
    updateExtraItem(restaurantId, updatedExtraItem)
      .then(() => setExtraItem(null))
      .then(() => {
        fetchExtras();
      })
      .catch(() => {});
  }
  
  const getCategories = () => {
    if (tab === "MENU") {
      return <GetContent t={t} headerTitle={t("menu-categories")} categoryName={"MENU"} category={menus} setCategoryItems={setMenusItems}
                         categoryItems={menuItems} categoryItem={menuItem} setCategoryItem={setMenuItem} expandedIds={expandedMenuIds} setExpandedIds={setExpandedMenuIds}
                         submitItem={submitMenuItem} allItemIds={allMenuItemsIds}/>
    }
    if (tab === "OPTIONS") {
      return <GetContent t={t} headerTitle={t("option-categories")} categoryName={"OPTIONS"} category={options} setCategoryItems={setOptionItems}
                         categoryItems={optionItems} categoryItem={optionItem} setCategoryItem={setOptionItem} expandedIds={expandedOptionIds}
                         setExpandedIds={setExpandedOptionIds} submitItem={submitOptionItem} allItemIds={allOptionIds}/>
    }
    if (tab === "EXTRAS") {
      return <GetContent t={t} headerTitle={t("extra-categories")} categoryName={"EXTRAS"} category={extras} setCategoryItems={setExtraItems}
                         categoryItems={extraItems} categoryItem={extraItem} setCategoryItem={setExtraItem} expandedIds={expandedExtraIds}
                         setExpandedIds={setExpandedExtraIds} submitItem={submitExtraItem} allItemIds={allExtraIds} />
    }
  }
  
  return (
    <div style={{
      height: "100%",
      display: "flex",
      flexDirection: "column"
    }}>
      <div>
        <div style={{ marginBottom: 24 }}>
          <Typography style={{ ...typography.medium.semiBold }}>{t("price-management")}</Typography>
        </div>
        <SegmentedControlBaseTabs value={tab} setValue={setTab} tabs={[
          { key: 'MENU', i18nKey: 'menu-categories' },
          { key: 'OPTIONS', i18nKey: 'option-categories'},
          { key: 'EXTRAS', i18nKey: 'extra-categories'},
        ]} />
      </div>
      <div style={{ flex: '1', marginTop: 24, whiteSpace: "nowrap" }}>
        {getCategories()}
      </div>
    </div>
  )
}

const GetContent = ({ t, classes, headerTitle, categoryName, category, categoryItems, setCategoryItems, categoryItem, setCategoryItem,
                      expandedIds, setExpandedIds, submitItem, allItemIds
                    }) => {
  
  const [isEditing, setIsEditing] = useState(false)
  const isAllExpanded = allItemIds.every(id => expandedIds.includes(id));
  
  const startEditing = (i) => {
    setIsEditing(true)
    setCategoryItem(i)
  };
  
  const cancelEditing = (i) => {
    setIsEditing(false)
    setCategoryItem(null)
  };
  
  const toggleExpansion = (id) => {
    setExpandedIds(prev => {
      if (id === "ALL") {
        const isAllExpanded = allItemIds.every(itemId => prev.includes(itemId));
        return isAllExpanded ? [] : [...allItemIds];
      }
      return prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id];
    });
  };
  
  const onPartnerPriceChange = (e, id) => {
    let validNumberInput = e.target.value.replace(/[^\d.]/g, '').trim()
    let val = validNumberInput
    if (val === "") {
      val = null;
    }
    setCategoryItems(prevState => {
      return prevState.map(item =>
        item.id === id
          ? { ...item, partnerPrices: { ...(item.partnerPrices ?? {}), [e.target.name]: val } }
          : item
      );
    });
    setCategoryItem(prevCategoryItem => ({
      ...prevCategoryItem,
      partnerPrices: { ...(prevCategoryItem.partnerPrices ?? {}), [e.target.name]: val }
    }));
  };
  
  const onAlloPriceChange = (e, id, key) => {
    const inputVal = e.target.value.replace(/[^\d.]/g, '').trim();
    const val = inputVal === "" ? null : inputVal;
    
    const isWebshop = key === "WEBSHOP"
    const fieldName = isWebshop ? "prices" : "unitPrice";
    const newValue = isWebshop ? { WEBSHOP: val } : val;
    
    setCategoryItems(prevState =>
      prevState.map(item =>
        item.id === id ? { ...item, [fieldName]: newValue } : item
      )
    );
    
    setCategoryItem({ ...categoryItem, [fieldName]: newValue });
  };
  
  
  const onDeliveryPartnerChange = (id, deliveryPartnerName) => {
    setCategoryItems(prevState =>
      prevState.map(item => {
        if (item.id !== id) return item;
      
        const updatedPartnerIds = item.partnerIds.includes(deliveryPartnerName) ?
          item.partnerIds.filter(partner => partner !== deliveryPartnerName)
          : [...item.partnerIds, deliveryPartnerName];
      
        setCategoryItem({ ...categoryItem, partnerIds: updatedPartnerIds });
        return { ...item, partnerIds: updatedPartnerIds };
      })
    );
  };
    
    const submit = () => {
    if (!categoryItem) return;
    
    const updatedCategoryItem = {
      ...categoryItem, partnerPrices: categoryItem.partnerPrices ?
        Object.fromEntries(
          Object.entries(categoryItem.partnerPrices).filter(([key, value]) => value !== "" && value !== null)
        )
        : {},
      prices: categoryItem.prices ?
        Object.fromEntries(
          Object.entries(categoryItem.prices).filter(([key, value]) => value !== "" && value !== null)
        ) : {},
      unitPrice: categoryItem.unitPrice || 0
    };
    submitItem(updatedCategoryItem);
  };
  
  
  if (!category && !categoryItems) {
    return null;
  }
  
  return (
    <>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: 12 }}>
        <Typography style={{ ...typography.medium.semiBold }}>{headerTitle}</Typography>
        <ButtonBase onClick={() => toggleExpansion("ALL")}
                    style={{ padding: '5px 8px 5px 6px', border: "1px solid #D9D9D8", borderRadius: "10px",
                      display: "flex", gap: 6, marginRight: 16
        }}>
          <Typography style={{ ...typography.body.regular }}>{isAllExpanded ? t("collapse-all") : t("expand-all")}</Typography>
          <div style={{ display: "flex", alignItems: "center", transform: isAllExpanded ? "rotateX(0deg)" : "rotateX(180deg)", width: 20, height: 20 }}>
            <CollapseIcon20Grayscale400 />
          </div>
        </ButtonBase>
      </div>
      {category.map((m, index) => {
        const { id, title, name, items } = m;
        const count = isEmpty(items) ? 0 : items.length
        const expanded = expandedIds.includes(id)
        
        return (
          <div key={index} style={{ overflowX: 'auto', width: '100%' }}>
            <TableContainer style={{ minWidth: 1450 }}>
              <Table stickyHeader aria-label="menu editor table" style={{ minWidth: 'max-content' }}>
                <TableHead>
                  <TableRow>
                    <TableCell padding='none' colSpan={10}>
                      <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 12, paddingBottom: 12, display: 'flex', justifyContent: 'space-between', alignItems: "center", borderBottom: expanded ? `1px solid ${palette.grayscale["300"]}` : 'none' }}>
                        <div>
                          <ButtonBase disableRipple disableTouchRipple style={{ display: "flex", flex: 1, justifyContent: "flex-start", width: "100%", paddingRight: 50 }}>
                            <Typography style={{ ...typography.body.regular, textTransform: "capitalize" }}>{title ?? name}</Typography>
                            {!!count && (
                              <div style={{ marginLeft: 6, borderRadius: 12, background: palette.grayscale["250"], minWidth: 20, height: 20, display: "flex", justifyContent: "center" }}>
                                <Typography style={{ ...typography.small.medium, color: palette.grayscale["800"], lineHeight: '20px', paddingLeft: 4, paddingRight: 4 }}>
                                  {count}
                                </Typography>
                              </div>
                            )}
                          </ButtonBase>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <ButtonBase
                            disableRipple
                            disableTouchRipple
                            style={{ padding: 6, transform: expanded ? "none" : 'rotate(180deg)' }}
                            onClick={() => toggleExpansion(id, categoryName)}
                          >
                            <CollapseIcon20Grayscale400 />
                          </ButtonBase>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableHead>
                {expanded && (
                  <TableBody>
                    {items.map(i => {
                      const { id, thumbnailUrl, numeration, name, description, partnerIds = [] } = i;
                      const isSelected = categoryItem?.id === i.id
                      
                      const lieferandoIsAdded = partnerIds.includes("LIEFERANDO");
                      const woltIsAdded = partnerIds.includes("WOLT");
                      const uberEatsIsAdded = partnerIds.includes("UBER_EATS");
                      
                      const lieferandoPriceDisabled = !categoryItems?.find(item => item.id === id)?.partnerIds.includes("LIEFERANDO");
                      const woltPriceDisabled = !categoryItems?.find(item => item.id === id)?.partnerIds.includes("WOLT");
                      const uberEatsPriceDisabled = !categoryItems?.find(item => item.id === id)?.partnerIds.includes("UBER_EATS");
                      
                      return (
                        <TableRow hover key={id} style={{ cursor: 'pointer' }}>
                          <TableCell style={{ whiteSpace: 'normal', width: '30%', maxWidth: 300 }}>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <div>
                                <img src={thumbnailUrl || defaultImg} style={{ height: 32, width: 32, objectFit: 'cover', borderRadius: 10, verticalAlign: 'middle' }}/>
                              </div>
                              <div style={{ marginLeft: 12, maxWidth: 270 }}>
                                <Typography style={{...typography.body.medium, whiteSpace: "break-spaces", display: "-webkit-box", WebkitLineClamp: 2, WebkitBoxOrient: "vertical", overflow: "hidden" }}>{name}</Typography>
                                {(numeration || description) && (
                                  <Typography style={{...typography.body.regular, color: palette.grayscale["600"], whiteSpace: "break-spaces",
                                    display: "-webkit-box", WebkitLineClamp: 2, WebkitBoxOrient: "vertical", overflow: "hidden" }}>{numeration}{numeration ? ' ' : ''}{description}</Typography>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell style={{ width: '70%' }}>
                            <div style={{
                              display: 'flex',
                              flexDirection: 'row',
                              justifyContent: 'end',
                              alignItems: 'center',
                            }}>
                              {isSelected ? (
                                <>
                                  {/* LIEFERANDO */}
                                  <div style={{ display: 'flex', justifyContent: "flex-start", marginRight: 16 }}>
                                    <div style={{ display: "flex", alignItems: "center", gap: 16, justifyContent: "flex-start", backgroundColor: palette.grayscale["300"], padding: "6px 8px", borderRadius: 12 }}>
                                      <div style={{ width: 20, height: 20, flexShrink: 0 }}>
                                        <LieferandoIcon20 />
                                      </div>
                                      <div style={{ display: 'flex', alignItems: "center", gap: 4 }}>
                                        <Field
                                          style={{ minWidth: 85, maxWidth: 80, height: 32, borderRadius: 8, backgroundColor: lieferandoPriceDisabled ? palette.grayscale["300"] : null }}
                                          value={categoryItems.find(item => item.id === id)?.partnerPrices?.LIEFERANDO ?? ""}
                                          name="LIEFERANDO"
                                          onChange={(e) => onPartnerPriceChange(e, id)}
                                          disabled={lieferandoPriceDisabled}
                                          iconAdornEnd={< EuroSymbolIcon20Light/>}
                                          slimField
                                        />
                                      </div>
                                      <Checkbox
                                        checked={categoryItems.find(item => item.id === id)?.partnerIds.includes("LIEFERANDO")}
                                        onClick={() => onDeliveryPartnerChange(id, "LIEFERANDO")}
                                      />
                                    </div>
                                  </div>
                                  {/* UBER EATS */}
                                  <div style={{ display: 'flex', justifyContent: "flex-start", marginRight: 16 }}>
                                    <div style={{ display: "flex", alignItems: "center", gap: 16, justifyContent: "flex-start", backgroundColor: palette.grayscale["300"], padding: "6px 8px", borderRadius: 12 }}>
                                      <div style={{ width: 20, height: 20, flexShrink: 0 }}>
                                        <UberEatsIcon20 />
                                      </div>
                                      <div style={{ display: 'flex', alignItems: "center", gap: 4 }}>
                                        <Field
                                          style={{ minWidth: 85, maxWidth: 80, height: 32, borderRadius: 8, backgroundColor: uberEatsPriceDisabled ? palette.grayscale["300"] : null }}
                                          name="UBER_EATS"
                                          value={categoryItems.find(item => item.id === id)?.partnerPrices?.UBER_EATS ?? ""}
                                          onChange={(e) => onPartnerPriceChange(e, id)}
                                          disabled={uberEatsPriceDisabled}
                                          iconAdornEnd={< EuroSymbolIcon20Light/>}
                                          slimField
                                        />
                                      </div>
                                      <Checkbox
                                        checked={categoryItems.find(item => item.id === id)?.partnerIds.includes("UBER_EATS")}
                                        onClick={() => onDeliveryPartnerChange(id,"UBER_EATS")}
                                      />
                                    </div>
                                  </div>
                                
                                  {/* WOLT */}
                                  <div style={{ display: 'flex', justifyContent: "flex-start", marginRight: 16 }}>
                                    <div style={{ display: "flex", alignItems: "center", gap: 16, justifyContent: "flex-start", backgroundColor: palette.grayscale["300"], padding: "6px 8px", borderRadius: 12 }}>
                                      <div style={{ width: 20, height: 20, flexShrink: 0 }}>
                                        <WoltIcon20 />
                                      </div>
                                      <div style={{ display: 'flex', alignItems: "center", gap: 4 }}>
                                        <Field
                                          style={{ minWidth: 85, maxWidth: 80, height: 32, borderRadius: 8, backgroundColor: woltPriceDisabled ? palette.grayscale["300"] : null  }}
                                          name="WOLT"
                                          value={categoryItems.find(item => item.id === id)?.partnerPrices?.WOLT ?? ""}
                                          onChange={(e) => onPartnerPriceChange(e, id)}
                                          disabled={woltPriceDisabled}
                                          iconAdornEnd={< EuroSymbolIcon20Light/>}
                                          slimField
                                        />
                                      </div>
                                      <Checkbox
                                        checked={categoryItems.find(item => item.id === id)?.partnerIds.includes("WOLT")}
                                        onClick={() => onDeliveryPartnerChange(id,"WOLT")}
                                      />
                                    </div>
                                  </div>
                                  
                                  {/* AllO WEBSHOP PRICE */}
                                  <div style={{ display: 'flex', alignItems: "center", marginRight: 16 }}>
                                    <div style={{ display: "flex", alignItems: "center", gap: 16, justifyContent: "flex-start", backgroundColor: palette.grayscale["300"], padding: "6px 8px", borderRadius: 12 }}>
                                      <div style={{ width: 20, height: 20, flexShrink: 0 }}>
                                        <AllOProviderLogo20 />
                                      </div>
                                      <div style={{ display: 'flex', alignItems: "center", gap: 4 }}>
                                        <Field
                                          style={{ minWidth: 85, maxWidth: 80, height: 32, borderRadius: 8 }}
                                          name="WEBSHOP"
                                          value={categoryItems.find(item => item.id === id)?.prices?.WEBSHOP ?? ""}
                                          onChange={(e) => onAlloPriceChange(e, id, "WEBSHOP")}
                                          iconAdornEnd={< EuroSymbolIcon20Light/>}
                                          slimField
                                        />
                                      </div>
                                    </div>
                                  </div>
                                  {/* PRIMARY PRICE */}
                                  <div style={{ display: 'flex', alignItems: "center", gap: 4,}}>
                                    <div style={{ display: "flex", alignItems: "center", gap: 16, justifyContent: "flex-start", backgroundColor: palette.grayscale["300"], padding: "6px 8px", borderRadius: 12 }}>
                                      <div style={{ width: 20, height: 20, flexShrink: 0 }}>
                                        <Typography style={{ ...typography.medium.regular }}>P</Typography>
                                      </div>
                                      <div style={{ display: 'flex', alignItems: "center", gap: 4 }}>
                                        <Field
                                          style={{ minWidth: 85, maxWidth: 80, height: 32, borderRadius: 8 }}
                                          name="unitPrice"
                                          value={categoryItems.find(item => item.id === id)?.unitPrice ?? ""}
                                          onChange={(e) => onAlloPriceChange(e, id, "unitPrice")}
                                          iconAdornEnd={< EuroSymbolIcon20Light/>}
                                          slimField
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </>
                              ) : (
                                <>
                                  <div style={{ display: "flex", alignItems: "center", gap: 32 }}>
                                    {/* LIEFERANDO */}
                                    <div style={{ display: lieferandoIsAdded ? 'flex' : 'none', alignItems: "center", gap: 8, flexShrink: 0, minWidth: 70 }}>
                                      <div style={{ width: 20, height: 20 }}>
                                        <LieferandoIcon20 />
                                      </div>
                                      <Typography style={{ ...typography.body.regular }}>{categoryItems.find(item => item.id === id)?.partnerPrices?.LIEFERANDO != null ?
                                        `${categoryItems.find(item => item.id === id)?.partnerPrices?.LIEFERANDO}€` : ""}
                                      </Typography>
                                    </div>
                                    {/* UBER EATS */}
                                    <div style={{ display: uberEatsIsAdded ? 'flex' : 'none', alignItems: "center", gap: 8, flexShrink: 0, minWidth: 70 }}>
                                      <div style={{ width: 20, height: 20 }}>
                                        <UberEatsIcon20 />
                                      </div>
                                      <Typography style={{ ...typography.body.regular }}>{categoryItems.find(item => item.id === id)?.partnerPrices?.UBER_EATS != null ?
                                        `${categoryItems.find(item => item.id === id)?.partnerPrices?.UBER_EATS}€` : ""}
                                      </Typography>
                                    </div>
                                    {/* WOLT */}
                                    <div style={{ display: woltIsAdded ? 'flex' : 'none', alignItems: "center", gap: 8, flexShrink: 0, minWidth: 70 }}>
                                      <div style={{ width: 20, height: 20 }}>
                                        <WoltIcon20 />
                                      </div>
                                      <Typography style={{ ...typography.body.regular }}>{categoryItems.find(item => item.id === id)?.partnerPrices?.WOLT != null ?
                                        `${categoryItems.find(item => item.id === id)?.partnerPrices?.WOLT}€` : ""}
                                      </Typography>
                                    </div>
                                    {/* AllO WEBSHOP PRICE */}
                                    <div style={{ display: 'flex', alignItems: "center", gap: 8, flexShrink: 0, minWidth: 70 }}>
                                      <div style={{ width: 20, height: 20 }}>
                                        <AllOProviderLogo20 />
                                      </div>
                                      <Typography style={{ ...typography.body.regular }}>{categoryItems.find(item => item.id === id)?.prices?.WEBSHOP != null ?
                                        `${categoryItems.find(item => item.id === id)?.prices?.WEBSHOP}€` : ""}
                                      </Typography>
                                    </div>
                                    {/* PRIMARY PRICE */}
                                    <div style={{ display: 'flex', alignItems: "center", gap: 8, flexShrink: 0, minWidth: 70 }}>
                                      <Typography style={{ ...typography.body.regular }}>{categoryItems.find(item => item.id === id)?.unitPrice != null ?
                                        `${categoryItems.find(item => item.id === id)?.unitPrice}€` : ""}
                                      </Typography>
                                    </div>
                                  </div>
                                </>
                              
                              )}
                              <div style={{ minWidth: 70 }}>
                                {isEditing && isSelected ? (
                                  <div style={{ display: "flex", alignItems: "center", gap: 6 }}>
                                    <ButtonBase style={{
                                      padding: '6px 12px', display: "flex", alignItems: "center",  backgroundColor: palette.primary["500"],
                                      borderRadius: 8, marginLeft: 6 }} onClick={submit}>
                                      <Typography style={{ ...typography.body.regular, color: palette.grayscale.white }}>{t("common-save")}</Typography>
                                    </ButtonBase>
                                    <ButtonBase style={{ padding: '5px 8px 5px 6px', border: "1px solid #D9D9D8", borderRadius: "10px" }}
                                                onClick={cancelEditing}>
                                      <Typography style={{ ...typography.body.regular }}>{t("cancel")}</Typography>
                                    </ButtonBase>
                                  </div>
                                ):(
                                  <ButtonBase style={{ padding: "6px 12px 6px 8px", border: "1px solid #D9D9D8", borderRadius: "8px" }}
                                              onClick={() => startEditing(i)}>
                                    <EditIcon20 />
                                    <Typography style={{ ...typography.body.regular }}>{t("order-item-edit-btn")}</Typography>
                                  </ButtonBase>
                                )}
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                )}
              </Table>
            </TableContainer>
          </div>
        )
      })}
    </>
  )
}

export default withTranslation("common")(PriceManagement)