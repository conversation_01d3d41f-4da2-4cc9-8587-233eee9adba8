import { withTranslation } from "../../../../../i18n";
import React, { useState } from "react";
import SegmentedControlBaseTabs from "../../../_tabs/SegmentedControlBaseTabs";
import UberEatsStoreDetails from "./UberEatsStore";
import UberEatsSettings from "../../UberEatsSettings";
import { UberEatsTabOptions, UberEatsTabs } from "../../../../utils/const";
import palette from "../../../../../styles/palette";
import { useSelector } from "react-redux";
import { accountSelectors } from "../../../../../redux/selectors";


const UberEatsConfigurations = ({ t, isManaged }) => {
  
  const [tab, setTab] = useState(UberEatsTabs.SETTINGS.key);
  
  const getUberEatsView = () => {
    if (tab === UberEatsTabs.STORE.key) {
      return <UberEatsStoreDetails />
    }
    if (tab === UberEatsTabs.SETTINGS.key) {
      return <UberEatsSettings isManaged={isManaged}/>
    }
  }
  
  return (
    <div style={{ display: "flex", flexDirection: "column", height: "100%", maxWidth: "100%", alignItems: "center", marginTop: 16 }}>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "top", width: 520 }}>
        <div style={{ position: "sticky", top: 0, zIndex: 1000, backgroundColor: palette.grayscale["200"], width: "100%", padding: "8px 0px 20px 8px" }}>
          <SegmentedControlBaseTabs value={tab} setValue={setTab} tabs={UberEatsTabOptions} />
        </div>
        <div>
          {getUberEatsView()}
        </div>
      </div>
    </div>
  )
}


export default withTranslation("common")(UberEatsConfigurations);