import { withTranslation } from "../../../../../i18n";
import React, { useState } from "react";
import WoltSetup from "../../WoltSettings/WoltSetup";
import UpdateToolWoltMenu from "../../WoltSettings/UpdateToolWoltMenu";
import SegmentedControlBaseTabs from "../../../_tabs/SegmentedControlBaseTabs";
import WoltStore from "./WoltStore";
import { WoltTabOptions, WoltTabs } from "../../../../utils/const";
import palette  from "../../../../../styles/palette";


const WoltSettings = ({ t, isManaged }) => {
  
  const [tab, setTab] = useState("setup");
  
  const getWoltView = () => {
    if (tab === WoltTabs.STORE.key) {
      return <WoltStore />
    }
    if (tab === WoltTabs.SETUP.key) {
      return <WoltSetup isManaged={isManaged} />
    }
    if (tab === WoltTabs.MENU.key) {
      return <UpdateToolWoltMenu />
    }
  }
  
  return (
    <div style={{ display: "flex", flexDirection: "column", height: "100%", maxWidth: "100%", alignItems: "center", marginTop: 16 }}>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "top", width: 520 }}>
        <div style={{ position: "sticky", top: 0, zIndex: 1000, backgroundColor: palette.grayscale["200"], width: "100%", padding: "8px 0px 20px 8px" }}>
          <SegmentedControlBaseTabs value={tab} setValue={setTab} tabs={WoltTabOptions} />
        </div>
        <div style={{ marginBottom: 24 }}>
          {getWoltView()}
        </div>
      </div>
    </div>
  )
}


export default withTranslation("common")(WoltSettings);