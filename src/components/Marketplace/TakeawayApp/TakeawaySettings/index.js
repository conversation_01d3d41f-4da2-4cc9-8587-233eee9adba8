import { withTranslation } from "../../../../../i18n"
import Typography from "@material-ui/core/Typography";
import typography from "../../../../../styles/typography";
import palette from "../../../../../styles/palette";
import React, { Fragment, useEffect, useState } from "react";
import Switch from "../../../_toggles/Switch";
import shadows from "../../../../../styles/shadows";
import {
  getAccounts,
  getRestaurantConfiguration,
  updateRestaurantConfigurationById
} from "../../../../api";
import { useDispatch, useSelector } from "react-redux";
import { configurationSelectors, restaurantSelectors } from "../../../../../redux/selectors";
import TableContainer from "@material-ui/core/TableContainer";
import Table from "@material-ui/core/Table";
import { ButtonBase, TableHead } from "@material-ui/core";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import calculateOpeningHours from "../../../../utils/calculateOpeningHours";
import DayPeriodsUpdateModal from "../../../_popup/DayPeriodsUpdateModal";
import { ChevronDown20new, ClockIcon20, EditIcon20 } from "../../../../utils/icons";
import removeUndefined from "../../../../utils/removeUndefined";
import Field from "../../../form/Field";
import PrepTimeFieldsUpdateModal from "../../../_popup/PrepTimeFieldsUpdateModal";
import isEmpty from "../../../../utils/isEmpty";
import FormControl from "@material-ui/core/FormControl";
import Select from "@material-ui/core/Select";
import { MenuProps } from "../../../../utils/const";
import Chip from "@material-ui/core/Chip";
import MenuItem from "@material-ui/core/MenuItem";
import { Paragraph } from "../../../Text";
import byId from "../../../../utils/byId";
import useStyles from "./styles"
import TextUpdateModal from "../../../_popup/TextUpdateModal";

const partners = [{
  value: "UBER_EATS",
  i18nKey: "UberEats",
}, {
  value: "WOLT",
  i18nKey: "Wolt"
}, {
  value: "LIEFERANDO",
  i18nKey: "Lieferando"
}];

const configurationData = {
  webshopConfig: {
    printWebshopUrlQRCodeOnFinalReceipt: false,
    printAddressQrCodeForDriver: false,
    incomingOrderSound: null,
    pollSoundNotificationAccountIds: [],
    promotionText: "",
    unseenOrder: {
      disabled: true,
      action: "ACCEPT",
      waitingTimeInSeconds: 60
    },
    disableTakeawayEmailCopy: true,
    disableNewOrderReceipt: false,
    pickupPaymentChannels: [],
    deliveryPaymentChannels:[],
    deliveryAreas: [],
    deliveryRadius: [],
    deliveryFeeMode: "KM_RADIUS",
    allowGiftCards: false,
    allowPromoCodes: false
  },
  takeawayConfig: {
    bufferTime: 0,
    allowDriverAssignment: false,
  },
  preparationTimeInMinutes: {
    PICKUP: 45,
    DELIVERY: 45,
    DINE_IN: 45,
    EXPRESS: 45,
    WOLT: 10,
    UBER_EATS: 10,
    LIEFERANDO: 10
  }
};

const TakeawaySettings = ({ t }) => {
  const classes = useStyles();
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  const { configuration = {} } = useSelector(configurationSelectors.getConfiguration);
  const { hasFlashTakeaway, webshopConfig = {} } = (configuration || {});
  const { disableNewOrderReceipt, webshopHours = [], hasDifferentWebshopHours, incomingOrderSound, pollSoundNotificationAccountIds, } = (webshopConfig || {})
  
  const [configurationForm, setConfigurationForm] = useState(configurationData);
  
  const [updatingOpeningHoursDay, setUpdatingOpeningHoursDay] = useState(null);
  const [updatingPrepTimeFields, setUpdatingPrepTimeFields] = useState(false);
  const [updatingBufferTime, setUpdatingBufferTime] = useState(false);
  
  const [accounts, setAccounts] = useState([]);
  const [accountsById, setAccountsById] = useState({});
  
  const [submitting, setSubmitting] = useState(false);
  const [editing, setEditing] = useState(false);
  
  const fetchConfiguration = () => {
    getRestaurantConfiguration(restaurantId)
      .then(({ data = {} }) => {
        setConfigurationForm(data ?? {});
      })
      .catch(() => {
      });
  };
  
  const updateConfiguration = (updatedConfiguration) => {
    setSubmitting(true);
    if (configurationForm && configurationForm.id) {
      updateRestaurantConfigurationById(restaurantId, updatedConfiguration)
        .then(() => setEditing(false))
        .then(() => {
          fetchConfiguration();
        })
        .catch(() => {
        })
        .finally(() => setSubmitting(false));
    }
  };
  
  useEffect(() => {
    updateConfigurationForm(configuration);
  }, [JSON.stringify(configuration)]);
  
  const updateConfigurationForm = (config = {}) => {
    if (isEmpty(config)) {
      return;
    }
    setConfigurationForm({ ...configurationForm, ...config });
  };
  
  useEffect(() => {
    getAccounts(restaurantId, 100, 0).then(({ data }) => {
      setAccounts(data.items)
      setAccountsById(byId(data.items))
    }).catch(() => {});
  }, [])
  
  const handleToggle = (stateKey, value) => {
    const updatedConfiguration = { ...configurationForm, [stateKey]: value };
    setConfigurationForm(updatedConfiguration);
    updateConfiguration(updatedConfiguration);
  };
  
  const handleWebshopConfigToggle = (stateKey, value) => {
    let configForm = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, [stateKey]: value } }
    setConfigurationForm(configForm);
    updateConfiguration(configForm);
  };
  
  const handleTakeawayConfigToggle = (stateKey, value) => {
    const configForm = { ...configurationForm, takeawayConfig: { ...configurationForm.takeawayConfig, [stateKey]: value } }
    setConfigurationForm(configForm);
    updateConfiguration(configForm);
  }
  
  const updateOpeningHoursDayIntervals = (periods = []) => {
    const updatedOpeningHours = { ...webshopHours, [updatingOpeningHoursDay]: periods };
    const updatedConfig = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, webshopHours: removeUndefined(updatedOpeningHours)}};
    setConfigurationForm(updatedConfig)
    updateConfiguration(updatedConfig)
  };
  
  const updatePrepTimeFields = (prepTime) => {
    const updatedConfig = { ...configurationForm, preparationTimeInMinutes: prepTime };
    setConfigurationForm(updatedConfig);
    updateConfiguration(updatedConfig)
  };
  
  const updateSoundNotificationAccountIds = (e) => {
    let val = e.target.value;
    val = [...new Set(val ?? [])]
    
    const updatedWebshopConfig = { ...configurationForm, webshopConfig: { ...configurationForm.webshopConfig, pollSoundNotificationAccountIds: val }}
    setConfigurationForm(updatedWebshopConfig)
    updateConfiguration(updatedWebshopConfig)
  };
  
  const onBufferTimeChange = (newTime) => {
    const updatedConfig = { ...configurationForm, takeawayConfig: { ...configurationForm.takeawayConfig, bufferTime: newTime }};
    setConfigurationForm(updatedConfig);
    updateConfiguration(updatedConfig)
  };
  
  const getOpeningHoursTable = () => {
    const daysOfTheWeek = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"];
    
    const resolvedOpeningHours = webshopHours ?? {}
    const days = daysOfTheWeek.map(day => [day, resolvedOpeningHours[day] ?? []])
    
    return (
      <Fragment>
        <TableContainer stickyHeader>
          <Table style={{ width: 520 }}>
            <TableHead>
              <TableRow>
                <TableCell>{t("day")}</TableCell>
                <TableCell>{t("time-period")}</TableCell>
                <TableCell />
              </TableRow>
            </TableHead>
            <TableBody>
              {days.map(([day, hours]) => {
                return (
                  <TableRow onClick={() => setUpdatingOpeningHoursDay(day)} style={{ cursor: "pointer" }}>
                    <TableCell>{t(`common-${day.toLowerCase()}`)}</TableCell>
                    <TableCell>{calculateOpeningHours(hours)}</TableCell>
                    <TableCell>
                      <TableUpdateBtn />
                    </TableCell>
                  </TableRow>
                  )
              })}
            </TableBody>
          </Table>
        </TableContainer>
        {!!updatingOpeningHoursDay && (
          <DayPeriodsUpdateModal
            open={updatingOpeningHoursDay}
            onClose={() => setUpdatingOpeningHoursDay(null)}
            value={resolvedOpeningHours[updatingOpeningHoursDay]}
            setValue={updateOpeningHoursDayIntervals}
          />
        )}
      </Fragment>
    );
  };
  
  const TableUpdateBtn = () => (
    <div style={{ display: "flex", alignItems: "center", paddingLeft: 10, paddingRight: 10, minWidth: 100 }}>
      <EditIcon20 />
      <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], marginLeft: 2 }}>{t("common-update")}</Typography>
    </div>
  );
  
  const getPrepTimeFields = () => (
    <Fragment>
      <div style={{display: "flex", flexDirection: "row", justifyContent:"space-between", gap: 12}}>
        <Field
          label={t("pickup-order-creation-form-type-field-pickup-option-label")}
          value={configurationForm.preparationTimeInMinutes.PICKUP}
          readOnly
          iconAdornEnd={<ClockIcon20/>}
        />
        <Field
          label={t("pickup-order-creation-form-type-field-delivery-option-label")}
          value={configurationForm.preparationTimeInMinutes.DELIVERY}
          readOnly
          iconAdornEnd={<ClockIcon20/>}
        />
      </div>
      <div style={{display: "flex", flexDirection: "row", justifyContent:"space-between", gap: 12,  marginTop: 8}}>
        {partners.map((partner)=> {
          return(
            <Field
              label={partner.i18nKey}
              key={partner.i18nKey}
              value={configurationForm.preparationTimeInMinutes[partner.value]}
              readOnly
              iconAdornEnd={<ClockIcon20/>}
              type={"number"}
              fieldProps={{
                type: "number"
              }}
            />
          )
        })}
      </div>
      <PrepTimeFieldsUpdateModal
        titleI18n={"default-prep-time-header"}
        open={updatingPrepTimeFields}
        onClose={() => setUpdatingPrepTimeFields(false)}
        deliveryPartners={partners}
        values={configurationForm.preparationTimeInMinutes}
        setValue={updatePrepTimeFields}
      />
    </Fragment>
  );
  
  const getBufferTimeField = () => (
    <Fragment>
      <div style={{ display: "flex", flexDirection: "row", justifyContent:"space-between", gap: 12 }}>
        <Field
          value={configurationForm.takeawayConfig?.bufferTime ?? 0}
          readOnly
          iconAdornEnd={<ClockIcon20/>}
        />
      </div>
      <TextUpdateModal
        titleI18n={"buffer-time"}
        open={updatingBufferTime}
        name={"bufferTime"}
        onClose={() => setUpdatingBufferTime(false)}
        value={configurationForm.takeawayConfig?.bufferTime ?? 0}
        setValue={onBufferTimeChange}
        type={"number"}
        fieldProps={{
          type: "number",
        }}
      />
    </Fragment>
  )
  
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        maxWidth: "100%",
        alignItems: "center",
        marginTop: 16,
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "top",
          maxWidth: 520,
        }}
      >
        <div style={{ marginRight: 0, marginBottom: 32, width: "100%" }}>
          <Typography style={{ ...typography.body.medium }}>
            {t("takeaway-settings")}
          </Typography>
          <Typography
            style={{ ...typography.body.regular, color: palette.grayscale["600"] }}
          >
            {t("control-takeaway-settings-configuration")}
          </Typography>
        </div>
        
        {/* Opening Hours */}
        <div style={{ flex: 1 }}>
          <div
            style={{
              marginBottom: 24,
              padding: 12,
              borderRadius: 12,
              background: palette.grayscale["100"],
              ...shadows.base,
              maxWidth: 520,
            }}
          >
            <div
              style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
            >
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("webshop-opening-hours")}
                </Typography>
                <Typography
                  style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"],
                  }}
                >
                  {t("enable-to-set-different-opening-hours-for-webshop")}
                </Typography>
              </div>
              <Switch
                checked={hasDifferentWebshopHours ?? false}
                onClick={() =>
                  handleWebshopConfigToggle("hasDifferentWebshopHours", !hasDifferentWebshopHours)
                }
              />
            </div>
          </div>
          
          {hasDifferentWebshopHours && (
            <div style={{ marginTop: 12, maxWidth: 520, marginBottom: 24 }}>
              <Typography style={{ ...typography.body.medium }}>
                {t("webshop-opening-hours")}
              </Typography>
              <div style={{ marginTop: 16 }}>{getOpeningHoursTable()}</div>
            </div>
          )}
          
          {/* Quick Service */}
          <div
            style={{
              marginBottom: 24,
              padding: 12,
              borderRadius: 12,
              background: palette.grayscale["100"],
              ...shadows.base,
              maxWidth: 520,
            }}
          >
            <div
              style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
            >
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("configuration-editor-flash-takeaway-toggle")}
                </Typography>
                <Typography
                  style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"],
                  }}
                >
                  {t("configuration-editor-flash-takeaway-toggle-description")}
                </Typography>
              </div>
              <Switch
                checked={configurationForm.hasFlashTakeaway ?? false}
                onClick={() => handleToggle("hasFlashTakeaway", !hasFlashTakeaway)}
              />
            </div>
          </div>
        </div>
        
        {/* Prep Time */}
        <div style={{ flex: 1 }}>
          <div>
            <div
              style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
            >
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("default-prep-time-header")}
                </Typography>
              </div>
              <ButtonBase
                disableRipple
                disableTouchRipple
                disableElevation
                disableFocusRipple
                style={{ padding: 0 }}
                onClick={() => setUpdatingPrepTimeFields(true)}
              >
                <TableUpdateBtn />
              </ButtonBase>
            </div>
            
            <div style={{ marginTop: 12 }}>{getPrepTimeFields()}</div>
            
            <div style={{ marginTop: 8 }}>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                {t("set-default-prep-time-description")}
              </Typography>
            </div>
          </div>
          
          {/* Buffer Time */}
          <div style={{ marginTop: 24 }}>
            <div
              style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
            >
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>{t("buffer-time")}</Typography>
              </div>
              <ButtonBase
                disableRipple
                disableTouchRipple
                disableElevation
                disableFocusRipple
                style={{ padding: 0 }}
                onClick={() => setUpdatingBufferTime(true)}
              >
                <TableUpdateBtn />
              </ButtonBase>
            </div>
            <div style={{ marginTop: 12 }}>{getBufferTimeField()}</div>
            <div style={{ marginTop: 8 }}>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                {t("set-buffer-time-description")}
              </Typography>
            </div>
          </div>
        </div>
        
        {/* Sound Notification */}
        <div
          style={{
            marginTop: 24,
            marginBottom: incomingOrderSound ? 0 : 24,
            padding: 12,
            borderRadius: 12,
            background: palette.grayscale["100"],
            ...shadows.base,
            maxWidth: 520,
          }}
        >
          <div
            style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
          >
            <div style={{ marginRight: 8 }}>
              <Typography style={{ ...typography.body.medium }}>{t("new-order-sound")}</Typography>
              <Typography
                style={{ ...typography.body.regular, color: palette.grayscale["600"] }}
              >
                {t("enable-sound-notification-for-any-incoming-takeaway-order")}
              </Typography>
            </div>
            <Switch
              checked={incomingOrderSound ?? false}
              onClick={() =>
                handleWebshopConfigToggle(
                  "incomingOrderSound",
                  configurationForm.webshopConfig.incomingOrderSound ? null : 1
                )
              }
            />
          </div>
        </div>
        
        {incomingOrderSound && (
          <div style={{ marginTop: 24 }}>
            <FormControl style={{ width: "100%" }} className={classes.chipSelectWrapper}>
              <Select
                name="pollSoundNotificationAccountIds"
                value={pollSoundNotificationAccountIds || []}
                onChange={updateSoundNotificationAccountIds}
                IconComponent={ChevronDown20new}
                multiple
                MenuProps={MenuProps}
                input={<Field id="select-multiple-chip" label={t("sound-notification-polling-accounts")} select />}
                renderValue={(selected) => (
                  <div style={{ display: "flex", flexWrap: "wrap" }}>
                    {selected.map((value) => {
                      const acc = accountsById[value] || {};
                      return (
                        <Chip
                          key={value}
                          label={`${acc.firstName || ""} ${acc.lastName || ""}`}
                          style={{ margin: 2 }}
                        />
                      );
                    })}
                  </div>
                )}
              >
                {accounts.map((account) => (
                  <MenuItem value={account.id} key={account.id}>
                    <div>
                      <Paragraph>{`${account.firstName || ""} ${account.lastName || ""}`}</Paragraph>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <div style={{ marginTop: 8, marginBottom: 24 }}>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"] }}>
                {t("set-sound-notification-accounts-description")}
              </Typography>
            </div>
          </div>
        )}
        
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
          maxWidth: 520,
          width: "100%"
        }}
      >
        <div style={{ marginRight: 0, marginBottom: 32, width: "100%" }}>
          <Typography style={{ ...typography.body.medium }}>
            {t("beta-features")}
          </Typography>
          <Typography
            style={{ ...typography.body.regular, color: palette.grayscale["600"] }}
          >
            {t("enable-features-that-are-still-in-beta")}
          </Typography>
        </div>
  
        <div style={{ width: "100%" }}>
          <div
            style={{
              marginBottom: 24,
              padding: 12,
              borderRadius: 12,
              background: palette.grayscale["100"],
              ...shadows.base,
              maxWidth: 520,
            }}
          >
            <div
              style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
            >
              <div style={{ marginRight: 8 }}>
                <Typography style={{ ...typography.body.medium }}>
                  {t("enable-driver-assignment-toggle-header")}
                </Typography>
                <Typography
                  style={{
                    ...typography.body.regular,
                    color: palette.grayscale["600"],
                  }}
                >
                  {t("enable-driver-assignment-toggle-description")}
                </Typography>
              </div>
              <Switch
                checked={configurationForm.takeawayConfig?.allowDriverAssignment ?? false}
                onClick={() =>
                  handleTakeawayConfigToggle("allowDriverAssignment", !configurationForm.takeawayConfig?.allowDriverAssignment)
                }
              />
            </div>
          </div>
        </div>
        
      </div>
    </div>
  );
}

export default withTranslation("common")(TakeawaySettings);