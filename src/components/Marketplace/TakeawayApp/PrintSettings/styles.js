import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../../../styles/palette";


const useStyles = makeStyles(theme => ({
 
  chipSelectWrapper: {
    ["& .MuiSelect-select.MuiSelect-select"]: { paddingRight: '19px', borderColor: palette.grayscale["350"] },
    ["& .MuiSelect-select.MuiSelect-select:focus"]: {borderRadius:12},
    ["& .MuiInputBase-root"]: {minHeight: 44 },
    ["& svg"]: {width:'12px', height: '7px', color: palette.grayscale["400"], position: 'absolute',
      right: 19,
      top: 'calc(50% - 2px)',
      pointerEvents: 'none',
    },
    ["& fieldset"]: { borderColor: palette.grayscale["350"] },
    iconOpen: {
      transform: 'rotate(180deg)',
    },
  }
}));

export default useStyles;
