import { withTranslation } from "../../../../i18n";
import useStyles from "./styles";
import { useDispatch, useSelector } from "react-redux";
import { accountActions } from "../../../../redux/actions";
import { accountSelectors } from "../../../../redux/selectors";
import React, { useEffect } from "react";
import isEmpty from "../../../utils/isEmpty";
import {
  takeawayAppSettingViewOptions,
  takeawayAppViews
} from "../../../utils/const";
import List from "@material-ui/core/List";
import SettingsConfigurationSidebar from "../../_navigation/SettingsConfigurationSideBar";
import clsx from "clsx";
import Typography from "@material-ui/core/Typography";
import TakeawaySettings from "../TakeawayApp/TakeawaySettings";
import WebshopSettings from "../TakeawayApp/WebshopSettings";
import UberEatsConfigurations from "../TakeawayApp/UberEatsConfigurations";
import WoltSettings from "../TakeawayApp/WoltSettings";
import LieferandoSettings from "../LieferandoSettings";
import PriceManagement from "../TakeawayApp/PriceManagement";
import PrintSettings from "../TakeawayApp/PrintSettings";

const TakeawayAppSettings = ({ t, id }) => {
  
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const account = useSelector(accountSelectors.getAccountMemo);
  const { isManaged } = (account || {})
  
  const updateView = (value) => () => dispatch(accountActions.setView(value));
  const { view } = useSelector(accountSelectors.getNavigation);
  
  useEffect(() => {
    dispatch(accountActions.setView(isEmpty(takeawayAppSettingViewOptions) ? null : takeawayAppSettingViewOptions[0].value));
  }, [])
  
  const renderNavigation = () => {
    return (
      <List style={{paddingTop: 16}}>
        {takeawayAppSettingViewOptions.map(({i18nKey, value, disabled, secondary, icon }) => (
          <SettingsConfigurationSidebar
            button
            disableRipple
            key={value}
            classes={{ root: clsx(classes.listItemRoot, { [classes.listItemSelected]: value === view }) }}
            onClick={updateView(value)}
            disabled={disabled}
            value={value}
            label={t(i18nKey)}
            icon={icon}
            secondary={secondary}
            active={view === value}
          />
        ))}
      </List>
    )
  }
  
  const renderView = () => {
    switch (view) {
      case takeawayAppViews.GENERAL.value:
        return <TakeawaySettings />
      case takeawayAppViews.PRICE_MANAGEMENT.value:
        return <PriceManagement />
      case takeawayAppViews.PRINT_SETTINGS.value:
        return <PrintSettings />
      case takeawayAppViews.ALLO_WEBSHOP.value:
        return <WebshopSettings />
      case takeawayAppViews.UBER_EATS.value:
        return <UberEatsConfigurations isManaged={isManaged}/>
      case takeawayAppViews.WOLT.value:
        return <WoltSettings isManaged={isManaged}/>
      case takeawayAppViews.LIEFERANDO.value:
        return <LieferandoSettings isManaged={isManaged}/>
      default:
        return null
    }
  };
  
  return(
    <div className={classes.container} >
      <div className={classes.content}>
        <div className={classes.left}>
          <Typography className={classes.headerTitle}>
            Takeaway
          </Typography>
          {renderNavigation()}
        </div>
        {view && (
          <div className={classes.right} style={{ marginTop: -5 }}>
            {renderView()}
          </div>
        )}
      </div>
    </div>
  )
}

export default withTranslation("common")(TakeawayAppSettings);