import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles(theme => ({
  content: {
    paddingTop: 16,
    paddingLeft: 12,
    paddingRight: 12,
    paddingBottom: 45 + 16,
    overflowY: "auto",
    overflowX: "hidden",
    display: "flex",
    flexDirection: "column",
    height: "100%",
    borderTop: "1px solid #E8E7E6"
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  buttonRoot: {
    fontSize: 14,
    padding: '4px 6px',
    borderRadius: theme.spacing(1)
  },
  spacing: {
    flex: 1
  },
  user: {
    display: 'flex',
    alignItems: 'center'
  },
  avatarRoot: {
    width: 16,
    height: 16
  },
  card: {
    border: '1px solid #f3f4f4',
    borderRadius: theme.spacing(1),
    padding: theme.spacing(2)
  },
  cardLayout: {
    display: 'flex',
    '& button + button': {
      marginLeft: 10
    }
  },
  img: {
    width: 32,
    height: 32,
    marginRight: theme.spacing(2),
    borderRadius: '20%'
  },
  secondaryText: {
    '& > p': {
      opacity: 0.6
    }
  },
  actionBtn: {
    marginTop: 8
  },
  section: {
    // maxWidth: theme.breakpoints.values.lg,
    // margin: '0 auto',
    '&+&': {
      marginTop: theme.spacing(6)
    }
  },
  sourceBtnText: {
    fontStyle: "normal",
    fontWeight: 500,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "-0.0014em",
    color: "#737372",
    marginRight: 2
  },
  sourceBtnIcon: {
    transform: "rotate(180deg)",
    display: "flex"
  },
  galleryPadding: {
    paddingLeft: `calc(100vw / 2 - 260px - 178px)`,
    '@media (max-width: 885px)': {
      paddingLeft: 16
    }
  }
}));

export default useStyles;
