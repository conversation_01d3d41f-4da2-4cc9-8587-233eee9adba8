import {fade, makeStyles} from '@material-ui/core/styles';

const drawerWidth = 240;

const useStyles = makeStyles(theme => ({
  drawer: {
    width: 800
  },
  content: {
    // padding: theme.spacing(2)
  },
  actions: {
    textAlign: 'right',
    '& > button': {
      marginLeft: 8
    }
  },
  page: {
    padding: theme.spacing(2),
    minHeight: `calc(100vh - 49px)`
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    justifyContent: 'space-between'
  },
  toolbox: {
    marginLeft: theme.spacing(2)
  },
  iconButtonRoot: {
    padding: theme.spacing(1) - 4
  },
  spacing: {
    flex: 1
  },
  box: {
    padding: theme.spacing(1)
  },
  info: {
    marginTop: theme.spacing(2),
    overflow: 'hidden'
  },
  cursor: {
    cursor: 'pointer'
  },
  taken: {
    '&&': {
      background: '#FFBCBC'
    }
  },
  selected: {
    '&&': {
      border: '2px solid #dbdede'
    }
  },
  requestingPayment: {
    '&&': {
      background: '#FEF7EB',
      border: '2px solid #fde7a1'
    }
  },
  infoHeader: {
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
  },
  data: {
    padding: theme.spacing(2),
    display: 'flex',
    flexDirection: 'column'
  },
  field: {
    width: '100%'
  },
  inputRoot: {
    '&&': {
      color: theme.palette.primary.dark
    }
  },
  capitalize: {
    textTransform: 'capitalize'
  },
  fieldGroup: {
    marginBottom: theme.spacing(2)
  },
  card: {
    border: '1px solid #f3f4f4',
    borderRadius: 8,
    '& button + button': {
      marginLeft: 10
    }
  },
  formControl: {
    minWidth: 160
  },
  uploaderContainer: {
    cursor: 'pointer',
    height: 180,
    width: '100%',
    borderRadius: 6,
    background: '#F7F8F9',
    backgroundPosition: 'center',
    backgroundSize: 'cover',
    display: 'flex',
    alignItems: 'center',
    '& > p': {
      textAlign: 'center',
      width: '100%'
    }
  },
  clearImage: {
    marginTop: theme.spacing(1),
    width: '100%',
    fontSize: 14,
    padding: '4px 6px',
    borderRadius: theme.spacing(1)
  },
  uploaderActions: {
    display: 'flex'
  },
  previewUploadAction: {
    marginTop: theme.spacing(1),
    width: '100%',
    fontSize: 14,
    padding: '4px 6px',
    borderRadius: theme.spacing(1),
    flexBasis: '20%'
  },
  clickable: {
    cursor: 'pointer'
  },
}));

export default useStyles;
