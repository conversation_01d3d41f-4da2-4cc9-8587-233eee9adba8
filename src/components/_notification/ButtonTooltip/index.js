import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import palette from "../../../../styles/palette";
import Tooltip from "@material-ui/core/Tooltip";
import { ButtonBase, withStyles } from "@material-ui/core";
import typography from "../../../../styles/typography";
import Typography from "@material-ui/core/Typography";
import { useDispatch, useSelector } from "react-redux";
import { restaurantSelectors } from "../../../../redux/selectors";
import isEmpty from "../../../utils/isEmpty";
import { useRouter } from "next/router";
import { views } from "../../../utils/administrationRoutes";
import { restaurantActions } from "../../../../redux/actions";
import byId from "../../../utils/byId";
import { onboardingTutorialSteps, operationModes } from "../../../utils/const";
import StandaloneTooltip from "../StandaloneTooltip";

const HtmlTooltip = withStyles((theme, props) => ({
  tooltip: {
    width: 280,
    borderRadius: 12,
    background: palette.grayscale["800"],
    color: palette.grayscale["100"],
    // maxWidth: "calc(100% - 24px)",
    padding: 16,
    marginBottom: 0
  },
  arrow: {
    top: "4px !important",
    color: palette.grayscale["800"],
    width: "14px !important",
    height: "20px !important",
    marginRight: props => (props.placement === 'left-start') ? "-13px !important" : null,
    marginLeft: props => (props.placement === 'right-start') ? "-13px !important" : null,
    "&:before": {
      borderTopRightRadius: 4,
    },
    
  },
}))(Tooltip);

const ButtonTooltip = ({ t, children, hide, stepId, subStepId, standalone, onClick, placement = "left-start" }) => {
  const dispatch = useDispatch();
  
  const router = useRouter();
  const { asPath, query = {} } = router;
  const { v } = query;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "")
  
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  
  const [step, setStep] = useState({});
  
  const { mode } = useSelector(restaurantSelectors.getMode);
  const isExplorationMode = mode === operationModes.EXPLORATION
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  const tutorial = useSelector(restaurantSelectors.getRestaurantTutorial);
  
  useEffect(() => {
    if (restaurantId) {
      dispatch(restaurantActions.getTutorial(restaurantId))
    }
  }, [restaurantId])
  
  const setupStep = (steps) => {
    if (isEmpty(steps)) {
      return;
    }
    let stepsById = byId(steps);
    
    if (hide) {
      setOpen(false);
      setStep({});
    } else if (v === views.FLOOR_PLAN_EDITOR && stepsById[onboardingTutorialSteps.TABLE_CREATION.value]) {
      setOpen(true);
      setStep(stepsById[onboardingTutorialSteps.TABLE_CREATION.value]);
    } else if (v === views.MENU_EDITOR_NEW && stepsById[onboardingTutorialSteps.ITEM_CREATION.value]) {
      setOpen(true);
      setStep(stepsById[onboardingTutorialSteps.ITEM_CREATION.value]);
    } else if ((stepId === onboardingTutorialSteps.EXPRESS_ORDER_CREATION.value) && stepsById[onboardingTutorialSteps.EXPRESS_ORDER_CREATION.value]) {
      setOpen(true);
      setStep(stepsById[onboardingTutorialSteps.EXPRESS_ORDER_CREATION.value]);
    } else if ((stepId === onboardingTutorialSteps.TAKEAWAY_ORDER_CREATION.value) && stepsById[onboardingTutorialSteps.TAKEAWAY_ORDER_CREATION.value]) {
      setOpen(true);
      setStep(stepsById[onboardingTutorialSteps.TAKEAWAY_ORDER_CREATION.value]);
    } else if (v === views.DINE_IN && stepsById[onboardingTutorialSteps.DINE_IN_ORDER_CREATION.value]) {
      setOpen(true);
      setStep(stepsById[onboardingTutorialSteps.DINE_IN_ORDER_CREATION.value]);
    } else if ((stepId === onboardingTutorialSteps.REVIEW_LEGAL_INFO.value) && stepsById[onboardingTutorialSteps.REVIEW_LEGAL_INFO.value]) {
      setOpen(true);
      setStep(stepsById[onboardingTutorialSteps.REVIEW_LEGAL_INFO.value]);
    } else if ((stepId === onboardingTutorialSteps.FINISH_ALLO_PAY_ONBOARDING.value) && stepsById[onboardingTutorialSteps.FINISH_ALLO_PAY_ONBOARDING.value]) {
      setOpen(true);
      setStep(stepsById[onboardingTutorialSteps.FINISH_ALLO_PAY_ONBOARDING.value]);
    } else {
      setOpen(false);
      setStep({});
    }
  }
  
  useEffect(() => {
    if (!isEmpty(tutorial) && !tutorial.completed && !isEmpty(tutorial.steps)) {
      const { steps: fetchedSteps = [] } = tutorial || {}
      const uncompletedSteps = fetchedSteps.filter(t => !t.complete)
      setupStep(uncompletedSteps);
    } else {
      setStep({})
      setOpen(false);
    }
  }, [JSON.stringify(tutorial), v, hide, stepId, subStepId])
  
  const getContent = () => {
    if (isEmpty(step) || hide) {
      return null;
    }
    
    const { name, description, subSteps: steps = [] } = step;
    
    let resolvedName = name;
    let resolvedDescription = description;
    
    if (subStepId && !isEmpty(steps)) {
      const lowerStep = steps.find(s => s.id === subStepId);
      if (!isEmpty(lowerStep)) {
        resolvedName = lowerStep.name;
        resolvedDescription = lowerStep.description;
      }
    }
    
    if (!resolvedName && !resolvedDescription) {
      return null;
    }
    
    const Content = () => (
      <div style={{ zIndex: 1600, display: 'flex', flexDirection: 'column' }}>
        {!standalone && (
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
            {resolvedName}
          </Typography>
        )}
        <Typography style={{ ...typography.body.regular, color: palette.grayscale["100"], marginTop: 4 }}>
          {resolvedDescription}
        </Typography>
        {standalone && (
          <div>
            <ButtonBase onClick={onClick} disableRipple disableTouchRipple style={{ padding: '5px 15px', border: `1px solid ${palette.transparency.light.border["20"]}`, borderRadius: 10, marginTop: 12 }}>
              <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                {t('common-continue')}
              </Typography>
            </ButtonBase>
          </div>
        )}
      </div>
    )
    
    if (standalone) {
      return (
        <StandaloneTooltip>
          <Content />
        </StandaloneTooltip>
      )
    }
    
    return <Content />
  }
  
  if (!isExplorationMode) {
    return children || null
  }
  
  if (standalone) {
    return getContent();
  }
  
  return (
    <HtmlTooltip
      open={open && !hide}
      onOpen={handleOpen}
      onClose={() => {}}
      placement={placement}
      arrow
      disableHoverListener
      title={getContent()}
    >
      {children}
    </HtmlTooltip>
  );
};

export default withTranslation('common')(ButtonTooltip);
