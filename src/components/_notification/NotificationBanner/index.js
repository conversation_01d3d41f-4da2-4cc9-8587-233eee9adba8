import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import PromotionCard from "../../_navigation/PromotionCard";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { getView, views } from "../../../utils/administrationRoutes";
import { applicationSelectors, notificationBannerSelectors } from "../../../../redux/selectors";
import { appActions } from "../../../../redux/actions";
import ProductUpdateModal from "../../_popup/ProductUpdateModal";
import { getProductUpdateEntries } from "../../../api";
import isEmpty from "../../../utils/isEmpty";

/*deprecated*/
const NotificationBanner = ({ t }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { v } = router.query;
  const [currentNavigation, setCurrentNavigation] = useState(getView(v));
  const hideBanner =  currentNavigation === views.TERMINAL || currentNavigation === views.PICKUP || currentNavigation === views.EXPRESS
  
  const { loading } = useSelector(applicationSelectors.getStateMemo)
  const { visible } = useSelector(notificationBannerSelectors.getNotificationBanner);
  
  const [isShowingProductUpdateModal, setIsShowingProductUpdateModal]= useState(false)
  const [publishedEntry, setPublishedEntry] = useState( null);
  
  useEffect(() => {
    setCurrentNavigation(getView(v))
  }, [router.query]);
  
  const fetchEntries = () => {
    getProductUpdateEntries()
      .then(({ data = {} }) => {
        const { items = [] } = (data || {});
        if (!isEmpty(items)) {
          setPublishedEntry(items.find(p => p.status === "PUBLISHED"))
        }
      })
      .catch(()=>{})
  }
  
  useEffect(()=> {
    if (visible) {
      fetchEntries()
    }
  },[visible])
  
  const onDismiss = () => {
    dispatch(appActions.clearNotificationBanner());
    window.location.reload();
  };
  
  const onStart = () => {
    setIsShowingProductUpdateModal(true)
    dispatch((appActions.clearNotificationBanner()));
  };
  
  const onClosePopUp = () => {
    setIsShowingProductUpdateModal(false)
    window.location.reload();
  };
  
  return (
    <div style={{ width: 324, position: "absolute", bottom: 10, right: 20 }}>
     {visible && !hideBanner && !loading ? (
       <PromotionCard
         onDismissBtn={onDismiss}
         startButtonLabel={t("open")}
         onStartBtn={onStart}
       />
      ) : null }
     {isShowingProductUpdateModal && (
       <ProductUpdateModal open={isShowingProductUpdateModal} onClose={onClosePopUp} publishedEntry={publishedEntry} />
     )}
    </div>
  );
}


export default withTranslation("common")(NotificationBanner);
