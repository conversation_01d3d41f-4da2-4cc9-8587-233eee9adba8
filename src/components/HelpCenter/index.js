import React, { useEffect, useState } from "react";
import { i18n, withTranslation } from "../../../i18n";
import {
	ArrowLeftDark20,
	ArrowRight20,
	BurgerIllustration,
	ChatIconWhite20,
	FriesIllustration,
	PlayVideoButton,
	SaladBowlIllustration
} from "../../utils/icons";
import { ButtonBase, Typography, useMediaQuery } from "@material-ui/core";
import palette from "../../../styles/palette";
import SecondaryBar from "../_navigation/SecondaryBar";
import typography from "../../../styles/typography";
import { getHelpCenterItemsAggregated } from "../../api";
import VideoTutorialModal from "../_popup/VideoTutorialModal";
import isEmpty from "../../utils/isEmpty";
import { systemLanguages } from "../../utils/const";
import { views } from "../../utils/administrationRoutes";
import { useRouter } from "next/router";
import { appBarHeight } from "../../../styles/theme";
import dynamic from "next/dynamic";

const FrontChatWithNoSSR = dynamic(
	() => import('../FrontChat'),
	{ ssr: false }
)


const HelpCenter = ({ t }) => {
	const isMobile = useMediaQuery('(max-width:700px)');
	
	const router = useRouter();
	const { asPath } = router;
	const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "")
	
	const [tags, setTags] = useState([]);
	const [categories, setCategories] = useState([]);
	const [selectedTutorial, setSelectedTutorial] = useState(null);
	
	const [expandedCategory, setExpandedCategory] = useState([])
	const [isShowingExpandedContent, setIsShowingExpandedContent] = useState(false)
	
	useEffect(() => {
		getHelpCenterItemsAggregated().then(({ data }) => {
			const { highlightedItems, tagGroups, tags } = data;
			setTags(tags);
			setCategories(tagGroups);
		}).catch(() => {})
	}, [])
	
	// const openChat = () => {
	// 	$crisp.push(['do', 'chat:open']);
	// 	$crisp.push(['do', 'chat:show']);
	// }
	
	const getForLanguage = (i18nObj = {}) => {
		const language = i18n.language
		if (isEmpty(i18nObj)) {
			return ""
		}
		
		const keys = Object.keys(i18nObj)
		
		if (isEmpty(keys)) {
			return ""
		}
		
		if (keys.indexOf(language) > -1) {
			return i18nObj[language]
		}
		
		return i18nObj[systemLanguages.de.value] || i18nObj[systemLanguages.en.value] || i18nObj[keys[0]]
	}
	
	const showAllVideosOfCategory = (category) => {
		setExpandedCategory(category)
		setIsShowingExpandedContent(true)
	}
	
	const getBackBtn = () => {
		return (
			<ButtonBase disableRipple disableTouchRipple style={{ padding: 5, borderRadius: 10, border: `1px solid ${palette.grayscale["350"]}`, marginLeft: 12, marginRight: 12 }} onClick={onBack}>
				<ArrowLeftDark20 />
			</ButtonBase>
		)
	}
	
	const onBack = () => {
		router.push(`${resolvedAsPath}?v=${views.SUPPORT}`, undefined, { shallow: true })
		setIsShowingExpandedContent(false)
	}

	const getExpandedContent = () => {
		return (
			<div style={{ flex: 1, overflow: "hidden", "-webkit-font-smoothing": "subpixel-antialiased" }}>
				<div style={{ display: "flex", flexDirection: "column", height: "100%", padding: "24px 12px 120px 12px", overflow: "scroll", position: "relative" }}>
					<div style={{ display: "flex", flexDirection: isMobile ? "column" : "row"}}>
						{(expandedCategory.items || []).map(({ id, nameI18n, imageUrlI18n, descriptionI18n, videoUrlI18n }) => {
							return (
								<div style={{ marginBottom: 32 }} key={id}>
									<div style={{ display: "flex", flexWrap: "wrap" }}>
											<ButtonBase key={id} style={{ marginRight: 16, marginTop: 12, width: 420, height: "100%", display: "block", textAlign: "left" }}
																	onClick={() => setSelectedTutorial({ nameI18n, descriptionI18n, videoUrlI18n })}>
												<div style={{ position: "relative", paddingTop: "56.25%", width: "100%", height: "100%", background: `url('${getForLanguage(imageUrlI18n)}')`, borderRadius: 16, backgroundPosition: "center", backgroundSize: "cover" }}>
													<div style={{ borderRadius: 16, overflow: "hidden", maxWidth: "100%", position: "absolute", left: 0, top: 0, width: "100%", height: "100%" }}>
														<div style={{ display: "flex", width: "100%", height: "100%", justifyContent: "center", alignItems: "center" }}>
															<PlayVideoButton />
														</div>
													</div>
												</div>
												<Typography style={{ ...typography.medium.medium, marginTop: 12, marginLeft: 4 }}>{getForLanguage(nameI18n)}</Typography>
											</ButtonBase>
									</div>
								</div>
							);
						})}
					</div>
				</div>
			</div>
		)
}
	
	return (
		<div style={{ position: "relative", height: "100%" }}>
			<div style={{ display: "flex", flexDirection: "column", height: "100%" }}>
				{!isShowingExpandedContent ? (<SecondaryBar title={t("help-and-feedback")} />) :
					(<>
						<div style={{ display: "flex", flexDirection: "row", justifyContent: "flex-start", alignItems: "center", borderBottom: `1px solid ${palette.grayscale.divider}`, marginTop: 8, paddingBottom: 12, height: appBarHeight + 1,}}>
							{getBackBtn()}
							<Typography style={{ ...typography.medium.semiBold }}>
								{getForLanguage(expandedCategory.tag.nameI18n)} {t("tutorials")}
							</Typography>
						</div>
					</>)
				}
				{!isShowingExpandedContent ? (
					<div style={{ flex: 1, overflow: "hidden", "-webkit-font-smoothing": "subpixel-antialiased" }}>
					<div style={{ display: "flex", flexDirection: "column", height: "100%", padding: "16px 12px 120px 12px", overflow: "scroll", position: "relative" }}>
						{/*	header */}
						<div style={{ width: "100%", paddingTop: isMobile ? 0 : 88, display: "flex", justifyContent: "center" }}>
							<div style={{ display: "flex", flexDirection: "column", textAlign: "center" }}>
								<div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
									<Typography style={{ ...typography.small.medium, marginBottom: 14, padding: "2px 6px", background: palette.transparency.dark["4"], borderRadius: 12 }}>{t("tutorials")}</Typography>
								</div>
								<Typography style={{ ...typography.x.helpCenter, maxWidth: 478 }}>{t("dont-miss-out-on-all-our-features")}</Typography>
							</div>
						</div>
						<div style={{ maxWidth: 532, margin: "64px auto 0" }}>
							<div style={{ display: "flex", alignItems: "center", justifyContent: "center", flexWrap: "wrap", overflow: "hidden" }}>
								{(tags || []).map(({ id, nameI18n }) => (
									<Typography key={id} style={{ ...typography.small.medium, marginBottom: 12, marginRight: 12, padding: "2px 6px", background: palette.transparency.dark["4"], borderRadius: 12 }}>
										{getForLanguage(nameI18n)}
									</Typography>
								))}
							</div>
						</div>
						<div style={{ marginTop: 64 }}>
							{(categories || []).map(category => {
								const { tag = {}, items: videos = [] } = category;
								const { nameI18n = {}, id } = tag;
								return (
									<div style={{ marginTop: 48 }} key={id}>
										<div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
											<Typography style={{ ...typography.large.semiBold }}>{getForLanguage(nameI18n)}</Typography>
											<ButtonBase onClick={() => showAllVideosOfCategory(category)}>
												<Typography style={{ ...typography.body.medium, color: palette.grayscale["600"] }}>{t("see-all")}</Typography>
												<ArrowRight20 />
											</ButtonBase>
										</div>
										<div style={{ display: "flex", flexWrap: "nowrap", overflowX: "auto" }}>
											{(videos || []).map(({ id, nameI18n, imageUrlI18n, descriptionI18n, videoUrlI18n }) => (
												<ButtonBase key={id} style={{ marginRight: 16, marginTop: 12, minWidth: isMobile ? 210 : 420, height: "100%", display: "block", textAlign: "left" }}
																		onClick={() => setSelectedTutorial({ nameI18n, descriptionI18n, videoUrlI18n })}>
													<div style={{ position: "relative", paddingTop: "56.25%", width: "100%", height: "100%", background: `url('${getForLanguage(imageUrlI18n)}')`, borderRadius: 16, backgroundPosition: "center", backgroundSize: "cover" }}>
														<div style={{ borderRadius: 16, overflow: "hidden", maxWidth: "100%", position: "absolute", left: 0, top: 0, width: "100%", height: "100%" }}>
															<div style={{ display: "flex", width: "100%", height: "100%", justifyContent: "center", alignItems: "center" }}>
																<PlayVideoButton />
															</div>
														</div>
													</div>
													<Typography style={{ ...typography.medium.medium, marginTop: 12, marginLeft: 4 }}>{getForLanguage(nameI18n)}</Typography>
												</ButtonBase>
											))}
										</div>
									</div>
								);
							})}
						</div>
						{!isMobile && <div style={{ position: "absolute", left: "0", top: 77 }}><SaladBowlIllustration /></div>}
						{!isMobile && <div style={{ position: "absolute", right: 40, top: 90 }}><BurgerIllustration /></div>}
						{!isMobile && <div style={{ position: "absolute", right: "0", top: 200 }}><FriesIllustration /></div>}
					</div>
				</div>) :
					(
						<>
							{getExpandedContent()}
						</>
					)
				}
				<FrontChatWithNoSSR />
			</div>
			{!isEmpty(selectedTutorial) && (
				<VideoTutorialModal
					open={selectedTutorial.videoUrlI18n}
					url={getForLanguage(selectedTutorial.videoUrlI18n)}
					description={getForLanguage(selectedTutorial.descriptionI18n)}
					title={getForLanguage(selectedTutorial.nameI18n)}
					onClose={() => setSelectedTutorial(null)}
					large
				/>
			)}
		</div>
	)
}

export default withTranslation('common')(React.memo(HelpCenter))
