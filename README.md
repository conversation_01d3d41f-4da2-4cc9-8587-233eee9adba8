# Next.js example 

## How to use

Download the example [ or clone the repo](https://github.com/mui-org/material-ui):

```sh
curl https://codeload.github.com/mui-org/material-ui/tar.gz/master | tar -xz --strip=2  material-ui-master/examples/nextjs
cd nextjs
```

Install it and run:

```sh
yarn start
```
## Code improvement document
[Code Improvement](code_improvement.md)

## The idea behind the example

[Next.js](https://github.com/zeit/next.js) is a framework for server-rendered React apps.

https://preview.leviee.de/booking/DE80333SER%231


https://gist.github.com/koudaiii/54e3c438a85a52c7c368

For widget see:
widget-ui project
