// @ts-check
const { defineConfig } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './src/__tests__/e2e',
  timeout: 50000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['list', { printSteps: true }],
    ['html', { open: 'never' }],
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  },
  projects: [
    { 
      name: 'setup', testMatch: /.*\.setup\.js/,
      use: {
        browserName: 'chromium',
        storageState: './src/__tests__/e2e/.auth/user.json',
      }
    },
    {
      name: 'chromium',
      use: {
        browserName: 'chromium',
        storageState: './src/__tests__/e2e/.auth/user.json',
      },
      dependencies: ['setup'],
    },
  ],
  webServer: {
    command: 'PORT=3000 npm start',
    url: 'http://localhost:3000/health-check',
    timeout: 120 * 1000,
    reuseExistingServer: true,
    stdout: 'pipe',
    stderr: 'pipe',
    env: {
      PORT: '3000',
      NODE_ENV: 'production',
    },
  },
});