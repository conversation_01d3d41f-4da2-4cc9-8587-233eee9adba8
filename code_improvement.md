# Code improvement 
`v1` of the code improvement list, this will be an on-going process with the goal to 
modernize the application and make it easy to test and maintain.

## Simple guidelines
 * Any new component should be small/manageable (plan would be to be testable with cypress component testing)

## TODO
 * Make a list of parts of the application where unnecessary rerenders are happening and make a plan/roadmap to fix
 * Split the biggest components into more manageable parts. Split the `rendering` logic as well, especially decouple `if` statements in components outside into it's own component so we can benchmark rendering and re-rendering
 * Folder structure needs to be revamped, set rules for what goes where.
 * Make a list of the places we have "hacks" for anything `router` related, will need to figure out how to fix them when we upgrade `next.js`
 * Use `data-cy` in the components for cypress 
 * Write basic stories for `e2e` testing, this will help with long term goals.
 * Split components into smaller ones, make them as dump as possible, so we can start component testing, again this is a prerequisite for the long term goals.
 * Introduce `emotion` or check how easy it is for us to migrate to it since we need it for the eventual `material` upgrade to `v5`
## Long term goals for performance upgrades
 - [ ] Upgrade `React` to latest, will allow us to use the newest features for performance upgrades (eg `suspense`)
 - [ ] Upgrade `Next.js` to latest
 - [ ] Upgrade `Material-UI` to latest