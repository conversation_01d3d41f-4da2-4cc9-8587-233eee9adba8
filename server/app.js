require('dotenv').config();

const express = require('express');
const next = require('next');
const nextI18NextMiddleware = require('next-i18next/middleware').default;
const bodyParser = require('body-parser');
const compression = require('compression');
const sitemap = require('./middleware/sitemap');
const sitemapAndRobots = require('./sitemapAndRobots');
const axios = require("axios");

const nextI18next = require('../i18n.js');

const dev = process.env.NODE_ENV !== 'production';
const port = process.env.PORT || 4160;
const ROOT_URL = `http://localhost:${port}`;
const app = next({ dev });
const handle = app.getRequestHandler();

const Printer = require('zuzel-printer')

const lEnv = process.env.NEXT_PUBLIC_LEVIEE_ENV || 'local';


process.on('uncaughtException', (err) => {
    console.error('--- Uncaught Exception ---');
    console.error('Timestamp:', new Date().toISOString());
    console.error('Error Message:', err.message);
    console.error('Stack Trace:', err.stack);
    console.error('Environment Variables:', process.env);
    console.error('Node Version:', process.version);
    console.error('--------------------------------');
    process.exit(1); // Exit the process with a non-zero exit code
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('--- Unhandled Rejection ---');
    console.error('Timestamp:', new Date().toISOString());
    console.error('Reason:', reason);
    if (reason instanceof Error) {
        console.error('Error Message:', reason.message);
        console.error('Stack Trace:', reason.stack);
    }
    console.error('Promise:', promise);
    console.error('Environment Variables:', process.env);
    console.error('Node Version:', process.version);
    console.error('--------------------------------');
    process.exit(1); // Exit the process with a non-zero exit code
});


app
  .prepare()
  .then(() => {
    const server = express();
    console.log('NODE_END: ' + process.env.NODE_ENV);
    console.log('NEXT_PUBLIC_LEVIEE_ENV: ' + process.env.NEXT_PUBLIC_LEVIEE_ENV);
    console.log('lEnv: ' + lEnv);

    server.use(nextI18NextMiddleware(nextI18next));
    server.use(compression({
      threshold: 0
    }));
    server.use(bodyParser.urlencoded({ extended: true }));
    server.use(bodyParser.json());
    server.use(sitemap);
    sitemapAndRobots({ server });

    server.post('/log', (req, res) => {
      console.log(req.body);
    })

    server.post('/print', (req, res) => {
      const { name, content } = req.body;
      const printerName = name;
      try {
        const list = Printer.list() || [];
        if (list.indexOf(printerName) === -1) {
          return res.json({
            printed: false,
            error: "Printer " + printerName + " not found"
          });
        }
        const printer = new Printer(printerName);

        const contentBytes = content;
        const bytes = contentBytes.concat([29, 86, 48])
        const buffer = Buffer.from(bytes);
        const jobFromBuffer = printer.printBuffer(buffer);
        // Listen events from job
        jobFromBuffer.once('sent', function () {
          console.log('Job ' + jobFromBuffer.identifier + ' has been sent');
          return res.json({
            printed: true
          });
        });
        jobFromBuffer.on('completed', function () {
          console.log('Job ' + jobFromBuffer.identifier + ' has been printed');
          jobFromBuffer.removeAllListeners();
          printer.destroy();
          return res.json({
            printed: true
          });
        });
        jobFromBuffer.on('error', function () {
          console.log('Job ' + jobFromBuffer.identifier + ' has failed');
          jobFromBuffer.removeAllListeners();
          printer.destroy();
          throw Error();
        });
        //destroys all child processes
        printer.destroy()
      } catch (err) {
        return res.json({
          printed: false,
          error: err
        });
      }
    })

    server.get('/system/printers', (req, res) => {
      return res.json({
        printers: Printer.list() || []
      });
    })

    server.get('/health-check', async (req, res) => {
      res.status(200).send('')
    });

    server.get('*', (req, res) => {
      res.header('Access-Control-Allow-Credentials', true);
      res.header('Access-Control-Allow-Origin', req.headers.origin);
      res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,UPDATE,OPTIONS');
      res.header('Access-Control-Allow-Headers', 'X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept');
      return handle(req, res)
    });

    // starting express server
    server.listen(port, err => {
      if (err) throw err;
      console.log(`> Ready on ${ROOT_URL}`) // eslint-disable-line no-console
    })
  })
  .catch(err => {
    // eslint-disable-line no-console
    console.error(err.stack);
  });
