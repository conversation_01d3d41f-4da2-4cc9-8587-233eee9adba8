const Printer = require('zuzel-printer')

const toBytes = (str) => {
  const arr = []
  for (let i=0; i < str.length; i++) {
    arr.push(str[i].charCodeAt(0))
  }
  return arr
}


  const list = Printer.list() || [];
  const printer = new Printer(list[1]);
  let content = "\n\nallO\n\nTest printout\n\nLorem Ipsum is simply dummy text of the printing and typesetting industry."
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industry"
  content = content + "Lorem Ipsum is simply dummy text of the printing and typesetting industryOO"

  const contentBytes = toBytes(content + "\n\n\n\n\n\n\n");
  const fontTypeBytes = [27, 33, 0]
  const fontSizeBytes = [29, 33, 1]
  const positionBytes = [27, 97, 0, 48]
  const emphasizedBytes = [27, 33, 8] // bold
  const buffer = Buffer.from(positionBytes.concat([83, 111, 109, 101, 116, 104, 105, 110, 103, 32, 104, 101, 114, 101, 10, 27, 33, 8, 98, 111, 108, 100, 32, 104, 101, 114, 101, 27, 33, 0, 10, 110, 111, 116, 32, 98, 111, 108, 100]));

  const options = { 'cpi': 5 }
  let jobFromBuffer = printer.printBuffer(buffer, options);
  // Listen events from job
  jobFromBuffer.once('sent', function () {
    console.log('Job ' + jobFromBuffer.identifier + ' has been sent');
  });
  jobFromBuffer.on('completed', function () {
    console.log('Job ' + jobFromBuffer.identifier + ' has been printed');
    // jobFromBuffer.removeAllListeners();
    printer.destroy();
  });
  jobFromBuffer.on('error', function () {
    console.log('Job ' + jobFromBuffer.identifier + ' has failed');
    jobFromBuffer.removeAllListeners();
    printer.destroy();
    throw Error();
  });



  const cutBytes = [29, 86, 48]

  jobFromBuffer = printer.printBuffer(Buffer.from(cutBytes), options);
  // Listen events from job
  jobFromBuffer.once('sent', function () {
    console.log('Job ' + jobFromBuffer.identifier + ' has been sent');
  });
  jobFromBuffer.on('completed', function () {
    console.log('Job ' + jobFromBuffer.identifier + ' has been printed');
    // jobFromBuffer.removeAllListeners();
    printer.destroy();
  });
  jobFromBuffer.on('error', function () {
    console.log('Job ' + jobFromBuffer.identifier + ' has failed');
    jobFromBuffer.removeAllListeners();
    printer.destroy();
    throw Error();
  });

  printer.destroy()
