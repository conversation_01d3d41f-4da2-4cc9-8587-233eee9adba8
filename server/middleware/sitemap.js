
const { createSitemap, EnumChangefreq } = require("sitemap");
const { Router } = require("express");
const router = Router();

router.get("/sitemap.xml", async (req, res) => {
  const sitemap = createSitemap({
    hostname: process.env.SITEMAP_HOSTNAME,
  });
  // Add any static entries here
  // sitemap.add({url: "/", changefreq: EnumChangefreq.DAILY});
  // sitemap.add({url: "/privacy-policy"});
  // To add dynamic entries
  // const products = [{ slug: 'testPage' }];
  // for (const product of products) {
  //   sitemap.add({url: `/product/${product.slug}`, changefreq: EnumChangefreq.DAILY});
  // }
  res.contentType("application/xml");
  res.send(sitemap.toString());
});

module.exports = router;
