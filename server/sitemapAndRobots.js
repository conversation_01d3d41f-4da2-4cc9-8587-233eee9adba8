const sm = require('sitemap')
const path = require('path')

const dev = process.env.NODE_ENV !== 'production';

const port = process.env.PORT || 4160;

const ROOT_URL = dev
  ? `http://localhost:${port}`
  : 'https://gluttony.now.sh';

const sitemap = sm.createSitemap({
  hostname: ROOT_URL,
  cacheTime: 600000 // 600 sec - cache purge period
})

const setup = ({ server }) => {
  server.get('/robots.txt', (req, res) => {
    res.sendFile(path.join(__dirname, '../public', 'robots.txt'))
  })
}

module.exports = setup
