import { createSelector } from "reselect";
import { isEmpty } from "../helpers";

const toJs = (s) => (s && typeof s.toJS !== 'undefined' ? s.toJS() : s);

export const getConfiguration = (state) => {
  const configuration = toJs(state.getIn(['configuration', 'configuration']));
  return {
    configuration
  };
};

export const getConfigurationMemo = createSelector(
  state => state.getIn(['configuration', 'configuration']),
  configuration => {
    if (isEmpty(configuration)) {
      return {}
    }
    return toJs(configuration)
  }
);

export const getCapabilities = createSelector(
  state => state.getIn(['configuration', 'capabilities']),
  capabilities => toJs(capabilities)
);

export const getConfigurationHasFlashTakeaway = createSelector(
  state => state.getIn(['configuration', 'configuration', 'hasFlashTakeaway']),
  hasFlashTakeaway => hasFlashTakeaway
);

export const getConfigurationShowInventory = createSelector(
  state => state.getIn(['configuration', 'configuration', 'showInventory']),
  showInventory => showInventory
);

export const getConfigurationUseUpdatedCheckout = createSelector(
  state => state.getIn(['configuration', 'configuration', 'useUpdatedCheckout']),
  useUpdatedCheckout => toJs(useUpdatedCheckout)
);

export const getConfigurationUseUpdatedTerminal = createSelector(
  state => state.getIn(['configuration', 'configuration', 'useUpdatedTerminal']),
  useUpdatedTerminal => toJs(useUpdatedTerminal)
);

export const getConfigurationUseUpdatedMenuEditor = createSelector(
  state => state.getIn(['configuration', 'configuration', 'useUpdatedMenuEditor']),
  useUpdatedMenuEditor => toJs(useUpdatedMenuEditor)
);

export const getExpressTerminalConfiguration = createSelector(
  (state) => state.getIn(['configuration', 'configuration', 'staticExpressTerminal']),
    staticExpressTerminal => toJs(staticExpressTerminal)
);

export const getSearchKeyboardConfiguration = createSelector(
  (state) => state.getIn(['configuration', 'configuration', 'staticSearchKeyboard']),
    staticSearchKeyboard => toJs(staticSearchKeyboard)
);

export const getShowConnectivityIssueNotification = createSelector(
  (state) => state.getIn(['configuration', 'configuration', 'showConnectivityIssueNotification']),
    showConnectivityIssueNotification => toJs(showConnectivityIssueNotification)
);

export const getCoursesConfiguration = createSelector(
  (state) => state.getIn(['configuration', 'configuration', 'courseConfig']),
    coursesConfiguration => toJs(coursesConfiguration)
)

export const getConfigurationHasOngoingItems = createSelector(
  state => state.getIn(['configuration', 'configuration', 'hasOngoingItems']),
  hasOngoingItems => toJs(hasOngoingItems)
);

export const getConfigurationHasEnabledTakeawayInDineIn = createSelector(
  state => state.getIn(['configuration', 'configuration', 'enableTakeawayInDineIn']),
  hasEnabledTakeawayInDineIn => toJs(hasEnabledTakeawayInDineIn)
);

export const getConfigurationDiscountPinConfigured = createSelector(
  state => state.getIn(['configuration', 'configuration', 'discountPin']),
  discountPin => !!discountPin
);

export const getDslSettings = createSelector(
  state => state.getIn(['configuration', 'configuration', 'dslSettings']),
  dslSettings => toJs(dslSettings)
);

export const getFrontEndSettings = createSelector(
  state => state.getIn(['configuration', 'configuration', 'frontEndSettings']),
  frontEndSettings => toJs(frontEndSettings)
);

export const getCustomExtrasEnabledConfiguration = createSelector(
  state => state.getIn(['configuration', 'configuration', 'customExtrasEnabled']),
  customExtrasEnabled => toJs(customExtrasEnabled)
);

export const getMandatoryCancellationReasonConfiguration = createSelector(
  state => state.getIn(['configuration', 'configuration', 'enableMandatoryCancelReason']),
  enableMandatoryCancelReason => toJs(enableMandatoryCancelReason)
);