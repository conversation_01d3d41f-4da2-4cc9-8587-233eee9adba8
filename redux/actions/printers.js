import { fail, ok } from '../helpers';

const prefix = 'printers';

export const GET_PRINTERS = `${prefix}/get-printers`;
export function getPrinters(restaurantId) {
  return {
    type: GET_PRINTERS,
    payload: {
      restaurantId
    }
  };
}

export const CONSUME_PRINTING = `${prefix}/consume-printing`;
export function consumePrinting(payload) {
  return {
    type: CONSUME_PRINTING,
    payload
  };
}

export const PING_PRINTERS = `${prefix}/ping-printers`;
export function pingPrinters() {
  return {
    type: PING_PRINTERS
  };
}

export const PRINT_CONTENT = `${prefix}/print-content`;
export function printContent(restaurantId) {
  return {
    type: PRINT_CONTENT
  };
}

export function getPrintersSuccess(printers) {
  return {
    type: ok(GET_PRINTERS),
    payload: {
      printers
    }
  };
}
export function getPrintersFailure() {
  return {
    type: fail(GET_PRINTERS)
  };
}

export const OPEN_CASH_DRAWER = `${prefix}/open-cash-drawer`;
export function openCashDrawer() {
  return {
    type: OPEN_CASH_DRAWER
  };
}
