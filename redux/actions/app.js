import { ok, fail } from '../helpers';

const prefix = 'app';

export const LOAD_RESTAURANT = `${prefix}/load-restaurant`;
export function loadRestaurant(restaurantId) {
  return {
    type: LOAD_RESTAURANT,
    restaurantId
  };
}

export function loadRestaurantSuccess() {
  return {
    type: ok(LOAD_RESTAURANT)
  };
}

export function loadRestaurantFailure() {
  return {
    type: fail(LOAD_RESTAURANT)
  };
}

export const LOAD_ORGANIZATION = `${prefix}/load-organization`;
export function loadOrganization(organizationId) {
  return {
    type: LOAD_ORGANIZATION,
    organizationId
  };
}

export const SET_NOTIFICATION = `${prefix}/set-notification`;
export function setNotification(msgKey, severity, msgVars, position) {
  return {
    type: SET_NOTIFICATION,
    payload: {
      msgKey,
      severity,
      msgVars,
      position
    }
  };
}

export const CLEAR_NOTIFICATION = `${prefix}/clear-notification`;
export function clearNotification() {
  return {
    type: CLEAR_NOTIFICATION
  };
}
export const SET_NOTIFICATION_BANNER = `${prefix}/set-notification-banner`;

export function setNotificationBanner(visible) {
  return {
    type: SET_NOTIFICATION_BANNER,
    payload: {
     visible
    }
  };
}

export const CLEAR_NOTIFICATION_BANNER = `${prefix}/clear-notification-banner`;
export function clearNotificationBanner() {
  return {
    type: CLEAR_NOTIFICATION_BANNER
  };
}

export const SET_SOUND_NOTIFICATION = `${prefix}/set-sound-notification`;
export function setSoundNotification(playable) {
  return {
    type: SET_SOUND_NOTIFICATION,
    payload: {
      playable
    }
  };
}

export const SET_EMBEDDED = `${prefix}/set-embedded`;
export function setEmbedded(embedded) {
  return {
    type: SET_EMBEDDED,
    payload: {
      embedded
    }
  };
}

export const SET_PARAMS = `${prefix}/set-params`;
export function setParams(version, platform, device, readerId) {
  return {
    type: SET_PARAMS,
    payload: {
      version,
      platform,
      device,
      readerId
    }
  };
}

export const RESET = `${prefix}/reset`;
export function reset() {
  return {
    type: RESET
  };
}
