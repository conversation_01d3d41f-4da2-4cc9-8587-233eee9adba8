import { fail, ok } from '../helpers';

const prefix = 'restaurant';

export const GET_RESTAURANT = `${prefix}/get-restaurant`;
export function getRestaurant(restaurantId) {
  return {
    type: GET_RESTAURANT,
    restaurantId
  };
}
export function getRestaurantSuccess(restaurant) {
  return {
    type: ok(GET_RESTAURANT),
    payload: {
      restaurant
    }
  };
}
export function getRestaurantFailure() {
  return {
    type: fail(GET_RESTAURANT)
  };
}

export const GET_RESTAURANTS = `${prefix}/get-restaurants`;
export function getRestaurantsSuccess(restaurants, total) {
  return {
    type: ok(GET_RESTAURANTS),
    payload: {
      restaurants,
      total
    }
  };
}

export const SET_ONBOARDING_STATE = `${prefix}/set-onboarding-state`;
export function setOnboardingState(onboardingState, restaurantId) {
  return {
    type: SET_ONBOARDING_STATE,
    payload: {
      onboardingState,
      restaurantId
    }
  };
}

export const GET_ORGANIZATIONS = `${prefix}/get-organizations`;
export function getOrganizationsSuccess(organizations) {
  return {
    type: ok(GET_ORGANIZATIONS),
    payload: {
      organizations
    }
  };
}

export const GET_TUTORIAL = `${prefix}/get-tutorial`;
export function getTutorial(restaurantId) {
  return {
    type: GET_TUTORIAL,
    payload: {
      restaurantId
    }
  };
}

export function getTutorialSuccess(tutorial) {
  return {
    type: ok(GET_TUTORIAL),
    payload: {
      tutorial
    }
  };
}

export function getTutorialFailure() {
  return {
    type: fail(GET_TUTORIAL)
  };
}

export const RESET = `${prefix}/reset`;
export function reset() {
  return {
    type: RESET
  };
}
