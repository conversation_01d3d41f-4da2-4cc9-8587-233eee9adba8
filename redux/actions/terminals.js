import { fail, ok } from '../helpers';

const prefix = 'terminals';

export const GET_TERMINALS = `${prefix}/get-terminals`;
export function getTerminals(restaurantId, online, readerId) {
  return {
    type: GET_TERMINALS,
    payload: {
      restaurantId,
      online,
      readerId
    }
  };
}

export function getTerminalsSuccess(terminals) {
  return {
    type: ok(GET_TERMINALS),
    payload: {
      terminals
    }
  };
}
export function getTerminalsFailure() {
  return {
    type: fail(GET_TERMINALS)
  };
}
