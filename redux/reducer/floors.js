import { fromJS } from 'immutable';
import {appActions, floorsActions} from '../actions';
import { ok } from '../helpers';
import byId from "../../src/utils/byId";

export const initialState = fromJS({
  floors: [],
  tables: {
    tables: [],
    byId: {}
  },
  selectedFloorIndex: 0
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case ok(floorsActions.GET_FLOORS): {
      const { version, floors } = action.payload;
      const updatedState = state
        .set('floors', fromJS(floors))
        .set('version', version);
      
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
  
    case ok(floorsActions.GET_TABLES): {
      const { tables = [] } = action.payload;
      const updatedState = state
        .setIn(['tables', 'tables'], fromJS(tables))
        .setIn(['tables', 'byId'], fromJS(byId(tables)));
    
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
    
    case floorsActions.SET_SELECTED_FLOOR_INDEX: {
      const { selectedFloorIndex } = action.payload;
      const updatedState = state
        .set('selectedFloorIndex', selectedFloorIndex);
    
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
  
    case appActions.RESET: {
      return initialState;
    }

    default:
      return state;
  }
}

export default reducer;
