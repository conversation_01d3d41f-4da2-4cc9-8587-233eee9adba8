import { fromJS } from "immutable";
import { appActions, terminalsActions } from "../actions";
import { ok } from "../helpers";

export const initialState = fromJS({
  terminals: []
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case ok(terminalsActions.GET_TERMINALS): {
      const { terminals = [] } = action.payload;
      return state.set('terminals', fromJS(terminals))
    }
  
    case appActions.RESET: {
      return initialState;
    }

    default:
      return state;
  }
}

export default reducer;
