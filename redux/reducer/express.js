import { fromJS } from 'immutable';
import { appActions, expressActions } from "../actions";
import { operationViews } from "../constants";

export const initialState = fromJS({
  view: operationViews.BOARD.key,
  loading: true,
  [operationViews.BOARD.key]: {
    lanes: [],
    total: 0
  },
  [operationViews.LIST.key]: {
    items: [],
    total: 0,
    totalAmount: 0
  },
  total: 0
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case expressActions.SET_LOADING: {
      const { loading } = action.payload
      const updatedState = state
        .set("loading", loading);
      
      if (state.equals(updatedState)){
        return state;
      }
      return updatedState;
    }
  
    case expressActions.SET_VIEW: {
      const { view } = action.payload
      const updatedState = state
        .set("view", view);
    
      if (state.equals(updatedState)){
        return state;
      }
      return updatedState;
    }
    
    case expressActions.SET_BOARD: {
      const { total, lanes = [] } = action.payload;
      const lanesWithActiveGroup = lanes.map(({ groups = [] }) => {
        const activeGroupId = groups ? groups[0].id : null;
        return {
          groupId: activeGroupId,
          groups
        }
      })
      const updatedState = state
        .setIn([operationViews.BOARD.key, "lanes"], fromJS(lanesWithActiveGroup))
        .setIn([operationViews.BOARD.key, "total"], total)
        .set("total", total)
      
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
    
    case expressActions.SET_BOARD_ACTIVE_GROUP: {
      const { laneIndex, groupId } = action.payload;
      return state
        .setIn(["lanes", laneIndex, "groupId"], fromJS(groupId));
    }
  
    case expressActions.SET_LIST: {
      const { total, totalAmount, items = [] } = action.payload;
      const updatedState =  state
        .setIn([operationViews.LIST.key, "total"], total)
        .setIn([operationViews.LIST.key, "totalAmount"], totalAmount)
        .setIn([operationViews.LIST.key, "items"], fromJS(items))
        .set("total", total);
      
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
  
    case appActions.RESET: {
      return initialState;
    }

    default:
      return state;
  }
}

export default reducer;
