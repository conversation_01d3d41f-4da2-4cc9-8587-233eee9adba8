import { fromJS } from "immutable";
import { appActions, marketplaceActions} from "../actions";

export const initialState = fromJS({
  myApps: {
    items: [],
    total: 0
  },
  apps: {},
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case marketplaceActions.SET_MY_APPS: {
      const { total, items = [] } = action.payload;
      const updatedState = state
        .setIn(["myApps", "total"], total)
        .setIn(["myApps", "items"], fromJS(items))
  
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
  
    case appActions.RESET: {
      return initialState;
    }

    default:
      return state;
  }
}

export default reducer;
