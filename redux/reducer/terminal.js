import { fromJS } from "immutable";
import { accountActions, appActions, terminalActions } from "../actions";
import { orderTypes, takeawayPartners } from "../constants";
import { orderItemsGroups } from "../../src/utils/const";
import { isEmpty } from "../helpers";

export const initialState = fromJS({
  order: {},
  orderByOrderingStatus: [],
  groupBy: "NONE",
  processingPayment: false,
  details: {
    type: orderTypes.PICKUP.key,
    takeawayDate: null,
    pickupTime: null,
    partnerId: takeawayPartners.ALLO.key,
    customer: null
  },
  params: {
    orderId: null,
    orderType: null,
    stage: null
  },
  courses: {
    selectedCourse: null
  },
  pager: "",
  creatingTakeaway: false,
});

function reducer(state = initialState, action) {
  switch (action.type) {
  
    case terminalActions.SET_ORDER: {
      const { order = {} } = action.payload;
      const updatedState = state
        .set('order', fromJS(order))
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
    
    case terminalActions.SET_ORDER_BY_ORDERING_STATUS: {
      const { orderByOrderingStatus = [] } = action.payload;
      const updatedState = state.set('orderByOrderingStatus', fromJS(orderByOrderingStatus));
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
    
    case terminalActions.SET_PROCESSING_PAYMENT: {
      const { status } = action.payload;
      return  state.set('processingPayment', status)
    }
    
    case terminalActions.SET_ORDER_DETAILS: {
      const { details = {} } = action.payload;
      const updatedState = state.set('details', fromJS(details));
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }

    case terminalActions.SET_TAKEAWAY_ORDER_CUSTOMER: {
      const { customer = {} } = action.payload;
      if (!isEmpty(customer)) {
        const updatedState = state
          .setIn(['details', 'customer'], fromJS(customer))
          .set('creatingTakeaway', true)
        if (state.equals(updatedState)) {
          return state;
        }
        return updatedState;
      }
      return state;
    }

    case terminalActions.SET_CREATING_TAKEAWAY: {
      const { creating } = action.payload;
      return state
        .set('creatingTakeaway', creating)
    }
    
    case terminalActions.SET_PAGER: {
      const { pager } = action.payload;
      const updatedState = state.set('pager', fromJS(pager));
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
    
    case terminalActions.SET_PARAMS: {
      const { params } = action.payload;
      return state.set('params', fromJS(params))
    }
  
    case terminalActions.SET_GROUP_BY: {
      const { groupBy } = action.payload;
      return state.set('groupBy', groupBy)
    }
  
    case terminalActions.SET_SELECTED_COURSE: {
      const { course } = action.payload;
      return state.setIn(['courses', 'selectedCourse'], fromJS(course))
    }
    
    case accountActions.SET_ACCOUNT: {
      const { account } = action.payload;
      const { preferences = {} } = (account || {})
      const { enableOrderGroupBy, orderGroupByDefault } = (preferences || {})
      if (enableOrderGroupBy && Object.keys(orderItemsGroups).indexOf(orderGroupByDefault) > -1) {
        return state.set('groupBy', orderGroupByDefault)
      }
      
      return state
    }
    
    case appActions.RESET: {
      return initialState.set('groupBy', state.get('groupBy'));
    }
  
    case terminalActions.RESET: {
      return initialState
        .set('groupBy', state.get('groupBy'))
        .setIn(['courses', 'selectedCourse'], state.getIn(['courses', 'selectedCourse']));
    }

    default:
      return state;
  }
}

export default reducer;
