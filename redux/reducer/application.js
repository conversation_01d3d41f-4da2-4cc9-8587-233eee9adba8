import { fromJS } from 'immutable';
import {fail, ok} from "../helpers";
import { LOAD_RESTAURANT, SET_EMBEDDED, SET_PARAMS } from "../actions/app";

export const initialState = fromJS({
  ui: {
    loading: true,
    error: false,
    forbidden: false
  },
  embedded: false,
  platform: null,
  device: null,
  readerId: null,
  version: null
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case ok(LOAD_RESTAURANT): {
      return state
        .setIn(['ui', 'loading'], false);
    }
  
    case fail(LOAD_RESTAURANT): {
      return state
        .setIn(['ui', 'loading'], false)
        .setIn(['ui', 'error'], true);
    }
    
    case SET_EMBEDDED: {
      const { embedded } = action.payload;
      return state.set("embedded", embedded)
    }
  
    case SET_PARAMS: {
      const { version, platform, device, readerId } = action.payload;
      return state
        .set("version", version)
        .set("platform", platform)
        .set("device", device)
        .set("readerId", readerId)
    }
    
    default:
      return state;
  }
}

export default reducer;
