import { fromJS } from 'immutable';
import { ok, fail } from '../helpers';
import { shopOrderHistoryActions } from '../actions';

const initialState = fromJS({
  items: [],
  pages: 0,
  total: 0
})

function reducer(state = initialState, action){
  switch (action.type) {
    case ok(shopOrderHistoryActions.GET_ORDERS): {
      const { data } = action.payload;
      return state
        .setIn(['items'], fromJS(data.items))
        .setIn(['total'], fromJS(data.total))
        .setIn(['pages'], fromJS(data.pages))
    }
    default: {
      return state;
    }
  }
}

export default reducer;
