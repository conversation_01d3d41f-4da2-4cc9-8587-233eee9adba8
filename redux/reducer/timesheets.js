import { fromJS } from "immutable";
import { appActions, timesheetsActions } from "../actions";
import { operationViews } from "../constants";

export const initialState = fromJS({
  view: operationViews.LIST.key,
  date: new Date(),
  loading: true,
  [operationViews.LIST.key]: {
    items: [],
    total: 0
  },
  [operationViews.TIMELINE.key]: {
    tables: [],
    floors: [],
    items: []
  },
  total: 0,
  timesheet: {}
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case timesheetsActions.SET_LOADING: {
      const { loading } = action.payload
      const updatedState = state
        .set("loading", loading);
    
      if (state.equals(updatedState)){
        return state;
      }
      return updatedState;
    }
  
    case timesheetsActions.SET_VIEW: {
      const { view } = action.payload
      const updatedState = state
        .set("view", view);
    
      if (state.equals(updatedState)){
        return state;
      }
      return updatedState;
    }
  
    case timesheetsActions.SET_DATE: {
      const { date } = action.payload
      const updatedState = state
        .set("date", date ?? new Date());
    
      if (state.equals(updatedState)){
        return state;
      }
      return updatedState;
    }
  
    case timesheetsActions.SET_LIST: {
      const { total, items = [] } = action.payload;
      const updatedState = state
        .setIn([operationViews.LIST.key, "total"], total)
        .setIn([operationViews.LIST.key, "items"], fromJS(items))
        .set("total", total);
  
      if (state.equals(updatedState)) {
        return state;
      }
      return updatedState;
    }
  
    case timesheetsActions.SET_TIMESHEET: {
      const { timesheet } = action.payload;
      return state
        .set('timesheet', fromJS(timesheet));
    }
    
    case timesheetsActions.RESET_TIMESHEET: {
      return state
        .set('timesheet', fromJS({}));
    }
  
    case appActions.RESET: {
      return initialState;
    }

    default:
      return state;
  }
}

export default reducer;
