import { call, take, race, delay, put, all, takeLatest, select, takeEvery } from "redux-saga/effects";
import {
  appActions,
  pollingActions, printersActions
} from "../actions";
import * as api from '../../src/api';
import isEmpty from '../../src/utils/isEmpty'
import { resolvePrinters } from "../../src/api";
import { printerBrands } from "../constants";
import { accountSelectors, configurationSelectors, printerSelectors, restaurantSelectors } from "../selectors";

const _encodeEscapeSequence = function(a) {
  let b = /[<>&]/g;
  b.test(a) && (a = a.replace(b, function(a) {
    switch (a) {
      case"<":
        return "&lt;";
      case ">":
        return "&gt;";
    }
    return "&amp;";
  }));
  return a;
};

function* getPrinters({ payload }) {
  try {
    const { restaurantId } = payload;
    const { data = [] } = yield call(resolvePrinters, restaurantId);
    yield put(printersActions.getPrintersSuccess(data));
    if (!isEmpty(data) && data.some(p => (p.brand === printerBrands.EPSON.key) || (p.brand === printerBrands.STAR.key))) {
      yield put(pollingActions.stopPollingPrinting());
      yield put(pollingActions.startPollingPrinting(restaurantId, data));
    } else {
      yield put(pollingActions.stopPollingPrinting());
    }
  } catch (err) {
    yield put(printersActions.getPrintersFailure())
  }
}

function* pingPrinter() {
  try {
    const printers = yield select(printerSelectors.getPrinters);
    const printer = printers.find(pr => pr.printerCategory === "MAIN" && ((pr.brand === printerBrands.EPSON.key) || (pr.brand === printerBrands.STAR.key)))
    if (printer) {
      if (printer.brand === printerBrands.EPSON.key) {
        const printPayload = `<?xml version="1.0" encoding="utf-8"?><s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><epos-print xmlns="http://www.epson-pos.com/schemas/2011/03/epos-print"></epos-print></s:Body></s:Envelope>`
        yield call(api.printPrintingQueueItemEpson, printer.ip, 'local_printer', printPayload);
      }
      if (printer.brand === printerBrands.STAR.key) {
        const printPayload = `<StarWebPrint xmlns="http://www.star-m.jp" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Request>&lt;root&gt;&lt;initialization /&gt;&lt;/root&gt;</Request></StarWebPrint>`
        yield call(api.printPrintingQueueItemStar, printer.ip, printPayload);
      }
    } else {}
  } catch (err) {}
}

function* openCashDrawer() {
  try {
    const printers = yield select(printerSelectors.getPrinters);
    const printer = printers.find(pr => pr.printerCategory === "MAIN" && ((pr.brand === printerBrands.EPSON.key) || (pr.brand === printerBrands.STAR.key)))
    if (printer) {
      if (printer.brand === printerBrands.EPSON.key) {
        const printPayload = `<?xml version="1.0" encoding="utf-8"?><s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><epos-print xmlns="http://www.epson-pos.com/schemas/2011/03/epos-print"><pulse drawer=\"drawer_1\" time=\"pulse_100\" /></epos-print></s:Body></s:Envelope>`
        yield call(api.printPrintingQueueItemEpson, printer.ip, 'local_printer', printPayload);
      }
      if (printer.brand === printerBrands.STAR.key) {
        const printPayload = `<StarWebPrint xmlns="http://www.star-m.jp" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Request>&lt;root&gt;&lt;initialization /&gt;&lt;peripheral channel=\"1\" on=\"200\" off=\"200\" /&gt;&lt;/root&gt;</Request></StarWebPrint>`
        yield call(api.printPrintingQueueItemStar, printer.ip, printPayload);
      }
    } else {}
  } catch (err) {}
}

function* printContent()  {
  try {
    const restaurantId = yield select(restaurantSelectors.getRestaurantId);
    const { configuration = {} } = yield select(configurationSelectors.getConfiguration);
    const { pollPrinterAccountIds } = configuration;
  
    if (!isEmpty(pollPrinterAccountIds)) {
      // if there are pollAccounts configured
      // get current account id
      // and if its not in the polling accounts
      // return before getting any data
      const accountId = yield select(accountSelectors.getAccountId);
      if (pollPrinterAccountIds.indexOf(accountId) === -1) {
        return
      }
    }
  
    const { data = {} } = yield call(api.getPrintingQueueNext, restaurantId);
    console.log(new Date().toISOString() + " - PContent data fetched")
  
    if (!isEmpty(data)) {
      const { id, parentPrintingContentId, data: printPayload, dataBytes: printPayloadBytes, printerId, printerIp, printerBrand, printerName } = data;
      console.log(new Date().toISOString() + " - PContent " + id + " fetched")
      const printers = yield select(printerSelectors.getPrinters);
      const printer = isEmpty(printers) ? null : printers.find(p => p.id === printerId) //|| p.osName === printerName);
    
      let resolvedId = parentPrintingContentId ? parentPrintingContentId : id
    
      let resolvedIp = printerIp;
      let resolvedBrand = printerBrand
      let resolvedName = printerName
      if (!isEmpty(printer)) {
        const { ip, brand, osName } = printer;
        // resolvedIp = ip;
        resolvedBrand = brand;
        resolvedName = osName
      }
  
      if (!resolvedIp) {
        return
      }
    
      let printerFeedback = null;
    
      try {
        let response;
        yield put(appActions.setNotification('pushing-print-request', 'success'));
      
        if (printerBrand === printerBrands.EPSON.key) {
          console.log(new Date().toISOString() + " - PContent " + id + " printer called")
          console.log("Resolved ip " + resolvedIp + " | resolvedName " + resolvedName)
          if (resolvedIp) {
            response = yield call(api.printPrintingQueueItemEpson, resolvedIp, 'local_printer', printPayload);
          }
          // if (resolvedName) {
          //   response = yield call(api.printPrintingQueueItemEpsonViaUsb, resolvedName, printPayloadBytes);
          // }
          console.log(new Date().toISOString() + " - PContent " + id + " printer responded")
        } else if (printerBrand === printerBrands.STAR.key) {
          console.log(new Date().toISOString() + " - PContent " + id + " printer called")
          if (resolvedIp) {
            response = yield call(api.printPrintingQueueItemStar, resolvedIp, printPayload);
          }
          // if (resolvedName) {
          //   response = yield call(api.printPrintingQueueItemStarViaUsb, resolvedName, printPayloadBytes);
          // }
          console.log(new Date().toISOString() + " - PContent " + id + " printer responded")
        } else {
          response = `no brand found to print for content, ip: ${resolvedIp} brand: ${resolvedBrand}`
        }
        
        if (!response) {
          response = `failed to print ip: ${resolvedIp}`;
        }
      
        if ((printerBrand === printerBrands.EPSON.key) && response) {
          if (resolvedIp && response.request && response.request.responseXML.getElementsByTagName('response')[0].getAttribute('success') === "true") {
            printerFeedback = { response: "success" }
          // } else if (resolvedName && response) {
          //   printerFeedback = { response: "success" }
          } else {
            printerFeedback = { response: JSON.stringify(response), failure: true }
          }
        } else if ((printerBrand === printerBrands.STAR.key) && response) {
          if (resolvedIp) {
            printerFeedback = { response: "success" }
          // } else if (resolvedName && response) {
          //   printerFeedback = { response: "success" }
          } else {
            printerFeedback = { response: JSON.stringify(response), failure: true }
          }
        } else {
          printerFeedback = { response: JSON.stringify(response), failure: true }
        }
      } catch (err) {
        printerFeedback = { response: JSON.stringify(err), failure: true }
      }
      yield call(api.sendPrintingQueueItemResponse, restaurantId, resolvedId, printerFeedback)
    }
    
  } catch (err) {
    yield put(appActions.setNotification('pushing-print-request', 'error'));
    console.log("error")
  }
}

function* consumePrinting({ payload }) {
  try {
  
    const { configuration = {} } = yield select(configurationSelectors.getConfiguration);
    const { pollPrinterAccountIds } = configuration;
  
    if (!isEmpty(pollPrinterAccountIds)) {
      // if there are pollAccounts configured
      // get current account id
      // and if its not in the polling accounts
      // return before getting any data
      const accountId = yield select(accountSelectors.getAccountId);
      if (pollPrinterAccountIds.indexOf(accountId) === -1) {
        return
      }
    }
    
    const { restaurantId, printers = [] } = payload;
    const { data = {} } = yield call(api.getPrintingQueueNext, restaurantId);
    
    if (!isEmpty(data)) {
      const { id, parentPrintingContentId, data: printPayload, printerId, printerIp, printerBrand } = data;
      const printer = isEmpty(printers) ? null : printers.find(p => p.id === printerId);
      
      let resolvedId = parentPrintingContentId ? parentPrintingContentId : id
      
      let resolvedIp = printerIp;
      let resolvedBrand = printerBrand
      if (!isEmpty(printer)) {
        const { ip, brand } = printer;
        resolvedIp = ip;
        resolvedBrand = brand;
      }
      
      if (!resolvedIp) {
        return
      }
      
      
      // const url = `https://${ip}/cgi-bin/epos/service.cgi?devid=local_printer&timeout=10000`;
      
      // Send print document
      // const xhr = new XMLHttpRequest();
      // xhr.open('POST', url, true);
      // xhr.setRequestHeader('Content-Type', 'text/xml; charset=utf-8');
      // xhr.setRequestHeader('If-Modified-Since', 'Thu, 01 Jan 1970 00:00:00 GMT');
      // xhr.setRequestHeader('SOAPAction', '""');
      // xhr.onreadystatechange = function () {
      //   if (xhr.readyState === 4) {
      //     if (xhr.status === 200) {
      //       // alert(xhr.responseXML.getElementsByTagName('response')[0].getAttribute('success'));
      //     }
      //     else {
      //       //error
      //     }
      //   }
      // };
      // xhr.send(printPayload);
  
      let printerFeedback = null;
      
      try {
        let response;
        yield put(appActions.setNotification('pushing-print-request', 'success'));
        
        if (printerBrand === printerBrands.EPSON.key) {
          response = yield call(api.printPrintingQueueItemEpson, resolvedIp, 'local_printer', printPayload);
        } else if (printerBrand === printerBrands.STAR.key) {
          response = yield call(api.printPrintingQueueItemStar, resolvedIp, printPayload);
        } else {
          response = `no brand found to print for content, ip: ${resolvedIp} brand: ${resolvedBrand}`
        }
        
        if ((printerBrand === printerBrands.EPSON.key) && response && response.request && response.request.responseXML.getElementsByTagName('response')[0].getAttribute('success') === "true") {
          printerFeedback = { response: "success" }
        } else if ((printerBrand === printerBrands.STAR.key) && response) {
          printerFeedback = { response: "success" }
        } else {
          printerFeedback = { response: JSON.stringify(response), failure: true }
        }
      } catch (err) {
        printerFeedback = { response: JSON.stringify(err), failure: true }
      }
      yield call(api.sendPrintingQueueItemResponse, restaurantId, resolvedId, printerFeedback)
    } else {
      // yield put(printersActions.pingPrinters())
    }
  } catch (err) {
    console.log("error")
  }
}

function* pollPrinting(action) {
  while (true) {
    try {
      yield delay(30000);
      // yield put(printersActions.consumePrinting(action.payload));
      yield* consumePrinting(action);
    } catch (err) {
      yield delay(60000);
    }
  }
}

// function* pingPrinting() {
//   while (true) {
//     try {
//       yield delay(4000);
//       yield put(printersActions.pingPrinters());
//     } catch (err) {
//       yield delay(15000);
//     }
//   }
// }

function* pollPrintingWatcher() {
  while (true) {
    const action = yield take(pollingActions.POLL_PRINTING);
    yield race([
      call(pollPrinting, action),
      take(pollingActions.STOP_POLL_PRINTING)
    ]);
  }
}

// function* pingPrintingWatcher() {
//   while (true) {
//     const action = yield take(pollingActions.POLL_PRINTING);
//     yield race([
//       call(pingPrinting, action),
//       take(pollingActions.STOP_POLL_PRINTING)
//     ]);
//   }
// }

function* rootSaga() {
  yield all([
    takeLatest(printersActions.GET_PRINTERS, getPrinters),
    takeLatest(printersActions.OPEN_CASH_DRAWER, openCashDrawer),
    takeLatest(printersActions.CONSUME_PRINTING, consumePrinting),
    takeLatest(printersActions.PING_PRINTERS, pingPrinter),
    takeEvery(printersActions.PRINT_CONTENT, printContent)
  ]);
}

export default rootSaga;

export {
  pollPrintingWatcher,
  // pingPrintingWatcher
};
