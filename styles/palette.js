/**
 All Rights Reserved
 Copyright (c) 2022 allO Technology GmbH
 Created by <PERSON><PERSON><PERSON>
 
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 <PERSON>ITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
 */

export default {
  primary: {
    100: "#FDE7D6",
    200: "#FFD5B8",
    300: "#FFA787",
    400: "#FF9378",
    500: "#FF7C5C",
    600: "#E56F52",
    700: "#CC6449",
    800: "#994B37",
    900: "#663225",
  },
  secondary: {
    blue: {
      100: "#CCE5FF",
      700: "#2B5178"
    },
    purple: {
      100: "#F6ECFF",
      700: '#524072'
    },
    green: {
      100: "#E1EAE8",
      200: "#D0E1DC",
      400: "#73AF9F",
      500: "#4D9985",
      600: "#428271"
    },
    yellow: {
      100: "#FFF2D4",
      500: "#FFD36E",
      700: '#9E8344'
    },
  },
  negative: {
    100: "#FFE5E8",
    200: "#FDCDD0",
    300: "#FCB7BC",
    400: "#F5898D",
    500: "#F06060",
    600: "#E04649",
    700: "#D1152B",
    800: "#A30F2D",
    900: "#48041B",
  },
  grayscale: {
    white: "#FFFFFF",
    100: "#F9F9F9",
    200: "#F2F2F2",
    250: "#EFEFEE",
    300: "#E8E7E6", //"#E5E4E3",
    350: "#D8D7D6",
    400: "#BAB9B8",
    500: "#929191",
    600: "#737372",
    700: "#525251",
    800: "#333332",
    900: "#242423",
    black: "#000000",
    divider: "#E8E7E6",
    border: "#D9D9D8"
  },
  transparency: {
    light: {
      90: "rgba(242, 242, 242, 0.90)",
      80: "rgba(249, 249, 249, 0.8)",
      50: "rgba(249, 249, 249, 0.5)",
      20: "rgba(249, 249, 249, 0.2)",
      12: "rgba(249, 249, 249, 0.12)",
      border: {
        20: "rgba(216, 215, 214, 0.2)"
      }
    },
    dark: {
      80: "rgba(51, 51, 50, 0.8)",
      50: "rgba(51, 51, 50, 0.5)",
      20: "rgba(51, 51, 50, 0.2)",
      8: "rgba(51, 51, 50, 0.08)",
      4: "rgba(51, 51, 50, 0.04)"
    },
  },
};
