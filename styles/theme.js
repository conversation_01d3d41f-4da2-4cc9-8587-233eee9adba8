import { createMuiTheme } from '@material-ui/core/styles';
import { red } from '@material-ui/core/colors';
import { fade } from '@material-ui/core';
import palette from "./palette";
import shadows from "./shadows";

export const appBarHeight = 44;
export const drawer = 348;
export const bottomNavigation = 56;
export const tabs = 49;
export const offset = 56;

export const colors = {
  leviee: {
    main: {
      green: '#FF7C5C', //'#00848A',
      dark: '#FF7C5C', //'#04172F',
      white: '#FFFFFF'
    },
    secondary: {
      wine: '#B10744',
      red: '#FE4C56',
      brown: '#D0772A',
      orange: '#FE754B',
      yellow: '#FFAE33',
      lightYellow: '#FFC475',
      beige: '#FFDFB0',
      blue: '#4696F7',
      midGreen: '#209571',
      darkGreen: '#236B55',
    },
    greyscale: {
      darkGray: '#435163',
      midGray: '#919CA9',
      gray: '#CCD1D7',
      lightGray: '#E0E3E7',
      lighterGray: '#F0F1F3',
      lightestGray: '#F9F9F9',
    }
  },
};

export const fontStyles = {
  displayTitle: {
    fontSize: 24,
    fontWeight: 600,
    lineHeight: '30px',
    letterSpacing: '0.48px'
  },
  titleRegular: {
    fontSize: 19,
    fontWeight: 400,
    lineHeight: '24px',
    letterSpacing: '0.2px'
  },
  titleMedium: {
    fontSize: 19,
    fontWeight: 500,
    lineHeight: '24px',
    letterSpacing: '0.2px'
  },
  secondaryTitleRegular: {
    fontSize: 17,
    fontWeight: 400,
    lineHeight: '21px',
    letterSpacing: '0.3px'
  },
  paragraphRegular: {
    fontSize: 15,
    fontWeight: 400,
    lineHeight: '20px',
    letterSpacing: '-0.012em'
  },
  paragraphMedium: {
    fontSize: 15,
    fontWeight: 500,
    lineHeight: '20px',
    letterSpacing: '-0.012em'
  },
  smallParagraphRegular: {
    fontSize: 13,
    fontWeight: 400,
    lineHeight: '16px',
    letterSpacing: '-0.005em'
  },
  smallParagraphMedium: {
    fontSize: 13,
    fontWeight: 500,
    lineHeight: '16px',
    letterSpacing: '-0.005em'
  }
};

export const containerStyles = {
  displayTitleContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 4,
    marginBottom: 4
  },
  titleMediumContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 4 + 14,
    marginBottom: 4
  },
  titleMediumWithDividerContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 12,
    marginBottom: 4
  },
  titleMediumWithTextContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 4,
    marginBottom: 4
  },
  titleMediumDivider: {
    marginTop: 24,
    borderBottom: `1px solid ${fade(colors.leviee.main.dark, 0.06)}`
  },
  titleMediumWithTextDivider: {
    marginTop: 32,
    borderBottom: `1px solid ${fade(colors.leviee.main.dark, 0.06)}`
  },
  secondaryTitleContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 24
  },
  buttonContainer: {
    padding: 12
  },
  buttonContainerWithMargin: {
    padding: 12,
    marginTop: 24
  },
  textContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 4,
    marginBottom: 4
  }
};

export const drawerModalStyle = {
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 1500,
  overscrollBehaviorY: 'contain',
  inset: 0,
  display: 'flex',
  justifyContent: 'center',
  flexShrink: 0,
  alignItems: 'center',
  transition: 'background-color 0.2s linear 0s',
  overflowY: 'auto',
};

// Create a theme instance.
const theme = createMuiTheme({
  palette: {
    primary: {
      main: '#333332',
    },
    secondary: {
      main: '#FF7C5C',
      contrastText: '#F2F2F2'
    },
    error: {
      main: red.A400,
    },
    background: {
      paper: '#F2F2F2',
      default: '#F2F2F2'
    },
    text: {
      primary: '#333332'
    },
    common: {
      white: "#F9F9F9",
      black: '#333332'
    }
  },
  typography: {
    fontFamily: [
      'Inter',
      'Nunito Sans',
      '-apple-system',
      'system-ui',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    h1: {
      fontSize: 24,
      lineHeight: '36px',
      fontWeight: 500,
      fontDisplay: 'swap'
    },
    h2: {
      fontSize: 20,
      lineHeight: 'normal',
      fontWeight: 500,
      fontDisplay: 'swap'
    },
    h3: {
      fontSize: 18,
      lineHeight: '24px',
      fontWeight: 600,
      fontDisplay: 'swap'
    }
  },
  props: {
    MuiButtonBase: {
      disableRipple: false, // No more ripple, on the whole application 💣!
    }
  },
});

const getZoom = () => {
  let preferences = {};
  try {
    if (typeof window !== 'undefined') {
      const preferencesString = localStorage.getItem('preferences')
      preferences = JSON.parse(preferencesString);
    }
  } catch (e) {}
  const { appFontSize = 1 } = (preferences || {});
  return appFontSize;
}

const withOverrides = {
  ...theme,
  breakpoints: {
    values: {
      xl: 1660,
      ...theme.breakpoints.values
    },
    ...theme.breakpoints
  },
  // mixins: {
  //   ...theme.mixins,
  //   toolbar: {
  //     ...theme.mixins.toolbar,
  //     minHeight: 50
  //   }
  // },
  overrides: {
    MuiCssBaseline: {
      '@global': {
        html: {
          WebkitFontSmoothing: 'auto',
          userSelect: 'none',
          '-webkit-tap-highlight-color': 'transparent',
          textRendering: 'optimizeLegibility !important',
          '-webkit-font-smoothing': 'antialiased !important',
          '-moz-osx-font-smoothing': 'grayscale',
          'zoom': getZoom(),
          '-webkit-text-size-adjust': 'none',
          background: palette.grayscale["100"]
        },
        body: {
          touchAction: 'pan-x pan-y',
          position: "relative",
          overflow: "hidden"
        }
      }
    },
    MuiPaper: {
      elevation1: {
        boxShadow: '0 2px 10px rgb(0 0 0 / 20%)'
      }
    },
    MuiToolbar: {
      gutters: {
        paddingLeft: theme.spacing(2),
        paddingRight: theme.spacing(2)
      }
    },
    MuiButton: {
      root: {
        fontSize: 16,
        lineHeight: '28px',
        padding: '8px 16px',
        textTransform: 'none',
        whiteSpace: 'nowrap',
        borderRadius: 12
      },
      textSizeSmall: {
        fontSize: 14,
        paddingLeft: theme.spacing(1),
        paddingRight: theme.spacing(1)
      },
      containedSizeSmall: {
        fontSize: 14
      },
      outlinedSizeSmall: {
        borderRadius: 12,
        border: "1px solid #D9D9D8"
      }
    },
    MuiToggleButton: {
      root: {
        "&$selected": {
          backgroundColor: theme.palette.secondary.main,
          color: '#fff'
        },
      }
    },
    MuiAvatar: {
      root: {
        width: 32,
        height: 32
      }
    },
    MuiAutocomplete: {
      popper: {
        zIndex: 1801
      },
      paper: {
        background: "#F9F9F9",
        ...shadows.large,
        transform: "translateZ(0)",
        willChange: "transform",
        borderRadius: 12
      },
      option: {
        minHeight: "44px !important",
        "&[data-focus=\"true\"]": {
          backgroundColor: "#F2F2F2",
        }
      }
    },
    MuiAvatarGroup: {
      avatar: {
        '&:first-child': {
          marginLeft: 0
        }
      }
    },
    // MuiDrawer: {
    //   paper: {
    //     borderRadius: '10px 10px 0 0'
    //   }
    // },
    MuiDrawer: {
      root: {
        '&&': {
          zIndex: '1800 !important'
        }
      },
      paper: {
        '&&': {
          margin: 16,
          height: `calc(100% - 32px)`,
          maxHeight: "-webkit-fill-available !important",
          borderRadius: 16,
          boxShadow: "0px 24px 32px rgba(0, 0, 0, 0.04), 0px 16px 24px rgba(0, 0, 0, 0.04), 0px 4px 8px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.04)",
          '@media (max-width: 600px)': {
            margin: 0,
            marginTop: '0 !important',
            borderRadius: 0,
            height: "100% !important"
          }
        }
      }
    },
    MuiDialog: {
      root: {
        '&&': {
          zIndex: '1800 !important'
        }
      },
      paper: {
        margin: 8
      }
    },
    MuiMenu: {},
    MuiPopover: {
      root: {
        '&&': {
          zIndex: '1801 !important'
        }
      }
    },
    MuiBottomNavigation: {
      root: {
        // boxShadow: 'rgba(0, 0, 0, 0.075) 0px -1.64px 2.46px 0px',
        width: '100%',
        position: 'fixed',
        bottom: 0,
        zIndex: 1301,
        // borderTop: '1px solid #f3f4f4',
        background: '#fbfbfb',
        height: bottomNavigation
      }
    },
    MuiAppBar: {
      root: {
        zIndex: 1300
      }
    },
    MuiBottomNavigationAction: {
      // wrapper: {
      //   display: 'block'
      // },
      root: {
        // color: theme.palette.text.primary,
        // paddingTop: theme.spacing(1),
        // '&&:selected': {
        //   color: theme.palette.secondary.main
        // }
        paddingBottom: theme.spacing(1) - 2,
        maxWidth: 100,
        minWidth: 0,
        "&$selected": {
          color: theme.palette.secondary.main,
        },
      },
      selected: {
        color: theme.palette.primary.main,
        fontSize: 'inherit'
      },
      label: {
        "&$selected": {
          color: theme.palette.secondary.main,
          // fontSize: 10,
          fontWeight: 600,
          fontSize: theme.typography.caption.fontSize
        },
        marginTop: theme.spacing(1) - 4,
        // fontSize: 10,
        fontWeight: 600
      },
      wrapper: {
        '& > svg': {
          height: 18,
          width: 18
        }
      }
    },
    MuiContainer: {
      root: {
        paddingLeft: theme.spacing(2) + 4,
        paddingRight: theme.spacing(2) + 4,
        '&+&': {
          marginTop: theme.spacing(5)
        }
      }
    },
    MuiSvgIcon: {
      root: {
        width: 20,
        height: 20
      }
    },
    MuiInputBase: {
      root: {
        width: '100%'
      }
    },
    MuiInput: {
      underline: {
        '&::before': {
          borderBottom: '1px solid #f3f4f4'
        },
        '&::after': {
          borderBottom: '2px solid #f3f4f4'
        }
      }
    },
    MuiTabs: {
      root: {
        // borderBottom: '1px solid #f3f4f4',
        // background: theme.palette.common.white,
        minHeight: 36
      },
      scroller: {
        height: 36,
        marginTop: 4
      },
      indicator: {
        display: 'flex',
        justifyContent: 'center',
        backgroundColor: 'transparent',
        height: '32px',
        marginBottom: 4,
        '& > div': {
          // maxWidth: 90,
          width: '100%',
          backgroundColor: theme.palette.common.black,
          boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.04), 0px 0px 1px rgba(0, 0, 0, 0.02)',
          borderRadius: 8
        },
      }
    },
    MuiTab: {
      root: {
        textTransform: 'none',
        minHeight: 32,
        minWidth: 50,
        paddingLeft: 10,
        paddingRight: 10,
        paddingTop: 0,
        paddingBottom: 0,
        '@media (min-width: 600px)': {
          minWidth: 50
        },
        color: palette.grayscale["600"]
      },
      selected: {
        zIndex: 1,
        color: palette.grayscale["100"]
      },
      textColorInherit: {
        opacity: 1,
        color: palette.grayscale["600"]
      }
    },
    MuiTableContainer: {
      root: {
        background: "#F9F9F9",
        borderRadius: "12px",
        marginBottom: 16,
        boxShadow: "0px 4px 8px rgb(0 0 0 / 2%), 0px 0px 2px rgb(0 0 0 / 4%), 0px 0px 1px rgb(0 0 0 / 2%)"
      }
    },
    MuiTableRow: {
      root: {
        backgroundColor: palette.grayscale["100"],
        "&:nth-of-type(2n+1):not(&$footer):not(&$head)": {
          backgroundColor: "rgba(242, 242, 242, 0.6)"
        },
        '&$selected': {
          backgroundColor: 'inherit'
        }
      },
      head: {
        left: 0,
        top: 0,
        position: "sticky",
        zIndex: 2,
        background: "#F9F9F9",
      },
      footer: {
        left: 0,
        bottom: 0,
        position: "sticky",
        background: "#F9F9F9",
        borderBottomLeftRadius: 12,
        borderBottomRightRadius: 12,
      }
    },
    MuiTableHead: {
      root: {
        "&&": {
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          background: "#F9F9F9 !important"
        }
      }
    },
    MuiTableFooter: {
      root: {
        backgroundColor: "#F9F9F9",
      }
    },
    MuiTableCell: {
      root: {
        borderBottom: "none",
        whiteSpace: 'nowrap',
        paddingTop: 12,
        paddingBottom: 12,
        maxWidth: 400,
        verticalAlign: 'middle',
        overflowX: "hidden",
        lineHeight: "20px",
        height: 44
      },
      head: {
        whiteSpace: 'nowrap',
        paddingTop: 12,
        paddingBottom: 12,
        maxWidth: 400,
        verticalAlign: 'middle',
        fontStyle: "normal",
        fontWeight: "500",
        fontSize: "11px",
        lineHeight: "16px",
        letterSpacing: "0.02em",
        textTransform: "uppercase",
        color: "#737372",
        "&&:first-child": {
          borderTopLeftRadius: 12,
        },
        "&&:last-child": {
          borderTopRightRadius: 12,
        }
      },
      stickyHeader: {
        "&&": {
          background: "transparent",
          // borderTopLeftRadius: 12,
          // borderTopRightRadius: 12,
          position: "static"
        }
      },
      footer: {
				fontStyle: "normal",
        fontWeight: 500,
        fontSize: "11px",
        lineHeight: "16px",
        letterSpacing: "0.02em",
        textTransform: "uppercase",
        color: "#737372",
        borderTop: "1px solid #EFEFEE",
        "&&:first-child": {
          borderBottomLeftRadius: 12
        },
        "&&:last-child": {
          borderBottomRightRadius: 12
        }
      }
    },
    MuiChip: {
      root: {
        borderRadius: theme.spacing(1) - 4
      }
    },
    MuiSnackbar: {
      root: {
        bottom: 0,
        left: 0,
        right: 0,
        '&& > div': {
          minHeight: bottomNavigation,
          alignItems: 'center',
          width: '100%'
        }
      },
      anchorOriginBottomCenter: {
        bottom: 0
      },
      anchorOriginBottomRight: {
        left: "auto",
        bottom: 24,
        right: 24
      }
    },
    MuiAlert:{
      standardError: {
        color: '#fff',
        backgroundColor: theme.palette.secondary.main,
        '& > $icon': {
          color: '#fff'
        }
      },
    },
    MuiPickersStaticWrapper: {
      root: {
        backgroundColor: theme.palette.common.white,
      }
    },
    MuiPickersDesktopDateRangeCalendar: {
      root: {
        justifyContent: "center"
      },
      arrowSwitcher: {
        paddingLeft: 0,
        paddingRight: 0
      }
    },
    MuiPickersDay: {
      root: {
        color: "#333332",
        "&:focus": {
          "&$selected": {
            backgroundColor: '#FF7C5C',
          },
        },
        "&$selected": {
          backgroundColor: '#FF7C5C',
          "&:hover": {
            backgroundColor: '#FF7C5C',
          },
          "&:focus": {
            backgroundColor: '#FF7C5C',
          }
        },
      },
    },
    MuiPickersDateRangeDay: {
      root: {
        paddingLeft: 2,
        paddingRight: 2
      },
      rangeIntervalDayHighlight: {
        backgroundColor: "#FDE7D6"
      },
      dayInsideRangeInterval: {
        color: "#333332",
      }
    },
    MuiPickersCalendar: {
      week: {
        justifyContent: "center"
      }
    },
    MuiPickersArrowSwitcher: {
      iconButton: {
        backgroundColor: "transparent"
      }
      
    },
    MuiBackdrop: {
      root: {
        backgroundColor: "rgba(51, 51, 50, 0.5)"
      }
    },
    MuiNativeSelect: {
      icon: {
        top: 'calc(50% - 10px)',
        color: "#333332",
        right: 8
      }
    },
    MuiListItem: {
      root: {
        '&$focusVisible': {
          backgroundColor: "transparent"
        }
      }
    }
  }
};

export const severityColors = {
  success: palette.grayscale["800"],
  error: palette.grayscale["800"]
};

export default withOverrides;
