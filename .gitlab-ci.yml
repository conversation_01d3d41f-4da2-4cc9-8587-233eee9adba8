variables:
  BUILD_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/allo-docker-public/allo-pipeline:node20_gcloud_512"
  DEFAULT_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/library/docker:27.3.1-git"
  DEFAULT_SERVICE_DIND_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/library/docker:27.3.1-dind"
  DEPLOY_KUBERNETES_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/gcr-io/google.com/cloudsdktool/cloud-sdk"
  E2E_VERSION: 1.50.0 # this is also maybe used in package.json
  E2E_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/mcr-microsoft-com/playwright:v${E2E_VERSION}-noble"
  PUBLISH_CONTAINER_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/library/docker:27.3.1"

image: $DEFAULT_IMAGE
services:
  - $DEFAULT_SERVICE_DIND_IMAGE

# Set default config for jobs
# see https://docs.gitlab.com/ee/ci/yaml/#default
default:
  retry:
    max: 2
    # specify retry on certain conditions
    # see https://docs.gitlab.com/ee/ci/yaml/index.html#retrywhen
    when:
      - unknown_failure
      - api_failure
      - runner_system_failure
      - job_execution_timeout
      - stuck_or_timeout_failure

.before_pkg: &before_pkg
  - |
    while (! docker stats --no-stream >/dev/null 2>&1); do
      sleep 2
    done

.authenticate: &authenticate
  - echo $ARTIFACT_REGISTRY_PUBLISHER_JSON_KEY | base64 -d -i > ~/gcloud-service-key.json
  - gcloud auth activate-service-account --key-file ~/gcloud-service-key.json
  - gcloud config set project $GCLOUD_PROJECT_ID
  - gcloud --quiet container clusters get-credentials --zone=$CLOUDSDK_COMPUTE_ZONE $CLOUDSDK_CONTAINER_CLUSTER
  - gcloud auth configure-docker europe-west3-docker.pkg.dev  
  - export GOOGLE_APPLICATION_CREDENTIALS=~/gcloud-service-key.json
  - npx -y google-artifactregistry-auth --credential-config .npmrc

stages:
  - build
  - e2e-test
  - package
  - deploy
  - system-refresh

build_test:
  stage: build
  image: ${BUILD_IMAGE}
  script:
    - *authenticate
    - NODE_ENV=development yarn install
    - npm run-script test
    - npm run-script build
    - ls -la
  artifacts:
    paths:
      - node_modules/
      - .next/
    expire_in: 1 day
  only:
    - /^(development|feature\/.+)$/

build:
  stage: build
  image: europe-west3-docker.pkg.dev/iac-dev-432418/allo-docker-public/allo-pipeline:node20_gcloud_503
  script:
    - *authenticate
    - yarn install --production
    - npm run-script build
    - ls -la
  artifacts:
    paths:
      - node_modules/
      - .next/
    expire_in: 1 day
  only:
    - master


##########################################################
# 
# E2E TEST
#
##########################################################
e2e-test:
  stage: e2e-test
  image: 
    name: $E2E_IMAGE
    entrypoint: ["/bin/sh", "-c"]
  needs:
    - job: build_test
      artifacts: true
  script:
    - |
      if [[ -f playwright.config.js ]];then
        echo "Start e2e tests..."
        echo "---"
        ls -lah
        echo "---"
        export PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
        export NEXT_TELEMETRY_DISABLED=1
        printenv | sort
        echo "---"
        DEBUG=pw:webserver npx playwright@${E2E_VERSION} test
      else
        echo "no playwright.config.js found - skipping tests...";
        exit 219;
      fi
  after_script:
    - |
      if [[ -f playwright.config.js ]];then
       echo "====================================="
       echo "-"
       echo "- Report will be available at https://$CI_PROJECT_ROOT_NAMESPACE.$CI_PAGES_DOMAIN/-/$CI_PROJECT_NAME/-/jobs/$CI_JOB_ID/artifacts/playwright-report/index.html"
       echo "-"
       echo "====================================="
      fi
  allow_failure:
    exit_codes: 219
  artifacts:
    paths:
      - playwright-report
      - test-results
      - node_modules/
      - .next/
    expire_in: 1 day
  only:
    - /^(development|feature\/.+)$/


.push_image: &push_image
  - echo -n "${RELEASE_NAME}:${CI_COMMIT_SHORT_SHA}-$(date '+%Y_%m_%d_%H_%M_%S')" >> release_name
  - echo "$ARTIFACT_REGISTRY_PUBLISHER_JSON_KEY" | base64 -d -i > ~/gcloud-service-key.json
  - cat ~/gcloud-service-key.json | docker login -u _json_key --password-stdin https://europe-docker.pkg.dev
  - cat ~/gcloud-service-key.json | docker login -u _json_key --password-stdin https://europe-west3-docker.pkg.dev
  - docker build -f Dockerfile -t europe-docker.pkg.dev/${GCR_PROJECT}/allo-docker/allo/$(cat release_name) --build-arg ENVIRONMENT=${ACTIVE_PROFILE_VALUE} .
  - docker push europe-docker.pkg.dev/${GCR_PROJECT}/allo-docker/allo/$(cat release_name)
  - cp $HOME/.docker/config.json docker_config.json

package-docker_test:
  image: ${PUBLISH_CONTAINER_IMAGE}
  stage: package
  before_script: *before_pkg
  script:
    - export ACTIVE_PROFILE_VALUE=staging
    - export RELEASE_NAME=gluttony-client-development
    - *push_image
  artifacts:
    paths:
      - release_name
      - docker_config.json
    expire_in: 1 day
  only:
    - /^(development|feature\/.+)$/

package-docker:
  image: ${PUBLISH_CONTAINER_IMAGE}
  stage: package
  before_script: *before_pkg
  script:
    - export ACTIVE_PROFILE_VALUE=production
    - export RELEASE_NAME=gluttony-client
    - *push_image
  artifacts:
    paths:
      - release_name
      - docker_config.json
    expire_in: 1 day
  only:
    - master

.gcloud_test: &gcloud_test
  - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
  - echo $GCLOUD_SERVICE_KEY_TEST
  - echo $GCLOUD_SERVICE_KEY_TEST | base64 --decode -i > ~/gcloud-service-key-test.json
  - gcloud auth activate-service-account --key-file ~/gcloud-service-key-test.json
  - gcloud config set project $GCLOUD_PROJECT_ID_TEST
  - gcloud --quiet container clusters get-credentials --region=$CLOUDSDK_COMPUTE_REGION_TEST $CLOUDSDK_CONTAINER_CLUSTER_TEST

.gcloud: &gcloud
  - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
  - echo $GCLOUD_SERVICE_KEY
  - echo $GCLOUD_SERVICE_KEY | base64 --decode -i > ~/gcloud-service-key.json
  - gcloud auth activate-service-account --key-file ~/gcloud-service-key.json
  - gcloud config set project $GCLOUD_PROJECT_ID
  - gcloud --quiet container clusters get-credentials $CLOUDSDK_CONTAINER_CLUSTER

.deploy_to_k8s: &deploy_to_k8s
  - |
    kubectl apply -f - <<EOF
      apiVersion: v1
      kind: Secret
      metadata:
        name: artifact-registry-publisher
      type: kubernetes.io/dockerconfigjson
      data:
        .dockerconfigjson: $(cat docker_config.json | base64 | tr -d '\n')
    EOF
  - sed -e "s/SERVICE-VERSION/europe-docker.pkg.dev\/${GCR_DEPLOY_DEV_PROJECT}\/allo-docker\/allo\/$(cat release_name)/g" $KUBE_FILE_NAME > kubernetes-out.yaml
  - kubectl apply -f kubernetes-out.yaml

deploy_test:
  image: ${DEPLOY_KUBERNETES_IMAGE}
  stage: deploy
  before_script: *gcloud_test
  script:
    - export KUBE_FILE_NAME=kubernetes.yaml
    - *deploy_to_k8s
  only:
    - /^(development|feature\/.+)$/

deploy:
  image: ${DEPLOY_KUBERNETES_IMAGE}
  stage: deploy
  before_script: *gcloud
  script:
    - export KUBE_FILE_NAME=kubernetes-prod.yaml
    - *deploy_to_k8s
  only:
    - master

system_refresh_preview:
  image: europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/alpine/curl:latest
  stage: system-refresh
  script:
    - curl -X POST "https://restaurant-preview.leviee.de/websocket-service/system-events/_system-refresh?token=LH%2BseG4nS%3AYGDN%3EQ"
  when: manual
  allow_failure: true
  only:
    - /^(development|feature\/.+)$/

system_refresh_production:
  image: europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/alpine/curl:latest
  stage: system-refresh
  when: manual
  allow_failure: true
  script:
    - curl -X POST "https://restaurant.leviee.de/websocket-service/system-events/_system-refresh?token=LH%2BseG4nS%3AYGDN%3EQ"
  only:
    - master
