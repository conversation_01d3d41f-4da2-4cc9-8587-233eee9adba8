const NextI18Next = require('next-i18next').default;

const languages = ['de', 'en', 'zh', 'tr', 'vi', 'pt', 'ko', 'ja', 'hi', 'es', 'sq', 'zt'];

const options = {
  ignoreRoutes:	['/_next/', '/static/'],
  defaultLanguage: 'de',
  otherLanguages: ['en', 'zh', 'tr', 'vi', 'pt', 'ko', 'ja', 'hi', 'es', 'sq', 'zt'],
  fallbackLng: 'de',
  shallowRender: true
};

const NextI18NextInstance = new NextI18Next(options);

NextI18NextInstance.i18n.languages = languages;

module.exports = NextI18NextInstance;
