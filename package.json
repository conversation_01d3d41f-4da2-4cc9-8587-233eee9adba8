{"name": "gluttony", "version": "0.0.574", "private": true, "scripts": {"dev": "NODE_OPTIONS=--openssl-legacy-provider next", "build": "NODE_OPTIONS=--openssl-legacy-provider next build", "start": "NODE_OPTIONS=--openssl-legacy-provider node server/app.js", "startProd": "NODE_OPTIONS=--openssl-legacy-provider next start", "now": "NODE_OPTIONS=--openssl-legacy-provider now --prod", "playwright:install": "npx playwright install --with-deps", "test": "jest", "test:e2e": "DEBUG=pw:webserver npx playwright test", "test:e2e:ui": "npx playwright test --ui", "test:e2e:debug": "playwright test --debug"}, "dependencies": {"@allo/ui-lib": "1.0.4", "@date-io/moment": "^1.3.13", "@material-ui/core": "4.12.1", "@material-ui/lab": "^4.0.0-alpha.40", "@material-ui/pickers": "^4.0.0-alpha.11", "@react-google-maps/api": "^2.19.2", "@react-hook/size": "^2.1.2", "@zxing/library": "^0.17.0", "axios": "^0.19.1", "babel-plugin-transform-imports": "^2.0.0", "body-parser": "^1.19.0", "client-oauth2": "^4.3.3", "clsx": "latest", "compression": "^1.7.4", "compressorjs": "^1.0.6", "cookie": "^0.4.0", "cookie-session": "^1.4.0", "crisp-sdk-web": "^1.0.13", "crypto": "^1.0.1", "dotenv": "^8.2.0", "emoji-picker-react": "^3.4.8", "express": "^4.17.1", "handlebars": "^4.7.7", "html-to-image": "^1.6.2", "i18next": "^19.6.2", "immutable": "^4.0.0-rc.14", "initials": "^3.1.1", "interactjs": "^1.10.11", "js-cookie": "^2.2.1", "jwt-decode": "^2.2.0", "lodash": "^4.17.15", "lottie-react": "^2.3.1", "moment": "^2.24.0", "net": "^1.0.2", "next": "10.2.3", "next-i18next": "^4.5.0", "next-redux-wrapper": "^6.0.2", "path-browserify": "^1.0.1", "postcss": "^8.4.39", "posthog-js": "1.171.0", "prop-types": "latest", "qrcode.react": "^1.0.1", "react": "latest", "react-calendar-timeline": "^0.27.0", "react-charts": "^3.0.0-beta.51", "react-detect-offline": "^2.4.5", "react-diff-viewer": "^3.1.1", "react-dom": "latest", "react-drag-drawer": "^3.3.4", "react-draggable": "^4.4.5", "react-dropzone": "^11.2.4", "react-fade-in": "^0.1.8", "react-grid-layout": "^0.18.3", "react-lazy-load-image-component": "^1.5.1", "react-moment": "^0.9.7", "react-otp-input": "^2.4.0", "react-player": "^2.9.0", "react-quill": "^2.0.0", "react-redux": "^7.1.3", "react-swipeable-views": "0.13.8", "redux": "^4.0.5", "redux-immutablejs": "^0.0.8", "redux-saga": "^1.1.3", "reselect": "^4.1.5", "shortid": "^2.2.16", "sitemap": "^5.1.0", "sockjs-client": "^1.5.1", "stompjs": "^2.3.3", "use-long-press": "^1.0.5", "zuzel-printer": "^1.0.4"}, "devDependencies": {"@next/bundle-analyzer": "^14.1.0", "@playwright/test": "1.50.0", "@welldone-software/why-did-you-render": "^8.0.1", "babel-eslint": "^10.1.0", "eslint": "^7.28.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-next": "^11.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "jest": "^29.7.0", "redux-devtools-extension": "^2.13.8", "start-server-and-test": "^2.0.1"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}