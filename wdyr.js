import React from 'react'

if (process.env.NODE_ENV === 'development') {
  // if (typeof window !== 'undefined') {
  //   const whyDidYouRender = require('@welldone-software/why-did-you-render')
  //   whyDidYouRender(React, {
  //     exclude: [/^CalendarTimeline/, /^Interval/, /^CustomDateHeader/, /^PickersDay/, /^DateRangeDay/, /^ArrowSwitcher/],
  //     // trackAllPureComponents: true,
  //     trackExtraHooks: [[require('react-redux/lib'), 'useSelector']],
  //     logOwnerReasons: true,
  //     logOnDifferentValues: true,
  //     collapseGroups: true
  //   })
  // }
}
