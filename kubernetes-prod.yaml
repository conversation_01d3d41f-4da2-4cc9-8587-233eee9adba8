---
apiVersion: "v1"
kind: "List"
items:
- apiVersion: policy/v1
  kind: PodDisruptionBudget
  metadata:
    labels:
      app: "gluttony-client"
    name: "gluttony-client"
  spec:
    minAvailable: 1
    selector:
      matchLabels:
        app: "gluttony-client"
- apiVersion: "v1"
  kind: "Service"
  metadata:
    labels:
      app: "gluttony-client"
    name: "gluttony-client"
  spec:
    ports:
    - name: "http"
      port: 80
      targetPort: 4160
    selector:
      app: "gluttony-client"
- apiVersion: getambassador.io/v2
  kind: Mapping
  metadata:
    name: gluttony-client
  spec:
    prefix: /
    host: restaurant.leviee.de
    service: gluttony-client:80
    allow_upgrade:
      - websocket
    timeout_ms: 30000
    connect_timeout_ms: 30000
- apiVersion: getambassador.io/v2
  kind: Mapping
  metadata:
    name: gluttony-client-app
  spec:
    prefix: /
    host: app.allo.restaurant
    service: gluttony-client:80
    allow_upgrade:
      - websocket
    timeout_ms: 30000
    connect_timeout_ms: 30000
- apiVersion: "apps/v1"
  kind: "Deployment"
  metadata:
    labels:
      app: "gluttony-client"
    name: "gluttony-client"
  spec:
    replicas: 4
    selector:
      matchLabels:
        app: "gluttony-client"
    template:
      metadata:
        labels:
          app: "gluttony-client"
      spec:
        topologySpreadConstraints:
          - labelSelector:
              matchLabels:
                app: "gluttony-client"
            matchLabelKeys:
              - pod-template-hash
            maxSkew: 1
            whenUnsatisfiable: DoNotSchedule
            topologyKey: "kubernetes.io/hostname"
        imagePullSecrets:
          - name: artifact-registry-publisher
        containers:
        - env:
          - name: "KUBERNETES_NAMESPACE"
            valueFrom:
              fieldRef:
                fieldPath: "metadata.namespace"
          - name: FRONT_CHAT_ID
            value: 599434b39c0a372153b94f237d8b5cac
          - name: FRONT_CHAT_IDENTITY
            value: 81aaaf2368e8e2f9dd12750cb3a317b1
          - name: POSTHOG_KEY
            value: phc_Fw1qW8cfWVBoCruU8tUdC2FW7ZKqfN4vpMhbFhZ3jRP
          image: "SERVICE-VERSION"
          imagePullPolicy: "IfNotPresent"
          name: "gluttony-client"
          resources:
            requests:
              cpu: 75m
              memory: 200Mi
            limits:
              memory: 1000Mi
          lifecycle:
            preStop:
              exec:
                command:
                  - "/bin/sh"
                  - "-ce"
                  - |
                    sleep 10 # give k8s svc balancer the chance to unregister routing before shutting down
          ports:
          - containerPort: 4160
            name: "http"
            protocol: "TCP"
          startupProbe:
            httpGet:
              scheme: HTTP
              path: /health-check
              port: 4160
            periodSeconds: 5
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 40 # number of tries every periodSeconds - total time would be failureThreshold * periodSeconds
          readinessProbe:
            httpGet:
              scheme: HTTP
              path: /health-check
              port: 4160
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 2
            failureThreshold: 10
          livenessProbe:
            httpGet:
              #Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.
              scheme: HTTP
              #Path to access on the HTTP server.
              path: /health-check
              #Name or number of the port to access on the container. Number must be in the range 1 to 65535.
              port: 4160
            #Number of seconds after the container has started before liveness or readiness probes are initiated. Defaults to 0 seconds. Minimum value is 0.
            initialDelaySeconds: 5
            #How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.
            periodSeconds: 10
            #Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1.
            timeoutSeconds: 3
            #Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness. Minimum value is 1.
            successThreshold: 1
            #When a Pod starts and the probe fails, Kubernetes will try failureThreshold times before giving up. Giving up in case of liveness probe means restarting the container. In case of readiness probe the Pod will be marked Unready. Defaults to 3. Minimum value is 1
            failureThreshold: 10
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            seccompProfile:
              type: "RuntimeDefault"
            capabilities:
              drop:
                - "ALL"
          volumeMounts:
            - mountPath: /tmp
              name: tmp-volume
            - mountPath: /var/run
              name: tmp-run-volume
            - mountPath: /var/log
              name: tmp-log-volume
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          fsGroup: 1000
        terminationGracePeriodSeconds: 20
        volumes:
          - emptyDir: {}
            name: tmp-volume
          - emptyDir: {}
            name: tmp-run-volume
          - emptyDir: {}
            name: tmp-log-volume
