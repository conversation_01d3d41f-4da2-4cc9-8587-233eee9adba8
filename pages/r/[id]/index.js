import React from "react";
import { useRouter } from "next/router";
import Application from "../../../src/features/application";

const RestaurantPage = (props) => {
  const router = useRouter();
  const restaurantId = router.query.id;

  return <Application restaurantId={restaurantId} {...props} />;
};

RestaurantPage.getInitialProps = async function() {
  return {
    namespacesRequired: ['common']
  };
};

export default RestaurantPage;
