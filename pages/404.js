import React, { useEffect } from "react";
import { useRouter } from "next/router";
import EmptyScreen from "../src/components/_placeholder/EmptyScreen";
import { NoReservation120 } from "../src/utils/icons";

const Page = () => {
  const router = useRouter();

  const onBackToHomeHandler = () => {
    router.push("/").then(() => {});
  };

  return (
    <div style={{ height: '100%' }}>
      <EmptyScreen
        icon={<NoReservation120 />}
        titleI18nKey={"generic-error-page-title"}
        descriptionI18nKey={"generic-error-page-description"}
        action={{ i18nKey: "generic-error-page-button-text", onClick: onBackToHomeHandler }}
      />
    </div>
  );
};

export default Page;
